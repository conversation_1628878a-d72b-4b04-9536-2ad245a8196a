// .container {
//   border-top: 1px solid #eee;
//   margin-top: 16px;
//   width: 100%;
//   overflow: hidden;
// }

// .allTableBox {
//   width: 100%;
//   position: relative;
// }

// .tableBox {
//   min-width: 100%;
//   overflow: auto;
//   white-space: nowrap;
//   position: relative;
// }

// .tableHeader {
//   width: 100%;
//   display: flex;
//   height: 66px;
//   font-family: Roboto-Regular, Roboto !important;
//   font-weight: 500 !important;
//   font-size: 20px;
// }
// .columnOne {
//   padding-left: 34px;
//   flex: 1 0 264.96px;
//   border-right: 1px solid #eee;
//   color: #333;
//   line-height: 66px;
//   border-bottom: 1px solid #eee;
// }
// .columnOnePlayer {
//   display: flex;
//   align-items: center;
//   height: 100%;
//   position: relative;
// }

// .playerName {
//   max-width: 150px;
//   display: inline-block;
//   font-size: 20px;
//   font-family: Roboto-Regular, <PERSON><PERSON>;
//   font-weight: 400;
//   color: #0f80da !important;
//   overflow: hidden;
//   white-space: nowrap;
//   text-overflow: ellipsis;
// }

// .playerP {
//   color: rgb(153, 153, 153);
//   margin-left: 3.2px;
//   transform: scale(0.8);
// }

// .firstIcon {
//   color: #e6732e;
//   position: absolute;
//   right: 3.2px;
// }

// .columnOther {
//   line-height: 66px;
//   box-sizing: border-box;
//   flex: 1 0 92.74px;
//   text-align: center;
//   border-bottom: 1px solid #eee;
// }

// .font400 {
//   font-family: Roboto-Regular, Roboto !important;
//   font-weight: 400 !important;
// }

// .trow {
//   display: flex;
//   height: 66px;
//   min-width: 100%;
// }

// .copyTableColumnOne {
//   width: 264.96px;
//   white-space: nowrap;
//   position: absolute;
//   top: 0;
//   left: 0;
//   z-index: 99;
//   background: #fff;
// }

.basketball-box-score-table-container {
  margin-top: 16px;
  width: 100%;
  overflow-x: scroll;
  background: #2C2C2C;

  .custom-box-score-table {
    width: 100%;
    display: flex;
    flex-direction: column;
    border-collapse: collapse;
    margin-bottom: 10px;

    .table-header {
      display: flex;
      justify-content: space-between;
      text-align: left;
      color: #fff;
      background-color: #2c2c2c;

      .header-cell {
        text-align: center;
        padding: 8px;
      }

      .first-column {
        position: sticky; 
        left: 0;
        z-index: 11; 
        width: 250px;
        flex-shrink: 0;
        background-color: inherit;
      }

      .header-cell:not(.first-column) {
        padding: 8px;
        flex: 1;
        min-width: 10%;
      }
    }

    .table-row {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #2c2c2c;
      text-align: left;
      color: #fff;
      position: relative;

      .table-cell {
        font-size: 24px;
        text-align: center;
        background: #121212;
      }

      .player-cell {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: sticky; 
        left: 0;
        z-index: 10; 
        width: 250px;
        flex-shrink: 0;
        padding: 0px 8px;
        background-color: #121212;

        .player-name {
          font-weight: 600;
          text-align: left;
        }

        .player-position {
          color: #727476;
        }
      }

      .table-cell:not(.player-cell) {
        padding: 8px;
        flex: 1;
        min-width: 10%;
      }
    }
  }
}
