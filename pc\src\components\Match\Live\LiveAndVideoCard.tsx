import './LiveAndVideoCard.less'

import { useCallback, useEffect, useState } from 'react';
import { useHistory, useLocation } from 'umi';

import { Carousel } from 'antd';
import { getCompetitionList } from 'iscommon/api/home';
import { isPluginMode } from '@/layouts/plugin';

const LiveAndVideoCard: React.FC = () => {
  const [liveMatches, setLiveMatches] = useState([]);
  const history = useHistory();
  const location = useLocation();
  const pluginMode = isPluginMode();

  const handleMatchClick = (matchId: string) => {
    const searchParams = new URLSearchParams(location.search);
    const pluginParams = searchParams.toString();
    const basePath = '/football-match'; // or '/football-match' depending on sport

    history.replace(`${basePath}/${matchId}/Overview?${pluginParams}`);
  };

  const fetchLiveMatches = useCallback(async () => {
    try {
      const { competitions } = await getCompetitionList({ listType: 0 });
      const filtered = competitions
        .flatMap((comp) =>
          (comp.matches || [])
            .filter((match) => match.vlive === 1) // Only vlive is required now
            .map((match) => ({ ...match, competitionName: comp.competitionName }))
        );
      setLiveMatches(filtered);
    } catch (e) {
      console.error('Failed to fetch live matches in LiveAndVideoCard:', e);
    }
  }, []);

  useEffect(() => {
    fetchLiveMatches(); // initial
    const interval = setInterval(fetchLiveMatches, 3000); // every 3s
    return () => clearInterval(interval);
  }, [fetchLiveMatches]);

  useEffect(() => {
    if (pluginMode && liveMatches.length > 0) {
      const currentMatchId = liveMatches[0].matchId;
      const urlParts = location.pathname.split('/');
      const currentUrlMatchId = urlParts[2]; // Assumes /football-match/:matchId/Overview
  
      if (currentMatchId !== currentUrlMatchId) {
        const searchParams = new URLSearchParams(location.search);
        const pluginParams = searchParams.toString();
        const basePath = '/football-match';
  
        history.replace(`${basePath}/${currentMatchId}/Overview?${pluginParams}`);
      }
    }
  }, [pluginMode, liveMatches, location.pathname]);

  const getAsiaOdds = (match) => {
    const asiaOdds = (match.matchOdds || []).find((odds) => odds.oddsType === 'asia');
    return asiaOdds?.oddsData || null;
  };

  if (!liveMatches.length) return null;
  
  return (
    <div className="live-featured-card-container">
      <Carousel dots={false} swipeToSlide draggable infinite={false} variableWidth>
        {liveMatches.map((match) => {
          const asiaOdds = getAsiaOdds(match);
          return (
            <div key={match.matchId} className="carousel-item" onClick={() => handleMatchClick(match.matchId)}>
              <div className="container-title">{match.competitionName}</div>
              <div className="container-body">
                <div className="home-team">
                  <img className="team-icon" loading="lazy" src={match.homeTeam?.logo} alt={match.homeTeam.name} />
                  {asiaOdds && <div className="odds-text">{asiaOdds[0]}</div>}
                </div>
                <div className="match-score-container">
                  <div className="match-score">{`${match.calculatedHomeScore} - ${match.calculatedAwayScore}`}</div>
                </div>
                <div className="away-team">
                  <img className="team-icon" loading="lazy" src={match.awayTeam?.logo} alt={match.awayTeam.name} />
                  {asiaOdds && <div className="odds-text">{asiaOdds[2]}</div>}
                </div>
              </div>
            </div>
          );
        })}
      </Carousel>
    </div>
  )
}
export default LiveAndVideoCard;