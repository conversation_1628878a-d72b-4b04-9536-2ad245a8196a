// .section {
//   display: flex;
//   flex-direction: column;
//   width: 100%;
//   padding: 24px 0;
//   background: #ffffff;
//   margin-bottom: 16px;

//   .header {
//     display: flex;
//     align-items: center;
//     margin-bottom: 24px;
//     padding: 0 24px;
//     box-sizing: border-box;
//     .title {
//       flex: 1;
//       padding: 0 24px;
//       font-size: 28px;
//       font-weight: bold;
//       color: #0f80da;
//       line-height: 28px;
//     }
//     .count {
//       font-size: 32px;
//       font-weight: 500;
//       color: #0f80da;
//       line-height: 33px;
//     }
//   }

//   .list {
//     display: flex;
//     flex-wrap: wrap;
//     padding: 0 60px 0 104px;
//     .year {
//       margin-right: 10px;
//       font-size: 24px;
//       font-weight: 500;
//       color: #999999;
//       line-height: 40px;
//     }
//   }
// }

.team-champion-history-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .container-body {
    background: #1e1e1e;
    padding: 10px;

    .custom-collapse {
      .adm-list-default, 
      .adm-list-body,
      .adm-list-item,
      .adm-list-item-content {
        background: transparent;
        border: none;
      }

      .adm-list {
        --border-inner: none;
      }

      .panel-header {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .honor-container {
          display: flex;
          align-items: center;

          .honor-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 48px;
            height: 48px;
            border-radius: 50%;
            margin-right: 10px;
            overflow: hidden;
            background-size: cover;
          }
  
          .honor-name {
            font-size: 22px;
            font-weight: bold;
            color: #fff;
            text-overflow: ellipsis;
          }
        }

        .honor-count {
          margin-right: 10px;
          font-size: 22px;
          color: #fff;
        }
      }

      .seasons-list {
        display: flex;
        flex-wrap: wrap;

        .season-item {
          flex: 1 1 20%;
          padding: 10px;
          display: flex;
          justify-content: center;
          color: #fff;
          font-size: 22px;
        }
      }
    }
  }
}