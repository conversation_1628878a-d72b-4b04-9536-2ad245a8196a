import './ScoreTable.less'

import { BadmintonInLiveStatusEnum, BadmintonStatusCodeEnum } from 'iscommon/const/badminton/constant';
import { inject, observer } from 'mobx-react';

import { FallbackPlayerImage } from 'iscommon/const/icon';
import { getBadmintonScoreList } from 'iscommon/utils/dataUtils';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

const ScoreTable = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Smallball: {
          SmallballMatch: { matchHeaderInfo },
        },
      },
    } = props;

    const { scores, matchStatus, servingSide, homeTeam, awayTeam } = matchHeaderInfo;
    const labelMaps = useTranslateKeysToMaps(['Score']);

    if (matchStatus === BadmintonStatusCodeEnum.NotStarted) return null;

    const list = getBadmintonScoreList(matchStatus, scores, servingSide, false);
    if (!list || list.length < 3) return null;

    const gameSets = list.slice(2);
    const finalSet = list[1];
    const finalIndex = gameSets.length; // Final will always be after sets
    const scoreList = [...gameSets, finalSet];

    const showThirdSet = gameSets.length >= 3 && (gameSets[2]?.h || gameSets[2]?.w);
    const isMatchEnded = !BadmintonInLiveStatusEnum.includes(matchStatus);

    const renderScoreRow = (isHome: boolean) => {
      const team = isHome ? homeTeam : awayTeam;
      const key = isHome ? 'h' : 'w';
      const opponentKey = isHome ? 'w' : 'h';
      const finalSet = scoreList[scoreList.length - 1];
    
      return (
        <div className="custom-score-table-body">
          <div className="score-cell team-cell">
            <img className="team-icon" src={team.logo || FallbackPlayerImage} alt={team.name} loading="lazy" />
            <span className="team-name">{team.name}</span>
          </div>
    
          {/* Set 1 and Set 2 */}
          {scoreList.slice(0, 2).map((set, index) => {
            const current = set?.[key] ?? '-';
            const opponent = set?.[opponentKey] ?? '-';
    
            let colorClass = '';
            const hasStarted = (set?.h || set?.w); // true only if either score is non-zero
            
            if (!hasStarted) {
              colorClass = ''; // no color for unplayed set
            } else if (set?.isRed) {
              colorClass = 'color-c1272d';
            } else if (current > opponent) {
              colorClass = 'color-white';
            } else if (current < opponent) {
              colorClass = 'color-dim';
            }
    
            return (
              <div key={index} className="score-cell">
                <span className={colorClass}>{current}</span>
              </div>
            );
          })}
    
          {/* Optional Set 3 */}
          {showThirdSet && (() => {
            const set = scoreList[2];
            const current = set?.[key] ?? '-';
            const opponent = set?.[opponentKey] ?? '-';
    
            let colorClass = '';
            if (set?.isRed) {
              colorClass = 'color-c1272d';
            } else if (current > opponent) {
              colorClass = 'color-white';
            } else if (current < opponent) {
              colorClass = 'color-dim';
            }
    
            return (
              <div className="score-cell">
                <span className={colorClass}>{current}</span>
              </div>
            );
          })()}
    
          {/* Final Score (always shown) */}
          {(() => {
            const current = finalSet?.[key] ?? '-';
            const opponent = finalSet?.[opponentKey] ?? '-';
    
            let colorClass = '';
            if (finalSet?.isRed) {
              colorClass = 'color-c1272d';
            } else if (current > opponent) {
              colorClass = 'color-white';
            } else if (current < opponent) {
              colorClass = 'color-dim';
            }
    
            return (
              <div className="score-cell total-cell">
                <span className={colorClass}>{current}</span>
              </div>
            );
          })()}
        </div>
      );
    };

    return (
      <div className="badminton-overview-score-table-container">
        <div className="container-title">{labelMaps.Score}</div>
        <div className="container-body">
          <div className="custom-score-table">

            <div className="custom-score-table-header">
              <div className="header-cell team-cell">Players</div>
              <div className="header-cell">1</div>
              <div className="header-cell">2</div>
              {showThirdSet && <div className="header-cell">3</div>}
              <div className="header-cell total-cell">Finals</div>
            </div>

            {renderScoreRow(true)}
            {renderScoreRow(false)}
          </div>
        </div>
      </div>
    );
  })
);

export default ScoreTable;
