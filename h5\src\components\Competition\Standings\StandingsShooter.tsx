import { Avatar } from 'antd-mobile';
import { getTopPlayers } from 'iscommon/api/competition';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { inject, observer } from 'mobx-react';
import { useEffect, useState } from 'react';

import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';

import Loading from '@/components/Loading';

import styles from './StandingsShooter.less';

const StandingsShooter = inject('store')(
  observer((props: any) => {
    const {
      store: { Competitions },
    } = props;
    const {
      competitionId,
      currentSeason: { id: seasonId },
    } = Competitions;

    const labelMaps = useTranslateKeysToMaps(['Player', 'Team', 'Goals', 'Assists']);
    const [players, setPlayers] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);

    useEffect(() => {
      if (competitionId && seasonId) {
        setLoading(true);
        getTopPlayers({
          seasonId,
          competitionId,
          pageSize: 100,
          orderBy: 0,
        }).then(({ topScorers }: any) => {
          setPlayers(topScorers);
          setLoading(false);
        });
      }
    }, [competitionId, seasonId]);

    return (
      <div className={styles.shooter_table_container}>
        <div className={styles.heater_style}>
          <div className={styles.index_column}>#</div>
          <div className={styles.player_column}>{labelMaps.Player}</div>
          <div className={styles.team_column}>{labelMaps.Team}</div>
          <div className={styles.goals_column}>{labelMaps.Goals}</div>
          <div className={styles.assists_column}>{labelMaps.Assists}</div>
        </div>
        <Loading loading={loading} isEmpty={!players?.length}>
          {players.map((item: any, index: any) => (
            <div className={styles.rows_style} key={item?.player?.id}>
              <div className={styles.index_column}>{index + 1}</div>
              <div className={styles.player_column} onClick={() => GlobalUtils.goToPage(PageTabs.player, item.player.id)}>
                <Avatar src={item.player?.logo} style={{ width: 20, height: 20 }} />
                <span className={styles.player_name}>{item.player?.name}</span>
              </div>
              <div className={styles.team_column}>{item.team?.name}</div>
              <div className={`${styles.goals_column} ${styles.color333}`}>{`${item.goals}(${item.penalty})`}</div>
              <div className={`${styles.pts_column} ${styles.color333}`}>{item.assists}</div>
            </div>
          ))}
        </Loading>
      </div>
    );
  }),
);

export default StandingsShooter;
