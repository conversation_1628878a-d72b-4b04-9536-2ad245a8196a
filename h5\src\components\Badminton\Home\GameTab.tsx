import { CalendarPicker } from "antd-mobile";
import { CalendarOutline } from 'antd-mobile-icons';
import { HomeGameTab } from "iscommon/const/constant";
import { translate } from "iscommon/i18n/utils";
import { inject, observer } from "mobx-react";
import { useState } from "react";
import './GameTab.less'

const TabLeftMap = [
  { key: HomeGameTab.All, name: 'All', },
  {
    key: HomeGameTab.Live,
    icon: <i className="iconfont icon-jinhangzhong ml-0" />,
    name: 'LIVE',
  },
  { key: HomeGameTab.Finished, name: 'Finished', },
  { key: HomeGameTab.Scheduled, name: 'Scheduled', },
];

const GameTab = inject('store')(
  observer((props: any) => {
    const {
      Smallball: { SmallballCommon: WebHome },
    } = props.store;
    const { currentGameTab } = WebHome;
    const [calendarVisible, setCalendarVisible] = useState(false)

    const onConfirm = (date: any) => {
      WebHome.changeDate(date)
      setCalendarVisible(false);
    };

    return (
      <>
        <div className="game-tab-container">
          <div className="game-tab-container-left-aligned">
            {TabLeftMap.map((item) => (
              <div
                key={item.key}
                className={`tab-item ${item.key === currentGameTab ? (item.key === 0 ? 'activelive' : 'active') : ''}`}
                onClick={() => {
                  WebHome?.switchHomeGameTab(item.key);
                }}
              >
                {item.icon || ''}
                <span className="tab-item-text">{translate(item.name)}</span>
              </div>
            ))}
          </div>

          {currentGameTab !== HomeGameTab.Live && (
            <CalendarOutline className="header-calender-icon" onClick={() => setCalendarVisible(true)} />
          )}
        </div>
        {currentGameTab !== HomeGameTab.Live && (
          <CalendarPicker
            className="game-tab-calendar-picker"
            visible={calendarVisible}
            selectionMode="single"
            onConfirm={onConfirm}
            onClose={() => setCalendarVisible(false)}
          />
        )}
      </>
    );
  }),
);

export default GameTab;