.basketball-top-player-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .container-body {
    background: #121212;
    padding: 10px;

    .btn-container {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .button {
        background-color: #2c2c2c;
        color: #fff;
        border: none;
        margin-right: 8px;
        padding: 4px 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        border-radius: 16px;
        font-size: 24px;
  
        &.active {
          background-color: #fff;
          color: #000;
        }
      }
    }

    .top-player-table {
      width: 100%;
      display: flex;
      flex-direction: column;
      border-collapse: collapse;
      margin: 10px 0px;

      .table-header {
        display: flex;
        color: #fff;
        background-color: #2c2c2c;
        font-weight: bold;
        border-bottom: 1px solid #121212;
  
        .header-cell {
          padding: 8px;
          text-align: center;
          background-color: #2c2c2c;
        }
  
        .player-cell {
          position: sticky; 
          left: 0;
          z-index: 11; 
          width: 250px;
          width: 60%;
          flex-shrink: 0;
          background-color: inherit;
          text-align: left;
        }
  
        .header-cell:not(.player-cell) {
          padding: 8px;
          flex: 1;
          min-width: 10%;
          width: 20%;
        }
      }

      .table-body {
        .table-row {
          display: flex;
          align-items: center;
          background-color: #121212;
          border-bottom: 1px solid #2c2c2c;
          color: #fff;

          .table-cell {
            text-align: center;
            padding: 8px;
            background-color: #121212;
          }

          .player-cell {
            display: flex;
            flex-direction: row;
            align-items: center;
            position: sticky;
            left: 0;
            z-index: 10;
            background-color: inherit;
            padding: 8px;
            width: 250px;
            width: 60%;
            flex-shrink: 0;

            .player-icon {
              width: 58px;
              height: 58px;
              border-radius: 50%;
              margin: 5px 10px;
              overflow: hidden;
              object-fit: cover;
            }

            .player-info {
              display: flex;
              flex-direction: column;
              text-align: left;

              .player-name {
                font-size: 20px;
                font-weight: 600;
                color: #fff;
                overflow: hidden;
                text-overflow: ellipsis;
              }

              .player-minutes {
                font-size: 18px;
                color: #fff;
              }
            }
          }

          .table-cell:not(.player-cell) {
            padding: 8px;
            flex: 1;
            min-width: 10%;
            width: 20%;
          }
        }
      }
    }
  }
}