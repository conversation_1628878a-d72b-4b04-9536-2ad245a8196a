.basketball-player-stats-card-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;

  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .container-body {
    background: #1E1E1E;
    padding: 10px;

    .basketball-stats-table-container {
      margin-top: 16px;
      width: 100%;
      overflow-x: scroll;
    
      .custom-stats-table {
        width: 100%;
        display: flex;
        flex-direction: column;
        border-collapse: collapse;
        margin-bottom: 10px;

        .table-header {
          display: flex;
          color: #fff;
          background-color: #2c2c2c;
          font-weight: bold;
          border-bottom: 1px solid #121212;
    
          .header-cell {
            padding: 8px;
            text-align: center;
            background-color: #2c2c2c;
          }

          .empty-cell {
            display: flex;
            flex-direction: row;
            align-items: center;
            position: sticky;
            left: 0;
            z-index: 10;
            background-color: inherit;
            padding: 8px;
            width: 200px;
            flex-shrink: 0;
          }

          .header-cell:not(.empty-cell) {
            padding: 8px;
            flex: 1;
            min-width: 10%;
          }
        }

        .table-body {
          .table-row {
            display: flex;
            align-items: center;
            background-color: #121212;
            border-bottom: 1px solid #2c2c2c;
            color: #fff;
    
            .table-title {
              position: sticky;
              left: 0;
              background-color: inherit;
              z-index: 10; 
              width: 200px; 
              flex-shrink: 0;
              text-align: left;
              padding: 8px;
              font-weight: bold;
            }

            .table-cell {
              text-align: center;
              padding: 8px;
              background-color: #121212;
            }

            .table-value {
              flex: 1;
              min-width: 10%;
            }
          }
        }
      }
    }
  }
}