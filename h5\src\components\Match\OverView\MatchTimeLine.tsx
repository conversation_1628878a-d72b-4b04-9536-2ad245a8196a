import React from 'react';

import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

import { inject, observer } from 'mobx-react';
import './SingleMatchLine.less';

import './MatchTimeline.less'

import assist from 'iscommon/assets/images/football/assist.png'
import goal from 'iscommon/assets/images/football/goal.png'
import ownGoal from 'iscommon/assets/images/football/ownGoal.png'
import penaltyMissed from 'iscommon/assets/images/football/penaltyMissed.png'
import penalty from 'iscommon/assets/images/football/penalty.png'
import red from 'iscommon/assets/images/football/red.png'
import yellow from 'iscommon/assets/images/football/yellow.png'
import secYellow from 'iscommon/assets/images/football/secYellow.png'
import subIn from 'iscommon/assets/images/football/subIn.png'
import subOut from 'iscommon/assets/images/football/subOut.png'
import { normalizeUnits } from 'moment';

interface ListItem {
  isNeutral: boolean;
  tagText: string;
  isHome: boolean;
  h5Mtime: string;
  score: string;
  h5Icon: string;
  homePlayers: {
    playerName: string;
    iconId: string;
    tagKey: string;
  }[];
  awayPlayers: {
    playerName: string;
    iconId: string;
    tagKey: string;
  }[];
}

const iconMap : { [key: string]: string } = {
  'icon-assist': assist,
  'icon-goal': goal,
  'icon-ownGoal': ownGoal,
  'icon-penaltyMissed': penaltyMissed,
  'icon-penalty': penalty,
  'icon-red-card': red,
  'icon-yellow-card': yellow,
  'icon-sec-yellow-card': secYellow,
  'icon-substitution-up': subIn,
  'icon-substitution-down': subOut,
};

const FutureIcon = React.memo(
  () => {
    return (
      <div className='future-icon-container'>
        <span className='icon-circle'/>
        <span className='iconfont icon-time icon-timelock'/>
      </div>
      // <div className={styles.futureIcon}>
      //   {/* <span className={styles.iconLine}></span> */}
      //   <span className={styles.iconCircle}></span>
      //   <span className={`iconfont icon-time ${styles.timelock}`}></span>
      // </div>
    );
  },
  () => true,
);

const DiffLine = React.memo<{ item: ListItem }>(({ item }) => {
  const { isNeutral, h5Mtime, tagText, homePlayers = [], awayPlayers = [], h5Icon, score } = item;
  const normalizedTime = h5Mtime?.toString()
  // const labelMaps = useTranslateKeysToMaps(['In', 'Out', 'Assists']);

  console.log('score123', JSON.stringify(score))
  if (isNeutral) {
    const [tag, ...scores] = tagText.split(" ")
    return (
      <div className='time-title-container'>
        <div className='time-title' />
        <div className='time-pill'>
          <div className='time-circle'>{tag}</div>
          <span className='time-score'>{scores}</span>
        </div>
        <div className='time-title' />
      </div>
    )
    // return <div className="sectionTitle">{tagText}</div>;
  } else {
    return (
      <div className='time-item'>
        <div className='item-half'>
          {homePlayers.length > 0 && (
            <div className='card-container card-left'>
              {homePlayers.map((player) => {
                const iconSrc = iconMap[player.iconId] || null;
                const playerNameColor = player.iconId === 'icon-substitution-up' ? '#01935C' : player.iconId === 'icon-substitution-down' ? '#FF3131' : '#FFF'
                return (
                  <div key={player.iconId + h5Mtime} className='card-text'>
                    <span className="inline-text text-right" style={{ color: playerNameColor}}>
                      {player.playerName}
                    </span>
                    {iconSrc && <img src={iconSrc} alt={player.iconId} className='incident-icon'/>}
                  </div>
                )
              })}
            </div>  
          )}
        </div>
        <div className='timeline-container'>
          <span className='timestamp'>{normalizedTime}</span>
        </div>
        <div className='item-half'>
          {awayPlayers.length > 0 && (
            <div className='card-container'>
              {awayPlayers.map((player) => {
                const iconSrc = iconMap[player.iconId] || null;
                const playerNameColor = player.iconId === 'icon-substitution-up' ? '#01935C' : player.iconId === 'icon-substitution-down' ? '#FF3131' : '#FFF'
                return (
                  <div key={player.iconId + h5Mtime} className='card-text'>
                    {iconSrc && <img src={iconSrc} alt={player.iconId} className='incident-icon'/>}
                    <span className="inline-text text-left" style={{ color: playerNameColor}}>
                      {player.playerName}
                    </span>
                  </div>
                )
              })}
            </div>
          )}
        </div>
      </div>
    )
  }

  // if (homePlayers.length > 0) {
  //   return (
  //     <div className="timeLineUp">
  //       <div className="time">{`${h5Mtime}'`}</div>
  //       <svg aria-hidden="true" className="icon fs-14">
  //         <use xlinkHref={`#${h5Icon}`}></use>
  //       </svg>
  //       {score ? <span className="identScore">{score}</span> : null}
  //       <div className="info">
  //         {homePlayers.map((player, index) => {
  //           return (
  //             <div key={player.playerName + 'h' + index} className={`infoText ${player.tagKey === 'Out' ? 'color-999' : ''}`}>
  //               {labelMaps[player.tagKey] ? `${labelMaps[player.tagKey]} :` : ''} {player.playerName}
  //             </div>
  //           );
  //         })}
  //       </div>
  //     </div>
  //   );
  // }
  // if (awayPlayers.length > 0) {
  //   return (
  //     <div className="timeLineUp fright">
  //       <div className="time">{`${h5Mtime}'`}</div>
  //       <svg aria-hidden="true" className="icon fs-14">
  //         <use xlinkHref={`#${h5Icon}`}></use>
  //       </svg>
  //       {score ? <span className="identScore">{score}</span> : null}
  //       <div className="info">
  //         {awayPlayers.map((player, index) => {
  //           return (
  //             <div key={player.playerName + 'w' + index} className={`infoText ${player.tagKey === 'Out' ? 'color-999' : ''}`}>
  //               {labelMaps[player.tagKey] ? `${labelMaps[player.tagKey]} :` : ''} {player.playerName}
  //             </div>
  //           );
  //         })}
  //       </div>
  //     </div>
  //   );
  // }
  // return null;
});

const MatchTimeLine = inject('store')(
  observer((props: any) => {
    const {
      store: { Match },
    } = props;
    const { timeLineList } = Match;

    if (!timeLineList || timeLineList.length === 0) {
      return null;
    }

    return (
      <div className="overview-match-timeline-container">
        {timeLineList.map((item, index) => (
          <DiffLine key={`${item.h5Mtime}_${index}`} item={item} />
        ))}
        <FutureIcon />
      </div>
    );
  }),
);

export default MatchTimeLine;
