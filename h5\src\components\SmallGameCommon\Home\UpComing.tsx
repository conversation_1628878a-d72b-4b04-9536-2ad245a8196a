import { inject, observer } from 'mobx-react';
import React, { ReactNode, useEffect, useState } from 'react';

import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

import HomeLoading from '@/components/SmallGameCommon/Home/HomeLoading';

import './UpComing.less';

interface Props {
  renderCateGory(competition: any): ReactNode;
  getUpComingList(): any;
  store?: any;
}

const UpComing = inject('store')(
  observer((props: Props) => {
    const {
      Smallball: { SmallballCommon: WebHome },
    } = props.store;
    const { getUpComingList, renderCateGory } = props;
    const { upcomingCompetitions } = WebHome;
    const labelMaps = useTranslateKeysToMaps(['UpcomingMatches']);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
      getUpComingList().then(({ competitions }: any) => {
        requestAnimationFrame(() => {
          WebHome.setUpComingCompetitions(competitions);
        });
        setLoading(false);
      });
    }, []);

    return (
      <>
        {upcomingCompetitions.length > 0 && (
          <div className="upcoming">
            <i className="icon iconfont iconshalou upcoming_icon" style={{ marginRight: '10px', color: '#1081db' }} />
            {labelMaps.UpcomingMatches}
          </div>
        )}
        {loading ? (
          <HomeLoading />
        ) : (
          upcomingCompetitions.map((item: any, index: number) => {
            return <div key={item.competitionId + index}>{renderCateGory(item)}</div>;
          })
        )}
      </>
    );
  }),
);

export default React.memo(UpComing, () => true);
