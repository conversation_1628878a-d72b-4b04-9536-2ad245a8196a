import { inject, observer } from 'mobx-react';
import { useCallback, useEffect, useMemo, useState } from 'react';

import NextSchedule from '@/components/Common/NextSchedule';

import { getNextMatch } from 'iscommon/api/competition';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { translate } from 'iscommon/i18n/utils';
import { CompetitionsTabH5 } from 'iscommon/mobx/modules/competition';
import { momentTimeZone } from 'iscommon/utils';

import './ScheduleCard.less';

const ScheduleCard = inject('store')(
  observer((props: any) => {
    const {
      store: { Competitions },
    } = props;
    const {
      competitionId,
      currentSeason: { id: seasonId },
    } = Competitions;
    const { isCurrent, startTime, endTime } = Competitions.currentSeason;

    const [nextMatch, setNextMatch] = useState<any>(null);

    const onClick = useCallback(() => {
      GlobalUtils.goToPage(PageTabs.competition, competitionId, {
        target: 'history',
        customTab: CompetitionsTabH5.schedule,
      });
    }, [competitionId]);

    useEffect(() => {
      if (isCurrent) {
        getNextMatch({
          competitionId,
          seasonId,
        }).then((res: any) => {
          setNextMatch(res?.nextMatch);
        });
      }
    }, [competitionId, isCurrent, seasonId]);

    const percent = useMemo(() => {
      let num = ((endTime * 1000 - Date.now()) / (endTime * 1000 - startTime * 1000)) * 100;
      num = num > 100 ? 100 : num;
      return num + '%';
    }, [endTime, startTime]);

    return (
      <div className="scard">
        <div className="titmeline">
          <div className="titmelineName">
            <span>{startTime ? momentTimeZone(startTime, 'YYYY/MM/DD') : '-'}</span>
            <span className="color-000">{translate('season')}</span>
            <span>{endTime ? momentTimeZone(endTime, 'YYYY/MM/DD') : '-'}</span>
          </div>
          <div className="timelineprogress">
            <span className="currentline" style={{ width: isCurrent ? percent : '100%' }}></span>
          </div>
        </div>
        {isCurrent && nextMatch ? <NextSchedule nextMatch={nextMatch} clickMore={onClick} /> : null}
      </div>
    );
  }),
);

export default ScheduleCard;
