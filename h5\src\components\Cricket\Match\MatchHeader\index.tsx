import { CricketInLiveStatusEnum, CricketMatchStatusCodeToText, CricketStatusCodeEnum } from 'iscommon/const/cricket/constant';
import { getCricketScore } from 'iscommon/utils/dataUtils';
import SmallballMatchHeader from './MatchHeader';

const MatchHeader = () => {
  const getHomeBasketBallMatchTimeText = (matchHeaderInfo = null) => {
    // @ts-ignore
    const { matchStatus } = matchHeaderInfo || {};
    // const list: any[] = getCricketScore(matchStatus, extraScores);
    const isIng = CricketInLiveStatusEnum.includes(matchStatus);
    const text = (CricketMatchStatusCodeToText as any)[matchStatus];
    return {
      statusText: text,
      isIng,
    };
  };

  return (
    <SmallballMatchHeader
      checkIsIng={(matchStatus: number) => CricketInLiveStatusEnum.includes(matchStatus)}
      getHomeScore={({ extraScores, matchStatus, scores }: any) => {
        const list: any[] = getCricketScore(matchStatus, extraScores);
        const isIng = CricketInLiveStatusEnum.includes(matchStatus);
        const isFT = CricketStatusCodeEnum.End === matchStatus;
        const latestScore = list[0];
        if (latestScore) {
          if (isIng) return latestScore.compareStatus === 1 ? latestScore.h : latestScore.w;
          if (isFT) return `${scores.ft[0]} - ${scores.ft[1]}`;
        }
        return '';
      }}
      getAwayScore={() => ''}
      notStartedCode={CricketStatusCodeEnum.NotStarted}
      getHomeBasketBallMatchTimeText={getHomeBasketBallMatchTimeText}
      // ballIconCode="#iconbaseball_ball_1"
    />
  );
};

export default MatchHeader;
