.scoreTableContainer {
  width: 100%;
  // height: 90px;
  position: relative;
  margin: 13px 0;
}

.tableTitle {
  width: 100%;
  height: 50px;
  display: flex;
  font-size: 24px;

  font-weight: 400;
  color: #999;
  background-color: #fff;
  border-bottom: 1px solid #eee;
  > div {
    line-height: 50px;
    text-align: center;
  }
}

.firstBoxContent {
  width: 120px;
}

.flex1 {
  flex: 1;
  position: relative;
}
.hxScore{
  position: absolute;
  top:-10px;
  right: 50%;
  font-size: 10px;
  transform: scale(.7) translateX(250%);
}

.tableContent {
  width: 100%;
  height: 50px;
  background: #fff;
  display: flex;
  font-size: 24px;
  font-family: "Urbanist-Regular", sans-serif;
  font-weight: 400;
  color: #333;
  > div {
    line-height: 50px;
    text-align: center;
  }
}

.teamNameBox {
  width: 50%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding-left: 32px;
  position: relative;
}

.teamTip {
  width: 14px;
  height: 30px;
  position: absolute;
  left: 0;
  top: 9.729px;
  border-radius: 13px;
  left: -6.624px;
}

.teamImg {
  width: 34px;
  height: 34px;
  margin-right: 8px;
  object-fit: contain;
}

.teamName {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
}

.insideLoserColor {
  color: #999;
}
.wangqiu{
  width: 30px;
  margin-left: 5px;
}
