.container {
  display: flex;
  flex-direction: column;
  margin: 10px;
  border-radius: 24px;
}

.radio_container {
  // background: rgb(241, 241, 241);
  // display: flex;
  // flex-direction: row;
  // align-items: center;
  // padding: 12px 0;
  font-size: 26px;
  font-weight: 700;
  background: #1e1e1e;
  color: #fff;
  border-radius: 24px 24px 0 0;
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  padding: 10px 15px;
  align-items: center;
  width: 100%;
}

.radio_btn {
  // padding: 8px 20px;
  // font-size: 24px;
  // color: #999;
  // display: inline-block;
  // border-radius: 4px;
  // overflow: hidden;
  // white-space: nowrap;
  // text-overflow: ellipsis;
  // margin-left: 24px;
  background-color: #2c2c2c;
  color: #fff; 
  padding: 8px 16px;
  border-radius: 24px;
  margin-right: 10px; 
  cursor: pointer;
  font-size: 24px;
  font-weight: 600;
}

.active {
  // background: #2196f3;
  // color: #fff;
  background-color: #fff;
  color: #000;
}

.table_container {
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
}

.table_header_box {
  display: flex;
  flex-direction: row;
  height: 50px;
  background: #1e1e1e;
}

.seat_box {
  width: 234px;
}

.active_seat_box {
  width: 50px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #1e1e1e;
  color: #fff;
}

.common_center_box {
  flex: 1;
  display: flex;
  flex-direction: row;
  height: 100%;
  // background: rgb(247, 249, 251);
  // color: rgb(153, 153, 153);
}

.common_center_item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  background-color: #121212;
  color: #fff;
  border: 1px solid #2c2c2c;
}
.common_center_item_title{
  background: #1e1e1e;
  color: #fff;
  text-align: center;
  line-height: 50px;
  font-size: 24px;
  flex: 1;
}

.content_container {
  display: flex;
  flex-direction: column;
  // background-color: white;
}

.content_item_box {
  height: 80px;
  width: 100%;
  // border-bottom: 1px solid hsla(0, 0%, 89%, 0.56);
  display: flex;
  flex-direction: row;
  // background-color: white;
}

.content_company_item {
  width: 234px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #1e1e1e;
}

.stats_img {
  animation: Twinkle 1s infinite;
  width: 24px;
  height: 24px;
  margin-left: 10px;
}

@keyframes Twinkle {
  0% {
    opacity: 0;
  }
  25% {
    opacity: 1;
  }
  50% {
    opacity: 0;
  }
  75% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

// .match-odds-container {
//   border-radius: 24px;
//   margin: 10px;

//   .match-odds-radio-container {
//     font-size: 26px;
//     font-weight: 700;
//     background: #1e1e1e;
//     color: #fff;
//     border-radius: 24px 24px 0px 0px;
//     display: flex;
//     flex-direction: row;
//     justify-content:flex-end;
//     padding: 10px 15px 10px 15px;
//     align-items: center;
//     width: 100%;

//     .radio-btn {
//       background-color: #2c2c2c;
//       color: #fff; 
//       padding: 8px 16px;
//       border-radius: 24px;
//       margin-right: 10px; 
//       cursor: pointer;
//       font-size: 24px;
//       font-weight: 600;

//       .active {
//         background-color: #fff; 
//         color: #000;
//       }
//     }

//     .radio-btn:last-child {
//       margin-right: 0;
//     }
//   }

//   .container-body {
//     background: #1e1e1e;
//     padding: 10px;

//     .custom-odd-table {
//       display: flex;
//       flex-direction: column;
//       // background-color: #1e1e1e;
//       border-radius: 16px;
//       color: #fff;

//       .table-header {
//         display: grid;
//         grid-template-columns: repeat(4, 1fr);
//         font-weight: bold;
//         padding: 10px;
//         border-radius: 16px 16px 0px 0px;
//         background-color: #2c2c2c; 

//         .table-header-empty {
//           visibility: hidden;
//         }

//         .table-header-item {
//           padding: 8px 10px;
//           text-align: center;
//           text-overflow: ellipsis;
//         }
//       }
    
//     }
//   }
// }