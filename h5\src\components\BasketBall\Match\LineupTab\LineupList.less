.container {
  display: flex;
  flex-direction: row;
  width: 100%;
  background-color: white;
}

.w_50 {
  width: 50%;
}

.lineup_list_item {
  display: flex;
  align-items: center;
  flex-direction: row;
  height: 100px;
}

.lineup_list_item:not(:first-child) {
  padding: 0 12px;
}
.line_item_left{
  flex: 1;
  display: flex;
  overflow: hidden;
  height: 100%;
  align-items: center;
}

.lineup_list_item:not(:last-child) {
  border-bottom: 1px solid #eee;
}

.border_right_eee {
  border-right: 1px solid #eee;
}

.lineup_list_column {
  display: flex;
  flex-direction: column;
  border-right: 1px solid #eee;
  width: 50%;
}

.shirtNumber {
  color: #999;
  width: 32px;
  text-align: center;
}

.player_avatar_box {
  position: relative;
  margin: 0 8px;
  width: 60px;
}

.player_rating {
  height: 30px;
  border-radius: 8px;
  position: absolute;
  top: -10px;
  right: -30px;
  color: #fff;
  background-color: #5cb400;
  transform: scale(0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0 5px;
}

.player_name_box {
  display: flex;
  flex-direction: column;
}

.player_name {
  max-width: 180px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #333 !important;
}
.desc {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 180px;
  color: #999;
}

.event_box {
  // width: 68px;
  // flex: 1;
  // display: flex;
  // justify-content: flex-end;
  // flex-wrap: wrap;
  margin-left: 10px;
  display: flex;
  flex-direction: row-reverse;
  justify-content: flex-end;
}

.event {
  display: inline-flex;
  vertical-align: middle;
  float: right;
  justify-content: center;
  align-items: center;
}

.fs_12 {
  font-size: 24px;
}
