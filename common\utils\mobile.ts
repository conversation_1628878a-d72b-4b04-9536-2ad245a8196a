const ua = navigator.userAgent;
const browser = {
  versions: (function () {
    return {
      trident: ua.indexOf("Trident") > -1, // IE   trident
      presto: ua.indexOf("Presto") > -1, // opera  presto
      webKit: ua.indexOf("AppleWebKit") > -1, // chrome safari  webkit
      gecko: ua.indexOf("Gecko") > -1 && ua.indexOf("KHTML") == -1, //firefox  gecko
      mobile: !!ua.match(/AppleWebKit.*Mobile.*/), // is mobile
      ios: !!ua.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/), // ios
      android: ua.indexOf("Android") > -1 || ua.indexOf("Linux") > -1, // android or UC
      iPad: ua.indexOf("iPad") > -1, //iPad
      webApp: ua.indexOf("Safari") == -1,
    };
  })(),
};

// jump to mobile website
if (browser.versions.mobile) {
  const baseUrl = location.href;
  location.href = baseUrl.replace("www.", "m.");
}
