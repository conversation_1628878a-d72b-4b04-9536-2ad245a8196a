import { inject, observer } from "mobx-react";
import './TopPlayers.less'
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { useCallback, useEffect, useState } from "react";
import { getTopPlayers } from "iscommon/api/competition";
import { <PERSON><PERSON>, <PERSON>er } from "antd-mobile";
import { DownOutline } from 'antd-mobile-icons'
import { Link } from "umi";
import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";
import { FallbackImage, FallbackPlayerImage } from "iscommon/const/icon";
import Loading from "@/components/Loading";

const TopPlayers: React.FC = inject('store')(
  observer((props: any) => {
    const {
      store: { Competitions },
    } = props;
    const {
      competitionId,
      competitionName,
      currentSeason: { id: seasonId, year: seasonText },
    } = Competitions;

    const labelMaps = useTranslateKeysToMaps(['topPlayers', 'Goals', 'Assists', 'GoalsPk', 'FullStats', 'Players', 'Matches']);
    const [tab, setTab] = useState<0 | 1>(0);
    const [players, setPlayers] = useState<any[]>([]);
    const [selectedStat, setSelectedStat] = useState('goals');
    const [playerStats, setPlayerStats] = useState(false)
    const [sortedPlayers, setSortedPlayers] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(true);

    useEffect(() => {
      getTopPlayers({
        seasonId,
        competitionId,
        orderBy: tab,
      }).then(({ topScorers }: any) => {
        setPlayers(topScorers);
        setLoading(false)
      });
    }, [seasonId, competitionId, tab]);
  
    useEffect(() => {
      const sorted = [...players].sort((a, b) => b[selectedStat] - a[selectedStat]);
      setSortedPlayers(sorted);
    }, [players, selectedStat]);
  
    if (players?.length === 0) {
      return null;
    }
  
    const onPlayerConfirm = (value: any) => {
      const stat = value[0];
      setSelectedStat(stat);
      setPlayerStats(false); 
    };

    const statOptions = [
      { label: labelMaps.Goals, value: 'goals' },
      { label: labelMaps.Assists, value: 'assists' },
    ];
    
    return (
      <div className="competition-overview-top-player-container">
        <div className="container-title">
          {labelMaps.Players}
          <>
            <Button className='top-player-header-btn' size="small" onClick={() => setPlayerStats(true)}>
              <span className='btn-content'>
                <span className='btn-value'>{statOptions.find(item => item.value === selectedStat)?.label || ''}</span>
                <DownOutline className='down-icon'/>
              </span>
            </Button>
            <Picker
              columns={[statOptions.map(item => ({ label: item.label, value: item.value }))]}
              visible={playerStats}
              onClose={() => setPlayerStats(false)}
              onConfirm={onPlayerConfirm}
            />
          </>
        </div>
        <div className="container-body">
          <div className='player-stat-table'>
            <div className="header-row">
              <div className="header-cell">#</div>
              <div className="header-cell">Players</div>
              <div className="header-cell">{statOptions.find(item => item.value === selectedStat)?.label || ''}</div>
            </div>
            {sortedPlayers.length !== 0 ? (
              sortedPlayers.map((item: any, index) => {
                return (
                  <Link className="table-row" key={item?.player?.id} to={GlobalUtils.getPathname(PageTabs.player, item?.player?.id)}>
                    <div className="table-cell">
                      <span>{index + 1}</span>
                    </div>
                    <div className="table-cell player-cell">
                      <img className="player-icon" src={item?.player?.logo || FallbackPlayerImage} alt={item?.player?.name} loading="lazy"/>
                      <div className="player-info">
                        <span className="player-name">{item?.player?.name}</span>
                        <div className="player-team-detail">
                          <img className="team-icon" src={item?.team?.logo || FallbackImage} alt={item?.team?.name} loading="lazy"/>
                          <span className="team-name">{item?.team?.name}</span>
                        </div>
                      </div>
                    </div>
                    <div className="table-cell">
                      {selectedStat === "goals"
                      ? `${item.goals}${item.penalty > 0 ? ` (${item.penalty})` : ""}`
                      : item[selectedStat]}
                    </div>
                  </Link>
                )
              })
            ) : (
              <Loading loading={loading}/> 
            )}
          </div>
        </div>
      </div>
    )
  })
)

export default TopPlayers