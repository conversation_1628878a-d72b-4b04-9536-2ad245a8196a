import Loading from "@/components/Loading";
import { getPlayerStatusPreview } from "iscommon/api/basketball/basketball-player";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { CompetitionScopeMap } from "iscommon/mobx/basketball/modules/competition";
import { inject, observer } from "mobx-react";
import { useState, useEffect } from "react";
import './StatsCard.less'

const StatsCard = inject('store')(
  observer((props: any) => {
    const { store } = props;
    const { Player } = store.Basketball;
    const { playerId, playerStats = [] } = Player;
    const competitionId = Player.playerHeaderInfo.team.competitionId

    console.log('competition id', competitionId)

    const labelMaps = useTranslateKeysToMaps(['SeeAll', 'Stats', 'Preseason', 'RegularSeason', 'Playoffs', 'Career']);

    const [loading, setLoading] = useState<boolean>(false);
    const [tablesList, setTablesList] = useState<any>([]);

    const statsTitleMaps = [
      { label: 'GP', value: 'court', },
      { label: 'MIN', value: 'minutesPlayed', },
      { label: 'FG%', value: 'fieldGoalsAccuracy', },
      { label: '3P%', value: 'threePointsAccuracy', },
      { label: 'FT%', value: 'freeThrowsAccuracy', },
      { label: 'REB', value: 'rebounds', },
      { label: 'AST', value: 'assists', },
      { label: 'BLK', value: 'blocks', },
      { label: 'STL', value: 'steals', },
      { label: 'PF', value: 'personalFouls', },
      { label: 'TO', value: 'turnovers', },
      { label: 'PTS', value: 'points', },
    ];

    useEffect(() => {
      if (playerId && competitionId) {
        setLoading(true);
        getPlayerStatusPreview({
          playerId,
          competitionId,
        }).then((res: any) => {
          let list = [];
          if (res.currentSeasonStats && res.currentSeasonStats.length > 0) {
            res.currentSeasonStats.forEach((item) => {
              list.push({
                title: CompetitionScopeMap[item.scope],
                value: item,
              });
            });
          }
          if (res.careerStats) {
            list.push({
              title: 'Career',
              value: res.careerStats,
            });
          }
          setTablesList(list);
          Player.changePlayerStats(list);
          setLoading(false);
        });
      }
    }, [playerId, competitionId, Player]);

    const processPlayerStats = (playerStats: any) => {
      return playerStats.map((item: any) => {
        let value = item.value;
        if (value.court > 0) {
          value = {
            ...value,
            minutesPlayed: value.minutesPlayed === undefined ? '-' : (value.minutesPlayed / value.court).toFixed(1),
            rebounds: value.rebounds === undefined ? '-' : (value.rebounds / value.court).toFixed(1),
            assists: value.assists === undefined ? '-' : (value.assists / value.court).toFixed(1),
            blocks: value.blocks === undefined ? '-' : (value.blocks / value.court).toFixed(1),
            steals: value.steals === undefined ? '-' : (value.steals / value.court).toFixed(1),
            personalFouls: value.personalFouls === undefined ? '-' : (value.personalFouls / value.court).toFixed(1),
            turnovers: value.turnovers === undefined ? '-' : (value.turnovers / value.court).toFixed(1),
            points: value.points === undefined ? '-' : (value.points / value.court).toFixed(1),
          };
        }
        return {
          key: item.title, 
          title: labelMaps[item.title],
          ...value,
        };
      });
    };

    console.log('table list', tablesList)

    console.log('player stats', JSON.stringify(playerStats))
    return (
      <div className="basketball-player-stats-card-container">
        <div className="container-title">{labelMaps.Stats}</div>
        <div className="container-body">
          <Loading isEmpty={playerStats.length === 0}>
            <div className="basketball-stats-table-container">
              <div className="custom-stats-table">
                <div className="table-header">
                  <div className="header-cell empty-cell"></div>
                  {statsTitleMaps.map((item) => (
                    <div className="header-cell" key={item.value}>{item.label}</div>
                  ))}
                </div>
                <div className="table-body">
                  {processPlayerStats(playerStats).map((row) => (
                    <div className="table-row" key={row.key}>
                      <div className="table-cell table-title">{row.title}</div>
                      {statsTitleMaps.map((item) => (
                        <div className="table-cell table-value" key={item.value}>
                          {row[item.value] !== undefined ? row[item.value] : '-'}
                        </div>
                      ))}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </Loading>
        </div>
      </div>
    );
  }),
);

export default StatsCard;