import { getTea<PERSON><PERSON>eyplayer } from "iscommon/api/basketball/basketball-team";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { inject, observer } from "mobx-react";
import { useState, useEffect, useCallback } from "react";
import { RightOutline } from 'antd-mobile-icons'
import './TopPlayerCard.less'
import { Button } from "antd-mobile";
import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";
import { TeamTabH5 } from "iscommon/mobx/modules/team";
import Loading from "@/components/Loading";

const TopPlayerCard = inject('store')(
  observer((props: any) => {
    const {
      store: { 
        Basketball: { 
          Team : { teamId, teamHeaderInfo }
        }
      }
    } = props;

    const seasonId = teamHeaderInfo?.currentSeason?.id;
    const competitionId = teamHeaderInfo?.currentSeason?.competitionId;

    const [loading, setLoading] = useState(false);
    const [players, setPlayers] = useState<any[]>([]);
    const [selectedValue, setSelectedValue] = useState<string>('points');

    const labelMaps = useTranslateKeysToMaps([
      'topPlayers',
      'Assists',
      'Points',
      'Rebounds',
      'Players',
      'FullStandings',
      'PointsPerGame',
      'Blocks',
      'Steals',
    ]);

    const options: any = [
      { label: labelMaps.Points, id: 'points' , value: 'points'},
      { label: labelMaps.Assists, id: 'assists', value: 'assists'},
      { label: labelMaps.Rebounds, id: 'rebounds', value: 'rebounds'},
      { label: labelMaps.Blocks, id: 'blocks', value: 'blocks'},
      { label: labelMaps.Steals, id: 'steals', value: 'steals'},
    ];

    const [activeButton, setActiveButton] = useState(options[0].value);

    useEffect(() => {
      if (teamId && seasonId && competitionId) {
        setLoading(true);
        getTeamKeyplayer({
          teamId: teamId,
          seasonId: seasonId,
          orderType: selectedValue,
        }).then(({ statsList }: any) => {
          setLoading(false);
          setPlayers(statsList || []);
        });
      }
    }, [selectedValue, teamId, seasonId, competitionId]);

    const handleButtonClick = (value: string) => {
      setSelectedValue(value);
      setActiveButton(value)
    };

    const goStanding = useCallback(() => {
      GlobalUtils.goToPage(PageTabs.team, teamId, {
        target: 'history',
        customTab: TeamTabH5.standings
      });
    }, [teamId]);

    return (
      <div className="basketball-top-player-container">
        <div className="container-title">
          {labelMaps.topPlayers}
          <div onClick={goStanding}>
            <RightOutline />
          </div>
        </div>
        <div className="container-body">
          <div className="btn-container">
            {options.map((option: any) => (
              <Button key={option.id} 
              onClick={() => handleButtonClick(option.value)} 
              className={`button ${activeButton === option.value ? 'active' : ''}`}>
                {option.label}
              </Button>
            ))}
          </div>

          <Loading loading={loading} isEmpty={players.length === 0}>
            <div className="top-player-table">
              <div className="table-header">
                <div className="header-cell">#</div>
                <div className="header-cell player-cell">{labelMaps.Players}</div>
                <div className="header-cell">{options.find((option: any) => option.value === selectedValue)?.label}</div>
              </div>

              <div className="table-body">
                {players.map((player, index) => (
                  <div className="table-row">
                    <div className="table-cell" key={index}>{index+1}</div>
                    <div className="table-cell player-cell">
                      <img src={player.logo} alt={player.name} className="player-icon"/>
                      <div className="player-info">
                        <span className="player-name">{player.name}</span>
                        <span className="player-position">{player.position}</span>
                      </div>
                    </div>
                    <div className="table-cell">{player[selectedValue]}</div>
                  </div>
                ))}
              </div>
            </div>
          </Loading>
        </div>
      </div>
    );
  }),
);

export default TopPlayerCard;