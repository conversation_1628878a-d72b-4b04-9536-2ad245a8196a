import styles from './TennisScores.less';

interface ListItem {}

const TennisScores: React.FC<{ item: ListItem }> = ({ item }) => {
  return (
    <div className={styles.container}>
      <span className={styles.title}>S3, 7-6</span>
      <div className={styles.gameBox}>
        <div className={styles.scoreLeft}>
          <div className={styles.scoreLeftItem}>
            7
            <svg aria-hidden="true" className={`icon ${styles.tennisIcon} `}>
              <use xlinkHref="#iconiconwangqiu"></use>
            </svg>
          </div>
          <div className={styles.scoreLeftItem}>6</div>
        </div>
        <div className={styles.pointBox}>
          <div className={styles.pointItem}>
            <span className={`${styles.item} ${styles.color000}`}>1</span>
            <span className={styles.item}>2</span>
          </div>
          <div className={styles.pointItem}>
            <span className={styles.item}>1</span>
            <span className={styles.item}>2</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TennisScores;
