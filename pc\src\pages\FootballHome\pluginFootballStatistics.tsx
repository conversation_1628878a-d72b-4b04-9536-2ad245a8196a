import { Button, Image } from 'antd';
import { getMatchStatistics, getMatchTimeLine } from 'iscommon/api/match';
import { useEffect, useState } from 'react';

import assist from 'iscommon/assets/images/football/assist.png';
import { CloseOutlined } from '@ant-design/icons';
import { FallbackImage } from 'iscommon/const/icon';
import { formatTimeLineData } from 'iscommon/utils/dataUtils';
import goal from 'iscommon/assets/images/football/goal.png';
import Loading from '@/components/Loading';
import ownGoal from 'iscommon/assets/images/football/ownGoal.png';
import penalty from 'iscommon/assets/images/football/penalty.png';
import penaltyMissed from 'iscommon/assets/images/football/penaltyMissed.png';
import red from 'iscommon/assets/images/football/red.png';
import secYellow from 'iscommon/assets/images/football/secYellow.png';
import subIn from 'iscommon/assets/images/football/subIn.png';
import subOut from 'iscommon/assets/images/football/subOut.png';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import yellow from 'iscommon/assets/images/football/yellow.png';

interface StatisticsContainerProps {
  match: any;
  onClose: () => void;
  themeColor: string;
}

// Icon mapping for timeline events - matches TimeLine.tsx exactly
const iconMap: { [key: string]: string } = {
  'icon-assist': assist,
  'icon-goal': goal,
  'icon-own-goal': ownGoal,
  'icon-penaltyMissed': penaltyMissed,
  'icon-PenaltySaved': penaltyMissed,
  'icon-Penalty': penalty,
  'icon-red-card': red,
  'icon-yellow-card': yellow,
  'icon-sec-yellow-card': secYellow,
  'icon-twoyellow-red': secYellow,
  'icon-substitution-up': subIn,
  'icon-substitution-down': subOut,
};

// DiffLine component following TimeLine.tsx structure
const DiffLine = ({ item }: { item: any }) => {
  const { isNeutral, mTime, tagText, homePlayers = [], awayPlayers = [] } = item;
  const normalizedTime = mTime?.toString();

  console.log('DiffLine item:', item);

  if (isNeutral) {
    const [tag, ...scores] = tagText.split(" ");
    return (
      <div className="timeTitle">
        <div className="timeItem"/>
        <div className="pill">
          <div className="circle">{tag}</div>
          <span className="score">{scores}</span>
        </div>
        <div className="timeItem"/>
      </div>
    );
  } else {
    return (
      <div className="timeItem">
        <div className="half">
          {homePlayers.length > 0 && (
            <div className="card cardLeft">
              {homePlayers.map((player: any) => {
                const iconSrc = iconMap[player.iconId] || null;
                const playerNameColor = player.iconId === 'icon-substitution-up' ? '#01935C' : player.iconId === 'icon-substitution-down' ? '#FF3131' : '#FFF';
                return (
                  <div key={player.iconId + mTime} className="intext">
                    <span className="text-overflow text-right" style={{ marginRight: '5px', color: playerNameColor}}>
                      {player.playerName}
                    </span>
                    {iconSrc && <img src={iconSrc} alt={player.iconId} className="icon"/>}
                  </div>
                );
              })}
            </div>
          )}
        </div>
        <div className="timeLine">
          <span className="time">{normalizedTime}'</span>
        </div>
        <div className="half">
          {awayPlayers.length > 0 && (
            <div className="card">
              {awayPlayers.map((player: any) => {
                const iconSrc = iconMap[player.iconId] || null;
                const playerNameColor = player.iconId === 'icon-substitution-up' ? '#01935C' : player.iconId === 'icon-substitution-down' ? '#FF3131' : '#FFF';
                return (
                  <div key={player.iconId + mTime} className="intext">
                    {iconSrc && <img src={iconSrc} alt={player.iconId} className="icon"/>}
                    <span className="text-overflow text-left" style={{ marginLeft: '5px', color: playerNameColor }}>
                      {player.playerName}
                    </span>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    );
  }
};

const StatisticsContainer: React.FC<StatisticsContainerProps> = ({ match, onClose, themeColor }) => {
  const { matchId, homeTeam, awayTeam } = match;
  const [statistics, setStatistics] = useState([]);
  const [timeLineList, setTimeLineList] = useState([]);
  const [loading, setLoading] = useState(true);

  const labelMaps = useTranslateKeysToMaps(['Possession', 'Shots', 'ShotsOnTarget', 'YellowCard']);

  useEffect(() => {
    if (matchId !== -1) {
      getMatchStatistics({ matchId }).then(({ statistics = [] }: any) => {
        if (statistics.length > 0) {
          setStatistics(statistics);
        }
        setLoading(false);
      });

      getMatchTimeLine({ matchId }).then(({ awayScores, homeScores, incidents }: any) => {
        const list: any = formatTimeLineData({
          awayScores,
          homeScores,
          incidents,
        });
        // Don't filter - show all timeline events like the live timeline
        console.log('Timeline data received:', list);
        setTimeLineList(list || []);
        setLoading(false);
      });
    }
  }, [matchId]);

  const getData = (type: number): any => {
    console.log('statistics', statistics)
    return statistics.filter((item: any) => item.type == type)[0];
  };

  const rendPrs = () => {
    const data = getData(25);
    if (!data) {
      return null;
    }
    const { home, away } = data;
    const total = (home + away) / 100;
    if (total === 0) return null;
    const l = home / total;
    const r = away / total;
    return (
      <>
        <span>{labelMaps.Possession}</span>
        <div className="process">
          <span className="percent" style={{ width: `${l}%` }}>
            {l.toFixed(0)}%
          </span>
          <span className="percent pright" style={{ width: `${r}%` }}>
            {r.toFixed(0)}%
          </span>
        </div>
      </>
    );
  };

  return (
    <div className="statistics-container">
      <div className="statistics-header" style={{ backgroundColor: themeColor }}>
        <div className='statistics-title'>Match Statistics</div>
        <Button
          type="text"
          icon={<CloseOutlined />}
          className="close-btn"
          onClick={onClose}
        />
      </div>

      <div className="statistics-content">
        <div className="team-title-section">
          <div className="team-info">
            <img
              className="team-icon"
              src={homeTeam?.logo || FallbackImage}
              alt={homeTeam?.name || 'Home Team'}
              onError={(e) => { e.currentTarget.src = FallbackImage; }}
            />
            <span className="team-name">{homeTeam?.name || 'Home'}</span>
          </div>
          <div className="team-info">
            <img
              className="team-icon"
              src={awayTeam?.logo || FallbackImage}
              alt={awayTeam?.name || 'Away Team'}
              onError={(e) => { e.currentTarget.src = FallbackImage; }}
            />
            <span className="team-name">{awayTeam?.name || 'Away'}</span>
          </div>
        </div>
        <Loading loading={loading} isEmpty={timeLineList.length === 0 && statistics.length === 0}>
          <div className="timeline">
            <div className="timeSection">
              {timeLineList.map((item: any, index: number) => (
                <DiffLine key={`${item.mTime}_${index}`} item={item} />
              ))}
            </div>
          </div>
          {/* {statistics.length > 0 && (
            <div className="statistics">
              {rendPrs()}
              <div className="statistics-item">
                <span className="text-left">{getData(22)?.home || 0 + getData(21)?.home || 0}</span>
                <span>{labelMaps.Shots}</span>
                <span className="text-right">{getData(22)?.away || 0 + getData(21)?.away || 0}</span>
              </div>
              <div className="statistics-item">
                <span className="text-left">{getData(21)?.home || 0}</span>
                <span>{labelMaps.ShotsOnTarget}</span>
                <span className="text-right">{getData(21)?.away || 0}</span>
              </div>
              <div className="statistics-item">
                <span className="text-left">{getData(3)?.home || 0}</span>
                <span>{labelMaps.YellowCard}</span>
                <span className="text-right">{getData(3)?.away || 0}</span>
              </div>
            </div>
          )} */}
        </Loading>
      </div>
    </div>
  );
};

export default StatisticsContainer;