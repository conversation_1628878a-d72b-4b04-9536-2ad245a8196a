import { inject, observer } from 'mobx-react';

import H2H from './H2H';
import LiveAndVideo from '@/components/Common/LiveAndVideo/LiveAndVideo';
import MatchInfo from './MatchInfo';
import ScoreRatings from './ScoreRatings';
import ScoreTable from './ScoreTable';
import SingleMatchLine from './SingleMatchLine';
import Statistics from '../StatisticsTab/Statistics';
import TopPlayersCard from './TopPlayersCard';

const MatchLive = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Basketball: { Match },
      },
    } = props;
    const { matchHeaderInfo, matchId } = Match;

  return (
    <div>
      <LiveAndVideo matchHeaderInfo={matchHeaderInfo} matchId={matchId}/>
      <SingleMatchLine /> 
      <ScoreTable />
      {/* <SingleMatchLine /> */}
      <ScoreRatings />
      <TopPlayersCard />
      <Statistics />
      <MatchInfo />
      {/* <H2H /> */}
    </div>
  );
}));

export default MatchLive;
