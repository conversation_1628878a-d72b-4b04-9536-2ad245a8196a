// @ts-nocheck
import _ from 'lodash';

import store from 'iscommon/mobx';
import { getArrayFromString } from 'iscommon/utils';

const syncTennisMatchOdds = (data) => {
  const {id, score} = data

  if(!score || score.length === 0) return

  const { Smallball: { SmallballMatch} } = store;
  const matchHeaderInfo = _.cloneDeep(SmallballMatch.matchHeaderInfo);
  const { matchId } = matchHeaderInfo;
  if (matchId === id) {
    const matchStatus = score[1];
    matchHeaderInfo.matchStatus = matchStatus;
    matchHeaderInfo.scores = score[3];
    matchHeaderInfo.servingSide = score[2];
    SmallballMatch.changeMatchHeaderInfo(matchHeaderInfo);
  }
};



const mixinCompetitionOdds = (matchRecentOdds, oldOdds = [], matchId) => {
  let cloneList = _.cloneDeep(oldOdds);
  if(!cloneList || cloneList.length === 0) {
    return []
  }
  // latest odd
  const validOddList = matchRecentOdds[matchId] // []
  if(validOddList && validOddList.length > 0) {
      // 循环添加
      for(let v of validOddList) {
        const {companyId, oddsType} = v
        for (let i = 0; i < cloneList.length; i++) {
            const item = cloneList[i];
            if (companyId === item.companyId) {
              for(let j = 0; j < item.companyOdds.length; j++) {
                if(oddsType === item.companyOdds[j].oddsType) {
                  item.companyOdds[j].odds.unshift(v)
                }
              }
            }
          }
      }
  }
  return cloneList;
}

const syncMatchOdds = (matchRecentOdds) => {
  const { SmallballMatch: Match } = store.Smallball;
  const { matchOdds, matchId } = Match;
  const cloneList = mixinCompetitionOdds(matchRecentOdds, matchOdds, matchId);
  Match.setMatchOdds(cloneList);
};

export const matchDataControllerType = {
  score: 'score',
  syncMatchOdds: 'syncMatchOdds',
};

export const smallBallMatchDataController = {
  [matchDataControllerType.syncMatchOdds]: syncMatchOdds,
};

export const tennisMatchDataController = {
  [matchDataControllerType.score]: syncTennisMatchOdds,
}

export default smallBallMatchDataController;
