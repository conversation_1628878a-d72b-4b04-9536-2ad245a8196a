.basketball-statistics-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .player-team-container {
    display: flex;
    justify-content: space-between;
    background: #1e1e1e;
    padding: 10px;

    .team-container {
      display: flex;
      align-items: center;

      .team-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin: 5px 10px;
        overflow: hidden;
        background-size: cover;
      }
      
      .team-name {
        font-size: 22px;
        color: #fff;
      }
    }
  }

  .container-body {
    display: flex;
    flex-direction: column;
    background: #1e1e1e;
    padding: 10px;

    .stats-data-container {
      display: flex;
      flex-direction: column;
      padding: 0px 15px;

      .stats-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 16px;
        color: #fff;

        .data {
          font-size: 22px;
          width: 50px; 
          text-align: center;
          font-weight: 600;
          color: #fff;
        }
      }

      .bar-container {
        display: flex;
        justify-content: space-between;
        width: 100%;

        .progress-container {
          width: 250px;
          height: 6px;
          background: #121212;
          position: relative;
          border-radius: 3px;
          margin: 5px 0px;
        
          .current-progress {
            position: absolute;
            top: 0;
            height: 100%;
          }
        
          .home-progress {
            right: 0;
            background-color: #49B18C;
            border-radius: 3px;
          }
        
          .away-progress {
            left: 0;
            background-color: #7892B7;
            border-radius: 3px;
          }
        }
      }
    }
  }
}