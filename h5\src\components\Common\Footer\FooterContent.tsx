import { Collapse } from 'antd-mobile';
import { useMemo } from 'react';
import { useLocation } from 'umi';

import { SeoFooterContentMaps } from './seoFooter';

import './FooterContent.less';

const FooterContent: React.FC = () => {
  const location = useLocation();
  const seoContent = useMemo(() => {
    if (!location.pathname || location.pathname === '/') {
      return SeoFooterContentMaps['/football/'];
    }
    return SeoFooterContentMaps[location.pathname];
  }, [location.pathname]);

  return (
    <div className="footerContent">
      {seoContent ? (
        <Collapse defaultActiveKey={[]}>
          <Collapse.Panel title="More Info" key="1" forceRender destroyOnClose={false}>
            {seoContent}
          </Collapse.Panel>
        </Collapse>
      ) : null}
    </div>
  );
};

export default FooterContent;
