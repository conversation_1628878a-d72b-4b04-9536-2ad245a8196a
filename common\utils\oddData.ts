
import _ from 'lodash';

// 每次新的数据第二条数据和上一次数据的第二条进行对比，需要显示升降icon
// 从第三条开始，都是和上一条对比，3对比2,4对比3，只显示颜色区分
export const formatMatchDetailOdds = (matchOdds, isMobile = false) => {
  let list = [];
  if (matchOdds.length > 0) {
    list = _.cloneDeep(matchOdds)
    if(!isMobile) {
      for (let { companyOdds } of list) {
        for (let { odds } of companyOdds) {
          const totalLen = odds.length;
          odds.unshift(odds[totalLen - 1]);
          odds.splice(totalLen, 1);
        }
      }
    }
    if (list && list.length) {
      list.map((item, allIndex) => {
        item.companyOdds.map((companyOddsItem, valIndex) => {
          if (companyOddsItem && companyOddsItem.odds && companyOddsItem.odds[1]) {
            let contrastList = [];
            companyOddsItem.odds[1].oddsData.forEach((item, index) => {
              const currentOdds = list[allIndex]['companyOdds'][valIndex]['odds'][1]['oddsData'][index];
              if (parseFloat(item) > parseFloat(currentOdds)) {
                contrastList.push('up');
              } else if (parseFloat(item) < parseFloat(currentOdds)) {
                contrastList.push('down');
              } else {
                contrastList.push('equal');
              }
            });
            companyOddsItem.odds[1].contrastList = contrastList;
          }
          // return companyOddsItem.odds[1];
        });
        // return item;
      });
    }
  }
  return matchOdds
}