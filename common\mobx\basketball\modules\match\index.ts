// @ts-nocheck
import { makeAutoObservable } from 'mobx';
import { GlobalConfig } from 'iscommon/const/globalConfig';
import { getArrayFromString } from '../../../../utils';
import { FallbackImage } from '../../../../const/icon';

export const MatchTab = {
  overview: 'Overview',
  odds: 'Odds',
  lineup: 'Lineup',
  live: 'MatchLive',
  standings: 'Standings',
  h2h: 'H2H',
};

export const MatchTabH5 = {
  overview: 'Overview',
  chat: 'Chat',
  lineup: 'Lineup',
  odds: 'Odds',
  live: 'MatchLive',
  h2h: 'H2H',
  standings: 'Standings',
};

export default class Match {
  constructor() {
    this.matchId = '';
    this.serverTime = GlobalConfig.serverTime;
    this.matchHeaderInfo = {
      matchId: -1,
      competition: {
        id: '',
        name: '',
        logo: FallbackImage,
        countryId: '',
        categoryId: '',
        type: 0,
      },
      homeTeam: {
        id: '',
        name: '',
        logo: FallbackImage,
        shortName: '',
        marketValueCurrency: '',
        competitionId: '',
      },
      awayTeam: {
        id: '',
        name: '',
        logo: FallbackImage,
        shortName: '',
        marketValueCurrency: '',
        competitionId: '',
      },
      venue: {
        name: '',
        city: '',
        capacity: 0,
      },
      matchTime: 0,
      aggScore: '',
      matchStatus: -1,
      homeScores: '',
      awayScores: '',
    };

    this.keyPlayers = {}
    this.teamStats = []
    this.statsList = []
    this.matchOdds = []

    makeAutoObservable(this);
  }

  changeMatchId(id = '') {
    this.matchId = id;
  }

  changeServerTime(time) {
    this.serverTime = time;
  }

  changeMatchHeaderInfo(data = {}) {
    this.matchHeaderInfo = data;
  }
  
  setKeyPlayers(keyPlayers = {}) {
    this.keyPlayers = keyPlayers
  }

  setStatsList(list = []) {
    // "list": [
    //   [
    //    1,//Type，see thesprots -> status code -> technical statistics
    //    8,//Home team value
    //    10//Away team value
    //  ]
    // ]
    // Technical Statistics
    // status code	description
    // 1	3-point goals
    // 2	2-point goals
    // 3	Free throw goals
    // 4	Number of pauses remaining
    // 5	Fouls
    // 6	Free throw percentage
    // 7	Total pauses
    // [[1, 3, 0], [2, 4, 15], [3, 1, 0], [4, 4, 5], [5, 1, 0], [6, 50.0, 0.0], [7, 5, 5]]
    this.statsList = getArrayFromString(list)
  }

  setTeamStats(teamStats = {}) {
    this.teamStats = teamStats
  }

  setMatchOdds(matchOdds = []) {
    this.matchOdds = matchOdds
  }
}
