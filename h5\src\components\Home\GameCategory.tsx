import './GameCategory.less';

import { CategoryIcon, FallbackImage } from 'iscommon/const/icon';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { HomeGameTab, InLiveStatusEnum, StatusCodeEnum } from 'iscommon/const/constant';
import { inject, observer } from 'mobx-react';
import { memo, useCallback, useEffect, useMemo, useRef } from 'react';

import { getHomeCompetitionMatchTime } from 'iscommon/utils/dataUtils';
import { isEqual } from 'lodash';
import MatchLiveIcon from '@/components/MatchLiveIcon';
import { momentTimeZone } from 'iscommon/utils';

const MatchStatusText = inject('store')(
  observer((props: any) => {
    const {
      matchStatus,
      matchActiveTime,
      secondHalfKickOffTime,
      store: {
        WebHome: { currentGameTab, serverTime },
      },
    } = props;
    const { statusText, isAni, isIng } = getHomeCompetitionMatchTime({
      serverTime,
      matchStatus,
      matchActiveTime,
      secondHalfKickOffTime,
      currentGameTab,
    });

    if (matchStatus === 1) {
      return <span className="match-time">{momentTimeZone(props.matchTime, 'HH:mm')}</span>;
    } else if (matchStatus === 8) {
      return <span className="match-time">{statusText}</span>;
    } else {
      return (
        <span className={`match-time-live ${isAni ? 'twinkleScore' : ''} ${isIng ? 'ing' : ''}`}>
          {statusText}
        </span>
      );
    }
  }),
);

const ScoreText = (props: any) => {
  const { currentGameTab, item } = props;
  const { matchStatus, calculatedAwayScore, calculatedHomeScore } = item;

  const isLive = InLiveStatusEnum.includes(matchStatus);
  const isCurrentTabLive = currentGameTab === 0;
  const scoreColor = isLive || isCurrentTabLive ? '#c1272d' : '#fff';

  return (
    <div className="match-score-container">
      <span className="match-score" style={{ color: scoreColor }}>
        {calculatedHomeScore}
      </span>
      <span className="match-score" style={{ color: scoreColor }}>
        {calculatedAwayScore}
      </span>
    </div>
  );
};

const ScoreOrOdds = inject('store')(
  observer((props: any) => {
    const {
      store: { WebHome, WebConfig },
      item,
    } = props;
    const { matchId, matchStatus } = item;
    const { currentGameTab, homeCompetitionOdds } = WebHome;
    const matchOdds = homeCompetitionOdds[matchId] || [];

    const euOdds = matchOdds.find((odd: any) => odd.oddsType === 'eu')?.oddsData || [];

    const previousOddsRef = useRef(euOdds);
    const colorRef = useRef(['#fff', '#fff', '#fff']);

    useEffect(() => {
      if (euOdds.length) {
        const updatedColors = euOdds.map((val: any, i: number) => {
          const newVal = Number(val);
          const oldVal = Number(previousOddsRef.current?.[i] ?? newVal);

          if (newVal > oldVal) return '#14AE5C'; // green
          if (newVal < oldVal) return '#FF3131'; // red
          return colorRef.current[i]; // retain last color
        });

        colorRef.current = updatedColors;
        previousOddsRef.current = [...euOdds];
      }
    }, [euOdds]);
    
    const displayOdds = WebConfig.showOdds && euOdds.length;

    if (matchStatus === StatusCodeEnum.NotStarted) {
      return displayOdds ? (
        <div className="match-odd-container">
          {[0, 2].map((i) => (
            <span key={i} className="odd" style={{ color: colorRef.current[i] }}>
              {Number(euOdds[i]).toFixed(2)}
            </span>
          ))}
        </div>
      ) : (
        <div className="match-odd-container">
          <span className="odd">-</span>
        </div>
      );
    }

    return (
      <div className="match-live-score-container">
        <ScoreText item={item} currentGameTab={currentGameTab} />
        <MatchLiveIcon vlive={item.vlive} mlive={item.mlive} />
        {displayOdds && (
          <div className="match-odd-container">
            {[0, 2].map((i) => (
              <span key={i} className="odd" style={{ color: colorRef.current[i] }}>
                {Number(euOdds[i]).toFixed(2)}
              </span>
            ))}
          </div>
        )}
      </div>
    );
  }),
);

export const MatchList = ({ matches }: any) => {
  return (
    matches.map((item: any) => {
      return (
        <div className='match-list-container' key={item.matchId} onClick={() => GlobalUtils.goToPage(PageTabs.match, item.matchId)}>
          <div className='match-time-container'>
            {/* <FavoriteIcon match={item} isMatch/> */}
            <MatchStatusText {...item} />
          </div>
          <div className='match-team-container'>
            <div className='match-team-info'>
              <img className='team-logo' src={item.homeTeam?.logo || FallbackImage} alt={item.homeTeam?.name} loading="lazy"/>
              <span className='team-name text-overflow'>{item.homeTeam?.name}</span>
              {item.homeScores[3] > 0 && <span className="card fs-8-center yellow">{item.homeScores[3]}</span>}
              {item.homeScores[2] > 0 && <span className="card fs-8-center red">{item.homeScores[2]}</span>}
            </div>
            <div className='match-team-info'>
              <img className='team-logo' src={item.awayTeam?.logo || FallbackImage} alt={item.awayTeam?.name} loading="lazy"/>
              <span className='team-name text-overflow'>{item.awayTeam?.name}</span>
              {item.awayScores[3] > 0 && <span className="card fs-8-center yellow">{item.awayScores[3]}</span>}
              {item.awayScores[2] > 0 && <span className="card fs-8-center red">{item.awayScores[2]}</span>}
            </div>
          </div>
          <ScoreOrOdds item={item} />
        </div>
      )
    })
  );
};

const H5StickIcon = inject('store')(
  observer((props: any) => {
    const {
      store: { WebHome },
      competitionId,
    } = props;
    const { h5StickIdList = [], currentGameTab } = WebHome;
    const isStick = useMemo(() => h5StickIdList?.includes(competitionId), [h5StickIdList, competitionId]);

    if (currentGameTab !== HomeGameTab.All) {
      return null;
    }

    if (isStick) {
      return (
        <span
          onClick={(e) => {
            WebHome.removeH5Stick(competitionId);
            e.preventDefault();
            e.stopPropagation();
          }}
          className="icon iconfont iconSign_Sele myIcon touchArea"
          style={{ color: '#c72a1d' }}
        />
      );
    }
    return (
      <span
        onClick={(e) => {
          WebHome.addH5Stick(competitionId);
          e.preventDefault();
          e.stopPropagation();
        }}
        className="icon iconfont iconSign_Def touchArea"
        style={{ color: 'rgb(131, 131, 131)' }}
      />
    );
  }),
);

const GameCategory = memo (
  ({ competition }: any) => {
    const { competitionName, country, category, onlineCount, matches, competitionId, curStageId } = competition;

    const goToCompetition = useCallback(() => {
      if (curStageId) {
        GlobalUtils.goToPage(PageTabs.competition, competitionId);
      }
    }, [competitionId, curStageId]);

    if (matches.length === 0) return null;

    return (
      <div className='home-game-category-container'>
        <div className='game-category' onClick={goToCompetition}>
          <div className='game-category-left-aligned'>
            {/* <FavoriteIcon isMatch={false} competition={competition} /> */}
            <i style={{ backgroundImage: `url(${country?.logo || (CategoryIcon as any)[category?.logo] || FallbackImage})`,}} className="category-icon" />
            <div className='competition-container'>
              <span className="competition-name">{competitionName}</span>
              <span className="country-name">{country?.name || category?.name}</span>
            </div>
          </div>
          <div className='game-category-right-aligned'>
            {onlineCount > 0 && (
              <div className="count-container">
                <span className="icon iconfont iconguanzhong icon-size"></span>
                <span className='online-count'>{onlineCount > 9999 ? '9999+' : onlineCount}</span>
              </div>
            )}
            <H5StickIcon competitionId={competitionId} />
          </div>
        </div>
        <MatchList matches={matches} />
      </div>
    );
  },
  (pre, next) => {
    return isEqual(pre, next);
  },
);

export default GameCategory;
