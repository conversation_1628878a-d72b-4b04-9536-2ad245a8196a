.title {
  padding: 16px 20px;
  font-size: 24px;
  font-weight: 500;
  color: #999999;
}
.item {
  background: #fefffe;
  padding: 22px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .left {
    .logo {
      width: 28px;
      height: 28px;
      border-radius: 14px;
    }
    .text {
      font-weight: 500;
      color: #333;
      font-size: 24px;
      margin-left: 12px;
    }
  }
  .iconfont {
    color: #d0d0d0;
  }
  border-bottom: 1px solid #e3e3e3;
}
p {
  margin-bottom: 0;
}

.inner-list {
  padding-left: 60px !important;
  background-color: #fff !important;
  .inner-item {
    padding: 22px 0;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eee;
    .inner-logo {
      width: 28px;
      height: 28px;
      border-radius: 14px;
    }
    .inner-name {
      margin-left: 12px;
      color: #333;
      font-weight: 400;
      font-size: 24px;
    }
  }
}
