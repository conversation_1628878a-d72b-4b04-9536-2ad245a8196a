import SmallballMatchHeader from '@/components/SmallGameCommon/Match/MatchHeader';
import {
  TableTennisInLiveStatusEnum,
  TableTennisMatchStatusCodeToText,
  TableTennisStatusCodeEnum,
} from 'iscommon/const/tabletennis/constant';
import { getTableTennisScoreList } from 'iscommon/utils/dataUtils';

const MatchHeader = () => {
  const getHomeBasketBallMatchTimeText = (matchHeaderInfo) => {
    const { matchStatus, scores, servingSide } = matchHeaderInfo;
    const list: any[] = getTableTennisScoreList(matchStatus, scores, servingSide, false);
    const isIng = TableTennisInLiveStatusEnum.includes(matchStatus);
    const latestScore = list[list.length - 1];
    const text = (TableTennisMatchStatusCodeToText as any)[matchStatus];
    const res = {
      statusText: text,
      isIng,
    };
    return res;
  };

  return (
    <SmallballMatchHeader
      checkIsIng={(matchStatus) => TableTennisInLiveStatusEnum.includes(matchStatus)}
      getHomeScore={({ scores }) => scores?.ft?.[0] || 0}
      getAwayScore={({ scores }) => scores?.ft?.[1] || 0}
      notStartedCode={TableTennisStatusCodeEnum.NotStarted}
      getHomeBasketBallMatchTimeText={getHomeBasketBallMatchTimeText}
      ballIconCode="#icontabletennis_ball"
    />
  );
};

export default MatchHeader;
