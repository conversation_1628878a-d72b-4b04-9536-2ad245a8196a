// import CommonStatsCard from '@/components/Basketball/Common/CommonStatsCard';
import Loading from '@/components/Loading';
import { getMatchPlayerStatistics } from 'iscommon/api/basketball/basketball-match';
import { inject, observer } from 'mobx-react';
import { useEffect, useState } from 'react';
import BoxScoreCard from './BoxScoreCard';
import styles from './index.less';

const BoxScoreTab = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Basketball: { Match },
      },
    } = props;
    const { matchId, matchHeaderInfo } = Match;
    const { homeTeam, awayTeam } = matchHeaderInfo;

    const [result, setResult] = useState({});
    const [loading, setLoading] = useState(true);

    useEffect(() => {
      console.log('matchid', matchId);
      // for right stats
      if (matchId && matchId !== -1) {
        getMatchPlayerStatistics({ matchId }).then(({ all = [] }: any) => {
          setResult({
            home: all
              .filter((item) => item.teamId === homeTeam.id)
              .map((item) => {
                return {
                  ...item,
                  team: {
                    name: homeTeam.name,
                    logo: homeTeam.logo,
                    id: homeTeam.id,
                  },
                };
              }),
            away: all
              .filter((item) => item.teamId === awayTeam.id)
              .map((item) => {
                return {
                  ...item,
                  team: {
                    name: awayTeam.name,
                    logo: awayTeam.logo,
                    id: awayTeam.id,
                  },
                };
              }),
          });
          setLoading(false);
        });
      }
    }, [matchId]);
    
    return (
      <Loading loading={loading} isEmpty={result?.home?.length === 0 && result?.away?.length === 0}>
        <div className={styles.container}>
          <BoxScoreCard result={result} matchHeaderInfo={matchHeaderInfo} />
        </div>
      </Loading>
    );
  }),
);

export default BoxScoreTab;
