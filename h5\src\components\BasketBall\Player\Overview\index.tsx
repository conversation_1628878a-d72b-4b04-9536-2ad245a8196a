import { inject, observer } from "mobx-react";
import SeasonStatCard from "./SeasonStatCard";
import MatchCard from "../MatchTab/MatchesCard";
import PlayerCard from "./PlayerCard";
import StatsCard from "./StatsCard";


const OverView = inject('store')(
  observer((props: any) => {

    return (
      <>
        <SeasonStatCard />
        <PlayerCard />
        <StatsCard />
        <MatchCard />
      </>
    );
  })
);

export default OverView;