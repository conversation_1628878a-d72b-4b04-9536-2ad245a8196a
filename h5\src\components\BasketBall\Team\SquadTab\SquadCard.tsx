import { Badge } from "antd-mobile";
import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";
import { getTeamLineup } from 'iscommon/api/basketball/basketball-team';
import { FallbackPlayerImage, FallbackCategoryImage } from "iscommon/const/icon";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { inject, observer } from "mobx-react";
import { useState, useEffect } from "react";
import { Link } from "umi";
import Loading from "@/components/Loading";
import './SquadCard.less'

const SquadCard = inject('store')(
  observer((props: any) => {
    const {
      store: { 
        Basketball: { Team }
      }
    } = props;

    const [dataSource, setDataSource] = useState<any>({});
    const [loading, setLoading] = useState<boolean>(false);
    const labelMaps = useTranslateKeysToMaps([
      'Squad',
      'UpdateLineups',
      'Coach',
      'PointGuards',
      'ShootingGuards',
      'SmallForwards',
      'PowerForwards',
      'Centers',
    ]);

    const {teamId} = Team;

    const positionList = [
      { key: 'PG', label: 'PointGuards' },
      { key: 'SG', label: 'ShootingGuards' },
      { key: 'SF', label: 'SmallForwards' },
      { key: 'PF', label: 'PowerForwards' },
      { key: 'C', label: 'Centers' },
    ];

    useEffect(() => {
      if (teamId) {
        setLoading(true)
        getTeamLineup({ teamId }).then((data: any) => {
          setLoading(false)
          setDataSource(data);
        });
      }
    }, [teamId]);

    const formatBirthday = (birthday: any) => {
      return new Date(birthday * 1000).toLocaleDateString('en-GB', {
        day: '2-digit',
        month: 'short',
        year: 'numeric'
      });
    };

    const formatMarketValue = (value: string, currency: string): string => {
      const stringValue = value != null ? value.toString().trim() : '';
      if (stringValue === '0' || isNaN(parseFloat(stringValue))) {
        return '-';
      }

      const numericValue = parseFloat(value);
      if (isNaN(numericValue)) {
        return `Invalid value ${currency}`;
      }
  
      const valueInMillions = numericValue / 1_000_000;
      return `${currency} ${valueInMillions.toFixed(2)} M`;
    };

    return (
      <div className='basketball-team-squad-container'>
        <Loading loading={loading} isEmpty={!dataSource?.coach && !dataSource?.squad}>
          {dataSource?.coach && (
            <>
              <div className='container-title'>{labelMaps.Coach}</div>
              <div className='container-body'>
                <div className="player-card">
                  <div className="player-info">
                    <div className='player-image-container'>
                      <img className="player-image" src={dataSource?.coach.logo || FallbackPlayerImage} alt={dataSource?.coach?.name} loading="lazy"/>
                    </div>
                    <div className="player-details">
                      <span className="player-name">{dataSource?.coach?.name}</span>
                      <div className='player-country'>
                        <img className='player-country-image' src={dataSource?.coach?.country?.logo || FallbackCategoryImage} alt={dataSource?.coach?.country?.name} loading="lazy"/>
                        <span className='player-country-text'>{dataSource?.coach?.country?.name}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}

          {positionList.map((position) => {
            const players = dataSource?.squad?.filter((item: any) => item.position === position.key) || [];
            return (
              <>
                <div className='container-title' key={position.key}>{position.label}</div>
                <div className='container-body'>
                  {players.map((item: any) => (
                    <Link to={GlobalUtils.getPathname(PageTabs.player, item.id)} className="player-card" key={item.id}>
                      <div className="player-info">
                        <div className='player-image-container'>
                          <img className="player-image" src={item.logo || FallbackPlayerImage} alt={item.name} loading="lazy"/>
                          <Badge className='player-badge' content={item.shirtNumber} />
                        </div>
                        <div className="player-details">
                          <span className="player-name">{item.name}</span>
                          <div className='player-country'>
                            <span className='player-country-text'>{item.nationality}</span>
                          </div>
                        </div>
                      </div>
                      <div className='player-extra-info'>
                        <div className='player-footer'>
                          <span className='player-footer-value'>{item.age} years</span>
                          <span className='player-footer-label'>{formatBirthday(item.birthday)}</span>
                        </div>
                        <div className='player-footer'>
                          <span className='player-footer-value'>{formatMarketValue(item.salary, '$')}</span>
                          <span className='player-footer-label'>{labelMaps['Player Value']}</span>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </>
            )
          })}
        </Loading>
      </div>
    );
  }),
);

export default SquadCard;