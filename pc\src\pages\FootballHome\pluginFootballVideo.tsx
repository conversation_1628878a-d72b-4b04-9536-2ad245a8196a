import { useEffect, useState } from 'react';

import { Button } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import moment from 'moment';

interface VideoFrameProps {
  matchId: number;
  competitionName: string;
  matchTime: number;
  videoLink: string;
  animationLink: string;
  onClose: () => void;
  themeColor: string;
  isPluginMode?: boolean;
}

const VideoFrame: React.FC<VideoFrameProps> = ({
  matchId,
  competitionName,
  matchTime,
  videoLink,
  animationLink,
  onClose,
  themeColor,
  isPluginMode = false
}) => {
  const [videoTab, setVideoTab] = useState<'animation' | 'stream'>('animation');
  const [timeRemaining, setTimeRemaining] = useState<number>(0);
  const [isTimeExpired, setIsTimeExpired] = useState<boolean>(false);

  const STORAGE_KEY = `video_expiry_${matchId}`;
  const STREAMING_LIMIT = 5 * 60 * 1000; // 5 minutes

  useEffect(() => {
    if (!isPluginMode) return;

    const now = Date.now();
    const saved = localStorage.getItem(STORAGE_KEY);
    let expiryTime: number;

    if (saved) {
      expiryTime = parseInt(saved);
    } else {
      expiryTime = now + STREAMING_LIMIT;
      localStorage.setItem(STORAGE_KEY, expiryTime.toString());
    }

    const remaining = expiryTime - now;

    if (remaining <= 0) {
      setIsTimeExpired(true);
    } else {
      setTimeRemaining(remaining);
      const timeout = setTimeout(() => {
        setIsTimeExpired(true);
      }, remaining);
      return () => clearTimeout(timeout);
    }
  }, [matchId, isPluginMode]);

  useEffect(() => {
    if (videoLink) {
      setVideoTab('stream');
    } else if (animationLink) {
      setVideoTab('animation');
    }
  }, [videoLink, animationLink]);

  const formatTime = (ms: number) => {
    const min = Math.floor(ms / 60000);
    const sec = Math.floor((ms % 60000) / 1000);
    return `${min}:${sec.toString().padStart(2, '0')}`;
  };

  const renderContent = () => {
    if (videoTab === 'stream') {
      if (isPluginMode && isTimeExpired) {
        return (
          <div className="video-timeout">
            <div className="timeout-message">
              <p>Enjoy full live streaming experience on our mobile app.</p>
              <Button
                type="primary"
                style={{ backgroundColor: themeColor }}
                className='btn-download'
                onClick={() => window.open('https://onelink.to/igscore-newapp', '_blank')}
              >
                Download Now
              </Button>
            </div>
          </div>
        );
      }
    
      if (videoLink) {
        return <iframe src={videoLink} width="100%" height="100%" allowFullScreen />;
      }
    }

    if (videoTab === 'stream' && videoLink) {
      return <iframe src={videoLink} width="100%" height="100%" allowFullScreen />;
    }

    if (videoTab === 'animation' && animationLink) {
      return <iframe src={animationLink} width="100%" height="100%" allowFullScreen />;
    }

    return (
      <div className="no-video">
        <div className="no-video-text">No video available for this match</div>
      </div>
    );
  };

  return (
    <div className="video-frame-container">
      <div className="video-frame-header">
        <div className="video-tabs">
          {videoLink && (
            <div
              className={`video-tab ${videoTab === 'stream' ? 'active' : ''}`}
              style={{
                backgroundColor: videoTab === 'stream' ? themeColor : '#2c2c2c',
                borderColor: videoTab === 'stream' ? themeColor : '#2c2c2c',
                color: videoTab === 'stream' ? '#1e1e1e' : '#fff',
              }}
              onClick={() => setVideoTab('stream')}
            >
              Live Stream
            </div>
          )}

          {animationLink && (
            <div
              className={`video-tab ${videoTab === 'animation' ? 'active' : ''}`}
              style={{
                backgroundColor: videoTab === 'animation' ? themeColor : '#2c2c2c',
                borderColor: videoTab === 'animation' ? themeColor : '#2c2c2c',
                color: videoTab === 'animation' ? '#1e1e1e' : '#fff',
              }}
              onClick={() => setVideoTab('animation')}
            >
              <span>Animation</span>
              <CloseOutlined
                className="close-icon"
                onClick={(e) => {
                  e.stopPropagation();
                  onClose();
                }}
              />
            </div>
          )}
        </div>
      </div>
      <div className='video-header' style={{borderColor: themeColor}}>{competitionName} {moment.unix(matchTime).format('DD/MM/YYYY HH:mm')}</div>
      <div className="video-content">{renderContent()}</div>
    </div>
  );
};

export default VideoFrame;