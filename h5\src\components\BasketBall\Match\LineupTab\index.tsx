import CommonEmpty from '@/components/Common/CommonEmpty';
import { getMatchLineup } from 'iscommon/api/match';
import { translate } from 'iscommon/i18n/utils';
import { showEmptyLineUp } from 'iscommon/utils/dataUtils';
import { inject, observer } from 'mobx-react';
import React, { useEffect, useState } from 'react';
import styles from './index.less';
import LineupList from './LineupList';
import MatchLineup from './MatchLineup';
interface Props {
  store?: any;
}

const LineupTab: React.FC<Props> = inject('store')(
  observer((props: any) => {
    const {
      store: { Match, WebConfig },
    } = props;
    const { matchHeaderInfo, matchId } = Match;
    const [matchLineupInfo, setMatchLineupInfo] = useState({});
    const [homeSubstitute, setHomeSubstitute] = useState([]);
    const [awaySubstitute, setAwaySubstitute] = useState([]);
    const [homeFirstRound, setHomeFirstRound] = useState([]);
    const [awayFirstRound, setAwayFirstRound] = useState([]);
    const [formation, setFormation] = useState({ awayFormation: '', homeFormation: '' });

    const text = translate('Substitutes');

    useEffect(() => {
      if (matchId) {
        getMatchLineup({ matchId }).then((data) => {
          console.log(data);
          setMatchLineupInfo(data || {});
          if (data) {
            if (data.homeLineup) {
              setHomeSubstitute(data.homeLineup['0'] || []);
              setHomeFirstRound(data.homeLineup['1'] || []);
            }
            if (data.awayLineup) {
              setAwayFirstRound(data.awayLineup['1'] || []);
              setAwaySubstitute(data.awayLineup['0'] || []);
            }
            //  setHomeLineup();
            setFormation({
              awayFormation: data.awayFormation,
              homeFormation: data.homeFormation,
            });
          }
        });
      }
    }, [matchId]);

    const isEmpty = showEmptyLineUp([[], homeFirstRound], [[], awayFirstRound]);

    return (
      <div className={styles.container}>
        {isEmpty ? (
          <CommonEmpty />
        ) : (
          <>
            <MatchLineup
              formation={formation}
              matchLineupInfo={matchLineupInfo}
              homeFirstRound={homeFirstRound}
              awayFirstRound={awayFirstRound}
            />
            <div>
              <span style={{ paddingLeft: 12 }}>{text}</span>
              <LineupList homeSubstitute={homeSubstitute} awaySubstitute={awaySubstitute} matchLineupInfo={matchLineupInfo} />
            </div>
          </>
        )}
        {/* <div>
          <span style={{ paddingLeft: 12 }}>{translate('injuredOrSuspended')}</span>
          <LineupList type="injuredOrSuspended" />
        </div> */}
        <div className={styles.clearfix_row}>
          <div className={styles.clearfix_row_item_box}>
            <div className={styles.clearfix_row_item}>
              <svg aria-hidden="true" className={styles.svg_style}>
                <use xlinkHref="#icongoal"></use>
              </svg>
              <span className={styles.svg_text}>Goals</span>
            </div>
          </div>
          <div className={styles.clearfix_row_item_box}>
            <div className={styles.clearfix_row_item}>
              <svg aria-hidden="true" className={styles.svg_style}>
                <use xlinkHref="#iconPenalty"></use>
              </svg>
              <span className={styles.svg_text}>Penalty</span>
            </div>
          </div>
          <div className={styles.clearfix_row_item_box}>
            <div className={styles.clearfix_row_item}>
              <svg aria-hidden="true" className={styles.svg_style}>
                <use xlinkHref="#iconPenaltySaved"></use>
              </svg>
              <span className={styles.svg_text}>Penalty missed</span>
            </div>
          </div>
          <div className={styles.clearfix_row_item_box}>
            <div className={styles.clearfix_row_item}>
              <svg aria-hidden="true" className={styles.svg_style}>
                <use xlinkHref="#icon-own-goal"></use>
              </svg>
              <span className={styles.svg_text}>Own Goal</span>
            </div>
          </div>
          <div className={styles.clearfix_row_item_box}>
            <div className={styles.clearfix_row_item}>
              <svg aria-hidden="true" className={styles.svg_style}>
                <use xlinkHref="#icon-yellow-card"></use>
              </svg>
              <span className={styles.svg_text}>Yellow card</span>
            </div>
          </div>
          <div className={styles.clearfix_row_item_box}>
            <div className={styles.clearfix_row_item}>
              <svg aria-hidden="true" className={styles.svg_style}>
                <use xlinkHref="#icon-red-card"></use>
              </svg>
              <span className={styles.svg_text}>Red card</span>
            </div>
          </div>

          <div className={styles.clearfix_row_item_box}>
            <div className={styles.clearfix_row_item}>
              <svg aria-hidden="true" className={styles.svg_style}>
                <use xlinkHref="#icontwoyellow-red"></use>
              </svg>
              <span className={styles.svg_text}>Second yellow</span>
            </div>
          </div>
        </div>
      </div>
    );
  }),
);

export default LineupTab;
