import React, { useEffect, useState } from 'react';


const RecentActivity: React.FC = () => {
  const [activities, setActivities] = useState<RecentActivityItem[]>([]);

  useEffect(() => {
    // Load from localStorage or your storage logic
    const loadActivities = () => {
      try {
        const data = localStorage.getItem('recentActivity');
        if (data) {
          setActivities(JSON.parse(data));
        }
      } catch (e) {
        setActivities([]);
      }
    };
    loadActivities();
    const handler = () => loadActivities();
    window.addEventListener('recentActivityUpdated', handler);
    return () => window.removeEventListener('recentActivityUpdated', handler);
  }, []);

  if (!activities.length) {
    return <div>No recent activity.</div>;
  }

  return (
    <div>
      <h3>Recent Activity</h3>
      <ul>
        {activities.map((item, idx) => (
          <li key={item.id + '-' + idx}>
            <strong>{item.type}</strong>: {item.name} {item.sport && <span>({item.sport})</span>}
            {item.logo && <img src={item.logo} alt={item.name} style={{ width: 24, verticalAlign: 'middle', marginLeft: 8 }} />}
            {item.metadata && <pre style={{ display: 'inline', marginLeft: 8 }}>{JSON.stringify(item.metadata)}</pre>}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default RecentActivity;
