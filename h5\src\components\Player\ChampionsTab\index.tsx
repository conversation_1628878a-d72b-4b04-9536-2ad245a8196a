import { inject, observer } from 'mobx-react';
import React, { useEffect, useMemo, useState } from 'react';

import { getPlayerHonor } from 'iscommon/api/player';

import Loading from '@/components/Loading';

import styles from './index.less';
import './index.less'
import { translate } from 'iscommon/i18n/utils';
import { FallbackImage } from 'iscommon/const/icon';
import { DownOutline } from 'antd-mobile-icons'
import { Collapse } from 'antd-mobile';

interface Props {
  store?: any;
}

interface HonorItemProps {
  honorName: string;
  honorList: any[];
}

interface HonorItem {
  logo: string;
  season: string;
  teamLogo?: string;
  teamName?: string;
}

const HonorItem = React.memo<HonorItemProps>(({ honorName, honorList = [] }) => {
  const isShow = useMemo(() => !!honorList.length && !honorList.find((i: any) => !i.teamLogo || !i.teamName), [honorList]);

  if (!isShow) {
    return null;
  }

  return (
    <div className={styles.honor_content}>
      <div className={styles.honor_content_title}>
        <div className={styles.honor_content_title_left}>
          <img className={styles.honor_content_img} src={honorList[0]?.logo} alt="" />
          <span className={styles.honor_content_name}>{honorName}</span>
        </div>
        <span className={styles.honor_title_count}>{honorList.length}</span>
      </div>
      {honorList.map((item: any, index: any) => (
        <div className={styles.honor_content_title} key={index}>
          <div>
            <img className={styles.honor_content_img} src={item.teamLogo} alt="" />
            <span className={styles.teamName}>{item.teamName}</span>
          </div>
          <span className={styles.honorSeason}>{item.season}</span>
        </div>
      ))}
    </div>
  );
});

const PlayerChampionsTab: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: { Player },
    } = props;
    const { playerId } = Player;

    // const [listData, setListData] = useState<any>({});
    const [loading, setLoading] = useState<boolean>(true);
    const [expandedItems, setExpandedItems] = useState<number[]>([]);
    const [listData, setListData] = useState<Record<string, HonorItem[]>>({});

    const dataKeys = useMemo(() => Object.keys(listData), [listData]);

    useEffect(() => {
      if (playerId) {
        getPlayerHonor({ playerId }).then((res: any) => {
          setLoading(false);
          if (res?.honors) {
            setListData(res.honors);
          }
        });
      }
    }, [playerId]);

    const toggleExpand = (index: number) => {
      setExpandedItems(prevState =>
        prevState.includes(index)
          ? prevState.filter(i => i !== index)
          : [...prevState, index]
      );
    };

    const renderSeasons = (seasons: HonorItem[]) => (
      <div className="seasons-list">
        {seasons.map((season, idx) => (
          <div key={idx} className="season-item">
            {season.season}
          </div>
        ))}
      </div>
    );

    return (
      <Loading loading={loading} isEmpty={!dataKeys.length}>
        <div className='player-champion-container'>
          <div className='container-title'>{translate('Champions')}</div>
          <div className='container-body'>
            <Collapse className='custom-collapse'>
              {Object.entries(listData).map(([key, honorList], index) => (
                <Collapse.Panel
                  key={key}
                  title={
                    <div className="panel-header">
                      <div className='honor-container'>
                        <img className='honor-icon' src={honorList[0]?.logo || FallbackImage}/>
                        <span className="honor-name">{key}</span>
                      </div>
                      <span className="honor-count">{honorList.length}</span>
                    </div>
                  }
                >
                  {renderSeasons(honorList)}
                </Collapse.Panel>
              ))}
            </Collapse> 
            {/* {Object.entries(listData).map(([key, honorList], index) => {
              return (
                <div className="honor-item" key={index}>
                  <div className="honor-header" onClick={() => toggleExpand(index)}>
                    <img className='honor-icon' src={honorList[0]?.logo || FallbackImage}/>
                    <span className="honor-name">{key}</span>
                    <span className="honor-count">{honorList.length}</span>
                    <DownOutline className={`chevron ${expandedItems.includes(index) ? 'expanded' : ''}`} />
                  </div>
                  {expandedItems.includes(index) && renderSeasons(honorList)}
                </div>
              )
            })} */}
          </div>
        </div>
      </Loading>
      // <Loading loading={loading} isEmpty={!dataKeys.length}>
      //   <div style={{ marginTop: '1.6vw' }}>
      //     {dataKeys.map((key: any) => (
      //       <HonorItem key={key} honorName={key} honorList={listData[key] || []} />
      //     ))}
      //   </div>
      // </Loading>
    );
  }),
);

export default PlayerChampionsTab;
