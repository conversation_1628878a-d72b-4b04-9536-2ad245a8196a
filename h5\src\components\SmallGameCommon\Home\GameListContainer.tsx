import { inject, observer } from 'mobx-react';
import { ReactNode, useCallback, useEffect, useRef, useState } from 'react';

import { HomeGameTab } from 'iscommon/const/constant';
import { GlobalConfig } from 'iscommon/const/globalConfig';
import { useMatchTimer } from 'iscommon/hooks/apiData';
import { momentTimeZone, simpleCloneObj, sortByMatchTime } from 'iscommon/utils';

import UmiLoading from '@/components/Common/UmiLoading';
import Loading from '@/components/Loading';
import LazyLoad from 'react-lazyload';
import AllCompetitionsContainer from './AllCompetitionsContainer';
import UpComing from './UpComing';

let refTimer: any = null;

interface Props {
  renderCateGory(competition: any): ReactNode;
  getUpComingList(): any;
  getCompetitionList(): any;
  store?: any;
  propsTab?: any;
  isSortByTime: boolean;
}

const SmallBallGameListContainer = inject('store')(
  observer((props: Props) => {
    const { propsTab, isSortByTime = false } = props;
    const {
      Smallball: { SmallballCommon: WebHome },
      WebConfig,
    } = props.store;
    const { getUpComingList, getCompetitionList, renderCateGory } = props;
    const { serverTime, currentGameTab: storeGameTab, date, h5StickIdList, homeCompetitions } = WebHome;

    const competitionsRef = useRef<any[]>([]);
    const isSortByTimeRef = useRef<boolean>(false);
    const [originList, setOriginList] = useState<any[]>([]);
    // const [fvids, setFvids] = useState([]);
    const [loading, setLoading] = useState<boolean>(true);
    const isMount = useRef<boolean>(true);

    const currentGameTab = propsTab || storeGameTab;

    const setCompetitionsWithStick = useCallback(() => {
      if (originList.length > 0) {
        const prevList: any[] = [];
        const nextList: any[] = [];
        if (currentGameTab === HomeGameTab.All) {
          for (let i = 0; i < originList.length; i++) {
            const item = originList[i];
            if (h5StickIdList.indexOf(item.competitionId) > -1) {
              prevList.push(item);
            } else {
              nextList.push(item);
            }
          }
          WebHome.setHomeCompetitions([...prevList, ...nextList]);
        } else {
          WebHome.setHomeCompetitions(originList);
        }
      } else {
        WebHome.setHomeCompetitions([]);
      }
    }, [currentGameTab, h5StickIdList, originList, WebHome]);

    const setFormatCompetitionList = useCallback(() => {
      const list = simpleCloneObj(competitionsRef.current);
      if (isSortByTimeRef.current) {
        // order by time
        setOriginList(sortByMatchTime(list));
      } else {
        setOriginList(list);
      }
    }, []);

    const getListData = useCallback(() => {
      getCompetitionList({
        dateFilter: momentTimeZone(date, 'YYYY-MM-DDTHH:mm:SS.SSS') + GlobalConfig.timezone,
        listType: currentGameTab,
      }).then(({ competitions, total }) => {
        competitionsRef.current = simpleCloneObj(competitions);
        setFormatCompetitionList();
        setLoading(false);

        if (currentGameTab === HomeGameTab.Live) {
          WebConfig.setOnTypeLiveCount(total);
        }
        if (currentGameTab === HomeGameTab.All) {
          WebConfig.setOnTypeLiveCount(0, total);
        }
      });
    }, [date, currentGameTab, setFormatCompetitionList, WebConfig]);

    useEffect(() => {
      setCompetitionsWithStick();
    }, [h5StickIdList, originList]);

    useEffect(() => {
      if (propsTab) {
        WebHome.switchHomeGameTab(propsTab);
      }
    }, [propsTab, WebHome]);

    useEffect(() => {
      setOriginList([]);
      setLoading(true);
      if (refTimer) {
        clearTimeout(refTimer);
      }
      if (currentGameTab !== HomeGameTab.Leagues) {
        getListData();
      }
    }, [currentGameTab, date]);

    useEffect(() => {
      isSortByTimeRef.current = isSortByTime;
      setFormatCompetitionList();
    }, [isSortByTime]);

    useEffect(() => {
      return () => {
        if (refTimer) {
          clearTimeout(refTimer);
        }
        isMount.current = false;
      };
    }, []);

    // update server time every 60s
    useMatchTimer(serverTime, WebHome);
    return (
      <>
        {loading && homeCompetitions.length === 0 && currentGameTab !== 5 && <UmiLoading htmlString={window.skeletonDetailString}/>}
        {currentGameTab !== 5 ? (
          loading || homeCompetitions.length > 0 ? (
            homeCompetitions.map((item: any, index: number) => {
              return <div key={item.competitionId + index}>{renderCateGory(item)}</div>;
            })
          ) : (
            <Loading isEmpty />
          )
        ) : null}
        {!loading && currentGameTab === HomeGameTab.Live && (
          <LazyLoad height={200} offset={10}>
            <UpComing getUpComingList={getUpComingList} renderCateGory={renderCateGory} />
          </LazyLoad>
        )}
        {/*{currentGameTab === 4 ? fvids.map((id, index) => <div key={index}>{id}</div>) : null}*/}
        {currentGameTab === 5 && (
          <LazyLoad height={200} offset={10}>
            <AllCompetitionsContainer />
          </LazyLoad>
        )}
      </>
    );
  }),
);

export default SmallBallGameListContainer;
