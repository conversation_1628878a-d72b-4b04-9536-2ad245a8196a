.container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: auto;
}

.substitutes {
  display: flex;
  flex-direction: column;
}

.clearfix_row {
  clear: both;
  display: inline-block;
  vertical-align: top;
  width: 100%;
  height: auto;
  padding: 20px 0;
}

.clearfix_row::before {
  content: '';
  display: table;
}

.clearfix_row_item_box {
  width: 33.3%;
  float: left;
  margin-top: 6px;
  height: 32px;
}

.w100 {
  width: 100%;
}

.clearfix_row_item {
  width: 100%;
  display: flex;
  align-items: center;
}

.svg_style {
  width: 28px;
  height: 28px;
  margin: 0 14px;
}

.svg_text {
  font-size: 24px;
  color: #333;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
