.container {
  position: relative;
}

.bg {
  position: absolute;
  width: 100%;
  height: 36px;
  background: #fff;
  z-index: 0;
}

.tabs {
  position: sticky;
  margin: 0 auto;
  z-index: 1;

  :global {
    .adm-tabs-header {
      // background-color: #2f3e4d;
      border-bottom: none;

      .adm-tabs-header-mask {
        display: none;
      }

      .adm-tabs-tab-line {
        background: #fff;
      }

      .adm-tabs-tab {
        // font-family: Roboto-Medium!important;
        font-weight: 500;
        font-size: 28px;
        color: rgba(255, 255, 255, 0.6);
        line-height: 36px;

        &.adm-tabs-tab-active {
          color: #fff;
        }
      }
    }
    .adm-tabs-content {
      padding: 0;
    }
  }
}
