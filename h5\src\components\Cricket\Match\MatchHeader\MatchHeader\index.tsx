import { Image } from 'antd-mobile';
import classNames from 'classnames';
import { inject, observer } from 'mobx-react';
import React, { useMemo, useState } from 'react';

import { StatusCodeEnum } from 'iscommon/const/constant';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

import CommonLiveHeader from '@/components/Common/LiveHeader';
import { getLiveUrl } from 'iscommon/utils/live';
import styles from './index.less';

interface Props {
  store?: any;
  checkIsIng: any;
  getHomeScore: any;
  getAwayScore: any;
  notStartedCode: any;
  getHomeBasketBallMatchTimeText: any;
  ballIconCode?: any;
  getHalfScoreText?: any;
}

interface StatusTextProps {
  matchHeaderInfo: any;
  serverTime: number;
  getHomeBasketBallMatchTimeText: any;
}

const StatusText = React.memo<StatusTextProps>(({ matchHeaderInfo, serverTime, getHomeBasketBallMatchTimeText }) => {
  const { statusText, isIng } = useMemo(() => getHomeBasketBallMatchTimeText(matchHeaderInfo), [serverTime, matchHeaderInfo]);

  return (
    <div className={`${isIng ? styles.progress : styles.resultType}`}>
      <div className={styles.pkTime}>{statusText}</div>
    </div>
  );
});

const SmallballMatchHeader: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: {
        Smallball: { SmallballMatch },
      },
      checkIsIng,
      getHomeScore,
      getAwayScore,
      notStartedCode,
      getHomeBasketBallMatchTimeText,
      getHalfScoreText,
      ballIconCode,
    } = props;
    const { serverTime, matchHeaderInfo, matchId } = SmallballMatch;
    const { homeTeam, awayTeam, statusId, servingSide } = matchHeaderInfo || {};

    const labelMaps = useTranslateKeysToMaps(['Animation']);
    const [showHeader, setShowHeader] = useState<boolean>(true);

    const hasLive = getLiveUrl(matchId);

    return (
      <div className={styles.container}>
        {showHeader ? (
          <>
            <div className={styles.content}>
              <div className={styles.team}>
                <Image
                  width={42}
                  height={42}
                  style={{ borderRadius: '50%', background: '#fff' }}
                  src={homeTeam?.logo}
                  fit="contain"
                />
                <div className={styles.teamName}>{homeTeam?.name}</div>
                {ballIconCode && servingSide === 1 ? (
                  <svg aria-hidden="true" className={classNames(styles.ballIcon, styles.ballIconRight)}>
                    <use data-v-50f2a635="" xlinkHref={ballIconCode}></use>
                  </svg>
                ) : null}
              </div>
              {statusId ? (
                <div className={styles.pk}>
                  {checkIsIng(statusId) && statusId !== StatusCodeEnum.NotStarted && (
                    <div className={`${styles.score} ${checkIsIng(statusId) ? 'color-ff4747' : ''}`}>
                      <div style={{ marginRight: 0 }}>{getHomeScore(matchHeaderInfo)}</div>
                      {/* <div style={{ marginLeft: 8 }}>{getAwayScore(matchHeaderInfo)}</div> */}
                    </div>
                  )}
                  {statusId === notStartedCode ? (
                    <>
                      <div className={styles.vs}>VS</div>
                      <div style={styles.vsText}>not started</div>
                    </>
                  ) : (
                    <StatusText
                      getHomeBasketBallMatchTimeText={getHomeBasketBallMatchTimeText}
                      matchHeaderInfo={matchHeaderInfo}
                      serverTime={serverTime}
                    />
                  )}
                  {!checkIsIng(statusId) && statusId !== StatusCodeEnum.NotStarted && (
                    <div className={`${styles.score} ${checkIsIng(statusId) ? 'color-ff4747' : ''}`}>
                      <div style={{ marginRight: 0 }}>{getHomeScore(matchHeaderInfo)}</div>
                      {/* <div style={{ marginLeft: 8 }}>{getAwayScore(matchHeaderInfo)}</div> */}
                    </div>
                  )}
                  {statusId !== StatusCodeEnum.NotStarted && getHalfScoreText && (
                    <div className={`${styles.halfScore}`}>
                      <div style={{ marginRight: 8 }}>{getHalfScoreText(matchHeaderInfo)}</div>
                    </div>
                  )}
                </div>
              ) : null}
              <div className={classNames(styles.team, styles.right)}>
                <Image
                  width={42}
                  height={42}
                  style={{ borderRadius: '50%', background: '#fff' }}
                  src={awayTeam?.logo}
                  fit="contain"
                />
                <div className={styles.teamName}>{awayTeam?.name}</div>
                {ballIconCode && servingSide === 2 ? (
                  <svg aria-hidden="true" className={classNames(styles.ballIcon, styles.ballIconLeft)}>
                    <use data-v-50f2a635="" xlinkHref={ballIconCode}></use>
                  </svg>
                ) : null}
              </div>
            </div>
            {hasLive && (
              <div className={styles.btnWrap}>
                <div className={styles.btn} onClick={() => setShowHeader(false)}>
                  <i className={classNames('icon iconfont icondonghuazhibo', styles.img)} />
                  {labelMaps.Animation}
                </div>
              </div>
            )}
          </>
        ) : (
          <CommonLiveHeader
            matchId={matchId}
            onPress={() => {
              setShowHeader(true);
            }}
          />
        )}
      </div>
    );
  }),
);

export default SmallballMatchHeader;
