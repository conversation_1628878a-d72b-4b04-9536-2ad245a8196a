import { FallbackPlayerImage } from 'iscommon/const/icon';
import { getCricketScore } from 'iscommon/utils/dataUtils';
import { inject, observer } from 'mobx-react';
import styles from './ScoreTable.less';

const ScoreTable = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Smallball: {
          SmallballMatch: { matchHeaderInfo },
        },
      },
    } = props;
    const { matchStatus, homeTeam, awayTeam, extraScores } = matchHeaderInfo;
    const scoreList = getCricketScore(matchStatus, extraScores);

    const renderScore = (type = 1) => {
      const isHome = type === 1;
      const teamInfo = isHome ? homeTeam : awayTeam;
      return (
        <div className={styles.tableContent}>
          <div className={styles.teamNameBox}>
            <img src={teamInfo.logo || FallbackPlayerImage} className={styles.teamImg} alt="" />
            <span className={styles.teamName}>{teamInfo?.name}</span>
          </div>
          {scoreList.map((item: any, index) => {
            return (
              <div
                key={index}
                className={`${styles.flex1} ${item.isRed && 'color-ff4747'} ${
                  item.compareStatus !== type && !item.isRed && styles.insideLoserColor
                }`}
              >
                {isHome ? item.h : item.w}
                <span className={styles.hxScore}>{isHome ? item.hx : item.wx}</span>
              </div>
            );
          })}
        </div>
      );
    };

    return (
      <div className={styles.scoreTableContainer}>
        {/* <div className={styles.tableTitle}>
          <div className={styles.teamNameBox} />
          {scoreList.slice(0, 9).map((item, index) => {
            return (
              <div className={`${styles.flex1} ${item.isRed && 'color-c1272d'}`} key={index}>
                {index + 1}
              </div>
            );
          })}
          <div className={`${styles.flex1}`}>R</div>
          <div className={`${styles.flex1}`}>H</div>
          <div className={`${styles.flex1}`}>E</div>
        </div> */}
        {renderScore(1)}
        {renderScore(2)}
      </div>
    );
  }),
);

export default ScoreTable;
