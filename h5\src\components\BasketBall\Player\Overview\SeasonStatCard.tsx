import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { inject, observer } from "mobx-react";
import './SeasonStatCard.less'

const SeasonStatCard = inject('store')(
  observer((props: any) => {
    const {
      store: { 
        Basketball: { Player }
      }
    } = props;
    const { playerHeaderInfo } = Player;

    const labelMaps = useTranslateKeysToMaps([ 'Stats', 'Season' ]);

    const StatItem = ({ label, value, rank }) => {
      return (
        <div className="stat-item">
          <div className="stat-label">{label}</div>
          <div className="stat-value">{value}</div>
          <div className="stat-rank">{rank}</div>
        </div>
      );
    };

    return (
      <div className="basketball-player-season-stat-conainer">
        <div className="container-title">
          {playerHeaderInfo?.season?.year}&nbsp;{labelMaps.Season}&nbsp;{labelMaps.Stats}
        </div>
        <div className="container-body">
          <div className="stats-row">
            <StatItem label='PPG' value={playerHeaderInfo?.player?.pointPerGame} rank={playerHeaderInfo?.player?.pointPerGameRank}/>
            <StatItem label='RPG' value={playerHeaderInfo?.player?.reboundsPerGame} rank={playerHeaderInfo?.player?.reboundsPerGameRank}/>
            <StatItem label='APG' value={playerHeaderInfo?.player?.assistsPerGame} rank={playerHeaderInfo?.player?.assistsPerGameRank}/>
            <StatItem label='BLK' value={playerHeaderInfo?.player?.blocks} rank={playerHeaderInfo?.player?.blocksRank}/>
            <StatItem label='STL' value={playerHeaderInfo?.player?.steals} rank={playerHeaderInfo?.player?.stealsRank}/>
          </div>
        </div>
      </div>
    )
  }),
);

export default SeasonStatCard;
