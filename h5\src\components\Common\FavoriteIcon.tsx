import { addFavorite, addFavoriteCompetitions, deleteFavorite, deleteFavoriteCompetitions } from 'iscommon/api/favorite';
import { GlobalConfig } from 'iscommon/const/globalConfig';
import { CategoryIcon } from 'iscommon/const/icon';
import { inject, observer } from 'mobx-react';
import './FavoriteIcon.less';

interface Props {
  store?: any;
  match?: {
    matchId: string;
    [key: string]: any;
  };
  competition?: {
    competitionName: string;
    competitionId: string;
    country?: {
      name: string;
      logo: string;
      [key: string]: string;
    };
    category?: {
      name: string;
      logo: string;
      [key: string]: string;
    };
  };
  isMatch: boolean;
}

const StoreKey = {
  football: '',
  basketball: 'Basketball',
};

// MATCH favorite
const FavoriteMatchIcon = inject('store')(
  observer((props: Props) => {
    const { store, match = {} } = props;
    const { matchId } = match!;
    const { WebHome } = StoreKey[GlobalConfig.pathname] ? store[StoreKey[GlobalConfig.pathname]] : store;
    const { favoriteMatches } = WebHome;
    const isFavorite = favoriteMatches.findIndex((item: any) => item.matchId == matchId) > -1;

    return isFavorite ? (
      <i
        onClick={(e) => {
          deleteFavorite(matchId).then((matchs) => {
            WebHome.changeFavoriteMatches(matchs);
          });
          e.preventDefault();
          e.stopPropagation();
        }}
        className="favoriteIcon collectImg iconfont icon-yishoucang"
      ></i>
    ) : (
      <i
        onClick={(e) => {
          addFavorite(match).then((matchs) => {
            WebHome.changeFavoriteMatches(matchs);
          });
          e.preventDefault();
          e.stopPropagation();
        }}
        className="favoriteIcon iconfont icon-weishoucang collectImg"
      ></i>
    );
  }),
);

const FavoriteCompetitionIcon = inject('store')(
  observer((props: Props) => {
    const { store, competition } = props;
    const { WebHome } = StoreKey[GlobalConfig.pathname] ? store[StoreKey[GlobalConfig.pathname]] : store;
    const { competitionId: id1, country, category, id } = competition!;
    const competitionId = id1 || id;
    const { favoriteCompetitions } = WebHome;
    const isFavorite = favoriteCompetitions.findIndex((item: any) => item.competitionId == competitionId) > -1;
    return isFavorite ? (
      <i
        onClick={(e) => {
          deleteFavoriteCompetitions({ competitionId }).then((list) => {
            WebHome.changeFavoriteCompetitions(list);
          });
          e.preventDefault();
          e.stopPropagation();
        }}
        className="favoriteIcon collectImg iconfont icon-yishoucang"
      ></i>
    ) : (
      <i
        onClick={(e) => {
          addFavoriteCompetitions({
            ...competition,
            competitionId,
            logo: country?.logo || category?.logo || (CategoryIcon as any)[category?.logo || ''],
          }).then((list) => {
            WebHome.changeFavoriteCompetitions(list);
          });
          e.preventDefault();
          e.stopPropagation();
        }}
        className="favoriteIcon iconfont icon-weishoucang collectImg"
      ></i>
    );
  }),
);

const FavoriteIcon = (props: Props) => {
  if (props.isMatch) {
    return <FavoriteMatchIcon {...props} />;
  }
  return <FavoriteCompetitionIcon {...props} />;
};

export default FavoriteIcon;