// .integral_table_container {
//   width: 100%;
//   display: flex;
//   flex-direction: column;
// }

// .heater_style {
//   height: 60px;
//   display: flex;
//   flex-direction: row;
//   align-items: center;
//   background-color: white;
//   color: rgba(153, 153, 153, 0.7);
//   border-bottom: 1px solid hsla(0, 0%, 89%, 0.6);
// }

// .rows_style {
//   height: 60px;
//   display: flex;
//   flex-direction: row;
//   align-items: center;
//   background-color: white;
//   color: black;
//   border-bottom: 1px solid hsla(0, 0%, 89%, 0.6);
// }

// .index_column {
//   width: 50px;
//   height: 100%;
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   font-size: 24px;
// }

// .team_column {
//   height: 100%;
//   flex: 1;
//   display: flex;
//   flex-direction: row;
//   align-items: center;
// }

// .team_logo {
//   width: 28px;
//   height: 28px;
//   border-radius: 50%;
//   overflow: hidden;
// }

// .team_name {
//   margin-left: 12px;
//   font-size: 24px;
//   color: #333;
//   display: inline-block;
//   max-width: 150px;
//   overflow: hidden;
//   text-overflow: ellipsis;
//   white-space: nowrap;
// }

// .data_rows {
//   width: 496.8px;
//   display: flex;
//   flex-direction: row;
// }

// .data_column {
//   width: 100px;
//   height: 100%;
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   font-size: 24px;
//   color: rgba(51, 51, 51, 0.7);
// }

// .bg_eff6fc {
//   background: #eff6fc;
// }

// .bg_F6F8F9 {
//   background: #f6f8f9;
// }

// .bg_FEF4E1 {
//   background: #fef4e1;
// }

// .goals_column {
//   height: 100%;
//   flex: 0.5;
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   background: #fff5e2;
//   color: rgba(51, 51, 51, 0.7);
// }

// .pts_column {
//   width: 65px;
//   height: 100%;
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   color: rgba(51, 51, 51, 0.7);
// }

// .index_box {
//   width: 40px;
//   height: 40px;
//   border-radius: 50%;
// }

// .color333 {
//   color: #333 !important;
// }

// .color999 {
//   background: #999999 !important;
// }

// .index1 {
//   background: #ff9966 !important;
// }

// .index2 {
//   background: #ff9966 !important;
// }

// .index3 {
//   background: #00d200 !important;
// }

// .index4 {
//   background: #52b2f5 !important;
// }

// .index5 {
//   background: #57a87b !important;
// }

// .legend_container {
//   display: flex;
//   flex-direction: column;
//   justify-content: center;
//   padding: 10px 0 10px 24px;

//   .legend_box {
//     display: flex;
//     flex-direction: row;
//     align-items: center;
//     font-size: 24px;

//     .legend {
//       // margin-left: 15px;
//       width: 16px;
//       height: 16px;
//       border-radius: 50%;
//       // margin-right: 8px;
//     }

//     .legend_name {
//       margin-left: 16px;
//       color: #999;
//     }

//     .ml_16 {
//       margin-left: 16px;
//     }

//     .mr_20 {
//       margin-left: 20px;
//     }
//   }
// }

.common-standing-integral-container {
  .custom-common-standing-table {
    width: 100%;
    display: flex;
    flex-direction: column;
    border-collapse: collapse;
  
    .header-row, .table-row {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #2c2c2c;
      text-align: left;
      color: #fff;
      align-items: center;
    }
  
    .header-row {
      background-color: #1E1E1E;
      font-weight: bold;
    }
  
    .table-row {
      background-color: #121212;
    }
  
    .header-cell, .table-cell {
      flex: 0 0 60px;
      text-align: center;
      padding: 10px;
      font-size: 24px;
      align-content: center;
    }
  
    .team-cell {
      display: flex;
      align-items: center;
      flex: 0 0 300px;
      text-align: left;
      overflow: hidden;
  
      .team-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin: 5px 10px 5px 0px;
        overflow: hidden;
        background-size: cover;
      }
  
      .team-name {
        font-size: 24px;
        color: #fff;
        text-overflow: ellipsis;
      }
    }
  }
  
  .standing-promotion-container {
    margin-top: 20px;
  
    .promotion-container {
      display: flex;
      align-items: center;
      color: #fff; 
      text-align: center;
  
      .promotion-badge {
        display: inline-block;
        margin-right: 5px;
        border-radius: 50%;
      }
    }
  }
}
