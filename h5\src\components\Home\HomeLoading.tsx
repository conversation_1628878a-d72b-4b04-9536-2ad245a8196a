import './HomeLoading.less';

// export default function () {
//   return (
//     <div className="matchListSke">
//       {[0, 1, 2].map((item) => {
//         return (
//           <div key={item} className="w100 itemske backAnimation">
//             {[0, 1, 2].map((s) => {
//               return (
//                 <div key={s} className="w100 flex tab" style={{ borderTop: '1px solid #e3e3e3' }}>
//                   <div className="time h100 flex align-center justify-center">
//                     <div className="time_content myback2"></div>
//                   </div>{' '}
//                   <div className="team h100">
//                     <div className="flex align-center" style={{ height: '50%', color: '#2c2c2c'}}>
//                       <div className="img myback2"></div> <div className="teamName myback2"></div>
//                     </div>{' '}
//                     <div className="flex align-center" style={{ height: '50%', color: '#2c2c2c'}}>
//                       <div className="img myback2"></div>
//                       <div className="teamName myback2 myback1"></div>
//                     </div>{' '}
//                     <div className="myicon myback2"></div>
//                   </div>{' '}
//                   <div className="score h100 flex align-center justify-center" style={{background: '#2c2c2c'}}>
//                     <div className="score_content myback2"></div>
//                   </div>
//                 </div>
//               );
//             })}
//           </div>
//         );
//       })}
//     </div>
//   );
// }

export default function HomeLoading() {
  return (
    <div className="matchListSke">
      {[0, 1, 2].map((item) => (
        <div key={item} className="w100 itemske">
          {[0, 1, 2].map((s) => (
            <div key={s} className="w100 flex tab">
              <div className="time h100 flex align-center justify-center">
                <div className="time_content skeleton"></div>
              </div>
              <div className="team h100">
                <div className="flex align-center skeleton-rows" style={{ height: '50%' }}>
                  <div className="img skeleton"></div> 
                  <div className="teamName skeleton"></div>
                </div>
                <div className="flex align-center skeleton-rows" style={{ height: '50%' }}>
                  <div className="img skeleton"></div>
                  <div className="teamName skeleton"></div>
                </div>
                <div className="myicon skeleton"></div>
              </div>
              <div className="score h100 flex align-center justify-center skeleton-rows">
                <div className="score_content skeleton"></div>
              </div>
            </div>
          ))}
        </div>
      ))}
    </div>
  )
};
