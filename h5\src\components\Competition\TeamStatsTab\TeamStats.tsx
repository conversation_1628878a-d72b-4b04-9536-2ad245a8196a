import { Avatar } from 'antd-mobile';
import { inject, observer } from 'mobx-react';
import React, { useCallback, useEffect, useState } from 'react';

import { getStatsTeam } from 'iscommon/api/competition';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';

import Loading from '@/components/Loading';

import styles from './TeamStats.less';

interface Props {
  type: string;
}

const TeamStats: React.FC<Props> = inject('store')(
  observer((props: any) => {
    const { type = 'goals' } = props;
    const {
      store: { Competitions },
    } = props;
    const { competitionId } = Competitions;
    const [sourceList, setSourceList] = useState<any[]>([]);
    const [listData, setListData] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);

    const onClick = useCallback((item: any) => {
      GlobalUtils.goToPage(PageTabs.team, item.team.id);
    }, []);

    useEffect(() => {
      if (sourceList.length) {
        setListData([sourceList[0], sourceList[1], sourceList[2]]);
      }
    }, [sourceList]);

    useEffect(() => {
      if (competitionId) {
        setLoading(true);
        getStatsTeam({
          competitionId,
          property: type,
        }).then(({ teamStatistics }: any) => {
          setSourceList(teamStatistics);
          setLoading(false);
        });
      }
    }, [competitionId, type]);

    return (
      <div className={styles.box}>
        <div className={styles.container}>
          <Loading loading={loading} isEmpty={!sourceList?.length}>
            {listData.map((item: any, index: any) => (
              <div className={styles.row_item} key={item.team?.id} onClick={() => onClick(item)}>
                <div className={styles.icon_column}>
                  {index <= 2 ? (
                    <span className="icon iconfont icongoals1" style={{ color: '#ffca28', fontSize: 20 }} />
                  ) : (
                    <span style={{ height: 20, width: 20 }}></span>
                  )}
                </div>
                <Avatar src={item.team ? item.team.logo : ''} className={styles.avatar_style} />
                <div className={styles.player_info_column}>
                  <span className={styles.player_name_style}>{item.team?.name}</span>
                </div>
                <div className={styles.value_column}>{`${item.goals}(${item.penalty})`}</div>
              </div>
            ))}
          </Loading>
        </div>
      </div>
    );
  }),
);

export default TeamStats;
