import { Tabs } from 'antd-mobile';
import React, { Children, useCallback } from 'react';
import { history, useLocation, useParams } from 'umi';

import { PageTabs } from 'iscommon/const/globalConfig';
import { useCrumbList } from 'iscommon/hooks/content';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

import PageBreadcrumb from '@/components/Common/PageBreadcrumb';

import styles from './index.less';
import PageHeader from '../PageHeader/Index';
import { inject, observer } from 'mobx-react';

interface Props {
  datasource: {
    key?: string;
    label: string;
    children: React.ReactNode;
  }[];
  crumbName?: string;
  meta?: {
    title: string;
    description: string;
  };
  store?: any;
  style?: React.CSSProperties;
}

// const TabList: React.FC<Props> = (props) => {
const TabList: React.FC<Props> = inject('store')(
  observer((props) => {
    const { datasource, crumbName, meta, style } = props;
    const location = useLocation();
    const { categoryId, tabId } = useParams<{ categoryId: string; tabId: string }>();
    // @ts-ignore
    const { crumbs } = useCrumbList(PageTabs.player, categoryId, crumbName, meta ? [{ meta }] : null);
    const labelMaps = useTranslateKeysToMaps(datasource, 'label');

    const onChange = useCallback(
      (nextTabId: string) => {
        console.log("Changing tab to:", nextTabId); 
        const basePath = location.pathname.split('/').filter(Boolean).slice(0, -2).join('/');
        history.push(`/${basePath}/${categoryId}/${nextTabId}/`);
      },
      [categoryId, location.pathname],
    );

    return (
      <div className={styles.container}>
        {/* {crumbName ? <PageBreadcrumb crumbs={crumbs} /> : null} */}
        {!!datasource?.length && (
          <Tabs className={styles.tabs} activeKey={tabId} stretch onChange={onChange}>
            {datasource.map(({ key, label, children }) => {
              return (
              <Tabs.Tab title={labelMaps[label]} key={key || label} forceRender={false} destroyOnClose style={style}>
                {children}
              </Tabs.Tab>
            )})}
          </Tabs>
        )}
      </div>
    );
  }),
);

export default React.memo(TabList);
