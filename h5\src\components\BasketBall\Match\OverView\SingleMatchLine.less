.singleMatchLine {
  .head {
    display: flex;
    width: 100%;
    background-color: rgba(241, 241, 241, 1);
    padding: 0 24px;
    justify-content: space-between;
    align-items: center;
    font-size: 24px;
    font-weight: 500;
    color: #343434;
    height: 54px;
  }
  .plugins {
    width: 100%;
    min-height: 224px;
    background: #ffffff;
    margin-bottom: 17px;
    overflow: hidden;
    .sectionTitle {
      font-size: 28px;
      font-weight: 500;
      color: #333333;
      padding: 5px 0;
      background-color: #fff;
      display: flex;
      justify-content: center;
    }
    .timeLineUp {
      display: flex;
      align-items: center;
      padding: 10px 24px;
      width: 100%;
      &.fright {
        flex-direction: row-reverse;
        .time {
          margin-left: 17px;
          margin-right: 0;
        }
        .infoText {
          text-align: right;
        }
      }
      .time {
        font-size: 20px;
        font-weight: 500;
        color: #999999;
        margin-right: 17px;
        margin-left: 0;
        width: 40px;
      }
      .icon {
        margin: 0 8px;
        width: 34px;
        height: 34px;
      }
      .identScore {
        margin: 0 8px;
        height: 34px;
        background: #0d7fd9;
        border-radius: 8px;
        padding: 0 5px;
        color: #ffffff;
        line-height: 34px;
      }
      .info {
        margin: 0 8px;
        .infoText {
          font-size: 20px;
          font-weight: 500;
          color: #333333;
        }
      }
    }
    .des {
      border-top: 1px solid rgba(241, 241, 241, 1);
      padding-left: 24px;
      .des_key {
        align-items: center;
        height: 70px;
        display: flex;
        .icon {
          font-size: 40px;
          margin-right: 10px;
        }
      }
    }
    .h2hDesc {
      padding: 0 24px;
      font-size: 24px;
      font-weight: 500;
      color: #9a9a9a;
      margin-bottom: 10px;
      // height: 24px;
    }
    .h2hItem {
      margin: 0 24px;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid rgba(241, 241, 241, 1);
      padding: 19px 0;
      .inner {
        flex: 1;
        display: flex;
        align-items: center;
        &.fright {
          justify-content: flex-end;
        }
        .time {
          font-size: 20px;
          font-weight: 500;
          color: #9a9a9a;
          margin-right: 60px;
          text-align: center;
        }
        .h2hTime {
          width: 140px;
        }
        .team {
          .teamSection {
            font-size: 24px;
            font-weight: 500;
            color: #333333;
            display: flex;
            align-items: center;
            .teamIcon {
              width: 30px;
              height: 30px;
              margin: 4px 8px 4px 0;
            }
          }
        }
        .result {
          margin-left: 60px;
        }
      }
    }
    .oddItem {
      display: flex;
      padding: 0 24px;
      justify-content: space-between;
      margin-bottom: 30px;
      .card {
        width: 160px;
        height: 48px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: rgba(246, 248, 249, 1);
        font-size: 24px;
        font-weight: 500;
        color: rgb(51, 51, 51);
        padding: 8px 20px;
        &.g {
          background: transparent;
          padding: 0;
        }
        &.g2 {
          background-color: rgba(192, 203, 0, 1);
          color: #fff;
          .line {
            background-color: #fff;
          }
        }
        &.b {
          background: rgb(121 162 206);
          color: #fff;
        }
        &.p {
          background: rgb(195 140 218);
          color: #fff;
        }
        &.o {
          background: rgb(234 133 45);
          color: #fff;
        }
        .line {
          width: 2px;
          height: 32px;
          background-color: #8b8585;
          margin-left: 20px;
        }
        .point {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
  .matchGoals {
    width: 100%;
    background: #ffffff;
    margin-bottom: 17px;
    .goalsType {
      display: flex;
      height: 137px;
      .goalsTypeItem {
        flex: 1;
        .typeName {
          height: 60px;
          text-align: center;
          line-height: 60px;
          font-size: 24px;
          color: #343434;
        }
        .goalsCount {
          display: flex;
          justify-content: space-around;
          align-items: center;
          > span {
            font-size: 20px;
          }
          .mycircle {
            width: 30%;
            height: auto;
          }
        }
      }
    }
    .onTarget {
      padding-bottom: 10px;
      margin-top: 10px;
      .name {
        text-align: center;
        font-size: 24px;
        color: #343434;
      }
      .targetItem {
        display: flex;
        justify-content: center;
        align-items: center;
        // padding: 0 24px;
        .targetCount {
          width: 100%;
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 30px;
          .spread {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 115px;
          }
          .w115 {
            width: 115px;
          }
          .foulStyle {
            background: #ff4747;
            font-size: 17px;
            color: #fff;
            padding: 2.1px 8.2px;
            border-radius: 6.624px;
          }
          .van-image {
            width: 20px;
            height: 20px;
            margin: 0 5px;
            margin-top: -5px;
          }
          .iconfont {
            width: 30px;
            height: 30px;
            margin-top: -5px;
          }

          .countLine {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            > span {
              width: 40px;
              text-align: center;
            }
            .percentLine {
              flex: 1;
              height: 8px;
              border-radius: 10px;
              background: #ebedf0;

              position: relative;
              overflow: hidden;
              .percent {
                position: absolute;
                top: 0;
                border-radius: 10px;

                // left: 0;
                height: 8px;
              }
            }
          }
        }
      }
    }
  }
}
