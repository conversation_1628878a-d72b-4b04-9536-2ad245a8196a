.basketball-team-header-container {
  background: #1e1e1e;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 80px 32px;
  width: 100%;

  .team-container {
    display: flex;
    flex-direction: row;
    align-items: center;

    .team-icon {
      height: 96px;
      width: 96px;
      border-radius: 50%;
      object-fit: contain;
      vertical-align: middle;
      margin-right: 10px;
    }

    .team-info {
      display: flex;
      flex-direction: column;

      .team-name-container {
        display: flex;
        align-items: center;

        .team-name {
          font-size: 28px;
          font-weight: bold;
          color: #fff;
        }
      }

      .team-detail {
        display: flex;
        align-items: center;

        .country-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin-right: 10px;
          overflow: hidden;
          background-size: cover;
        }

        .country-name {
          font-size: 24px;
          color: #fff;
        }
      }
    }
  }
}