// @ts-nocheck
import igsRequest from 'iscommon/request/instance';
import { genValidObj } from 'iscommon/utils';

// // import 'iscommon/mock/basketball-team-mock';

export const getTeamHeader = ({ teamId } = {}) => {
  return igsRequest.post('team/detail/header', { teamId }).then((result) => {
    return result;
  });
};

export const getTeamForm = ({ teamId, seasonId } = {}) => {
  return igsRequest.post('team/detail/form', { teamId, seasonId }).then((result) => {
    return result;
  });
};

export const getTeamSchedule = ({ teamId, seasonId, competitionId } = {}) => {
  return igsRequest.post('team/detail/schedule', { teamId, seasonId, competitionId }).then((result) => {
    return result;
  });
};

export const getTeamLineup = ({ teamId } = {}) => {
  return igsRequest.post('team/detail/lineup', { teamId }).then((result) => {
    return result;
  });
};

export const getTeamKeyplayer = ({ teamId, seasonId, orderType } = {}) => {
  return igsRequest.post('team/detail/keyplayer', { teamId, seasonId, orderType }).then((result) => {
    return result;
  });
};
