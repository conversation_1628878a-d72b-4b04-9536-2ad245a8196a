.tabContainer {
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  align-items: center;
  box-sizing: border-box;
  background: #fefffe;
  border-bottom: 2px solid #e3e3e3;
  // position: fixed;
  // width: 100%;
  // z-index: 999;
  > div {
    display: flex;
    height: 72px;
    align-items: center;
  }

  // .tabItem {
  //   margin: 0 24px;
  //   font-size: 28px;
  //   font-family: Urbanist-Regular;
  //   font-weight: 500;
  //   color: #333333;
  //   height: 72px;
  //   line-height: 72px;

  //   &.active {
  //     color: #0f80d9;
  //     border-bottom: 4px solid #0f80d9;
  //   }
  //   &.activelive {
  //     color: #c72a1d;
  //     border-bottom: 4px solid #c72a1d;
  //   }
  // }

  .calendarIcon {
    position: relative;

    .dateIcon {
      position: absolute;
      left: 50%;
      text-align: center;
      color: #666;
      font-family: Roboto-Medium;
      font-weight: 500;
      font-size: 20px;
      top: 50%;
      margin-left: 3px;
      transform: translateX(-50%) translateY(-45%);
    }
  }
}

.sortByTime {
  display: flex;
  height: 72px;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  margin-top: 20px;
  background-color: #fff;
  font-size: 28px;
  font-family: Roboto-Medium !important;
  font-weight: 500;
  color: #999;

  .current {
    font-style: normal;
    color: #de1e30;
  }

  .iconjiantou {
    color: #999;
    margin-left: 10px;
  }
}
