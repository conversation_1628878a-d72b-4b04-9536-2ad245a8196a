/* eslint-disable react/no-unknown-property */

import { HomeGameTab } from 'iscommon/const/constant';
import { translate } from 'iscommon/i18n/utils';
import { inject, observer } from 'mobx-react';
import moment from 'moment';
import React, { useMemo, useState } from 'react';
import { Link } from 'umi';

import CalendarBlock from '@/components/Common/Calendar';

import './GameTab.less';
import { CalendarPicker } from 'antd-mobile';
import { CalendarOutline } from 'antd-mobile-icons';

// const TabLeftMap = [
//   {
//     key: HomeGameTab.All,
//     name: 'AllGames',
//   },
//   {
//     key: HomeGameTab.Live,
//     name: 'LiveH5',
//   },
//   {
//     key: HomeGameTab.Leagues,
//     name: 'Leagues',
//   },
// ];

const TabLeftMap = [
  { key: HomeGameTab.All, name: 'All', },
  {
    key: HomeGameTab.Live,
    icon: <i className="iconfont icon-jinhangzhong ml-0" />,
    name: 'LIVE',
  },
  { key: HomeGameTab.Finished, name: 'Finished', },
  { key: HomeGameTab.Scheduled, name: 'Scheduled', },
];

const GameTab = inject('store')(
  observer((props: any) => {
    const {
      Basketball: { WebHome },
      WebConfig,
    } = props.store;
    const { currentGameTab, date } = WebHome;
    const { currentLiveCount } = WebConfig;
    const sortByTime = translate('SortByTime');
    const [calendarVis, setCalendarVis] = useState<boolean>(false);
    const [calendarVisible, setCalendarVisible] = useState(false)

    const dateText = useMemo(() => moment(date).format('DD'), [date]);

    const onConfirm = (date: any) => {
      WebHome.changeDate(date)
      setCalendarVisible(false);
    };

    return (
      <>
      <div className='game-tab-container'>
        <div className='game-tab-container-right-aligned'>
          {TabLeftMap.map((item) => {
            return (
              <div
                className={`tab-item ${item.key === currentGameTab ? (item.key == 0 ? 'activelive' : 'active') : ''}`}
                key={item.key}
                onClick={() => {
                  WebHome.switchHomeGameTab(item.key);
                }}
              >
                {item.icon || ''}
                <span className="tab-item-text">{translate(item.name)}</span>
                {/* {!!item.count && !!currentLiveCount?.liveCount && <span className="total">({currentLiveCount.liveCount})</span>} */}
              </div>
            );
          })}
        </div>  
        {currentGameTab !== HomeGameTab.Live && (
          <CalendarOutline className='header-calender-icon' onClick={() => setCalendarVisible(true)}/>
        )}
      </div>
      {currentGameTab !== HomeGameTab.Live && (
        <CalendarPicker 
          className='game-tab-calendar-picker'
          visible={calendarVisible}
          selectionMode="single"
          onConfirm={onConfirm}
          onClose={() => setCalendarVisible(false)}
        />
      )}
    </>
      // <>
      //   <div className="tabContainer">
      //     <div>
      //       {TabLeftMap.map((item) => {
      //         return (
      //           <div
      //             className={`tabItem ${item.key === currentGameTab ? (item.key == 0 ? 'activelive' : 'active') : ''}`}
      //             key={item.key}
      //             onClick={() => {
      //               WebHome.switchHomeGameTab(item.key);
      //             }}
      //           >
      //             <span className="innerText">{translate(item.name)}</span>
      //           </div>
      //         );
      //       })}
      //     </div>
      //     {currentGameTab == HomeGameTab.All && (
      //       <div className="calendarIcon" onClick={() => setCalendarVis(!calendarVis)}>
      //         <span className="icon iconfont iconrili myriliIcon"></span>
      //         <div className="dateIcon">{dateText}</div>
      //       </div>
      //     )}
      //   </div>
      //   {currentGameTab === HomeGameTab.All && (
      //     <Link to="/basketball/timesort" className="sortByTime">
      //       <span style={{ color: '#666' }}>{sortByTime}</span>
      //       {currentLiveCount ? (
      //         <span>
      //           <i className="current">{currentLiveCount.liveCount || 0}</i>/{currentLiveCount.totalCount || 0}
      //           <span className="icon iconfont fs-12 iconjiantou"></span>
      //         </span>
      //       ) : null}
      //     </Link>
      //   )}
      //   {calendarVis ? (
      //     <CalendarBlock
      //       defaultSingle={date}
      //       onChange={(date: any) => {
      //         WebHome.changeDate(date);
      //         setCalendarVis(false);
      //       }}
      //       onCancel={() => {
      //         setCalendarVis(false);
      //       }}
      //     />
      //   ) : null}
      // </>
    );
  }),
);

export default GameTab;
