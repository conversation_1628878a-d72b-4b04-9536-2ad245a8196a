import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { momentTimeZone } from 'iscommon/utils';

import './GameCategory.less';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { useState } from 'react';
import { Badge, CalendarPicker, DatePicker } from 'antd-mobile';
import moment from 'moment';
import { FallbackImage } from 'iscommon/const/icon';
import assist from 'iscommon/assets/images/football/assist.png'
import goal from 'iscommon/assets/images/football/goal.png'
import { Link } from 'umi';

// const RatingColorMap = (rating: number) => {
//   if (rating >= 9) {
//     return `rgb(14, 134, 4)`;
//   }
//   if (rating >= 8.0) {
//     return `rgb(92, 180, 0)`;
//   }
//   if (rating >= 7.0) {
//     return `rgb(193, 204, 1)`;
//   }
//   if (rating >= 6.5) {
//     return `rgb(255, 174, 15)`;
//   }
//   if (rating >= 6.0) {
//     return `rgb(255, 102, 0)`;
//   }
//   return `rgb(255, 32, 60)`;
// };

const getBadgeBackgroundColor = (count: number) => {
  if (count >= 8.0 && count <= 10.0) {
    return '#00ADC4';
  } else if (count >= 7.0 && count <= 7.9) {
    return '#1EC853';
  } else if (count >= 6.6 && count <= 6.9) {
    return '#FFAE0F';
  } else if (count >= 6.0 && count <= 6.5) {
    return '#F08022';
  } else if (count >= 0.1 && count <= 5.9) {
    return '#ED5454';
  }
};

export const MatchList = ({ matches }: any) => {
  return (
    <>
    </>
    // <div className="gameList">
    //   {matches.map((item: any) => {
    //     return (
    //       <div className="listItem" key={item.matchId} onClick={() => GlobalUtils.goToPage(PageTabs.match, item.matchId)}>
    //         <div className="listLeft">
    //           <span className="fs-10-center">{momentTimeZone(item.matchTime, 'YYYY/MM/DD')}</span>
    //           <span className="fs-10-center">FT</span>
    //         </div>
    //         <div className="listContent">
    //           <div className="vsCountry">
    //             <div className="vsCountryItem">
    //               <i className="squareLogo" style={{ backgroundImage: `url(${item.homeTeam.logo})` }}></i>
    //               <span className="teamName text-overflow">{item.homeTeam.name}</span>
    //               {item.homeScores[3] > 0 && <span className="card yellow">{item.homeScores[3]}</span>}
    //               {item.homeScores[2] > 0 && <span className="card red">{item.homeScores[2]}</span>}
    //             </div>
    //             <div className="vsCountryItem">
    //               <i className="squareLogo" style={{ backgroundImage: `url(${item.awayTeam.logo})` }}></i>
    //               <span className="teamName text-overflow">{item.awayTeam.name}</span>
    //               {item.awayScores[3] > 0 && <span className="card yellow">{item.awayScores[3]}</span>}
    //               {item.awayScores[2] > 0 && <span className="card red">{item.awayScores[2]}</span>}
    //             </div>
    //           </div>
    //           <div className="rating fs-10-center" style={{ backgroundColor: RatingColorMap(item.rating) }}>
    //             {item.rating?.toFixed(1)}
    //           </div>
    //         </div>
    //         <div className="listRight">
    //           <div className={`${item.homeScores[0] > item.awayScores[0] ? 'bold' : ''} `}>
    //             <span>{item.homeScores[0]}</span>
    //           </div>
    //           <div className={`${item.homeScores[0] < item.awayScores[0] ? 'bold' : ''} `}>
    //             <span>{item.awayScores[0]}</span>
    //           </div>
    //         </div>
    //       </div>
    //     );
    //   })}
    // </div>
  );
};

const GameCategory = (props: any) => {
  const matches = props.category || [];

  if (!matches || matches.length === 0) {
    return null;
  }

  const { competition } = matches[0];
  const { name, logo, id } = competition;

  console.log('matches123', matches)

  return (
    <>
      {matches.map((match: any) => (
        <Link key={match.matchId} className="match-card" to={GlobalUtils.getPathname(PageTabs.match, match.matchId)}>
          <div className='match-card-header'>
            <span className="competition-time">{moment.unix(match?.matchTime).format('YYYY-MM-DD')}</span>
            <span className='competition-name'>{match?.competition?.name}</span>
          </div>
          <div className='match-card-body'>
            <div className="team-container">
              <div className='team-section'>
                <img className="team-icon" src={match.homeTeam.logo || FallbackImage} alt={match.homeTeam.name} />
                <span className="team-name">{match.homeTeam.name}</span>
                <span className="team-score">{match.homeScores[0]}</span>
              </div>
              <div className='team-section'>
                <img className="team-icon" src={match.awayTeam.logo || FallbackImage} alt={match.awayTeam.name} />
                <span className="team-name">{match.awayTeam.name}</span>
                <span className="team-score">{match.awayScores[0]}</span>
              </div>
            </div>
            <div className="match-info">
              {match.goals > 0 && (
                <div className="goal-container">
                  {Array.from({ length: Number(match.goals) }).map((_, index) => (
                    <img key={`goal-${index}`} src={goal} alt="goal" className="goal-incident" />
                  ))}
                </div>
              )}
              {match.assists > 0 && (
                <div className="assist-container">
                  {Array.from({ length: Number(match.assists) }).map((_, index) => (
                    <img key={`assist-${index}`} src={assist} alt="assist" className="assist-incident" />
                  ))}
                </div>
              )}
              <span className='match-time'>{match?.minutesPlayed}'</span>
              {match.rating > 0 && (
                <Badge
                  className='player-rating-badge'
                  content={match.rating.toFixed(1)}
                  style={{ background: getBadgeBackgroundColor(match.rating.toFixed(1))}}
                />
              )}
            </div>
          </div>
        </Link>
      ))}
    </>
    
    // <>
    //   <div className="gameCategory">
    //     <div className="cnameContainer" onClick={() => GlobalUtils.goToPage(PageTabs.competition, id)}>
    //       <i className="squareLogo" style={{ backgroundImage: `url('${logo}')` }}></i>
    //       <span className="text-overflow" style={{ flex: 1 }}>
    //         {name}
    //       </span>
    //     </div>
    //   </div>
    //   <MatchList matches={matches} />
    // </>
  );
};

export default GameCategory;
