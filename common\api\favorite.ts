
import RequestStore from '../store/favoriteStore';

// add favorite
export const addFavorite = async (match = {}) => {
  return RequestStore.addFavorite(match);
};

// delete favorite
export const deleteFavorite = async (matchId) => {
  return RequestStore.deleteFavorite({ matchId });
};

// get favorite
export const getFavorites = async () => {
  return RequestStore.getFavorites();
};

export const addFavoriteCompetitions = async ({ competitionId = '', competitionName = '', logo = '', ...rest }) => {
  return RequestStore.addFavoriteCompetitions({ competitionId, competitionName, logo, ...rest });
};

// delete favorite
export const deleteFavoriteCompetitions = async ({ competitionId }) => {
  return RequestStore.deleteFavoriteCompetitions({ competitionId });
};

// get favorite
export const getFavoritesCompetitions = async () => {
  return RequestStore.getFavoritesCompetitions();
};