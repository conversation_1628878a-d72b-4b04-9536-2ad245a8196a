import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import _ from 'lodash';
import { useMemo } from 'react';
import styles from './BoxScoreTable.less';
import './BoxScoreTable.less'

const keyMaps = [
  'minutesPlayed',
  'points',
  'rebounds',
  'assists',
  'blocks',
  'steals',
  'fieldGoalsAccuracy',
  'threePointsAccuracy',
  'freeThrowsAccuracy',
  'offensiveRebounds',
  'defensiveRebounds',
  'turnovers',
  'personalFouls',
  'plusValue',
];

const BoxScoreTable: React.FC<Props> = (props: any) => {
  const { data, teamInfo } = props;
  const labelMaps = useTranslateKeysToMaps(['Starters', 'Bench', 'Team']);
  const typeMaps = {
    0: labelMaps.Bench,
    1: labelMaps.Starters,
    2: labelMaps.Team,
  };

  const headerMaps = useMemo(() => {
    return [
      {
        label: 'MIN',
        value: 'minutesPlayed',
      },
      {
        label: 'PTS',
        value: 'points',
      },
      {
        label: 'REB',
        value: 'rebounds',
      },
      {
        label: 'AST',
        value: 'assists',
      },
      {
        label: 'BLK',
        value: 'blocks',
      },
      {
        label: 'STL',
        value: 'steals',
      },
      {
        label: 'FG',
        value: 'fieldGoalsAccuracy',
      },
      {
        label: '3P',
        value: 'threePointsAccuracy',
      },
      {
        label: 'FT',
        value: 'freeThrowsAccuracy',
      },
      {
        label: 'OREB',
        value: 'offensiveRebounds',
      },
      {
        label: 'DREB',
        value: 'defensiveRebounds',
      },
      {
        label: 'TOV',
        value: 'turnovers',
      },
      {
        label: 'A/T',
        value: ({ assists, turnovers }) => (turnovers === 0 ? assists : (turnovers / turnovers).toFixed(2)),
      },
      {
        label: 'PF',
        value: 'personalFouls',
      },
      {
        label: '+/-',
        value: 'plusValue',
      },
    ];
  }, []);

  const renderHeader = (renderType: any, isCopy = false) => {
    return (
      <div className='table-header'>
        <div className='header-cell first-column'>{typeMaps[renderType]}</div>
        {isCopy ? null : headerMaps.map((item: any) => (
          <div className='header-cell' key={item.value}>{item.label}</div>
        ))}
      </div>
      // <div className={styles.tableHeader}>
      //   <div className={styles.columnOne}>{typeMaps[renderType]}</div>
      //   {isCopy
      //     ? null
      //     : headerMaps.map((item: any) => (
      //         <div className={styles.columnOther} key={item.value}>
      //           {item.label}
      //         </div>
      //       ))}
      // </div>
    );
  };

  const renderContent = (renderType: any, info: any, isCopy = false) =>
    info &&
    info.map((item: any) => (
      <div className='table-row'>
        <div className='table-cell player-cell'>
          <span className='player-name'>{item.player?.shortName}</span>
          <span className='player-position'>{item.player?.position}</span>
        </div>
        {isCopy ? null : headerMaps.map((val: any) => (
          <div className='table-cell' key={item.label}>
            {_.isString(val.value) ? item[val.value] : val.value(item)}
          </div>
        ))}
      </div>
      // <div className={styles.trow} key={Math.random()}>
      //   <div className={`${styles.columnOne} ${styles.font400}`}>
      //     <div className={styles.columnOnePlayer}>
      //       <span className={styles.playerName}>{item.player?.shortName}</span>
      //       <span className={styles.playerP}>{item.player?.position}</span>
      //       {renderType === 1 ? (
      //         <i
      //           className="icon iconfont iconiconlanqiu fs-12"
      //           style={{ color: '#e6732e', position: 'absolute', right: '0.1rem' }}
      //         />
      //       ) : null}
      //     </div>
      //   </div>
      //   {isCopy
      //     ? null
      //     : headerMaps.map((val: any) => (
      //         <div className={styles.columnOther} key={item.label}>
      //           {_.isString(val.value) ? item[val.value] : val.value(item)}
      //         </div>
      //       ))}
      // </div>
    ));

  const teamData = {
    player: {
      ...teamInfo,
    },
  };
  for (let v of keyMaps) {
    let total = 0;
    try {
      total = data.reduce((p, n) => {
        return (_.isNumber(p) ? p : p[v] || 0) + (n[v] || 0);
      }, 0);
    } catch (e) {}
    teamData[v] = total;
  }

  const substitute = data.filter((item) => item.isSubstitute == 1)
  const starting = data.filter((item) => item.isSubstitute == 0)

  return (
    <div className='basketball-box-score-table-container'>
      <div className='custom-box-score-table'>
        {renderHeader(1)}
        {renderContent(1, starting)}
        {renderHeader(0)}
        {renderContent(0, substitute)}
      </div>
    </div>
    // <div className={styles.container}>
    //   <div className={styles.allTableBox}>
    //     <div className={styles.tableBox}>
    //       {renderHeader(1)}
    //       {renderContent(1, starting)}
    //       {renderHeader(0)}
    //       {renderContent(0, substitute)}
    //       {renderHeader(2)}
    //       {renderContent(2, [teamData])}
    //       {/* {renderHeader(0)}
    //       {renderContent(0, [{ player: { shortName: 'K. Porzingis', position: 'C' } }, {}, {}, {}, {}, {}, {}, {}])}
    //       {renderHeader(2)} */}
    //     </div>
    //     <div className={styles.copyTableColumnOne}>
    //       {/* {renderHeader(1, true)}
    //       {renderContent(1, [{ player: { shortName: 'K. Porzingis', position: 'C' }, minutesPlayed: 38 }, {}, {}, {}, {}], true)}
    //       {renderHeader(0, true)}
    //       {renderContent(0, [{ player: { shortName: 'K. Porzingis', position: 'C' } }, {}, {}, {}, {}, {}, {}, {}], true)}
    //       {renderHeader(2, true)} */}
    //     </div>
    //   </div>
    // </div>
  );
};

export default BoxScoreTable;
