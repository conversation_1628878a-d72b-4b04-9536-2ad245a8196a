import './GameCategory.less';

import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { Image, Typography } from 'antd';
import { inject, observer } from 'mobx-react';
import { InLiveStatusEnum, OddsOptionsTypeMap, RedNumberStatusEnum, StatusCodeEnum } from 'iscommon/const/constant';
import React, { useCallback, useMemo } from 'react';
import { useRecentActivity } from '../../hooks/useRecentActivity';

import _ from 'lodash';
import { CategoryIcon } from 'iscommon/const/icon';
import classNames from 'classnames';
import Empty from '@/components/Common/Empty';
import FavoriteIcon from '@/components/Common/FavoriteIcon';
import { getHomeCompetitionMatchTime } from 'iscommon/utils/dataUtils';
import { Link } from 'umi';
import MatchLiveIcon from '@/components/Common/MatchLiveIcon';
import { momentTimeZone } from 'iscommon/utils';
import OddItem from '@/components/Common/OddItem';
import ScorePop from './ScorePop';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

interface PureMatchStatusTextProps {
  currentGameTab: number;
  serverTime: number;
  matchStatus: number;
  matchActiveTime: number;
  secondHalfKickOffTime: number;
}

const { Text } = Typography

const PureMatchStatusText = React.memo<PureMatchStatusTextProps>((props) => {
  const { currentGameTab, serverTime, matchStatus, matchActiveTime, secondHalfKickOffTime } = props;
  const { statusText, isAni, isIng } = useMemo(
    () =>
      getHomeCompetitionMatchTime({
        serverTime,
        matchStatus,
        matchActiveTime,
        secondHalfKickOffTime,
        currentGameTab,
      }),
    [serverTime, matchStatus, matchActiveTime, secondHalfKickOffTime, currentGameTab],
  );

  return (
    <span className={classNames('timeStatus', isAni && 'twinkleScore', isIng && 'ing', statusText === '-' && 'color-999')}>
      {statusText}
    </span>
  );
});

interface MatchStatusTextProps {
  match: any;
  store?: any;
}

const MatchStatusText: React.FC<MatchStatusTextProps> = inject('store')(
  observer((props: any) => {
    const {
      match: { matchStatus, matchActiveTime, secondHalfKickOffTime },
      store: {
        WebHome: { currentGameTab, serverTime },
      },
    } = props;

    return (
      <PureMatchStatusText
        serverTime={serverTime}
        matchStatus={matchStatus}
        matchActiveTime={matchActiveTime}
        secondHalfKickOffTime={secondHalfKickOffTime}
        currentGameTab={currentGameTab}
      />
    );
  }),
);

// oddsTR
const OddsTr = React.memo((props: { odds: number }) => {
  const { odds } = props;
  const labelMaps = useTranslateKeysToMaps(['OverBall', 'UnderBall', 'Over', 'Under']);
  if (odds === 0) {
    return (
      <div className="oddList oddTitle">
        <div className="oddItem">1</div>
        <div className="oddItem">X</div>
        <div className="oddItem">2</div>
      </div>
    );
  }
  if (odds === 1) {
    return (
      <div className="oddList oddTitle">
        <div className="oddItem">1</div>
        <div className="oddItem">2</div>
      </div>
    );
  }
  if (odds === 2) {
    return (
      <div className="oddList oddTitle">
        <div className="oddItem"></div>
        <div className="oddItem">{labelMaps.OverBall}</div>
        <div className="oddItem">{labelMaps.UnderBall}</div>
      </div>
    );
  }
  if (odds === 3) {
    return (
      <div className="oddList oddTitle">
        <div className="oddItem"></div>
        <div className="oddItem">{labelMaps.Over}</div>
        <div className="oddItem">{labelMaps.Under}</div>
      </div>
    );
  }
  return null;
});

const OddsTdEmpty = React.memo((props: { odds: number }) => {
  const { odds } = props;

  if (odds === 0) {
    return (
      <div className="oddList">
        <div className="oddItem">-</div>
        <div className="oddItem">-</div>
        <div className="oddItem">-</div>
      </div>
    );
  }
  if (odds === 1) {
    return (
      <div className="oddList">
        <div className="oddItem">-</div>
        <div className="oddItem">-</div>
      </div>
    );
  }
  if (odds === 2) {
    return (
      <div className="oddList">
        <div className="oddItem">-</div>
        <div className="oddItem">-</div>
        <div className="oddItem">-</div>
      </div>
    );
  }
  if (odds === 3) {
    return (
      <div className="oddList">
        <div className="oddItem"></div>
        <div className="oddItem">-</div>
        <div className="oddItem">-</div>
      </div>
    );
  }
  return null;
});

// odds display
interface Props {
  matchId: string;
  store?: any;
}

const OddsTdValue: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: { WebHome, WebConfig },
      matchId,
    } = props;
    const { odds, homeCompetitionOdds } = WebHome;
    const oddsList: any[] = useMemo(() => homeCompetitionOdds[matchId] || [], [homeCompetitionOdds, matchId]);
    const oddsType: string = useMemo(() => OddsOptionsTypeMap[odds], [odds]);
    const { oddsItem, lastOddsData } = useMemo(() => {
      const oddsItem = oddsList?.find((item) => item.oddsType === oddsType) || null;
      return {
        oddsItem,
        lastOddsData: oddsItem?.lastOddsData || [],
      };
    }, [oddsType, oddsList]);

    const { oddsData = [], sealTheDisk } = oddsItem || {};

    const renderItem = useCallback(
      (val: string, lastVal: string, key: number, options?: any) => {
        const { highlight = false, decimals = 2, handicap = '' } = options || {};
        const newVal = Number(val).toFixed(decimals);
        const oldVal = lastVal ? Number(lastVal).toFixed(decimals) : newVal;
        return (
          <OddItem
            key={val + key}
            index={key}
            type={oddsType}
            val={newVal}
            oldVal={oldVal}
            highlight={highlight}
            handicap={handicap}
            matchId={matchId}
          />
        );
      },
      [matchId, oddsType],
    );

    if (!WebConfig.showOdds) {
      return null;
    }

    if (oddsList?.length === 0 || !oddsItem) {
      return <OddsTdEmpty odds={odds} />;
    }

    if (sealTheDisk === 1) {
      return (
        <div>
          <i className="iconfont icon-suo"></i>
        </div>
      );
    }

    // 0: 1x2(eu), 1: Asian Handicap(asia), 2: Total Goals(bs) 3: Total Corners(cr)
    try {
      if (odds === 0) {
        return <div className="oddList">{[0, 1, 2].map((key) => renderItem(oddsData[key], lastOddsData[key], key))}</div>;
      }
      if (odds === 1) {
        return (
          <div className="oddList">
            {[0, 2].map((key) => {
              const isPlus = Number(oddsData[1]) > 0;
              const isMinus = Number(oddsData[1]) < 0;
              const plus = isPlus ? (key === 0 ? '-' : '+') : '';
              const minus = isMinus ? (key === 0 ? '+' : '-') : '';
              return renderItem(oddsData[key], lastOddsData[key], key, {
                handicap: oddsItem.handicap ? `${plus || minus}${oddsItem.handicap}` : '',
              });
            })}
          </div>
        );
      }
      if (odds === 2) {
        return (
          <div className="oddList">
            <div className="oddItem handicap">{oddsItem?.handicap || '-'}</div>
            {[0, 1].map((key) => renderItem(oddsData[key], lastOddsData[key], key))}
          </div>
        );
      }
      if (odds === 3) {
        return (
          <div className="oddList">
            {[1, 0, 2].map((key) => {
              return renderItem(oddsData[key], lastOddsData[key], key, {
                highlight: key === 1,
                decimals: key === 1 ? 1 : 2,
              });
            })}
          </div>
        );
      }
    } catch (e) {}
    return <OddsTdEmpty odds={odds} />;
  }),
);

// 首页赛事头部
interface GameCompetitionTabProps {
  competition: any;
  store?: any;
}

const GameCompetitionTab: React.FC<GameCompetitionTabProps> = inject('store')(
  observer((props) => {
    const {
      store: { WebHome, WebConfig },
      competition,
    } = props;
    const { competitionName, country, category, onlineCount, additionalCompetitionName, competitionId, curStageId } = competition;
    const { favoriteCompetitions, odds } = WebHome;

    const isFavorite = useMemo(
      () => favoriteCompetitions.findIndex((item: any) => item.id == competitionId) > -1,
      [competitionId, favoriteCompetitions],
    );

    return (
      <div className="game-category">
        <div className='competition-header-left'>
          <FavoriteIcon isMatch={false} competition={competition} />
          {country && (
            <>
              <Image className='teamLogo' src={country.logo} preview={false}/>
              <Text className='textWhite'>{country.name}: </Text>
            </>
          )}
          {category && !country && (
            <>
              <Image className='teamLogo' src={(CategoryIcon as any)[category.logo]} preview={false}/>
              <Text className='textWhite'>{category.name}: </Text>
            </>
          )}
          {!curStageId ? (
            <Text className="linkComp not-allow textWhite">{competitionName} &nbsp;</Text>
          ) : (
            <Link to={GlobalUtils.getPathname(PageTabs.competition, competitionId)} className="linkComp textWhite">
              {competitionName} &nbsp;
            </Link>
          )}
          {additionalCompetitionName && <Text className='textWhite'>{additionalCompetitionName}</Text>}
        </div>

        <div className="competition-header-right">
          {onlineCount > 0 && (
            <div className="score">
              {onlineCount >= 9999 ? (
                <img className="hot" src={require('../../../../common/assets/images/hot.png')} alt=""></img>
              ) : (
                <i className="iconfont icon-renshu"></i>
              )}
              {onlineCount >= 9999 ? '9999+' : onlineCount}
            </div>
          )}
          {WebConfig.showOdds && <OddsTr odds={odds} />}
        </div>
      </div>
    );
  }),
);

const ScoreText = inject('store')(
  observer((props: any) => {
    const {
      store: { WebHome: { currentGameTab } },
      item,
      isHome,
    } = props;
    const { matchStatus, calculatedAwayScore, calculatedHomeScore } = item;
    const score = isHome ? calculatedHomeScore : calculatedAwayScore;

    return (
      <div className="vsCountryItem">
        <ScorePop item={item}>
          {matchStatus === StatusCodeEnum.NotStarted ? (
            ''
          ) : (
            <span style={{ color: InLiveStatusEnum.includes(matchStatus) ? '#c1272d' : '#fff' }}>
              {score}
            </span>
          )}
        </ScorePop>
      </div>
    );
  }),
);

const MatchItem = React.memo(({ item, onMatchClick }: any) => {
  const path = useMemo(() => GlobalUtils.getPathname(PageTabs.match, item.matchId), [item.matchId]);
  const matchTime = useMemo(() => momentTimeZone(item.matchTime, 'HH:mm'), [item.matchTime]);

  const handleClick = useCallback(() => {
    if (onMatchClick) {
      onMatchClick(item);
    }
  }, [onMatchClick, item]);

  return (
    <Link to={path} className="game-category-football" onClick={handleClick}>
      <div>
        <FavoriteIcon match={item} isMatch />
        <Text className="time textWhite">{matchTime}</Text>
        <MatchStatusText match={item} />
      </div>

      <div className="vsCountry">
        <div className="vsCountryItem">
          <Image className="teamLogo" preview={false} src={item.homeTeam?.logo} />
          <Text className="teamName">{item.homeTeam?.name}</Text>
        </div>
        <div className="vsCountryItem">
          <Image className="teamLogo" preview={false} src={item.awayTeam?.logo} />
          <Text className="teamName">{item.awayTeam?.name}</Text>
        </div>
      </div>
      <div className='vsScoreText'>
        <ScoreText item={item} isHome={true}/>
        <ScoreText item={item} isHome={false}/>
      </div>

      <div>
        <div className="vsscore">
          {item.matchStatus === StatusCodeEnum.NotStarted ? (
            <span style={{ color: '#fff'}}>-</span>
          ) : (
            <div className="flex-bettween vshc">
              {item.matchStatus === StatusCodeEnum.HalfTime ? (
                <Text className="hidden textWhite">-</Text>
              ) : (
                <Text className="vsscoreht halfBox textWhite">
                  HT&nbsp; 
                  {item.homeScores[1]}-{item.awayScores[1]}
                </Text>
              )}
              {item.homeScores[4] !== -1 && item.awayScores[4] !== -1 ? (
                <Text className="vsscoreht cornerBox textWhite">
                  <i className="iconfont icon-corner"></i>
                  <Text className='textWhite'>
                    {item.homeScores[4]} - {item.awayScores[4]}
                  </Text>
                </Text>
              ) : null}
            </div>
          )}
          <MatchLiveIcon vlive={item.vlive} mlive={item.mlive} />
        </div>
        <OddsTdValue matchId={item.matchId} />
      </div>
    </Link>
  );
}, _.isEqual);

// 首页比赛列表，不包括 赛事头
export const HomeMatchList: React.FC<{ matches: any[], onMatchClick?: (item: any) => void }> = React.memo(({ matches, onMatchClick }) => {
  if (matches.length === 0) {
    return <Empty />;
  }

  return (
    <>
      {matches.map((item: any) => (
        <MatchItem key={item.matchId} item={item} onMatchClick={onMatchClick} />
      ))}
    </>
  );
}, _.isEqual);

interface GameCategoryProps {
  competition: any;
}

const GameCategory: React.FC<GameCategoryProps> = React.memo(({ competition }) => {
  const { matches } = competition;
  const { trackCustomActivity } = useRecentActivity();

  const handleMatchClick = useCallback((item: any) => {
    trackCustomActivity({
      id: item.matchId,
      type: 'match',
      name: `${item.homeTeam?.name || ''} vs ${item.awayTeam?.name || ''}`,
      logo: item.homeTeam?.logo,
      sport: competition.sport || '',
      metadata: {
        competitionId: competition.competitionId,
        matchTime: item.matchTime
      }
    });
  }, [trackCustomActivity, competition]);

  if (matches.length === 0) {
    return null;
  }

  return (
    <>
      <GameCompetitionTab competition={competition} />     
      <HomeMatchList matches={matches} onMatchClick={handleMatchClick} />
    </>
  );
});

export default GameCategory;
