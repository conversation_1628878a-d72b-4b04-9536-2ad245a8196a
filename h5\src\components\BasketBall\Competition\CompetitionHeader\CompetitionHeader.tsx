import FavoriteIcon from "@/components/Common/FavoriteIcon";
import { DownOutlined } from "@ant-design/icons";
import { Button, Space, Picker } from "antd-mobile";
import { PickerValue } from "antd-mobile/es/components/picker-view";
import { CategoryIcon, FallbackImage, FallbackCategoryImage } from "iscommon/const/icon";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { inject, observer } from "mobx-react";
import { useMemo, useCallback, useState } from "react";
import './CompetitionHeader.less'

interface Props {
  showSeason?: boolean;
  requestHeader: (id: string) => Promise<any>;
  store?: any;
}

const CompetitionHeader: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: {
        Basketball: { Competitions },
      },
      showSeason = true,
      requestHeader,
    } = props;
    const {
      commonInfo: { competition, mostMarketValuePlayer },
      seasons = [],
      currentSeason,
    } = Competitions;

    const labelMaps = useTranslateKeysToMaps(['TitleHolder', 'MostValuablePlayer']);

    const [visible, setVisible] = useState(false);
    const [selectedSeason, setSelectedSeason] = useState<PickerValue>(currentSeason?.id); 
    
    const countryInfo = useMemo(() => {
      if (competition?.type === 2) {
        const { logo, name } = competition.categoryDto;
        return {
          ...competition.categoryDto,
          logo: (CategoryIcon as any)[logo || ''] || logo || FallbackImage,
        };
      }
      return competition.country;
    }, [competition.categoryDto, competition.country, competition?.type]);

    const onSelect = useCallback(
      (id: string) => {
        const currentSeason = seasons.find((item: any) => item.id === id);
        Competitions.changeCurrentSeason(currentSeason);
        if (currentSeason?.id) {
          requestHeader(currentSeason?.id);
        }
      },
      [Competitions, requestHeader, seasons],
    );

    const handleConfirm = (value: PickerValue[]) => {
      const selectedId = value[0];
      if (typeof selectedId === 'string') { 
        onSelect(selectedId);
        setSelectedSeason(selectedId); 
      }
      setVisible(false); 
    };

    console.log('test123', showSeason)
    console.log('test234', seasons)
    console.log('test345', JSON.stringify(seasons))
    console.log('competition', JSON.stringify(competition))

    return (
      <div className='basketball-competition-header-container'>
        <div className='competition-container'>
          <img className='competition-icon' src={competition?.logo || FallbackCategoryImage} alt={competition?.name} loading="lazy"/>
          <div className='competition-info'>
            <div className='competition-name-container'>
              <span className='competition-name'>{competition?.name}</span>
              {/* <FavoriteIcon isMatch={false} competition={{ ...competition, curStageId: currentSeason.id }}/> */}
            </div>
            <div className='competition-detail'>
              <img className='country-icon' src={countryInfo?.logo || FallbackCategoryImage} alt={countryInfo?.name} loading="lazy"/>
              <span className='country-name'>{countryInfo?.name}</span>
            </div>
          </div>
        </div>
        {showSeason && !!seasons.length && currentSeason && (
          <>
            <Button className='competition-header-btn' size="small" onClick={() => setVisible(true)}>
              <Space>
                {currentSeason.year && (() => {
                  const [startYear, endYear] = currentSeason.year.split('-').map((year: any) => year.slice(-2));
                  return `${startYear}-${endYear}`;
                })()}
                <DownOutlined />
              </Space>
            </Button>

            <Picker
              columns={[
                seasons.map((item: any) => ({
                  label: item.year,
                  value: item.id,
                })),
              ]}
              visible={visible}
              onClose={() => setVisible(false)} 
              onConfirm={handleConfirm}
            />
          </>
        )}
      </div>
    );
  }),
);

export default CompetitionHeader;