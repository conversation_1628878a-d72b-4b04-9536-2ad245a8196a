import { inject, observer } from "mobx-react";
import SquadCard from "../SquadTab/SquadCard";
import MatchCard from "../MatchTab/MatchCard";
import TeamInfoCard from "./TeamInfoCard";
import TopPlayerCard from "./TopPlayerCard";
import SeasonStatCard from "./SeasonStatCard";

const OverView = inject('store')(
  observer((props: any) => {

    return (
      <>
        <SeasonStatCard />
        <MatchCard />
        <TopPlayerCard />
        <SquadCard />
        <TeamInfoCard />
      </>
    );
  })
);

export default OverView;