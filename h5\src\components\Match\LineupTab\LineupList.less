// .container {
//   display: flex;
//   flex-direction: row;
//   width: 100%;
//   background-color: white;
// }

// .w_50 {
//   width: 50%;
// }

// .lineup_list_item {
//   display: flex;
//   align-items: center;
//   flex-direction: row;
//   height: 100px;
// }

// .lineup_list_item:not(:first-child) {
//   padding: 0 12px;
// }
// .line_item_left{
//   flex: 1;
//   display: flex;
//   overflow: hidden;
//   height: 100%;
//   align-items: center;
// }

// .lineup_list_item:not(:last-child) {
//   border-bottom: 1px solid #eee;
// }

// .border_right_eee {
//   border-right: 1px solid #eee;
// }

// .lineup_list_column {
//   display: flex;
//   flex-direction: column;
//   border-right: 1px solid #eee;
//   width: 50%;
// }

// .shirtNumber {
//   color: #999;
//   width: 32px;
//   text-align: center;
// }

// .player_avatar_box {
//   position: relative;
//   margin: 0 8px;
//   width: 60px;
// }

// .player_rating {
//   height: 30px;
//   border-radius: 8px;
//   position: absolute;
//   top: -10px;
//   right: -30px;
//   color: #fff;
//   background-color: #5cb400;
//   transform: scale(0.8);
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   padding: 0 5px;
// }

// .player_name_box {
//   display: flex;
//   flex-direction: column;
// }

// .player_name {
//   max-width: 180px;
//   overflow: hidden;
//   text-overflow: ellipsis;
//   white-space: nowrap;
//   color: #333 !important;
// }
// .desc {
//   overflow: hidden;
//   text-overflow: ellipsis;
//   white-space: nowrap;
//   max-width: 180px;
//   color: #999;
// }

// .event_box {
//   // width: 68px;
//   // flex: 1;
//   // display: flex;
//   // justify-content: flex-end;
//   // flex-wrap: wrap;
//   margin-left: 10px;
//   display: flex;
//   flex-direction: row-reverse;
//   justify-content: flex-end;
// }

// .event {
//   display: inline-flex;
//   vertical-align: middle;
//   float: right;
//   justify-content: center;
//   align-items: center;
// }

// .fs_12 {
//   font-size: 24px;
// }

.line-up-list-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;

  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .container-body {
    background: #1e1e1e;

    .team-container-body {
      display: flex;
      flex-direction: row;
      justify-content: space-between;

      .home-body-left-aligned {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        width: 50%;
      }
  
      .away-body-right-aligned {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 50%;
      }
  
      .home-body-left-aligned,
      .away-body-right-aligned {
        .team-logo {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin: 5px 10px;
          overflow: hidden;
          background-size: cover;
        }
  
        .team-name {
          font-size: 20px;
          color: #fff;
        }
      }
    }

    .player-line-up-container {
      display: flex;
      justify-content: space-between;

      .container-column {
        width: 100%;

        &:first-child {
          border-right: 1px solid #C0C5C9;
        }

        .coach-container {
          display: flex;
          flex-direction: column;
          text-align: center;
          margin-bottom: 15px;
          color: #fff;
          
          .coach-label {
            font-size: 20px;
            font-weight: bold;
          }
        }
        
        .player-container {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;
          padding: 5px 0px;

          .player-left-aligned-container {
            display: flex;
            flex-direction: row;
            align-items: center;

            .player-shirt-container {
              width: 25px;
              margin: 0px 10px;
  
              .player-shirt-number {
                font-size: 20px;
                color: #fff;
              }
            }
  
            .player-icon {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 40px;
              height: 40px;
              border-radius: 50%;
              margin: 5px 10px;
              overflow: hidden;
              background-size: cover;
            }
  
            .player-info-container {
              display: flex;
              flex-direction: column;
  
              .player-name {
                font-size: 20px;
                color: #fff;
              }
  
              .player-position {
                font-size: 16px;
                color: #fff;
              }
            }

            .player-incident-container {
              margin-left: 10px;
              display: flex;
              flex-direction: row-reverse;
              justify-content: flex-end;

              .player-incident {
                display: inline-flex;
                vertical-align: middle;
                float: right;
                justify-content: center;
                align-items: center;

                .incident-icon {
                  margin: 0px 5px;
                }

                .incident-time {
                  color: #fff;
                  font-size: 16px;
                }
              }
            }
          }

          .player-rating-text-size {
            margin: 0px 10px;

            .adm-badge-content {
              font-size: 20px;
            }
          }
        }
      }
    }
  }
}