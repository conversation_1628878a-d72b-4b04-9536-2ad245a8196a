import MatchLiveIcon from '@/components/MatchLiveIcon';
import { inject, observer } from 'mobx-react';
import { ReactNode, useMemo } from 'react';

import { HomeGameTab } from 'iscommon/const/constant';
import { CategoryIcon, FallbackImage } from 'iscommon/const/icon';
import { momentTimeZone } from 'iscommon/utils';

import { MatchLink } from '@/components/Common/Football/Link';
import './GameCategory.less';

export const MatchList = ({
  matches,
  renderMatchStatusText = () => null,
  renderTeamNameExra = () => null,
  renderScoreOrOdds = () => null,
}: any) => {
  return (
    <div className="homeGameList">
      {matches.map((item: any) => {
        // if (!item?.homeTeam) return null;
        return (
          <MatchLink className="listItem" key={item.matchId} matchId={item.matchId}>
            <div className="listLeft">
              <span className="time fs-10-center">{momentTimeZone(item.matchTime, 'HH:mm')}</span>
              {renderMatchStatusText(item)}
            </div>
            <div className="listContent">
              <div className="vsCountry">
                <div className="vsCountryItem">
                  <div className="squareLogo">
                    <img src={item.homeTeam.logo || FallbackImage} alt="" />
                  </div>
                  <span className="teamName text-overflow">
                    {item.homeTeam.name}
                    {renderTeamNameExra(true, item)}
                  </span>
                </div>
                <div className="vsCountryItem">
                  <div className="squareLogo">
                    <img src={item.awayTeam.logo || FallbackImage} alt="" />
                  </div>
                  <span className="teamName text-overflow">
                    {item.awayTeam.name}
                    {renderTeamNameExra(false, item)}
                  </span>
                </div>
              </div>
              <MatchLiveIcon vlive={item.vlive} mlive={item.mlive} />
            </div>
            {renderScoreOrOdds(item)}
          </MatchLink>
        );
      })}
    </div>
  );
};

const H5StickIcon = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Smallball: { SmallballCommon: WebHome },
      },
      competitionId,
    } = props;
    const { h5StickIdList = [], currentGameTab } = WebHome;
    const isStick = useMemo(() => h5StickIdList?.includes(competitionId), [h5StickIdList, competitionId]);

    if (currentGameTab !== HomeGameTab.All) {
      return null;
    }

    if (isStick) {
      return (
        <span
          onClick={(e) => {
            WebHome.removeH5Stick(competitionId);
            e.preventDefault();
            e.stopPropagation();
          }}
          className="icon iconfont iconSign_Sele myIcon touchArea"
          style={{ color: '#0f80da' }}
        />
      );
    }
    return (
      <span
        onClick={(e) => {
          WebHome.addH5Stick(competitionId);
          e.preventDefault();
          e.stopPropagation();
        }}
        className="icon iconfont iconSign_Def touchArea"
        style={{ color: 'rgb(131, 131, 131)' }}
      />
    );
  }),
);

interface Props {
  competition: any;

  renderMatchStatusText(item: any): ReactNode;

  renderScoreOrOdds(item: any): ReactNode;

  renderTeamNameExra?(isHome: boolean, item: any): ReactNode;
}

const SmallballCommonGameCategory = (props: Props) => {
  const { renderMatchStatusText, renderScoreOrOdds, renderTeamNameExra } = props;
  const { competitionName, country, category, onlineCount, matches, competitionId } = props.competition;

  if (matches.length === 0) return null;

  return (
    <>
      <div className="homeGameCategory">
        <div className="cnameContainer">
          <i
            style={{
              backgroundImage: `url(${
                (CategoryIcon as any)[category?.logo] || country?.logo || category?.logo || FallbackImage
              })`,
            }}
            className="countryLogo"
          />
          <span className="countryName">{country?.name || category?.name}:</span>
          <div className="competitionName" style={{ flex: 1 }}>
            {competitionName}
          </div>
        </div>
        <div className="countContainer">
          {onlineCount > 0 && (
            <div className="count">
              <span className="icon iconfont iconguanzhong" style={{ fontSize: '14px', color: 'rgb(153, 153, 153)' }}></span>
              {onlineCount > 9999 ? '9999+' : onlineCount}
            </div>
          )}
          <H5StickIcon competitionId={competitionId} />
        </div>
      </div>
      <MatchList
        matches={matches}
        renderMatchStatusText={renderMatchStatusText}
        renderTeamNameExra={renderTeamNameExra}
        renderScoreOrOdds={renderScoreOrOdds}
      />
    </>
  );
};

export default SmallballCommonGameCategory;
