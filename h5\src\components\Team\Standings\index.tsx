import Standings from '@/components/Competition/Standings/Standings';

import { <PERSON><PERSON>, <PERSON><PERSON>, Picker } from 'antd-mobile';

import { useCallback, useEffect, useMemo, useState } from 'react';

import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { inject, observer } from 'mobx-react';
import { getStandings } from 'iscommon/api/competition';
import Loading from '@/components/Loading';
import { Link } from 'umi';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { FallbackImage } from 'iscommon/const/icon';
import './index.less'

const TeamStandings = inject('store')(
  observer((props: any) => {
    const {
      store: { Team },
      teamId,
    } = props;
    const { teamHeaderInfo } = Team;

    const competitionId = teamHeaderInfo.competition.id
    const seasonId = teamHeaderInfo.currentSeason.id
    const [tablesList, setTablesList] = useState([]);
    const [type, setType] = useState<0 | 1 | 2>(0);
    const [loading, setLoading] = useState(true);

    const labelMaps = useTranslateKeysToMaps([
      'Standings',
      'All',
      'Home',
      'Away',
    ]);

    const options = useMemo(
      () => [labelMaps.All, labelMaps.Home, labelMaps.Away].map((label, value) => ({ label, value })),
      [labelMaps.All, labelMaps.Away, labelMaps.Home],
    );

    const [activeButton, setActiveButton] = useState(options[0].value);

    const onChange = useCallback((value) => {
      setActiveButton(value);
      setType(value);
    }, []);
  
    useEffect(() => {
      if (competitionId && seasonId) {
        getStandings({ competitionId, seasonId, type, needMatchHistory: true }).then(({ tables }: any) => {
          setTablesList(tables || []);
        });
      }
    }, [competitionId, seasonId, type]);


    return (
      <div className='team-standing-tab-container'>
        <div className='container-title'>
          {labelMaps.Standings}
          <div className='container-title-right-aligned'>
            {options.map((option) => (
              <Button
                key={option.value}
                className={`button ${activeButton === option.value ? 'active' : ''}`}
                onClick={() => onChange(option.value)}>
                {option.label}
              </Button>
            ))}
          </div>
        </div>
        <div className='container-body'>
          <div className='custom-standing-table'>
            <div className="header-row">
              <div className="header-cell">#</div>
              <div className="header-cell">Team</div>
              <div className="header-cell">P</div>
              <div className="header-cell">GD</div>
              <div className="header-cell">Pts</div>
            </div>

            {tablesList.length !== 0 ? (
              tablesList.map((item: any, index) => {
                return (
                  item?.rows.map((row: any) => (
                    <Link className="table-row" key={row?.teamId} to={GlobalUtils.getPathname(PageTabs.team, row?.teamId)}>
                      <div className="table-cell">{row?.position}</div>
                      <div className="table-cell team-cell">
                        <img className="team-logo" src={row?.team?.logo || FallbackImage} alt={row?.team?.name} />
                        <span className='team-name'>{row?.team?.name}</span>
                      </div>
                      <div className="table-cell">{row?.total}</div>
                      <div className="table-cell">{row?.goalDiff}</div>
                      <div className="table-cell">{row?.points}</div>
                    </Link>
                  ))
                )
              })
            ) : (
              <Loading loading={loading}/> 
            )}
          </div>
        </div>
      </div>
    );
  }),
);

export default TeamStandings;
