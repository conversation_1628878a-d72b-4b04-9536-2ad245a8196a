import 'iscommon/mqtt';

import { BasketBallInLiveStatusEnum, BasketBallStatusCodeEnum } from 'iscommon/const/basketball/constant';
import { inject, observer } from 'mobx-react';
import { subscribeMatchData, unsubscribeMatchData } from 'iscommon/mqtt/basketball/match';
import { useCallback, useEffect, useMemo } from 'react';

import BoxScoreTab from '@/components/Basketball/Match/BoxScoreTab';
import { getArrayFromString } from 'iscommon/utils';
import { getMatchHeader } from 'iscommon/api/match';
import H2HTab from '@/components/Basketball/Match/H2HTab';
import { isPluginMode } from '@/layouts/plugin';
import MatchHeader from '@/components/Basketball/Match/MatchHeader';
import MatchLive from '@/components/Basketball/Match/Overview';
import MatchOddsTab from '@/components/Basketball/Match/OddsTab';
import { MatchTab } from 'iscommon/mobx/modules/match';
import PageBreadcrumb from '@/components/Common/PageBreadcrumb';
import PageHeader from '@/components/PageHeader';
import { PageTabs } from 'iscommon/const/globalConfig';
import StandingsTab from '@/components/Basketball/Match/StandingsTab';
import styles from './index.less';
import TabList from '@/components/TabList/Index';
import { useCrumbList } from 'iscommon/hooks/content';
import { useMatchTimer } from 'iscommon/hooks/apiData';
import { useMount } from 'ahooks';
import { useParams } from 'umi';

const BasketballMatch = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Basketball: { Match },
        WebConfig,
      },
    } = props;
    const { serverTime, matchHeaderInfo } = Match;
    let { categoryId: matchId, tabId } = useParams<{ categoryId: string; tabId: string }>();

    const pluginMode = isPluginMode();
    console.log('🏀 Basketball Match - Plugin Mode:', pluginMode, 'MatchId:', matchId);
    
    const matchName = useMemo(
      () => `${matchHeaderInfo?.homeTeam?.name} vs ${matchHeaderInfo?.awayTeam?.name}, ${tabId.toLowerCase()}`,
      [matchHeaderInfo?.awayTeam?.name, matchHeaderInfo?.homeTeam?.name, tabId],
    );
    
    const { crumbs } = useCrumbList(PageTabs.match, '', matchName, [], {
      competition: {
        id: matchHeaderInfo?.competition?.id,
        name: matchHeaderInfo?.competition?.name,
      },
    });
    
    const fetchMatchHeader = useCallback(() => {
      if (!matchId) return;

      console.log('🏀 Fetching basketball match header for:', matchId);

      getMatchHeader({ matchId })
        .then((res: any) => {
          if (res) {
            console.log('🏀 Basketball match header received:', res);
            const { name, logo, id } = res.competition || {};

            // Add a small delay for plugin mode to ensure DOM is ready
            const updateMatchInfo = () => {
              try {
                Match.changeMatchHeaderInfo({
                  ...res,
                  matchId,
                  scores: getArrayFromString(res.scores),
                  matchStatus: res.statusId,
                  remainSeconds: res.remainSeconds,
                  competition: { name, logo, id },
                });
                console.log('🏀 Basketball match header updated successfully');
              } catch (error) {
                console.error('🏀 Error updating basketball match header:', error);
              }
            };

            if (pluginMode) {
              // Small delay for plugin mode to ensure proper DOM initialization
              setTimeout(updateMatchInfo, 50);
            } else {
              updateMatchInfo();
            }
          }
        })
        .catch((error) => {
          console.error('🏀 Error fetching basketball match header:', error);
        });
    }, [matchId, pluginMode]);

    useEffect(() => {
      if (matchId) {
        console.log('🏀 Initializing basketball match:', matchId, 'Plugin mode:', pluginMode);

        try {
          Match.changeMatchId(matchId);

          // Add a small delay for plugin mode to ensure proper initialization
          if (pluginMode) {
            setTimeout(() => {
              fetchMatchHeader();
            }, 100);
          } else {
            fetchMatchHeader();
          }
        } catch (error) {
          console.error('🏀 Error initializing basketball match:', error);
        }
      }
    }, [matchId, pluginMode, fetchMatchHeader]);

    useEffect(() => {
      if (matchHeaderInfo.matchStatus !== -1 && BasketBallInLiveStatusEnum.includes(matchHeaderInfo.matchStatus)) {
        subscribeMatchData(matchId);
        return () => unsubscribeMatchData(matchId);
      }
    }, [matchHeaderInfo.matchStatus, matchId]);

    useMatchTimer(serverTime, Match, fetchMatchHeader);

    const oddsTab = { key: MatchTab.odds, label: MatchTab.odds, children: <MatchOddsTab /> };
    
    const tabList: any[] = useMemo(() => {
      const tabs = [
        { key: MatchTab.live, label: BasketBallInLiveStatusEnum.includes(matchHeaderInfo.statusId) ? MatchTab.live : MatchTab.overview, children: <MatchLive /> },
        { key: MatchTab.h2h, label: MatchTab.h2h, children: <H2HTab /> },
        null,
        { key: MatchTab.boxScore, label: MatchTab.boxScore, children: <BoxScoreTab /> },
        { key: MatchTab.standings, label: MatchTab.standings, children: <StandingsTab /> },
      ];
      if (WebConfig.showOdds) tabs.splice(2, 0, oddsTab);
      return tabs.filter(Boolean);
    }, [WebConfig.showOdds, matchHeaderInfo.statusId]);

    return (
      <div className={styles.container}>
        <PageBreadcrumb crumbs={crumbs} />
        <PageHeader>
          <MatchHeader />
        </PageHeader>
        <TabList key={matchId} datasource={tabList} centered defaultTabId={MatchTab.live} />
      </div>
    );
  }),
);

export default BasketballMatch;
