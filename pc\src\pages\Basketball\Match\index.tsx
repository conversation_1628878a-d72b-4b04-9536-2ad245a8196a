import 'iscommon/mqtt';

import { BasketBallInLiveStatusEnum, BasketBallStatusCodeEnum } from 'iscommon/const/basketball/constant';
import { inject, observer } from 'mobx-react';
import { subscribeMatchData, unsubscribeMatchData } from 'iscommon/mqtt/basketball/match';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import BoxScoreTab from '@/components/Basketball/Match/BoxScoreTab';
import { getArrayFromString } from 'iscommon/utils';
import { getMatchHeader } from 'iscommon/api/match';
import H2HTab from '@/components/Basketball/Match/H2HTab';
import { isPluginMode } from '@/layouts/plugin';
import MatchHeader from '@/components/Basketball/Match/MatchHeader';
import MatchLive from '@/components/Basketball/Match/Overview';
import MatchOddsTab from '@/components/Basketball/Match/OddsTab';
import { MatchTab } from 'iscommon/mobx/modules/match';
import PageBreadcrumb from '@/components/Common/PageBreadcrumb';
import PageHeader from '@/components/PageHeader';
import { PageTabs } from 'iscommon/const/globalConfig';
import StandingsTab from '@/components/Basketball/Match/StandingsTab';
import styles from './index.less';
import TabList from '@/components/TabList/Index';
import { useCrumbList } from 'iscommon/hooks/content';
import { useMatchTimer } from 'iscommon/hooks/apiData';
import { useMount } from 'ahooks';
import { useParams } from 'umi';

const BasketballMatch = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Basketball: { Match },
        WebConfig,
      },
    } = props;
    const { serverTime, matchHeaderInfo } = Match;
    let { categoryId: matchId, tabId } = useParams<{ categoryId: string; tabId: string }>();

    const pluginMode = isPluginMode();
    const [isInitialized, setIsInitialized] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const mountedRef = useRef(false);

    console.log('🏀 Basketball Match - Plugin Mode:', pluginMode, 'MatchId:', matchId);

    // Track component mount state
    useEffect(() => {
      mountedRef.current = true;
      setIsLoading(false);

      return () => {
        mountedRef.current = false;
      };
    }, []);

    const matchName = useMemo(
      () => `${matchHeaderInfo?.homeTeam?.name} vs ${matchHeaderInfo?.awayTeam?.name}, ${tabId.toLowerCase()}`,
      [matchHeaderInfo?.awayTeam?.name, matchHeaderInfo?.homeTeam?.name, tabId],
    );
    
    const { crumbs } = useCrumbList(PageTabs.match, '', matchName, [], {
      competition: {
        id: matchHeaderInfo?.competition?.id,
        name: matchHeaderInfo?.competition?.name,
      },
    });
    
    const fetchMatchHeader = useCallback(() => {
      if (!matchId || !mountedRef.current) return;

      console.log('🏀 Fetching basketball match header for:', matchId);

      getMatchHeader({ matchId })
        .then((res: any) => {
          // Double-check component is still mounted before updating
          if (!mountedRef.current || !res) return;

          console.log('🏀 Basketball match header received:', res);
          const { name, logo, id } = res.competition || {};

          // Safe update function with multiple safety checks
          const updateMatchInfo = () => {
            // Triple-check before DOM manipulation
            if (!mountedRef.current) {
              console.log('🏀 Component unmounted, skipping update');
              return;
            }

            try {
              // Use requestAnimationFrame to ensure DOM is ready
              requestAnimationFrame(() => {
                if (!mountedRef.current) return;

                Match.changeMatchHeaderInfo({
                  ...res,
                  matchId,
                  scores: getArrayFromString(res.scores),
                  matchStatus: res.statusId,
                  remainSeconds: res.remainSeconds,
                  competition: { name, logo, id },
                });

                setIsInitialized(true);
                console.log('🏀 Basketball match header updated successfully');
              });
            } catch (error) {
              console.error('🏀 Error updating basketball match header:', error);
            }
          };

          if (pluginMode) {
            // Longer delay for plugin mode with additional safety
            setTimeout(() => {
              if (mountedRef.current) {
                updateMatchInfo();
              }
            }, 150);
          } else {
            updateMatchInfo();
          }
        })
        .catch((error) => {
          console.error('🏀 Error fetching basketball match header:', error);
        });
    }, [matchId, pluginMode]);

    useEffect(() => {
      if (matchId && mountedRef.current) {
        console.log('🏀 Initializing basketball match:', matchId, 'Plugin mode:', pluginMode);

        try {
          Match.changeMatchId(matchId);

          // Add a delay for plugin mode to ensure proper initialization
          if (pluginMode) {
            setTimeout(() => {
              if (mountedRef.current) {
                fetchMatchHeader();
              }
            }, 200);
          } else {
            // Even for non-plugin mode, add a small delay to ensure DOM is ready
            setTimeout(() => {
              if (mountedRef.current) {
                fetchMatchHeader();
              }
            }, 50);
          }
        } catch (error) {
          console.error('🏀 Error initializing basketball match:', error);
        }
      }
    }, [matchId, pluginMode, fetchMatchHeader]);

    useEffect(() => {
      if (matchHeaderInfo.matchStatus !== -1 && BasketBallInLiveStatusEnum.includes(matchHeaderInfo.matchStatus)) {
        subscribeMatchData(matchId);
        return () => unsubscribeMatchData(matchId);
      }
    }, [matchHeaderInfo.matchStatus, matchId]);

    useMatchTimer(serverTime, Match, fetchMatchHeader);

    const oddsTab = { key: MatchTab.odds, label: MatchTab.odds, children: <MatchOddsTab /> };
    
    const tabList: any[] = useMemo(() => {
      const tabs = [
        { key: MatchTab.live, label: BasketBallInLiveStatusEnum.includes(matchHeaderInfo.statusId) ? MatchTab.live : MatchTab.overview, children: <MatchLive /> },
        { key: MatchTab.h2h, label: MatchTab.h2h, children: <H2HTab /> },
        null,
        { key: MatchTab.boxScore, label: MatchTab.boxScore, children: <BoxScoreTab /> },
        { key: MatchTab.standings, label: MatchTab.standings, children: <StandingsTab /> },
      ];
      if (WebConfig.showOdds) tabs.splice(2, 0, oddsTab);
      return tabs.filter(Boolean);
    }, [WebConfig.showOdds, matchHeaderInfo.statusId]);

    // Prevent rendering until component is properly initialized
    if (!matchId || isLoading) {
      return (
        <div className={styles.container}>
          <div style={{ padding: '20px', textAlign: 'center' }}>
            Loading basketball match...
          </div>
        </div>
      );
    }

    return (
      <div className={styles.container}>
        <PageBreadcrumb crumbs={crumbs} />
        <PageHeader>
          <MatchHeader />
        </PageHeader>
        <TabList key={`basketball-${matchId}-${isInitialized}`} datasource={tabList} centered defaultTabId={MatchTab.live} />
      </div>
    );
  }),
);

export default BasketballMatch;
