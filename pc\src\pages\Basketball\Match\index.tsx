import 'iscommon/mqtt';

import { BasketBallInLiveStatusEnum, BasketBallStatusCodeEnum } from 'iscommon/const/basketball/constant';
import { inject, observer } from 'mobx-react';
import { subscribeMatchData, unsubscribeMatchData } from 'iscommon/mqtt/basketball/match';
import { useCallback, useEffect, useMemo } from 'react';

import BoxScoreTab from '@/components/Basketball/Match/BoxScoreTab';
import { getArrayFromString } from 'iscommon/utils';
import { getMatchHeader } from 'iscommon/api/match';
import H2HTab from '@/components/Basketball/Match/H2HTab';
import MatchHeader from '@/components/Basketball/Match/MatchHeader';
import MatchLive from '@/components/Basketball/Match/Overview';
import MatchOddsTab from '@/components/Basketball/Match/OddsTab';
import { MatchTab } from 'iscommon/mobx/modules/match';
import PageBreadcrumb from '@/components/Common/PageBreadcrumb';
import PageHeader from '@/components/PageHeader';
import { PageTabs } from 'iscommon/const/globalConfig';
import StandingsTab from '@/components/Basketball/Match/StandingsTab';
import styles from './index.less';
import TabList from '@/components/TabList/Index';
import { useCrumbList } from 'iscommon/hooks/content';
import { useMatchTimer } from 'iscommon/hooks/apiData';
import { useMount } from 'ahooks';
import { useParams } from 'umi';

const BasketballMatch = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Basketball: { Match },
        WebConfig,
      },
    } = props;
    const { serverTime, matchHeaderInfo } = Match;
    let { categoryId: matchId, tabId } = useParams<{ categoryId: string; tabId: string }>();

    console.log('🏀 Basketball Match - MatchId:', matchId);

    const matchName = useMemo(
      () => `${matchHeaderInfo?.homeTeam?.name} vs ${matchHeaderInfo?.awayTeam?.name}, ${tabId.toLowerCase()}`,
      [matchHeaderInfo?.awayTeam?.name, matchHeaderInfo?.homeTeam?.name, tabId],
    );
    
    const { crumbs } = useCrumbList(PageTabs.match, '', matchName, [], {
      competition: {
        id: matchHeaderInfo?.competition?.id,
        name: matchHeaderInfo?.competition?.name,
      },
    });
    
    const fetchMatchHeader = useCallback(() => {
      getMatchHeader({ matchId }).then((res: any) => {
        if (res) {
          const { name, logo, id } = res.competition || {};
          Match.changeMatchHeaderInfo({
            ...res,
            matchId,
            scores: getArrayFromString(res.scores),
            matchStatus: res.statusId,
            remainSeconds: res.remainSeconds,
            competition: { name, logo, id },
          });
        }
      });
    }, [matchId]);

    useMatchTimer(serverTime, Match, fetchMatchHeader);

    useMount(() => {
      Match.changeMatchId(matchId);
      fetchMatchHeader();
    });

    useEffect(() => {
      if (matchHeaderInfo.matchStatus !== -1 && BasketBallInLiveStatusEnum.includes(matchHeaderInfo.matchStatus)) {
        subscribeMatchData(matchId);
        return () => unsubscribeMatchData(matchId);
      }
    }, [matchHeaderInfo.matchStatus, matchId]);

    useMatchTimer(serverTime, Match, fetchMatchHeader);

    const oddsTab = { key: MatchTab.odds, label: MatchTab.odds, children: <MatchOddsTab /> };
    
    const tabList: any[] = useMemo(() => {
      const tabs = [
        { key: MatchTab.live, label: BasketBallInLiveStatusEnum.includes(matchHeaderInfo.statusId) ? MatchTab.live : MatchTab.overview, children: <MatchLive /> },
        { key: MatchTab.h2h, label: MatchTab.h2h, children: <H2HTab /> },
        null,
        { key: MatchTab.boxScore, label: MatchTab.boxScore, children: <BoxScoreTab /> },
        { key: MatchTab.standings, label: MatchTab.standings, children: <StandingsTab /> },
      ];
      if (WebConfig.showOdds) tabs.splice(2, 0, oddsTab);
      return tabs.filter(Boolean);
    }, [WebConfig.showOdds, matchHeaderInfo.statusId]);

    return (
      <div className={styles.container}>
        <PageBreadcrumb crumbs={crumbs} />
        <PageHeader>
          <MatchHeader />
        </PageHeader>
        <TabList key={matchId} datasource={tabList} centered defaultTabId={MatchTab.live} />
      </div>
    );
  }),
);

export default BasketballMatch;
