// .gameCategory {
//   width: 100%;
//   height: 54px;
//   background: #000;
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
//   font-size: 24px;
//   box-sizing: border-box;
//   padding: 0 12px;
//   color: #666;
//   cursor: pointer;

//   > div {
//     display: flex;
//     height: 100%;
//     align-items: center;
//   }

//   .cnameContainer {
//     flex: 3;
//     overflow: hidden;
//   }

//   .countContainer {
//     flex: 1;
//     justify-content: space-between;

//     .count {
//       width: 100px;
//     }
//   }

//   .squareLogo {
//     width: 28px;
//     height: 28px;
//     background-size: contain;
//     margin: 0 3px;
//   }

//   .countryName {
//     color: #999999;
//   }
// }

// .gameList {
//   border-top: 2px solid #e3e3e3;
//   border-bottom: 2px solid #e3e3e3;
//   margin-top: -2px;
//   background-color: #fff;

//   .addGroup {
//     height: 47px;
//     display: flex;
//     align-items: center;
//     font-size: 26px;
//     font-weight: 500;
//     color: #333333;

//     .van-image__img {
//       width: 30px;
//       height: 30px;
//       margin: 0 10px 0 15px;
//     }
//   }

//   .listItem {
//     display: flex;
//     height: 121px;
//     border-top: 2px solid #e3e3e3;

//     div {
//       align-items: center;
//       justify-content: center;
//     }

//     .listLeft {
//       width: 118px;
//       display: flex;
//       flex-direction: column;
//       color: #999999;
//       font-size: 18px;

//       .date {
//         font-size: 24px;
//         color: #999;
//         transform: scale(0.84);
//         transform-origin: center;
//       }
//     }

//     .listRight {
//       width: 105px;
//       display: flex;
//       flex-direction: column;
//       font-size: 24px;
//       font-weight: bolder;

//       .score {
//         color: #999999;
//       }
//     }

//     .listContent {
//       flex: 1;
//       border-left: 1px solid #e3e3e3;
//       border-right: 1px solid #e3e3e3;
//       display: flex;
//       flex-direction: row;
//       justify-content: space-between;
//       align-items: center;
//       padding: 0 14px;

//       .vsCountry {
//         display: flex;
//         flex-direction: column;
//         justify-content: space-around;
//         align-items: flex-start;
//         flex: 1;

//         .vsCountryItem {
//           display: flex;
//           overflow: hidden;
//           height: 40%;

//           .teamName {
//             font-size: 26px;

//             font-weight: 500;
//             color: #333333;
//             margin-right: 6px;
//           }

//           .squareLogo {
//             width: 32px;
//             height: 32px;
//             background-size: contain;
//             margin-right: 14px;
//           }

//           .card {
//             padding: 0 4px;
//             border-radius: 2px;
//             margin-left: 6px;
//             font-size: 16px;
//             color: #fff;
//             transform: scale(0.8);
//             display: block;

//             &.red {
//               background-color: #c1272d;
//             }

//             &.yellow {
//               background-color: #ffa830;
//             }
//           }
//         }
//       }
//     }
//   }

//   :first-child {
//     border-top: none;
//   }

//   .ing {
//     color: #c1272d;
//   }
// }

.competition-schedule-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;

    .secondary-container-title {
      display: flex;
      flex-direction: row;
      align-items: center;

      .primary-competition-round-btn,
      .competition-round-btn {
        display: block;
        background: transparent;
        color: #fff;
        border-radius: 32px;
        width: 250px;
  
        .btn-content {
          display: flex; 
          justify-content: space-between;
          align-items: center;
          width: 100%;
  
          .btn-value {
            white-space: nowrap;   
            overflow: hidden;      
            text-overflow: ellipsis;
            flex-grow: 1;
            margin-right: 8px;
          }
  
          .down-icon {
            flex-shrink: 0;  
            margin-left: auto;
          }
        }
      }

      .competition-round-btn {
        margin: 0px 10px;
      }
    }
  }
  
  .container-body {
    background: #1e1e1e;
    padding: 10px;

    .date-group {
      margin-bottom: 20px;

      .date-header {
        font-size: 28px;
        font-weight: 600;
        color: #fff;
        margin: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .match-list-container {
        width: 100%;
        height: fit-content;
        background: #121212;
        border-radius: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        padding: 0 16px;
        cursor: pointer;
        margin-bottom: 16px;
    
        .match-time-container {
          display: flex;
          flex-direction: row;
          align-items: center;
          width: 20%;
          padding: 0px 10px;
    
          .match-time {
            font-size: 24px;
            font-weight: bold;
            color: #fff;
          }
        }
    
        .match-team-container {
          display: flex;
          flex-direction: column;
          width: 70%;
    
          .match-team-info {
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 10px 0px;
    
            .team-logo {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 40px;
              height: 40px;
              border-radius: 50%;
              margin-right: 10px;
              overflow: hidden;
              background-size: cover;
            }
    
            .team-name {
              font-size: 24px;
              font-weight: bold;
              color: #fff;
              text-overflow: ellipsis;
            }
          }
        }

        .match-score-container {
          display: flex;
          flex-direction: column;
          margin-right: 10px;
          color: #fff;
    
          .match-score {
            font-size: 20px;
            font-weight: bold;
            color: #fff;
            padding: 10px 0px;
          }
        }
      }
    }
  }
}

