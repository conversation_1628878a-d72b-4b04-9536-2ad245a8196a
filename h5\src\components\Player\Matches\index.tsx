import { inject, observer } from 'mobx-react';
import { useCallback, useEffect, useRef, useState } from 'react';

import { getPlayerMatches } from 'iscommon/api/player';

import Loading from '@/components/Loading';

import GameCategory from './GameCategory';

import './GameCategory.less'
import { translate, useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { Button, CalendarPicker, DatePicker } from 'antd-mobile';
import { DownOutline } from 'antd-mobile-icons'
import moment, { Moment } from 'moment';
import { momentTimeZone } from 'iscommon/utils';

const Matches = inject('store')(
  observer((props: any) => {
    const {
      store: { Player },
    } = props;

    const { playerId } = Player;

    const [category, setCategory] = useState({});
    const [loading, setLoading] = useState(false);
    const [selectedDate, setSelectedDate] = useState<Date>(new Date());
    const [calendarVisible, setCalendarVisible] = useState(false);
    const [filteredCategory, setFilteredCategory] = useState({});

    const matchesLabel = translate('Matches');

    const formatDate = (date: any) => {
      return date ? moment(date).format('MMM YY') : moment().format('MMM YY');
    };

    const filterMatchesForSelectedDate = (date: Date) => {
      const selectedMoment = moment(date);
      const selectedMonth = selectedMoment.month();
      const selectedYear = selectedMoment.year(); 

      const newFilteredCategory = {};
      Object.keys(category).forEach((competitionId) => {
        newFilteredCategory[competitionId] = category[competitionId].filter((match: any) => {
          const matchMoment = moment.unix(match.matchTime);
          return matchMoment.month() === selectedMonth && matchMoment.year() === selectedYear;
        });
      });

      setFilteredCategory(newFilteredCategory);
    };
    
    useEffect(() => {
      if (playerId) {
        setLoading(true);
        getPlayerMatches({ playerId }).then((d: any = {}) => {
          const { matchList = [] } = d;
          let ret: any = {};
          matchList.forEach((item: any) => {
            if (ret[item.competitionId]) {
              ret[item.competitionId].push(item);
            } else {
              ret[item.competitionId] = [item];
            }
          });
          setCategory(ret);
          setLoading(false);
        });
      }
    }, [playerId]);

    useEffect(() => {
      if (Object.keys(category).length > 0) {
        filterMatchesForSelectedDate(selectedDate);
      }
    }, [category, selectedDate]);

    const onDateChange = (date: any) => {
      setSelectedDate(date);
      setCalendarVisible(false);
      
      if (date) {
        filterMatchesForSelectedDate(date);
      } else {
        setFilteredCategory(category); 
      }
    };

    console.log('filteredcategory', filteredCategory)
    console.log('selecteddate123', selectedDate)

    return (
      <div className='player-game-category-container'>
        <div className='container-title'>
          {matchesLabel}
          <>
            <Button className='player-month-btn' size="small" onClick={() => setCalendarVisible(true)}>
              <span className='btn-content'>
                <span className='btn-value'>{formatDate(selectedDate)}</span>
                <DownOutline className='down-icon'/>
              </span>
            </Button>
            <DatePicker
              className='game-category-calendar-picker'
              visible={calendarVisible}
              precision='month'
              onConfirm={onDateChange}
              onClose={() => setCalendarVisible(false)}
            />
          </>
        </div>
        <div className='container-body'>
          <Loading loading={loading} isEmpty={!Object.keys(filteredCategory).length}>
            {Object.keys(filteredCategory).map((item, index) => {
              return <GameCategory category={(filteredCategory as any)[item]} key={index} />;
            })}
          </Loading>
        </div>
      </div>
      // <Loading loading={loading} isEmpty={!Object.keys(category).length}>
      //   {Object.keys(category).map((item, index) => {
      //     return <GameCategory category={(category as any)[item]} key={index} />;
      //   })}
      // </Loading>
    );
  }),
);

export default Matches;
