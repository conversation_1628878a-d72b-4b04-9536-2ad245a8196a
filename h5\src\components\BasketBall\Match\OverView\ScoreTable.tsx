import './ScoreTable.less'

import { inject, observer } from 'mobx-react';

import { BasketBallInLiveStatusEnum } from 'iscommon/const/basketball/constant';
import { FallbackPlayerImage } from 'iscommon/const/icon';
import { getBasketBallScoreList } from 'iscommon/utils/dataUtils';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

const ScoreTable = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Basketball: {
          Match: { matchHeaderInfo },
        },
      },
    } = props;
    const labelMaps = useTranslateKeysToMaps(['Total', 'Score']);

    const { matchStatus, homeScores, awayScores, homeTeam, awayTeam } = matchHeaderInfo;
    if (matchStatus === -1) return null;

    const list = getBasketBallScoreList(matchStatus, homeScores, awayScores, 5);
    if (list.length === 0) return null;

    const hasOverTime = list[5] && list[5].h > 0 && list[5].w > 0;
    const nextList = list.concat(new Array(6 - list.length).fill(false));
    const scoreList = nextList.slice(1, hasOverTime ? 6 : 5).concat([nextList[0]]);

    const renderScoreRow = (type = 1) => {
      const key = type === 1 ? 'h' : 'w';
      const info = type === 1 ? homeTeam : awayTeam;

      const hasOverTime = scoreList[5] && scoreList[5].h > 0 && scoreList[5].w > 0;
      const finalIndex = hasOverTime ? 5 : 4;

      return (
        <div className="custom-score-table-body">
          <div className="score-cell team-cell">
            <img className="team-icon" src={info.logo || FallbackPlayerImage} alt={info.name} loading="lazy"/>
            <span className="team-name">{info.name}</span>
          </div>

          {scoreList.map((item, index) => {
            console.log('score list', scoreList)
            const homeScore = item ? item.h : '-';
            const awayScore = item ? item.w : '-'; 
            const currentScore = key === 'h' ? homeScore : awayScore; 
            const opponentScore = key === 'h' ? awayScore : homeScore;

            const isFinal = index === finalIndex;
            const isScoreComplete = typeof currentScore === 'number' && typeof opponentScore === 'number';

            let colorClass = '';
            if (item.isRed) {
              colorClass = 'color-c1272d'; // All scores red during in-progress match
            } else if (isScoreComplete) {
              if (currentScore > opponentScore) {
                colorClass = 'color-white';
              } else if (currentScore < opponentScore) {
                colorClass = 'color-dim';
              }
            }

            const cellClassName = isFinal ? 'score-cell total-cell' : 'score-cell';

            return (
              <div key={index} className={cellClassName}>
                <span className={colorClass}>{currentScore}</span>
              </div>
            );
          })}
        </div>
      );
    };

    return (
      <div className='basketball-overview-score-table-container'>
        <div className='container-title'>{labelMaps.Score}</div>
        <div className='container-body'>
          <div className="custom-score-table">
            <div className="custom-score-table-header">
              <div className='header-cell team-cell'>Team</div>
              <div className="header-cell">Q1</div>
              <div className="header-cell">Q2</div>
              <div className="header-cell">Q3</div>
              <div className="header-cell">Q4</div>
              {hasOverTime && <div className="header-cell">OT</div>}
              <div className="header-cell total-cell">{labelMaps.Total}</div>
            </div>
            {renderScoreRow(1)}
            {renderScoreRow(2)}
          </div>
        </div>
      </div>
    );
  }),
);

export default ScoreTable;
