import Loading from '@/components/Loading';
import { formatMatchDetailOdds } from 'iscommon/utils/oddData';
import { inject, observer } from 'mobx-react';
import React, { useEffect, useState } from 'react';
import MatchOddsTab from './MatchOdds';
// import { getMatchStandings } from 'iscommon/api/match';

const OddsTab = inject('store')(
  observer((props) => {
    const [type, setType] = useState('eu');
    const {
      store: { Match },
    } = props;
    const { matchHeaderInfo = {}, matchOdds } = Match;
    const { homeTeam: { name: homeName = '' } = {}, awayTeam: { name: awayName = '' } = {} } = matchHeaderInfo || {};
    const [currentList, setCurrentList] = useState([]);

    useEffect(() => {
      if (matchOdds.length) {
        const list = formatMatchDetailOdds(matchOdds, true);
        let currentList = [];
        list.forEach((item: any) => {
          let companyOddsInfo = {};
          companyOddsInfo.companyId = item.companyId;
          companyOddsInfo.currentCompanyOdds = item.companyOdds.filter((val: any) => val.oddsType === type)[0];
          currentList.push(companyOddsInfo || {});
        });
        setCurrentList(currentList);
      }
    }, [matchOdds, type]);

    const onChangeType = (type) => {
      setType(type);
    };

    // if (loading) return <UmiLoading />;
    return (
      <Loading loading={false} isEmpty={currentList.length === 0}>
        <MatchOddsTab type={type} onChangeType={onChangeType} currentList={currentList} homeName={homeName} awayName={awayName} />
      </Loading>
    );
  }),
);

export default React.memo(OddsTab);
