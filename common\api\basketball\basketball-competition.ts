// @ts-nocheck
import igsRequest from 'iscommon/request/instance';
import { genValidObj } from 'iscommon/utils';

export const getCompetitionHeader = ({ competitionId = '', seasonId = '' } = {}) => {
  const config = { competitionId };
  if (seasonId) {
    config.seasonId = seasonId;
  }
  return igsRequest.post("/competition/header", config).then((result) => {
    return result;
  });
};

export const getCompetitionTeam = ({ competitionId = '' } = {}) => {
  return igsRequest.post("/competition/team", { competitionId }).then((result) => {
    return result;
  });
};

export const getCompetitionStatsTeam = ({ competitionId = '', seasonId = '', scope, teamId = null } = {}) => {
  return igsRequest.post("/competition/stats/team", { competitionId, seasonId, scope, teamId }).then((result) => {
    return result;
  });
};

export const getCompetitionStatsPlayer = ({ competitionId = '', seasonId = '', scope, teamId = null } = {}) => {
  return igsRequest.post("/competition/stats/player", { competitionId, seasonId, scope , teamId}).then((result) => {
    return result;
  });
};

export const getCompetitionStandings = ({ competitionId = '', seasonId = '', scope } = {}) => {
  return igsRequest.post("/competition/standings", { competitionId, seasonId, scope }).then((result) => {
    return result;
  });
};

export const getCompetitionMatches = ({ competitionId = '', seasonId = '', matchKind, stageId, teamId } = {}) => {
  return igsRequest.post("/competition/matches", { competitionId, seasonId, matchKind, stageId, teamId}).then((result) => {
    return result;
  });
};

export const getKeyplayer = ({ competitionId, seasonId, orderType } = {}) => {
  return igsRequest.post("/competition/keyplayer", { competitionId, seasonId, orderType }).then((result) => {
    return result;
  });
};

export const getSelector = ({ competitionId, seasonId } = {}) => {
  return igsRequest.post("/competition/detail/selector", { competitionId, seasonId }).then((result) => {
    return result;
  });
};

export const getScopeSelector = ({ competitionId, seasonId } = {}) => {
  return igsRequest.post("/competition/detail/scope/selector", { competitionId, seasonId }).then((result) => {
    return result;
  });
};
