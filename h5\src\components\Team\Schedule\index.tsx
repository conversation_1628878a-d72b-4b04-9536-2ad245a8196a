import Loading from '@/components/Loading';
import { getFootBallTeamMatches } from 'iscommon/api/football-team';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { inject, observer } from 'mobx-react';
import moment from 'moment';
import { useEffect, useState } from 'react';

import './index.less';
import { Button, Collapse, DatePicker } from 'antd-mobile';
import { DownOutline } from 'antd-mobile-icons'
import { FallbackImage } from 'iscommon/const/icon';
import { Link } from 'umi';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import FavoriteIcon from '@/components/Common/FavoriteIcon';
import { StatusCodeEnum } from 'iscommon/const/constant';

const TeamSchedule = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Team: { teamId },
      },
    } = props;

    const [loading, setLoading] = useState<boolean>(false);
    const [selectedDate, setSelectedDate] = useState<Date>(new Date());
    const [calendarVisible, setCalendarVisible] = useState(false);
    const [filteredList, setFilteredList] = useState([]);
    const [matches, setMatches] = useState<any[]>([]);

    const labelMaps = useTranslateKeysToMaps(['Schedule', 'Result']);

    const formatDate = (date: any) => {
      return date ? moment(date).format('MMM YY') : moment().format('MMM YY');
    };

    useEffect(() => {
      setLoading(true);
      getFootBallTeamMatches({
        teamId,
        month: moment().format('YYYY-MM'),
        externalType: 2,
      }).then(({ matchList }: any) => {
        setMatches(matchList)
        setLoading(false);
      });
    }, [teamId]);

    useEffect(() => {
      if (Object.keys(matches).length > 0) {
        filterMatchesForSelectedDate(selectedDate);
      }
    }, [matches, selectedDate]);
    
    const onDateChange = (date: any) => {
      setSelectedDate(date);
      setCalendarVisible(false);
      if (date) {
        filterMatchesForSelectedDate(date);
      }
    };

    const groupedMatches = matches.reduce((acc, match) => {
      const { competitionName } = match;
      if (!acc[competitionName]) {
        acc[competitionName] = [];
      }
      acc[competitionName].push(match);
      return acc;
    }, {});

    const filterMatchesForSelectedDate = (date: any) => {
      const selectedMoment = moment(date);
      const selectedMonth = selectedMoment.month();
      const selectedYear = selectedMoment.year();

      const filteredGroupedMatches = Object.entries(groupedMatches).reduce((acc, [competitionName, matches]) => {
        const filteredMatches = matches.filter((match: any) => {
          const matchDate = moment.unix(match.matchTime);
          return matchDate.month() === selectedMonth && matchDate.year() === selectedYear;
        });
    
        if (filteredMatches.length > 0) {
          acc[competitionName] = filteredMatches;
        }
        return acc;
      }, {});
    
      setFilteredList(filteredGroupedMatches);
    };
    
    return (
      <div className='team-schedule-container'>
        <div className='container-title'>
          <span>{labelMaps.Schedule}&nbsp;&&nbsp;{labelMaps.Result}</span>
          <>
            <Button className='team-month-btn' size="small" onClick={() => setCalendarVisible(true)}>
              <span className='btn-content'>
                <span className='btn-value'>{formatDate(selectedDate)}</span>
                <DownOutline className='down-icon'/>
              </span>
            </Button>
            <DatePicker
              className='game-category-calendar-picker'
              visible={calendarVisible}
              precision='month'
              onConfirm={onDateChange}
              onClose={() => setCalendarVisible(false)}
            />
          </>
        </div>
        <div className='container-body'>
          <Loading loading={loading} isEmpty={Object.keys(filteredList).length === 0}>
            <Collapse className='custom-collapse'>
              {Object.entries(filteredList).map(([competitionName, matches]: [string, any[]], index: number) => (
                <Collapse.Panel
                  key={index}
                  title={
                    <div className="panel-header">
                      <img src={matches[0]?.competition?.logo || FallbackImage} alt={matches[0]?.competition?.name} className="competition-icon" />
                      <span className='competition-name'>{matches[0]?.competition?.name}</span>
                    </div>
                  }
                >
                  {matches.sort((a, b) => a.matchTime - b.matchTime).map((match: any, index: number) => {
                    return (
                      <Link to={GlobalUtils.getPathname(PageTabs.match, match.id)} key={match.id} className="match-list-container">
                        <div className="match-time-container">
                          {/* <FavoriteIcon isMatch={true} match={match} /> */}
                          <span className="match-time">{moment.unix(match.matchTime).format('DD/MMM')}</span>
                        </div>
                        <div className='match-team-container'>
                          <div className='match-team-info'>
                            <img className='team-logo' src={match.homeTeam?.logo || FallbackImage} alt={match.homeTeam?.name}/>
                            <span className='team-name text-overflow'>{match.homeTeam?.name}</span>
                          </div>
                          <div className='match-team-info'>
                            <img className='team-logo' src={match.awayTeam?.logo || FallbackImage} alt={match.awayTeam?.name}/>
                            <span className='team-name text-overflow'>{match.awayTeam?.name}</span>
                          </div>
                        </div>
                        {match.matchStatus === StatusCodeEnum.NotStarted ? (
                          <div className='match-score-container'>
                            <span>-</span>
                          </div>
                        ) : (
                          <div className="match-score-container">
                            <span className="match-score">{match.homeScores[0]}</span>
                            <span className="match-score">{match.awayScores[0]}</span>
                          </div>
                        )}
                      </Link>
                    )
                  })}
                </Collapse.Panel>
              ))}
            </Collapse>
          </Loading>
        </div>
      </div>
      // <Loading loading={loading} isEmpty={nextList.length === 0 && list.length === 0}>
      //   <div className="teamSchedule">
      //     {nextList.length > 0 ? (
      //       <>
      //         <div
      //           className="nextButton"
      //           onClick={() => {
      //             setNextVis(!nextVis);
      //           }}
      //         >
      //           <div>
      //             {!nextVis ? (
      //               <svg
      //                 t="1663411370264"
      //                 className="icon"
      //                 viewBox="0 0 1024 1024"
      //                 version="1.1"
      //                 xmlns="http://www.w3.org/2000/svg"
      //                 p-id="2372"
      //                 width="64"
      //                 height="64"
      //               >
      //                 <path
      //                   d="M533.333333 776.533333L341.333333 584.533333l29.866667-29.866666 162.133333 162.133333 162.133334-162.133333 29.866666 29.866666-192 192z m0-503.466666l-162.133333 162.133333-29.866667-29.866667L533.333333 213.333333 725.333333 405.333333l-29.866666 29.866667-162.133334-162.133333z"
      //                   fill="#0D80D9"
      //                   p-id="2373"
      //                 ></path>
      //               </svg>
      //             ) : (
      //               <svg
      //                 t="1663413209228"
      //                 className="icon"
      //                 viewBox="0 0 1024 1024"
      //                 version="1.1"
      //                 xmlns="http://www.w3.org/2000/svg"
      //                 p-id="3297"
      //                 width="64"
      //                 height="64"
      //               >
      //                 <path
      //                   d="M554.666667 388.266667l98.133333-98.133334 29.866667 29.866667-149.333334 149.333333L384 320l29.866667-29.866667L512 388.266667V128h42.666667v260.266667z m0 290.133333V938.666667h-42.666667v-260.266667l-98.133333 98.133333-29.866667-29.866666 149.333333-149.333334 149.333334 149.333334-29.866667 29.866666-98.133333-98.133333z"
      //                   fill="#0D80D9"
      //                   p-id="3298"
      //                 ></path>
      //               </svg>
      //             )}
      //             {text}
      //           </div>
      //           <span className="count">{count}</span>
      //         </div>
      //         {nextVis && (
      //           <div className="nextList">
      //             {nextList.map((item: any) => (
      //               <LeagueItem key={item.title} leagueItem={item} />
      //             ))}
      //           </div>
      //         )}
      //       </>
      //     ) : null}
          // <div className="nextList">
          //   {list.map((item: any) => (
          //     <LeagueItem key={item.title} leagueItem={item} />
          //   ))}
          // </div>
      //   </div>
      // </Loading>
    );
  }),
);

export default TeamSchedule;
