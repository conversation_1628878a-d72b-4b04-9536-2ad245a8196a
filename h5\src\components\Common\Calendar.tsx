import { Calendar } from 'antd-mobile'
import { translate } from 'iscommon/i18n/utils'
import moment from 'moment'
import './Calendar.less'

export default function CalendarBlock(props: any) {
  const { onChange = () => null, onCancel = () => null, defaultSingle = moment() } = props
  return (
    <div className="fixContainer">
      <header className="header">
        <div
          className="backArea"
          onClick={() => {
            onChange(moment())
          }}
        >
          {translate('Today')}
        </div>
        <div className="title"></div>
        <div
          className="backArea"
          onClick={() => {
            onCancel()
          }}
        >
          {translate('Cancel')}
        </div>
      </header>
      <Calendar
        selectionMode="single"
        defaultValue={defaultSingle}
        onChange={(val) => {
          onChange(val)
        }}
        style={{ background: '#fff', marginTop: '44px' }}
      />
    </div>
  )
}
