// @ts-nocheck
import MqttClientInstance from '../index';
import homeDataController, { homeDataControllerType , startTimer} from './homeDataController';
import { GlobalConfig } from '../../const/globalConfig';
import { MqttTopicEnum, MqttListenKeyEnum } from '../constants';
import store from '../../mobx';

export const subscribeHomeData = () => {
  // subscribe home match data
  MqttClientInstance.subscribe(MqttTopicEnum.allMatch, MqttListenKeyEnum.allMatch, (topic, message) => {
    const { WebHome } = store;
    const { data, time } = message;
    console.log('MqttClientInstance match', topic, data);
    if (data) {
      GlobalConfig.serverTime = time;
      WebHome.changeServerTime(time);
      for (let dataKey in homeDataControllerType) {
        if (data[dataKey] && typeof homeDataController[dataKey] === 'function') {
          homeDataController[dataKey](data);
        }
      }
    }
  });

  startTimer()

  // subscribe home odds
  MqttClientInstance.subscribe(MqttTopicEnum.odds, MqttListenKeyEnum.odds, (topic, message = {}) => {
    // console.log('MqttClientInstance odds', topic, message);
    if (message && typeof message === 'object' && Object.keys(message).length) {
      homeDataController.syncHomeOdds(message);
    }
  });
};

export const unsubscribeHomeData = () => {
  MqttClientInstance.unsubscribe(MqttListenKeyEnum.footballMatch);
  MqttClientInstance.unsubscribe(MqttListenKeyEnum.odds);
};
