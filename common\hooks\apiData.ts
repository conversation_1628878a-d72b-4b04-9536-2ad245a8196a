// @ts-nocheck
import { useMemo, useEffect, useRef } from 'react';

import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

export const useFootballPositionMaps = (otherLabels = ['']) => {
  const labelMaps = useTranslateKeysToMaps([
    ...otherLabels,
    'TopValuablePlayers',
    'Strikers',
    'Midfielders',
    'Defender',
    'Goalkeeper',
  ]);
  const positionMaps = useMemo(
    () => ({
      F: labelMaps.Strikers,
      M: labelMaps.Midfielders,
      D: labelMaps.Defender,
      G: labelMaps.Goalkeeper,
    }),
    [labelMaps],
  );
  return {
    positionMaps,
    labelMaps,
  };
};

export const useMatchTimer = (serverTime = 0, store = {}, callback = () => {}) => {
  const refTimeout = useRef(null);

  useEffect(() => {
    // console.log(serverTime, 'serverTime')
    if (serverTime) {
      if (refTimeout.current) {
        clearTimeout(refTimeout.current);
      }
      refTimeout.current = setTimeout(() => {
        store.changeServerTime(Date.now() / 1000);
        console.log('serverTime 手动更新', 'serverTime')
        callback()
      }, 5 * 1000);
    }
    return () => {
      clearTimeout(refTimeout.current);
    };
  }, [serverTime, store, callback]);
};
