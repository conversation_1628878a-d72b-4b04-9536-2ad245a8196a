.competition-overview-top-player-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;

    .top-player-header-btn {
      display: block;
      background: transparent;
      color: #fff;
      border-radius: 32px;
      width: 250px;

      .btn-content {
        display: flex; 
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .btn-value {
          white-space: nowrap;   
          overflow: hidden;      
          text-overflow: ellipsis;
          flex-grow: 1;
          margin-right: 8px;
        }

        .down-icon {
          flex-shrink: 0;  
          margin-left: auto;
        }
      }
    }
  }
  
  .container-body {
    background: #1e1e1e;

    .player-stat-table {
      width: 100%;
      display: flex;
      flex-direction: column;
      border-collapse: collapse;
    
      .header-row, .table-row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #2c2c2c;
        text-align: left;
        color: #fff;
        padding: 10px;
      }

      .header-row {
        background-color: #1E1E1E;
        font-weight: bold;
      }

      .table-row {
        background-color: #121212;
      }

      .header-cell, .table-cell {
        text-align: center;
        padding: 10px;
        font-size: 24px;
        align-content: center;
      }

      .header-cell:first-child, 
      .table-cell:first-child {
        width: 10%;
      }

      .header-cell:last-child,
      .table-cell:last-child {
        width: 25%;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    
      .header-cell:nth-child(2), 
      .table-cell:nth-child(2) {
        width: 65%;
        text-align: left;
      }

      .player-cell {
        display: flex;
        align-items: center;
        width: 70%;
        text-align: left;

        .player-icon {
          width: 58px;
          height: 58px;
          border-radius: 50%;
          margin: 5px 10px;
          overflow: hidden;
          object-fit: cover;
        }
  
        .player-info {
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .player-name {
            font-size: 22px;
            font-weight: 600;
            color: #fff;
          }

          .player-team-detail {
            display: flex;
            align-items: center;

            .team-icon {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              margin-right: 10px;
              overflow: hidden;
              background-size: cover;
            }

            .team-name {
              font-size: 20px;
              color: #fff;
            }
          }
        }
      }
    }
  }
}