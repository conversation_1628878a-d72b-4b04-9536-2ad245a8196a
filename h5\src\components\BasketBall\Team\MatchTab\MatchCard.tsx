import { But<PERSON>, Collapse, DatePicker } from "antd-mobile";
import { getTeamSchedule } from "iscommon/api/basketball/basketball-team";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { inject, observer } from "mobx-react";
import moment from "moment";
import { useState, useEffect } from "react";
import { DownOutline } from 'antd-mobile-icons'
import Loading from "@/components/Loading";
import FavoriteIcon from "@/components/Common/FavoriteIcon";
import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";
import { FallbackImage } from "iscommon/const/icon";
import { Link } from "umi";
import './MatchCard.less'
import { StatusCodeEnum } from "iscommon/const/constant";

const MatchCard = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Basketball: {
          Team: { teamId, teamHeaderInfo }
        }
      },
    } = props;

    const competitionId = teamHeaderInfo?.competitions?.[0]?.id;
    const seasonId = teamHeaderInfo?.currentSeason?.id;

    const [loading, setLoading] = useState<boolean>(false);
    const [selectedDate, setSelectedDate] = useState<Date>(new Date());
    const [calendarVisible, setCalendarVisible] = useState(false);
    const [filteredList, setFilteredList] = useState([]);
    const [matches, setMatches] = useState<any[]>([]);

    const labelMaps = useTranslateKeysToMaps(['Schedule', 'Result']);

    const formatDate = (date: any) => {
      return date ? moment(date).format('MMM YY') : moment().format('MMM YY');
    };

    useEffect(() => {
      if (competitionId && seasonId) {
        setLoading(true);
        getTeamSchedule({
          competitionId,
          seasonId,
          teamId,
        }).then((res: any) => {
          let matchList = res?.matchList || [];
          setMatches(matchList);
          setLoading(false);
        });
      }
    }, [competitionId, seasonId, teamId]);

    useEffect(() => {
      if (Object.keys(matches).length > 0) {
        filterMatchesForSelectedDate(selectedDate);
      }
    }, [matches, selectedDate]);
    
    const onDateChange = (date: any) => {
      setSelectedDate(date);
      setCalendarVisible(false);
      if (date) {
        filterMatchesForSelectedDate(date);
      }
    };

    const groupedMatches = matches.reduce((acc, match) => {
      const { competitionName } = match;
      if (!acc[competitionName]) {
        acc[competitionName] = [];
      }
      acc[competitionName].push(match);
      return acc;
    }, {});

    const filterMatchesForSelectedDate = (date: any) => {
      const selectedMoment = moment(date);
      const selectedMonth = selectedMoment.month();
      const selectedYear = selectedMoment.year();

      const filteredGroupedMatches = Object.entries(groupedMatches).reduce((acc, [competitionName, matches]) => {
        const filteredMatches = matches.filter((match: any) => {
          const matchDate = moment.unix(match.matchTime);
          return matchDate.month() === selectedMonth && matchDate.year() === selectedYear;
        });
    
        if (filteredMatches.length > 0) {
          acc[competitionName] = filteredMatches;
        }
        return acc;
      }, {});
    
      setFilteredList(filteredGroupedMatches);
    };

    return (
      <div className='basketball-team-schedule-container'>
        <div className='container-title'>
          <span>{labelMaps.Schedule}&nbsp;&&nbsp;{labelMaps.Result}</span>
          <>
            <Button className='team-month-btn' size="small" onClick={() => setCalendarVisible(true)}>
              <span className='btn-content'>
                <span className='btn-value'>{formatDate(selectedDate)}</span>
                <DownOutline className='down-icon'/>
              </span>
            </Button>
            <DatePicker
              className='game-category-calendar-picker'
              visible={calendarVisible}
              precision='month'
              onConfirm={onDateChange}
              onClose={() => setCalendarVisible(false)}
            />
          </>
        </div>
        <div className='container-body'>
          <Loading loading={loading} isEmpty={Object.keys(filteredList).length === 0}>
            <Collapse className='custom-collapse'>
              {Object.entries(filteredList).map(([competitionName, matches]: [string, any[]], index: number) => (
                <Collapse.Panel
                  key={index}
                  title={
                    <div className="panel-header">
                      <img src={matches[0]?.competition?.logo || FallbackImage} alt={matches[0]?.competition?.name} className="competition-icon" />
                      <span className='competition-name'>{matches[0]?.competition?.name}</span>
                    </div>
                  }
                >
                  {matches.sort((a, b) => a.matchTime - b.matchTime).map((match: any, index: number) => {
                    const calculatedHomeScore = match.homeScores.reduce((acc: number, score: number) => acc + score, 0);
                    const calculatedAwayScore = match.awayScores.reduce((acc: number, score: number) => acc + score, 0);
                    return (
                      <Link to={GlobalUtils.getPathname(PageTabs.match, match.id)} key={match.id} className="match-list-container">
                        <div className="match-time-container">
                          <FavoriteIcon isMatch={true} match={match} />
                          <span className="match-time">{moment.unix(match.matchTime).format('DD/MMM')}</span>
                        </div>
                        <div className='match-team-container'>
                          <div className='match-team-info'>
                            <img className='team-logo' src={match.homeTeam?.logo || FallbackImage} alt={match.homeTeam?.name} loading="lazy"/>
                            <span className='team-name text-overflow'>{match.homeTeam?.name}</span>
                          </div>
                          <div className='match-team-info'>
                            <img className='team-logo' src={match.awayTeam?.logo || FallbackImage} alt={match.awayTeam?.name} loading="lazy"/>
                            <span className='team-name text-overflow'>{match.awayTeam?.name}</span>
                          </div>
                        </div>
                        {match.matchStatus === StatusCodeEnum.NotStarted ? (
                          <div className="match-score-container">
                            <span>-</span>
                          </div>
                        ) : (
                          <div className="match-score-container">
                            <span className="match-score">{calculatedHomeScore}</span>
                            <span className="match-score">{calculatedAwayScore}</span>
                          </div>
                        )}
                      </Link>
                    )
                  })}
                </Collapse.Panel>
              ))}
            </Collapse>
          </Loading>
        </div>
      </div>
    );
  }),
);

export default MatchCard;