.basketball-overview-match-info-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .container-body {
    background: #1e1e1e;
    padding: 10px;

    .item-container {
      padding: 10px 5px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .item-icon-text {
        display: flex;
        align-items: center;
        color: #fff;

        .item-icon {
          width: 28px;
          height: 28px;
          margin: 0px 10px;
        }

        .item-text {
          color: #fff;
          font-size: 24px;
          font-weight: 600;
        }

        .rotated-icon {
          transform: rotate(90deg);
          transition: transform 0.3s ease;
          margin: 0px 10px;
        }
        
        .default-icon {
          transform: rotate(0deg);
          transition: transform 0.3s ease;
          margin: 0px 10px;
        }
      }

      .item-value {
        color: #fff;
        font-size: 24px;
        font-weight: 600;
      }
    }
  }
}