import classNames from 'classnames';
import moment from 'moment';
import React, { useMemo } from 'react';

import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

import './index.less'

import styles from './index.less';
import { FallbackImage } from 'iscommon/const/icon';
import PlayerPosition from '../PlayerPosition';
import { Link } from 'umi';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';

interface Props {
  player: any;
  team: any;
  parsedCharacteristics: any[][][];
  parsedPositions: {
    mainLocation: string;
    secondaryLocations: any[];
  };
}

const PlayerBaseInfo: React.FC<Props> = (props) => {
  const { player, team, parsedCharacteristics, parsedPositions } = props;
  const labelMaps = useTranslateKeysToMaps([
    'Overview',
    'PlayerInfo',
    'Birthday',
    'Foot',
    'Height',
    'Weight',
    'PlayerValue',
    'Club',
    'ContractUntil',
    'LeftFoot',
    'RightFoot',
    'Forwards',
    'Midfielders',
    'Defender',
    'Goalkeeper',
    'Country',
    'Position',
    'Shirt',
  ]);

  const positionList = useMemo(
    () => [
      { key: 'F', label: labelMaps.Forwards },
      { key: 'M', label: labelMaps.Midfielders },
      { key: 'D', label: labelMaps.Defender },
      { key: 'G', label: labelMaps.Goalkeeper },
    ],
    [labelMaps],
  );

  const birthday = useMemo(() => moment(player.birthday * 1000).format('DD MMM YYYY'), [player.birthday]);

  const worth = useMemo(() => {
    if (player.marketValue === 0) return '-';
    return `${(player.marketValue || 0) / 1000000}M${player.marketValueCurrency}`;
  }, [player.marketValueCurrency, player.marketValue]);

  const contractUntil = useMemo(() => {
    return moment(player?.contractUntil * 1000).format('DD MMM YYYY');
  }, [player?.contractUntil]);

  // const preferredFoot = useMemo(() => {
  //   let ret = '-';
  //   if (player?.preferredFoot === 1) ret = labelMaps.RightFoot;
  //   if (player?.preferredFoot === 2) ret = labelMaps.LeftFoot;
  //   return ret;
  // }, [player?.preferredFoot, labelMaps.RightFoot, labelMaps.LeftFoot]);

  const playerPosition = player.position;

  const positionLabel = useMemo(() => {
    const normalizedPosition = playerPosition?.trim().toUpperCase();
    const position = positionList.find(pos => pos.key === normalizedPosition);
    return position ? position.label : 'Unknown Position';
  }, [playerPosition, positionList]);

  return (
    <div className='player-base-info-container'>
      <div className='container-title'>{labelMaps.Overview}</div>
      <div className='container-body'>
        <Link className='team-container' to={GlobalUtils.getPathname(PageTabs.team, team?.id)}>
          <img className='team-icon' src={team?.logo} alt={team?.name}/>
          <div className='team-detail'>
            <span className='team-name'>{team?.name}</span>
            <span className='contract-date'>{labelMaps.ContractUntil}&nbsp;{contractUntil}</span>
          </div>
        </Link>
        <div className='player-info-container'>
          <div className="detail-item">
            <div className='detail-info'>
              <img className="detail-logo" src={player.countryDto?.logo || FallbackImage} />
              <span className="detail-title">{player.countryDto?.name}</span>
            </div>
            <span className="detail-value">{labelMaps.Country}</span>
          </div>
          <div className="detail-item">
            <span className="detail-title">{player.preferredFoot === 1 ? labelMaps.LeftFoot : labelMaps.RightFoot}</span>
            <span className="detail-value">{labelMaps.Foot}</span>
          </div>
          <div className="detail-item">
            <span className="detail-title">{player.height} cm</span>
            <span className="detail-value">{labelMaps.Height}</span>
          </div>
          <div className="detail-item">
            <span className="detail-title">{player.age} Years</span>
            <span className="detail-value">{birthday}</span>
          </div>
          <div className="detail-item">
            <span className="detail-title">{positionLabel}</span>
            <span className="detail-value">{labelMaps.Position}</span>
          </div>
          <div className="detail-item">
            <span className="detail-title">{player.shirtNumber}</span>
            <span className="detail-value">{labelMaps.Shirt}</span>
          </div>
        </div>
        <PlayerPosition parsedCharacteristics={parsedCharacteristics} parsedPositions={parsedPositions}/>
      </div>
    </div>
    // <>
    //   <div className="playerOverviewTitle">{labelMaps.PlayerInfo}</div>
    //   <div className={styles.baseInfo}>
    //     <div className={classNames(styles.baseItem, styles.topLeft)}>
    //       <div className="fs-10-center color-999">{labelMaps.Birthday}</div>
    //       <div className={styles.value}>{birthday}</div>
    //     </div>
    //     <div className={classNames(styles.baseItem, styles.topRight)}>
    //       <div className="fs-10-center color-999">{labelMaps.Foot}</div>
    //       <div className={styles.value}>{preferredFoot}</div>
    //     </div>
    //     <div className={classNames(styles.baseItem, styles.bottomLeft)}>
    //       <div className="fs-10-center color-999">
    //         {labelMaps.Height}(cm)/{labelMaps.Weight}(kg)
    //       </div>
    //       <div className={styles.value}>
    //         {player.height}/{player.weight}
    //       </div>
    //     </div>
    //     <div className={classNames(styles.baseItem, styles.bottomRight)}>
    //       <div className="fs-10-center color-999">{labelMaps.PlayerValue}</div>
    //       <div className={styles.value} style={{ color: '#0F80D9' }}>
    //         {worth}
    //       </div>
    //     </div>
    //     <div className={styles.baseItemCenter}>
    //       <div className={styles.baseCenterWrap}>
    //         <div className={styles.baseCenterInfo}>{player?.shirtNumber}</div>
    //         <img
    //           src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABICAMAAABx5AOBAAABblBMVEUAAAD///9/f3/U1NTb29vf39/j4+Pm5ubo6Ojr6+vt7e3u7u7f39/v7+/w8PDk5OTq6urr6+vr6+t/f3/l5eXm5ubn5+fo6Ojo6Ojj4+Pr6+vj4+Pl5eXl5eXm5ubl5eXm5uaCgoLj4+PV1dXp6emBgYHj4+Pn5+fm5ubn5+fl5eWBgYHl5eXl5eXk5OTm5ubl5eXm5ubl5eXm5ubm5ubn5+fn5+eBgYHn5+fl5eXm5ubl5eXl5eXm5ubl5eWAgIDn5+fl5eXm5uaenp7m5ubl5eXn5+fl5eXn5+fn5+fm5ubn5+fm5ubm5ubl5eXl5eXm5ubl5eXm5ubl5eXm5uaAgIDm5ubl5eXm5ubl5eXm5ubm5ubl5eXm5uaAgIChoaHm5ubm5uaAgICgoKDm5ubm5ubl5eXm5ubl5eXm5ubl5eXm5ubm5ubm5ubm5ubm5ubm5ubm5ubn5+eAgIDm5ubn5+fm5ubm5uaAgIDm5ubAQ2tOAAAAeHRSTlMAAwQGBwgJCgsNDg8QEBETGBkaHB4fICEiJSYuMTI0PD4/QENGR0pLUVRYX2JkaGhtb3Z5en1+f4GChIqLjZKTk5SXmZmdnZ6eoKOoq62wsra8v8PEx8vX19jY2drc39/g5ufn5+jr7u/v8PHy8/T19/n6+/v7/P5Uy56nAAABcElEQVRYw+2YR1PCUBRGPyyAioC9AfaCXbBi7703xN6wUlQk3H/vJoBIAo+8jYt3VnmLc2aSydw78wAF8sauYukct4OV2ZgiX82Mfk9MhbsKJr/1+l6Nxey2fvCSMnA+UJBRt3j8lIXHUZOqbp0KEAMvHouiXrMQJkaC0+VpevVahHIgPGdN0Y2TH5QjoeH8pN92Sxq4cMT9+jBpIlAnBzZII+vyBwhpDQQKAQBdpJlOAMCS9sA8AOBGe8ALALXEQRmAXp5AN4BVnsAMAC9P4AQwSzyBiB4dxEUjJvgCLuzxBXbh5ws84J0v8Iz+KI//2QfYfYnjPpt1kHg6swGAYUU+Sk1sgZb4r7NskEeS85WIiLZL2QIlO0RE9ORMTtWqIyKSGlgDNomIDit/z3XdeJQ2YWILFGOLvkd0fzaLw21iD5jddsXtxvoKqttVBERABERABERABETg/wWK2AJG9QuIIIv/luEGw3Wa3fcNpSg/37vcK3zzv4kAAAAASUVORK5CYII="
    //           alt=""
    //           className={styles.baseCenterImg}
    //           style={{ objectFit: 'fill' }}
    //         />
    //       </div>
    //     </div>
    //   </div>
    //   <div className={styles.baseDesc}>
    //     <div className={styles.baseRow}>
    //       <div className={styles.label}>{labelMaps.Club}</div>
    //       <div className={styles.value}>
    //         <img className={styles.logo} src={team.logo} alt="" />
    //         <span>{team.name}</span>
    //       </div>
    //     </div>
    //     {player?.contractUntil ? (
    //       <div className={styles.baseRow}>
    //         <div className={styles.label}>{labelMaps.ContractUntil}</div>
    //         <div className={styles.value}>{contractUntil}</div>
    //       </div>
    //     ) : null}
    //   </div>
    // </>
  );
};

export default PlayerBaseInfo;
