.basketball-player-season-stat-conainer {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;

  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .container-body {
    background: #1E1E1E;
    padding: 10px;

    .stats-row {
      display: flex;
      justify-content: space-between;
      padding: 10px 20px;

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1; 
        text-align: center;
        color: #fff;

        .stat-label,
        .stat-rank {
          font-size: 20px;
          color: #C0C5C9;
        }

        .stat-value {
          font-size: 22px;
          font-weight: bold;
          margin: 5px 0;
        }
      }
    }
  }
}