import React, { useMemo } from 'react';

import { PLAYER_POSITION_MAP_H5 } from 'iscommon/const/player';
import { usePlayerAbilities } from 'iscommon/hooks/player';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

import styles from './index.less';

import './index.less'

interface Props {
  parsedCharacteristics: any[][][];
  parsedPositions: {
    mainLocation: string;
    secondaryLocations: any[];
  };
}

const PlayerPosition: React.FC<Props> = (props) => {
  const { parsedCharacteristics, parsedPositions } = props;

  const labelMaps = useTranslateKeysToMaps(['MainPosition', 'Strengths', 'Weaknesses']);
  const { abilities } = usePlayerAbilities(labelMaps, parsedCharacteristics as any);

  const positions = useMemo(() => {
    const { mainLocation = '', secondaryLocations = [] } = parsedPositions;
    return [mainLocation, ...(secondaryLocations || [])]
      .filter((i) => !!i && !!(PLAYER_POSITION_MAP_H5 as any)[i])
      .map((position) => ({
        ...(PLAYER_POSITION_MAP_H5 as any)[position],
        position,
      }));
  }, [parsedPositions]);

  const capitalizeEachWord = (string: string) => {
    if (!string) return '';
    return string
      .split(' ')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  return (
    <div className='player-position-container'>
      <div className='player-stats-container'>
      {abilities.map(({ label, values }: any) => {
        const color = label === 'Strengths' ? '#14AE5C' : '#FF3131';
        return (
          <div key={label} className='position-item' style={{color}}>
            {label}
            {values.map((value: any) => (
              <span className='position-value'>{capitalizeEachWord(value)}</span>
            ))}
          </div>
        )
      })}
      </div>
      <div className='match-field'>
        {positions.map(({ position, top, left }) => (
          <div key={position} className='lineup-court' style={{ top, left }}>
            {position}
          </div>
        ))}
      </div>
    </div>
    // <>
    //   <div className="playerOverviewTitle">{labelMaps.MainPosition}</div>
    //   <div className={styles.positionWrap}>
    //     <div className={styles.positionLeft}>
    //       {abilities.map(({ label, values }: any) => (
    //         <div key={label} className={styles.positionItem}>
    //           <div className={styles.positionItemTitle}>{label}</div>
    //           {values.map((v: any) => (
    //             <div key={v} className={styles.positionItemDesc}>
    //               {v}
    //             </div>
    //           ))}
    //         </div>
    //       ))}
    //     </div>
    //     <div className={styles.positionRight}>
    //       <div className={styles.field}>
    //         {positions.map(({ position, top, left }) => (
    //           <div key={position} className={styles.lineupCourt} style={{ top, left }}>
    //             <div className="fs-11-center">{position}</div>
    //           </div>
    //         ))}
    //       </div>
    //     </div>
    //   </div>
    // </>
  );
};

export default PlayerPosition;
