.liveAndVideo {
  width: 780px;
  border-radius: 16px;

  .content {
    height: 40px;
    // background-color: rgba(46, 46, 46, 1);
    display: flex;
    font-size: 16px;
    font-weight: 500;
    color: #999999;

    .item {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      transition: background-color 0.3s ease, color 0.3s ease;
      background-color: rgba(46, 46, 46, 1);

      &.current {
        background-color: rgba(70, 69, 69, 1);
        color: rgba(15, 128, 218, 1);
      }

      &:first-child.item { 
        border-radius: 16px 0px 0px 0px; 
      }

      &:last-child.item { 
        border-radius: 0px 16px 0px 0px; 
      }

      &:only-child.item {
        border-radius: 16px 16px 0px 0px;
      }
    }
  }

  .title {
    position: relative;
    background-color: rgba(38, 38, 38, 1);
    height: 36px;
    padding: 8px 6px;
    font-size: 16px;
    font-weight: 500;
    color: #FFFFFF;

    .text {
      padding-left: 8px;
      border-left: 4px solid #0F80DA;
      height: 20px;
      line-height: 20px;
    }

    .fullscreen {
      position: absolute;
      right: 0;
      top: 5px;
      padding: 5px;
      background-color: transparent;
    }
  }

  .liveContent {
    width: 100%;
    height: 546px;
    background: #000;
    position: relative;
    text-align: center;

    .noData {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: 100%;
      justify-content: center;
      align-content: center;

      .noDataText {
        color: #999999;
        padding-top: 8px;
        line-height: 17px;
        font-weight: 400;
        font-size: 14px;
      }
    }

    .box {
      position: relative;
      padding-top: 210px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;

      .text {
        margin-bottom: 20px;
        line-height: 22px;
        font-weight: 400;
        font-size: 18px;
        color: #fff;
      }

      .button {
        width: 198px;
        height: 48px;
        color: #fff;
        border-radius: 20px;
        padding: 12px 23px;
        background-color: #409eff;
        border-color: #409eff;
        display: inline-block;
        white-space: nowrap;
        cursor: pointer;
        box-sizing: border-box;
        font-weight: 500;
        font-size: 18px;
      }
    }

    > span {
      color: #333;
      font-size: 30px;
      padding: 10px 20px;
      background-color: #fff;
      cursor: pointer;
      border-radius: 10px;
    }

    iframe {
      width: 100%;
      height: 100%;
      border: none;
    }
  }

  .footer {
    height: 108px;
    padding: 10px 30px;
    font-size: 12px;
    color: #fff;

    div {
      display: flex;
      align-items: center;
    }

    .otd {
      margin: 10px 0;
      flex-direction: column;
    }

    .possession {
      display: flex;
      height: 22px;
      justify-content: center;
      width: 100%;

      .left {
        flex: 1;
        justify-content: flex-end;
      }

      .center {
        margin: 0 12px;
        width: 70px;
        justify-content: center;
      }

      .right {
        flex: 1;
      }

      .marginLeft24 {
        margin-left: 24px;
      }

      .marginLeft8 {
        margin: 0 8px;
      }

      .alignData {
        justify-content: space-between;
        width: 140px;
      }

      .typeName {
        width: 70px;
        justify-content: center;
        overflow: hidden;
        display: block;
        text-align: center;
      }

      .line {
        width: 1px;
        height: 100%;
        background: #666;
        margin: 0 10px;
      }

      .progress {
        width: 100px;
        height: 8px;
        position: relative;
        background-color: #000 !important;
        border-radius: 8px;

        .currentProgress {
          position: absolute;
          top: 0;
          height: 100%;
          border-radius: 8px;
        }

        .fright {
          right: 0;
          background-color: #49B18C;
        }

        .fleft {
          left: 0;
          background-color: #7892B7;
        }
      }
    }
  }
}
