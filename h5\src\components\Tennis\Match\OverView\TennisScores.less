.container {
  margin: 13px 0;
  padding: 8px;
  width: 100%;
  border-bottom: 1px solid #dcdcdc;
  background: white;
}

.title {
  width: 100%;
  height: 20px;
  line-height: 20px;
  font-size: 20px;
  font-family: Roboto-Medium;
  display: flex;
  justify-content: center;
}

.gameBox {
  display: flex;
  width: 100%;
}

.scoreLeft {
  width: 114px;
  height: 80px;
  border-right: 1px solid #dcdcdc;
  margin: 10px 0;
}

.scoreLeftItem {
  color: #999;
  width: 100%;
  text-align: center;
  height: 40px;
  position: relative;
}

.pointBox {
  flex-wrap: wrap;
  padding: 0 22px;
  margin: 10px 0;
  display: flex;
  flex-direction: row;
}

.pointItem {
  width: 50px;
  display: flex;
  flex-direction: column;
  .item {
    height: 40px;
    width: 50px;

    line-height: 40px;
    font-size: 20px;
    font-family: "Urbanist-Regular", sans-serif;;
    color: #999;
    text-align: center;
  }
}

.color000 {
  color: #000 !important;
}

.tennisIcon {
  position: absolute;
  // transform: scale(0.7);
  font-size: 24px;
  width: 20px;
  height: 20px;
  right: 20px;
  top: 10px;
}
