
export const IckHockeyStatusCodeEnum = {
  Abnormal: 0,
  NotStarted: 1
};


export const IckHockeyMatchStatusCodeToText = {
  0: "-",
  1: "-",
  30: "P1",
  331: "P1-PAUSE",
  31: "P2",
  332: "P2-PAUSE",
  32: "P3",
  100: "FT",
  6: "OT",
  10: "OT",
  105: "OT-ENDED",
  8: "PENALTY",
  13: "PENALTY",
  110: "ASO",
  14: "POSTPONED",
  15: "DELAYED",
  16: "CANCELED",
  17: "INTERRUPTED",
  19: "Cut in half",
  99: "TBD"
};

// 2-9
export const IckHockeyInLiveStatusEnum = [
  30, 331, 31, 32, 332, 6, 10, 8, 13
]

// 显示具体time
export const IckHockeyInLiveWithTimeEnum = [
  30, 31, 43, 10, 13
]

export const IckHockeyStatsKeys = {
}