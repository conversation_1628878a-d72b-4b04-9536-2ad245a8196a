// @ts-nocheck
import * as mqtt from 'mqtt';
import { generateUuid } from '../store';

const clientId = generateUuid(`webClient_${APP_ENV}_`, 5);

const transformUint8Array = (uint8Array) => {
  try {
    let res = '';
    const chunk = 8 * 1024;
    let i = 0;
    for (i = 0; i < uint8Array.length / chunk; i++) {
      res += String.fromCharCode.apply(null, uint8Array.slice(i * chunk, (i + 1) * chunk));
    }
    res += String.fromCharCode.apply(null, uint8Array.slice(i * chunk));
    return JSON.parse(res);
  } catch (e) {
    console.log('transformUint8Array', e);
    return {};
  }
};

// noinspection JSUnusedLocalSymbols
const startConnectMqtt = (onConnect = (client) => {}, onMessage = (topic, message) => {}) => {
  const client = mqtt.connect({
    username: 'igscoreWeb',
    password: 'igscoreWeb@2025',
    connectTimeout: 40000,
    keepalive: 60,
    port: 8084,
    host: 'xb480cf0.ap-southeast-1.emqx.cloud',
    hostname: 'xb480cf0.ap-southeast-1.emqx.cloud',
    clean: true,
    protocol: 'wss',
    path: '/mqtt',
    clientId,
  });

  client.on('connect', function (packet) {
    console.log('====== mqtt connect ======', packet);
    onConnect(client);
  });

  client.on('close', function (packet) {
    console.log('====== mqtt close ======', packet);
  });

  client.on('disconnect', function (packet) {
    console.log('====== mqtt disconnect ======', packet);
  });

  /**
   * event when receiving message from MQTT Queue
   * full topic ex: football/match/straging/0-6ypq3nh7v4w0md7
   */ 
  client.on('message', function (fullTopic, message) {
    const messageString = transformUint8Array(message);
    // const [game, type, env] = fullTopic.split('/');
    onMessage(fullTopic, messageString);
  });

  return client;
};

class MqttInstance {
  constructor() {
    /**
     * @type {mqtt.MqttClient}
     */
    this.instance = null;
    /**
     * @type {{[listenKey: string]: {topic: string, onGetData: (topic: string, message: any) => void}}}
     */
    this.listener = {};

    //
    this.busying = false;
  }

  subscribe(topic = '', listenKey = '', onGetData = () => {}) {
    const mapItem = {};
    mapItem.topic = topic;
    mapItem.onGetData = onGetData;
    this.listener[listenKey] = mapItem;
    if (this.instance) {
      this.listenData(topic);
    } else if (!this.busying) {
      this.busying = true;
      startConnectMqtt(this.onConnect.bind(this), this.onMessage.bind(this));
    }
  }

  unsubscribe(listenKey = '') {
    if (this.listener[listenKey]) {
      const { topic } = this.listener[listenKey];
      this.listener[listenKey] = null;
      if (this.instance) {
        this.instance.unsubscribe(topic);
      }
    }
  }

  listenData(topic) {
    this.instance.subscribe(topic);
  }

  // receive message from MQTT queue
  onMessage(topic, message) {
    const { onGetData } = this.listener[topic] || {};
    if (onGetData) {
      onGetData(topic, message);
    }
  }

  /**
   * @param {mqtt.MqttClient} clientInstance
   */
  onConnect(clientInstance) {
    this.instance = clientInstance;
    const keys = Object.keys(this.listener);
    for (let v of keys) {
      const { topic } = this.listener[v] || {};
      if (topic) {
        this.listenData(topic);
      }
    }
  }
}

const mqttClientInstance = new MqttInstance();

export default mqttClientInstance;
