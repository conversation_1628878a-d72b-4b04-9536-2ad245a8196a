import StandingsIntegral from '@/components/Competition/Standings/StandingsIntegral';
import Loading from '@/components/Loading';
import { getStandings } from 'iscommon/api/competition';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { inject, observer } from 'mobx-react';
import { useEffect, useMemo, useState } from 'react';
import styles from './Standings.less';
import StandingsShooter from './StandingsShooter';
import { Badge } from 'antd-mobile';
import { FallbackImage } from 'iscommon/const/icon';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { Link } from 'umi';

import './Standings.less'

interface Props {
  competitions?: {
    id: number;
    name: string;
    currentSeason: {
      id: number;
    };
  };
  showTopScorer?: boolean;
  showCompetition?: boolean;
  withHeader?: boolean;
  len?: number;
  store?: any;
}

const Standings = inject('store')(
  observer((props: Props) => {
    const {
      competitions,
      showTopScorer = true,
      showCompetition = true,
      withHeader = true,
      store: { Competitions },
      len = 100,
    } = props;
    const {
      commonInfo: {
        competition: { logo },
      },
    } = Competitions;
    const competitionId = competitions?.id || Competitions.competitionId;
    const competitionName = competitions?.name || Competitions.competitionName;
    const currentSeason = competitions?.currentSeason || Competitions.currentSeason;

    const labelMaps = useTranslateKeysToMaps(['All', 'TopScorers', 'Home', 'Away', 'Standings']);

    const [sourceList, setSourceList] = useState([]);
    const [promotions, setPromotions] = useState([]);
    const [type, setType] = useState(0);
    const [loading, setLoading] = useState(false);

    const radioItem = useMemo(() => {
      if (showTopScorer) {
        return [
          {
            label: labelMaps.All,
            value: 0,
          },
          {
            label: labelMaps.TopScorers,
            value: 3,
          },
          {
            label: labelMaps.Home,
            value: 1,
          },
          {
            label: labelMaps.Away,
            value: 2,
          },
        ];
      } else {
        return [
          {
            label: labelMaps.All,
            value: 0,
          },
          {
            label: labelMaps.Home,
            value: 1,
          },
          {
            label: labelMaps.Away,
            value: 2,
          },
        ];
      }
    }, [labelMaps, showTopScorer]);

    useEffect(() => {
      if (competitionId && currentSeason.id && type !== 3) {
        setLoading(true);
        getStandings({ competitionId, seasonId: currentSeason.id, type }).then((res: any) => {
          if (res) {
            const { tables, promotions } = res;
            setSourceList(tables ? tables.slice(0, len) : []);
            setPromotions(promotions || []);
          }
          setLoading(false);
        });
      }
    }, [competitionId, len, currentSeason.id, type]);

    const filteredData = sourceList.filter((item: any) => {
      return item.rows.length > 1 && item.rows[0].total > 0 && item.stageId != '';
    });

    const getPromotionColor = (promotionId: any) => {
      const promotion = promotions.find(promo => promo.id === promotionId);
      return promotion ? promotion.color : 'transparent';
    };

    return (
      <div className='competition-standing-tab-container'>
        <div className='container-title'>
          {labelMaps.Standings}
        </div>
        <div className='container-body'>
          <div className='custom-standing-table'>
            <div className="header-row">
              <div className="header-cell">#</div>
              <div className="header-cell">Team</div>
              <div className="header-cell">P</div>
              <div className="header-cell">GD</div>
              <div className="header-cell">Pts</div>
            </div>
            {sourceList.length !== 0 ? (
              sourceList.map((item: any, index) => {
                return (
                  item?.rows.map((row: any) => (
                    <Link className="table-row" key={row?.teamId} to={GlobalUtils.getPathname(PageTabs.team, row?.teamId)}>
                      <div className="table-cell promotion-cell">
                        <div className="promotion-indicator" style={{ backgroundColor: row?.promotionId ? getPromotionColor(row.promotionId) : 'transparent' }}></div>
                        <span>{row?.position}</span>
                      </div>
                      <div className="table-cell team-cell">
                        <img className="team-logo" src={row?.team?.logo || FallbackImage} alt={row?.team?.name} loading="lazy"/>
                        <span className='team-name'>{row?.team?.name}</span>
                      </div>
                      <div className="table-cell">{row?.total}</div>
                      <div className="table-cell">{row?.goalDiff}</div>
                      <div className="table-cell">{row?.points}</div>
                    </Link>
                  ))
                )
              })
            ) : (
              <Loading loading={loading}/> 
            )}
          </div>
          <div className='standing-promotion-container'>
            {promotions.map(promotion => (
              <div className='promotion-container' key={promotion.id}>
                <Badge className='promotion-badge' content={Badge.dot} style={{ backgroundColor: promotion.color }}/>
                {promotion.name}
              </div>
            ))}
          </div>
        </div>
      </div>
      // <div className={styles.container_box}>
      //   {withHeader ? (
      //     <div className={styles.radio_container}>
      //       {radioItem.map((item: any) => (
      //         <span
      //           className={`${styles.radio_btn} ${type === item.value ? styles.active : ''}`}
      //           key={item.value}
      //           onClick={() => {
      //             setType(item.value);
      //           }}
      //         >
      //           {item.label}
      //         </span>
      //       ))}
      //     </div>
      //   ) : null}
      //   {type === 3 ? (
      //     <StandingsShooter />
      //   ) : (
      //     <Loading loading={loading} isEmpty={type !== 3 && (!filteredData || filteredData.length === 0)}>
      //       {filteredData.map((item: any, index: number) => (
      //         <div key={item.stageId + index}>
      //           {withHeader && showCompetition ? (
      //             <div className={styles.table_title_team}>
      //               <img src={logo} className={styles.table_title_team_img} alt="" />
      //               <span className={styles.table_title_team_name}>{competitionName}</span>
      //             </div>
      //           ) : null}
      //           <StandingsIntegral data={item.rows} type={type} promotions={promotions} />
      //         </div>
      //       ))}
      //     </Loading>
      //   )}
      // </div>
    );
  }),
);

export default Standings;
