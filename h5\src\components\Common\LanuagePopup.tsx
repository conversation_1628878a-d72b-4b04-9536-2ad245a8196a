import { Popup } from 'antd-mobile';
import i18n from "i18next";
import { LeftOutline } from 'antd-mobile-icons';
import React, { useCallback, useEffect, useState } from 'react';

import { LanguageList } from 'iscommon/const/constant';
import GlobalUtils, { GlobalConfig } from 'iscommon/const/globalConfig';
import { changeLanguage, translate } from 'iscommon/i18n/utils';

import './Header.less';

const LanuagePopup: React.FC<any> = (props) => {
  const { onHide } = props;
  const [lngValue, setLngValue] = useState<string>(GlobalConfig.lng);

  const changeLng = useCallback((lng: string) => {
    changeLanguage(lng);
    setLngValue(lng);
  }, []);

  useEffect(() => {
    i18n.on("languageChanged", (lng) => {
      GlobalUtils.setGlobalLng(lng);
    });
  }, []);

  return (
    <Popup visible position="right" bodyStyle={{ width: '100%' }}>
      <div className="lngPopup">
        <div className="lngHeader">
          <div className="lngBack" onClick={onHide}>
            <LeftOutline fontSize={20} />
          </div>
          {translate('Language')}
        </div>
        <div className="lngList">
          {LanguageList.map(({ id, text }) => (
            <div className="lngItem" style={{ color: id === lngValue ? '#0F80D9' : '' }} onClick={() => changeLng(id)} key={id}>
              {text}
            </div>
          ))}
        </div>
      </div>
    </Popup>
  );
};

export default LanuagePopup;
