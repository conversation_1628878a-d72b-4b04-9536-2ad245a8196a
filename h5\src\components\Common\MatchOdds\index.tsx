import Loading from '@/components/Loading';
import downIcon from 'iscommon/assets/images/odds-down.jpg';
import upIcon from 'iscommon/assets/images/odds-up.jpg';
import { oddsCompanyNameMaps } from 'iscommon/const/constant';
import React, { useState } from 'react';
import styles from './index.less';
import OddsPopup from './OddsPopup';
// import { getMatchStandings } from 'iscommon/api/match';

const MatchOddsTab = ({ currentList, onChangeType, type, radioItem, tableHeaderList }: any) => {
  const [visible, setVisible] = useState(false);
  const [popupData, setPopupData] = useState({});

  const openPopup = (info: any) => {
    if (info) {
      let newInfo = JSON.parse(JSON.stringify(info));
      // if (newInfo.odds) {
      //   newInfo.odds.push(newInfo.odds[0]);
      //   newInfo.odds.splice(0, 2);
      // }
      setPopupData(newInfo);
      setVisible(true);
    }
  };

  const { label: headerLabel, oddsValueIndexList } = tableHeaderList[type];

  console.log('match odds')

  return (
    <div className={styles.container}>
      <div className={styles.radio_container}>
        {radioItem.map((item: any) => (
          <span
            className={`${styles.radio_btn} ${type === item.value ? styles.active : ''}`}
            key={item.value}
            onClick={() => {
              onChangeType(item.value);
            }}
          >
            {item.label}
          </span>
        ))}
      </div>
      <Loading loading={false} isEmpty={currentList.length === 0}>
        <div className={styles.table_container}>
          <div className={styles.table_header_box}>
            <span className={styles.seat_box} />
            <div className={styles.common_center_box}>
              {headerLabel.map((item: any) => (
                <span className={`${styles.common_center_item_title} text-overflow`} key={item}>
                  {item}
                </span>
              ))}
            </div>
            <span className={styles.active_seat_box} />
          </div>
          {currentList.map((item: any) => (
            <div className={styles.content_container} key={item.companyId}>
              <div className={styles.content_item_box}>
                <div className={styles.content_company_item}>
                  <img src={oddsCompanyNameMaps[item.companyId]} alt="" style={{ width: 83, height: 25 }} />
                </div>
                <div className={styles.common_center_box} style={{ background: '#ffffff', color: 'black' }}>
                  {oddsValueIndexList.map((validIndex, i) => {
                    return (
                      <span key={i} className={styles.common_center_item}>
                        <span
                          style={{
                            color:
                              item.currentCompanyOdds?.odds[1]?.contrastList &&
                              item.currentCompanyOdds?.odds[1]?.contrastList[validIndex] === 'up'
                                ? 'green'
                                : item.currentCompanyOdds?.odds[1]?.contrastList &&
                                  item.currentCompanyOdds?.odds[1]?.contrastList[validIndex] === 'down'
                                ? 'red'
                                : '',
                          }}
                        >
                          {Number(item.currentCompanyOdds?.odds[1]?.oddsData[validIndex])?.toFixed(2)}
                        </span>
                        {item.currentCompanyOdds?.odds[1]?.contrastList &&
                        item.currentCompanyOdds?.odds[1]?.contrastList[validIndex] === 'up' ? (
                          <img className={styles.stats_img} src={upIcon} alt="up-icon" loading="lazy"></img>
                        ) : item.currentCompanyOdds?.odds[1]?.contrastList &&
                          item.currentCompanyOdds?.odds[1]?.contrastList[validIndex] === 'down' ? (
                          <img className={styles.stats_img} src={downIcon} alt="down-icon" loading="lazy"></img>
                        ) : null}
                      </span>
                    );
                  })}
                </div>
                <div
                  className={styles.active_seat_box}
                  onClick={() => openPopup(item.currentCompanyOdds)}
                >
                  <i className="icon iconfont iconjiantou" style={{ fontSize: 13, color: 'rgb(153, 153, 153)' }}></i>
                </div>
              </div>
            </div>
          ))}
        </div>
      </Loading>
      <OddsPopup visible={visible} closePopup={() => setVisible(false)} data={popupData} headerInfo={tableHeaderList[type]} />
    </div>
  );
};

export default React.memo(MatchOddsTab);
