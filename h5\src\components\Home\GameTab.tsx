/* eslint-disable react/no-unknown-property */

import { HomeGameTab } from 'iscommon/const/constant';
import { translate } from 'iscommon/i18n/utils';
import { inject, observer } from 'mobx-react';
import moment from 'moment';
import React, { useMemo, useState } from 'react';
import { Link } from 'umi';

import { CalendarPicker } from 'antd-mobile';
import { CalendarOutline } from 'antd-mobile-icons';

const CalendarBlock = React.lazy(() => import('../Common/Calendar'));

import './GameTab.less';
import { Space } from 'antd-mobile';

const TabLeftMap = [
  { key: HomeGameTab.All, name: 'All', },
  {
    key: HomeGameTab.Live,
    icon: <i className="iconfont icon-jinhangzhong ml-0" />,
    name: 'LIVE',
  },
  { key: HomeGameTab.Finished, name: 'Finished', },
  { key: HomeGameTab.Scheduled, name: 'Scheduled', },
];

const GameTab = inject('store')(
  observer((props: any) => {
    const { WebHome, WebConfig } = props.store;
    const { currentGameTab, date } = WebHome;
    const { currentLiveCount } = WebConfig;
    const sortByTime = translate('SortByTime');
    const [calendarVis, setCalendarVis] = useState<boolean>(false);
    const [calendarVisible, setCalendarVisible] = useState(false)
    const [selectedDate, setSelectedDate] = useState(moment());

    const dateText = useMemo(() => moment(date).format('DD'), [date]);

    const onConfirm = (date: any) => {
      WebHome.changeDate(date)
      setCalendarVisible(false);
    };

    return (
      <>
        <div className='game-tab-container'>
          <div className='game-tab-container-left-aligned'>
            {TabLeftMap.map((item) => {
              return (
                <div
                  className={`tab-item ${item.key === currentGameTab ? (item.key == 0 ? 'activelive' : 'active') : ''}`}
                  key={item.key}
                  onClick={() => {
                    WebHome.switchHomeGameTab(item.key);
                  }}
                >
                  {item.icon || ''}
                  <span className="tab-item-text">{translate(item.name)}</span>
                  {/* {!!item.count && !!currentLiveCount?.liveCount && <span className="total">({currentLiveCount.liveCount})</span>} */}
                </div>
              );
            })}
          </div>  
          {currentGameTab !== HomeGameTab.Live && (
            <CalendarOutline className='header-calender-icon' onClick={() => setCalendarVisible(true)}/>
          )}
        </div>
        {currentGameTab !== HomeGameTab.Live && (
          <CalendarPicker 
            className='game-tab-calendar-picker'
            visible={calendarVisible}
            selectionMode="single"
            onConfirm={onConfirm}
            onClose={() => setCalendarVisible(false)}
          />
        )}
      </>
    );
  }),
);

export default GameTab;
