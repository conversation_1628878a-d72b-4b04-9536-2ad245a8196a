// .gameCategory {
//   width: 100%;
//   height: 54px;
//   background: #000;
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
//   font-size: 24px;
//   box-sizing: border-box;
//   padding: 0 12px;
//   color: #999;
//   cursor: pointer;

//   > div {
//     display: flex;
//     height: 100%;
//     align-items: center;
//   }

//   .cnameContainer {
//     flex: 3;
//     overflow: hidden;
//   }

//   .countContainer {
//     flex: 1;
//     justify-content: space-between;

//     .count {
//       width: 100px;
//     }

//     .touchArea {
//       padding: 20px;
//     }
//   }

//   .squareLogo {
//     width: 28px;
//     height: 28px;
//     background-size: contain;
//     margin: 0 3px;
//   }

//   .countryName {
//     color: #999999;
//   }
// }

// .gameList {
//   border-top: 2px solid #e3e3e3;
//   border-bottom: 2px solid #e3e3e3;
//   background-color: #fff;

//   .listItem {
//     display: flex;
//     height: 121px;
//     border-top: 2px solid #e3e3e3;

//     div {
//       align-items: center;
//       justify-content: center;
//     }

//     .listLeft {
//       width: 118px;
//       display: flex;
//       flex-direction: column;
//       color: #999999;
//       font-size: 18px;
//     }

//     .listRight {
//       width: 105px;
//       display: flex;
//       flex-direction: column;
//       font-weight: bolder;
//       justify-content: space-around;
//       box-sizing: border-box;
//       font-size: 16px;
//       color: #999999;

//       .odd {
//         height: 30px;
//       }
//     }

//     .listContent {
//       flex: 1;
//       border-left: 1px solid #e3e3e3;
//       border-right: 1px solid #e3e3e3;
//       display: flex;
//       flex-direction: row;
//       justify-content: space-between;
//       align-items: center;
//       padding: 0 14px;
//       overflow: hidden;

//       .vsCountry {
//         display: flex;
//         flex-direction: column;
//         justify-content: space-around;
//         align-items: flex-start;
//         flex: 1;
//         overflow: hidden;

//         .vsCountryItem {
//           display: flex;
//           overflow: hidden;
//           width: 100%;
//           justify-content: flex-start;
//           height: 40%;

//           .teamName {
//             font-size: 26px;
//             color: #333333;
//             margin-right: 6px;
//           }

//           .squareLogo {
//             width: 32px;
//             height: 32px;
//             background-size: contain;
//             margin-right: 14px;
//           }

//           .card {
//             padding: 0 5px;
//             border-radius: 2px;
//             margin-left: 3px;
//             line-height: 34px;
//             color: #fff;

//             &.red {
//               background-color: #c1272d;
//             }

//             &.yellow {
//               background-color: #ffa830;
//             }
//           }
//         }
//       }

//       .rating {
//         line-height: 44px;
//         padding: 0 6px;
//         background-color: red;
//         border-radius: 4px;
//         color: #fff;
//       }
//     }

//     .listRight {
//       font-size: 24px;

//       .bold {
//         color: #333;
//         font-weight: bolder;
//       }
//     }
//   }

//   :first-child {
//     border-top: none;
//   }

//   .ing {
//     color: #c1272d;
//   }
// }


.player-game-category-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;

    .player-month-btn {
      display: block;
      background: transparent;
      color: #fff;
      border-radius: 32px;

      .btn-content {
        display: flex; 
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .btn-value {
          white-space: nowrap;   
          overflow: hidden;      
          text-overflow: ellipsis;
          flex-grow: 1;
          margin-right: 8px;
        }

        .down-icon {
          flex-shrink: 0;  
          margin-left: auto;
        }
      }
    }
  }

  .container-body {
    background: #1e1e1e;
    padding: 10px;

    .match-card {
      display: flex;
      flex-direction: column;
      align-items: center;
      border-bottom: 1px solid #fff;
      padding: 10px;

      .match-card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        width: 100%;
        
        .competition-time,
        .competition-name {
          font-size: 20px;
          color: #C0C5C9;
        }
      }

      .match-card-body {
        display: flex;
        align-items: center;
        width: 100%;

        .team-container {
          display: flex;
          flex-direction: column;
          width: 50%;

          .team-section {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 5px 0px;
  
            .team-icon {
              width: 40px;
              height: 40px;
              border-radius: 50%;
              margin-right: 10px;
              overflow: hidden;
              background-size: cover;
            }
  
            .team-name,
            .team-score {
              font-size: 20px;
              color: #fff;
            }
          }
        }

        .match-info {
          margin-left: 5%;
          width: 45%;
          display: flex;
          align-items: center;
          justify-content: flex-end;

          .goal-container,
          .assist-container {
            margin-right: 10px;

            .goal-incident,
            .assist-incident {
              margin-right: 5px;
            }
          }

          .match-time {
            font-size: 20px;
            color: #fff;
            font-weight: bold;
            margin-right: 10px;
          }

          .player-rating-badge {
            margin: 0px 10px;

            .adm-badge-content {
              font-size: 20px;
            }
          }
        }
      }
    }
  }
}