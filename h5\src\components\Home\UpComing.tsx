import { inject, observer } from 'mobx-react';
import React, { useEffect, useState } from 'react';
import LazyLoad from 'react-lazyload';

import { getUpComingList } from 'iscommon/api/home';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

import GameCategory from './GameCategory';
import HomeLoading from './HomeLoading';

import './UpComing.less';
const splitCount = 11;
const MaxMatchItem = [4, 8];

const UpComing = inject('store')(
  observer((props: any) => {
    const { WebHome } = props.store;
    const { upcomingCompetitions } = WebHome;
    const labelMaps = useTranslateKeysToMaps(['UpcomingMatches']);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
      getUpComingList().then(({ competitions }: any) => {
        requestAnimationFrame(() => {
          WebHome.setUpComingCompetitions(competitions);
        });
        setLoading(false);
      });
    }, [WebHome]);

    return (
      <>
        <div className="home-upcoming-container">
          <i className="icon iconfont icon-shaozi" style={{ marginRight: '10px' }} />
          {labelMaps.UpcomingMatches}
        </div>
        {loading ? (
          <HomeLoading />
        ) : (
          <>
            <LazyLoad height={800} offset={10}>
              {upcomingCompetitions.slice(0, MaxMatchItem[0]).map((item: any, index: number) => {
                return <GameCategory key={item.competitionId + index} competition={item} />;
              })}
            </LazyLoad>
            {upcomingCompetitions.length > MaxMatchItem[0] && (
              <LazyLoad height={800} offset={10}>
                {upcomingCompetitions.slice(MaxMatchItem[0], MaxMatchItem[1]).map((item: any, index: number) => {
                  return <GameCategory key={item.competitionId + index} competition={item} />;
                })}
              </LazyLoad>
            )}
            {upcomingCompetitions.length > MaxMatchItem[1] && (
              <LazyLoad height={800} offset={10}>
                {upcomingCompetitions.slice(MaxMatchItem[1], upcomingCompetitions.length).map((item: any, index: number) => {
                  return <GameCategory key={item.competitionId + index} competition={item} />;
                })}
              </LazyLoad>
            )}
          </>
        )}
      </>
    );
  }),
);

export default React.memo(UpComing, () => true);
