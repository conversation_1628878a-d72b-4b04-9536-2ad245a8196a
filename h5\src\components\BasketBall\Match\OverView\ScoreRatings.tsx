import { getMatchLineup } from 'iscommon/api/match';
import { DefaultUserIcon } from 'iscommon/const/constant';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { translate } from 'iscommon/i18n/utils';
import { MatchTabH5 } from 'iscommon/mobx/modules/match';
import { formatRateColor } from 'iscommon/utils/dataUtils';
import { inject, observer } from 'mobx-react';
import { useCallback, useEffect, useState } from 'react';
import { history } from 'umi';
import './ScoreRatings.less';

const ScoreRatings = inject('store')(
  observer((props: any) => {
    const {
      store: { Match },
    } = props;
    const { matchId } = Match;
    const [awayUser, setAwayUser] = useState<any>(null);
    const [homeUser, setHomeUser] = useState<any>(null);
    const AiScoreRatingsText = translate('IGScoreRatings');
    const click = useCallback(() => {
      history.replace(GlobalUtils.getPathname(PageTabs.match, matchId, MatchTabH5.h2h));
    }, [matchId]);

    useEffect(() => {
      if (matchId) {
        getMatchLineup({ matchId }).then((res: any) => {
          if (res?.homeLineup && res?.awayLineup) {
            const { homeLineup, awayLineup } = res;
            const homeUser = [].concat(homeLineup[0], homeLineup[1]).sort((a: any, b: any) => {
              return Number(b.rating) - Number(a.rating);
            })[0];
            const awayUser = [].concat(awayLineup[0], awayLineup[1]).sort((a: any, b: any) => {
              return Number(b.rating) - Number(a.rating);
            })[0];
            setHomeUser(homeUser);
            setAwayUser(awayUser);
          }
        });
      }
    }, [matchId]);

    if (!homeUser || !awayUser) return null;
    return (
      <div className="ScoreRatings" onClick={click}>
        <div className="user">
          <img src={homeUser.logo || DefaultUserIcon} alt="" />
          <span>{homeUser.name}</span>
          {homeUser.rating > 0 && (
            <div className="star" style={{ backgroundColor: formatRateColor(homeUser.rating) }}>
              <i className="icon iconfont iconstar" style={{ color: '#fff' }}></i>
              <span data-v-856b50f0="">{homeUser.rating}</span>
            </div>
          )}
        </div>
        <div className="rtitle">
          <div>{AiScoreRatingsText}</div>
          <div className="goL" data-v-856b50f0="">
            {'>'}
          </div>
        </div>
        <div className="user right">
          <img src={awayUser.logo || DefaultUserIcon} alt="" />
          <span>{awayUser.name}</span>
          {awayUser.rating > 0 && (
            <div className="star" style={{ backgroundColor: formatRateColor(homeUser.rating) }}>
              <i className="icon iconfont iconstar" style={{ color: '#fff' }}></i>
              <span data-v-856b50f0="">{awayUser.rating}</span>
            </div>
          )}
        </div>
      </div>
    );
  }),
);

export default ScoreRatings;
