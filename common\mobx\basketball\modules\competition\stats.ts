// @ts-nocheck
import { makeAutoObservable } from "mobx";
import moment from "moment";
import { translate } from "iscommon/i18n/utils";
// import { getFavorites } from "../../api/home";

// function WebHome() {
//   this.currentGameTab = HomeGameTab.All;
//   makeAutoObservable(this)
// }

// WebHome.prototype.switchHomeGameTab = function(nextTab) {
//   this.currentGameTab = nextTab
// }

class StatsStore {
  // currentRanksTab = "All";
  constructor() {
    // 0-Live， 1-ALL, 2-Finished, 3-Scheduled
    this.currentKeyType = "Players";
    this.currentTeam = "";
    this.currentCategory = "goals";
    this.currentRound = "6";
    makeAutoObservable(this);
    // getFavorites().then((ids) => (this.favoriteMatches = ids));
  }

  selectCurrentCategory(nextValue) {
    this.currentCategory = nextValue;
  }

  selectCurrentRound(nextValue) {
    this.currentRound = nextValue;
  }

  switchKeyType(nextTab) {
    this.currentKeyType = nextTab;
  }

  selectCurrentTeam(nextValue) {
    this.currentTeam = nextValue;
  }
}

export default StatsStore;
