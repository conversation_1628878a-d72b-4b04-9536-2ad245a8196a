import { getScopeSelector, getCompetitionStatsPlayer } from "iscommon/api/basketball/basketball-competition";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { inject, observer } from "mobx-react";
import { useState, useEffect, useMemo } from "react";
import './StatsCard.less'
import { <PERSON><PERSON>, <PERSON>er } from "antd-mobile";
import { DownOutline } from 'antd-mobile-icons'
import { CompetitionScopeMap } from "iscommon/mobx/basketball/modules/competition";
import Loading from "@/components/Loading";
import TeamStatsTable from "./StatsTable";

interface Props {
  store?: any;
}

const TeamStatsCard = inject('store')(
  observer((props: Props) => {
    const {
      store: {
        Basketball: {
          Team : { teamId, teamHeaderInfo, teamSeason }
        },
      }
    } = props;

    const perOrAllList = [
      { id: '1', label: 'Per Game', value: 'Per Game',},
      { id: '2', label: 'All', value: 'All',},
    ];

    const competitionId = teamHeaderInfo?.competitions?.[0]?.id;

    const [tablesList, setTablesList] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [seasonId, setCurrentSeasonId] = useState<string>('');
    const [seasonList, setSeasonList] = useState<any>([]);
    const [curScope, setCurScope] = useState<number>();
    const [scopeList, setScopeList] = useState<any>([]);
    const [perOrAllId, setPerOrAllId] = useState<string>('');
    const [seasonVisible, setSeasonVisible] = useState(false);
    const [stageVisible, setStageVisible] = useState(false);
    const [gameVisible, setGameVisible] = useState(false)
    const [selectedScope, setSelectedScope] = useState<string | undefined>(undefined);
    const [selectedLabel, setSelectedLabel] = useState<string>(perOrAllList[1].label);

    const labelMaps = useTranslateKeysToMaps([
      'Stats',
      'Preseason',
      'RegularSeason',
      'Playoffs',
      'Season',
      'Qualifier',
      'GroupStage',
    ]);

    useEffect(() => {
      if (teamSeason && teamSeason.length > 0) {
        const currentSeason = teamSeason.find((item) => item.isCurrent === 1);
        setSeasonList(teamSeason);
        setCurrentSeasonId(currentSeason?.id);
      }
    }, [teamSeason]);

    useEffect(() => {
      if (competitionId && seasonId) {
        setTablesList([]);
        getScopeSelector({
          competitionId,
          seasonId,
        }).then(({ curScope, scopes }: any) => {
          setScopeList(scopes || []);
          setCurScope(curScope);
        });
      }
    }, [competitionId, seasonId]);

    useEffect(() => {
      if (competitionId && seasonId && curScope && teamId) {
        setLoading(true);
        getCompetitionStatsPlayer({
          competitionId,
          seasonId,
          scope: curScope,
          teamId,
        }).then(({ list }: any) => {
          if (list.length > 0) {
            setTablesList(list);
          }
          setLoading(false);
        });
      }
    }, [competitionId, curScope, seasonId, teamId]);

    const ptablesList = useMemo(() => {
      if (perOrAllId === '1') {
        return tablesList.map((item) => {
          if (item.court > 0) {
            return {
              ...item,
              minutesPlayed: item.minutesPlayed ? (item.minutesPlayed / item.court).toFixed(1) : '-',
              points: (item.points / item.court).toFixed(1),
              fieldGoalsScored: (item.fieldGoalsScored / item.court).toFixed(1),
              fieldGoalsTotal: (item.fieldGoalsTotal / item.court).toFixed(1),
              threePointsScored: (item.threePointsScored / item.court).toFixed(1),
              threePointsTotal: (item.threePointsTotal / item.court).toFixed(1),
              freeThrowsScored: (item.freeThrowsScored / item.court).toFixed(1),
              freeThrowsTotal: (item.freeThrowsTotal / item.court).toFixed(1),
              offensiveRebounds: (item.offensiveRebounds / item.court).toFixed(1),
              defensiveRebounds: (item.defensiveRebounds / item.court).toFixed(1),
              rebounds: (item.rebounds / item.court).toFixed(1),
              assists: (item.assists / item.court).toFixed(1),
              steals: (item.steals / item.court).toFixed(1),
              blocks: (item.blocks / item.court).toFixed(1),
              turnovers: (item.turnovers / item.court).toFixed(1),
              totalFouls: (item.totalFouls / item.court).toFixed(1),
            };
          } else {
            return item;
          }
        });
      } else {
        return tablesList;
      }
    }, [perOrAllId, tablesList]);

    const handleSeasonConfirm = (value: any) => {
      setCurrentSeasonId(value[0]);
      setSeasonVisible(false);
    };

    useEffect(() => {
      if (scopeList.length > 0 && !curScope) {
        const defaultScope = scopeList[0]; 
        const defaultScopeLabel = labelMaps[CompetitionScopeMap[defaultScope]];
        setSelectedScope(defaultScopeLabel);
        setCurScope(defaultScope); 
      }
    }, [scopeList, curScope]);

    const handleScopeConfirm = (selectedScopeValue: any) => {
      const scopeLabel = labelMaps[CompetitionScopeMap[selectedScopeValue[0]]];
      setSelectedScope(scopeLabel); 
      setCurScope(selectedScopeValue[0]);
      setStageVisible(false); 
    };

    const handleGameConfirm = (value: any) => {
      const selectedItem = perOrAllList.find((item: any) => item.id === value[0]);
      if (selectedItem) {
        setPerOrAllId(selectedItem.id); 
        setSelectedLabel(selectedItem.label);
      }
      setGameVisible(false); 
    };

    return (
      <div className="basketball-team-stats-container">
        <div className="container-title">{labelMaps.Stats}</div>
        <div className="container-body">
          <div className="picker-container">
            <>
              <Button className='custom-btn season' size="small" onClick={() => setSeasonVisible(true)}>
                <span className='btn-content'>
                  <span className="btn-value">{seasonList.find((item: any) => item.id === seasonId)?.year &&
                    (() => {
                      const year = seasonList.find((item: any) => item.id === seasonId)?.year;
                      const [startYear, endYear] = year.split('-').map((year: any) => year.slice(-2));
                      return `${startYear}-${endYear}`;
                    })()
                  }</span>
                  <DownOutline className='down-icon'/>
                </span>
              </Button>
              <Picker
                columns={[
                  seasonList.map((item: any) => ({
                    key: item.id, 
                    label: item.year,
                    value: item.id,
                  }))
                ]}
                visible={seasonVisible}
                onConfirm={handleSeasonConfirm}
                onClose={() => setSeasonVisible(false)}
              />
            </>
            <>
              <Button className='custom-btn stage' size="small" onClick={() => setStageVisible(true)}>
                <span className='btn-content'>
                  <span className="btn-value">{selectedScope}</span>
                  <DownOutline className='down-icon'/>
                </span>
              </Button>
              <Picker
                columns={[
                  scopeList.map((item: any) => ({
                    key: item,
                    label: labelMaps[CompetitionScopeMap[item]],
                    value: item
                  }))
                ]}
                visible={stageVisible}
                onConfirm={handleScopeConfirm}
                onClose={() => setStageVisible(false)}
              />
            </>
            <>
              <Button className='custom-btn game' size="small" onClick={() => setGameVisible(true)}>
                <span className='btn-content'>
                  <span className="btn-value">{selectedLabel}</span>
                  <DownOutline className='down-icon'/>
                </span>
              </Button>
              <Picker
                columns={[
                  perOrAllList.map((item: any) => ({
                    key: item.id,
                    label: item.label,
                    value: item.id,
                  }))
                ]}
                visible={gameVisible}
                onConfirm={handleGameConfirm}
                onClose={() => setGameVisible(false)}
              />
            </>
          </div>
          <Loading loading={loading} isEmpty={tablesList.length === 0}>
            <TeamStatsTable data={ptablesList} />
          </Loading>
        </div>
      </div>
    )
  }),
);

export default TeamStatsCard;