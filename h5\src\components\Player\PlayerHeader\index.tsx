import { Image } from 'antd-mobile';
import { inject, observer } from 'mobx-react';
import React from 'react';

import styles from './index.less';
import './index.less'
import { FallbackImage, FallbackPlayerImage } from 'iscommon/const/icon';
import { translate } from 'iscommon/i18n/utils';

interface Props {
  store?: any;
}

const PlayerHeader: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: { Player },
    } = props;
    const { playerHeaderInfo } = Player;

    return (
      <div className='common-player-header-container'>
        <div className='player-container'>
          <img className='player-icon' src={playerHeaderInfo?.player?.logo || FallbackPlayerImage} alt={playerHeaderInfo?.player?.name}/>
          <div className='player-info'>
            <div className='player-name-container'>
              <span className='player-name'>{playerHeaderInfo?.player?.name}</span>
              {/* <FavoriteIcon isMatch={false} competition={{ ...competition, curStageId: currentSeason.id }}/> */}
            </div>
            <div className='player-detail'>
              <img className='team-icon' src={playerHeaderInfo?.team?.logo || FallbackImage} alt={playerHeaderInfo?.team?.name}/>
              <span className='team-name'>{playerHeaderInfo?.team?.name}</span>
            </div>
          </div>
        </div>
        <div className='value-container'>
          <div className='value-container-title'>{translate('MarketValue')}</div>
          <div className='value-container-body'>{`${playerHeaderInfo?.player?.marketValueCurrency} ${(playerHeaderInfo?.player?.marketValue || 0) / 1000000}M`}</div>
          {/* <div>{playerHeaderInfo?.player?.marketValue ? `$${(playerHeaderInfo?.player?.salary / 1000000).toFixed(2)}M` : '-'`}</div> */}
        </div>
      </div>
      // <div className={styles.container}>
      //   <Image className={styles.logo} src={playerHeaderInfo?.player?.logo} fit="contain" />
      //   <div className={styles.name}>{playerHeaderInfo?.player?.name}</div>
      //   <div className={styles.info}>
      //     <Image className={styles.countryLogo} src={playerHeaderInfo?.player?.countryDto?.logo} fit="cover" />
      //     <div className={styles.country}>{playerHeaderInfo?.player?.countryDto?.name} /&nbsp;</div>
      //     <div className={styles.country}>{playerHeaderInfo?.team?.name}</div>
      //     <div className={styles.country}>
      //       {playerHeaderInfo?.player?.position ? ` / ${playerHeaderInfo?.player?.position}` : ''}
      //     </div>
      //   </div>
      // </div>
    );
  }),
);

export default PlayerHeader;
