
import igsRequest from "iscommon/request/instance";
import { getArrayFromString } from "../../utils";

/**
 * @param {{ teamId: string; size: number }}
 * @returns {Promise<any>}
 */
 export const getMatchRecentList = async ({ teamId, size = 30 } = {}) => {
  try {
    const result = await igsRequest.post("match/analysis/recent", { teamId, size });
    if(result.matches) {
      for(let v of result.matches) {
        const scores = getArrayFromString(v.scores) || {}
        v['calculatedHomeScore'] = scores.ft ? parseInt(scores.ft[0]) : 0 || 0
        v['calculatedAwayScore'] = scores.ft ? parseInt(scores.ft[1]) : 0 || 0
        const { name, logo, id } = v.uniqueTournament || {};
        v.competition = {
          name, logo, id
        }
      }
    }
    return result
  } catch (e) {
    console.log("getMatchRecentList error:", e);
    return null;
  }
};

/**
 * @param {{ homeTeamId: string; awayTeamId: string; size: number }}
 * @returns {any}
 */
export const getMatchHistoryList = async ({
  homeTeamId,
  awayTeamId,
  size = 30,
} = {}) => {
  try {
    const result = await igsRequest.post("match/analysis/history", {
      homeTeamId,
      awayTeamId,
      size,
    });
    if(result.matches) {
      for(let v of result.matches) {
        const scores = getArrayFromString(v.scores) || {}
        v['calculatedHomeScore'] = scores.ft ? parseInt(scores.ft[0]) : 0 || 0
        v['calculatedAwayScore'] = scores.ft ? parseInt(scores.ft[1]) : 0 || 0
        const { name, logo, id } = v.uniqueTournament || {};
        v.competition = {
          name, logo, id
        }
      }
    }
    return result
  } catch (e) {
    console.log("getMatchRecentList error:", e);
    return null;
  }
};

/**
 * @param {{ teamId: string; size: number }}
 * @returns {any}
 */
export const getMatchFutureList = async ({ teamId, size = 5 } = {}) => {
  try {
    const result = await igsRequest.post("match/analysis/future", { teamId, size });
    if(result.matches) {
      for(let v of result.matches) {
        const { name, logo, id } = v.uniqueTournament || {};
        v.competition = {
          name, logo, id
        }
      }
    }
    return result
  } catch (e) {
    console.log("getMatchRecentList error:", e);
    return null;
  }
};
