import * as echarts from 'echarts'
import 'echarts/lib/chart/line'
import './TeamResult.less'
import { useEffect } from 'react'

const TeamResult = () => {
  const initChart = () => {
    // 基于准备好的dom，初始化echarts实例
    let myChart = echarts.init(document.getElementById('main'))
    // if( myChart === undefined){

    // }
    const option = {
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: [],
      },
      grid: {
        x: 40,
        x2: 10,
        y: 20,
        y2: 20,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: ['1', '2', '3', '4', '5', '6'],
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          type: 'line',
          stack: 'Total',
          data: [100, 100, 100, 100, 50, 100],
        },
      ],
    }
    // 绘制图表，option设置图表格式及源数据
    myChart.setOption(option)
  }

  useEffect(() => {
    initChart()
  }, [])

  return <div className="teamResult" id="main"></div>
}

export default TeamResult
