import { Button, Carousel, Col, notification, Row } from 'antd';
import { subscribeHomeData, unsubscribeHomeData } from 'iscommon/mqtt/football/home';

import AdBanner from '@/components/Common/AdBanner';
import AllGames from '@/components/Home/AllGames';
import goalAudio from 'iscommon/events/goalAudio';
import { HomeOddsTrendCacheMaps } from 'iscommon/const/home';
import { isPluginMode } from '@/layouts/plugin';
import LeftGames from '@/components/Home/LeftGames';
import PageBreadcrumb from '@/components/Common/PageBreadcrumb';
import PluginHome from '@/pages/Plugin/pluginHome';
import styles from './index.less';
import { useCrumbList } from 'iscommon/hooks/content';
import { useEffect } from 'react';
import WidgetBox from '@/components/Common/Widget';

const FootballHome = () => {
  const { crumbs } = useCrumbList();
  const pluginMode = isPluginMode(); // ✅ detect plugin mode

  useEffect(() => {
    window.gtag('event', 'pv', { platform: 'web', sport: 'football', page_type: 'home' });
    // window.gtag('event', 'pv_football', { platform: 'web', football_page_type: 'home' });
    subscribeHomeData();

    // @ts-ignore
    goalAudio.init((matchInfo: any = null) => {
      const { homeTeam, awayTeam, homeScore, awayScore } = matchInfo || {};
      notification.open({
        message: (
          <>
            {homeTeam?.name}{' '}
            <span style={{ color: '#c1272d', fontWeight: 'bold' }}>
              {homeScore}-{awayScore}
            </span>{' '}
            {awayTeam?.name}
          </>
        ),
      });
    });

    return () => {
      unsubscribeHomeData();
      goalAudio.pause();
      try {
        // @ts-ignore
        if (HomeOddsTrendCacheMaps) HomeOddsTrendCacheMaps = {};
      } catch (e) {}
    };
  }, []);

  console.log('FootballHome - plugin mode:', pluginMode)
  console.log('FootballHome - URL search params:', window.location.search)
  console.log('FootballHome - isplugin param:', new URLSearchParams(window.location.search).get('isplugin'))

  return (
    <div className={styles.container}>
      <PageBreadcrumb crumbs={crumbs} />
      {pluginMode ? (
        <div id="ig-free-plugin-card">
          <PluginHome />
          <WidgetBox hidden pluginId="ig-free-plugin-card" />
        </div>
      ) : (
        <div className={styles.layout}>
          <LeftGames />
          <div className={styles.content} id="ig-free-plugin-card">
            <AdBanner position='homepage_v2' style={{ marginBottom: 10 }} />
            <AllGames />
            <WidgetBox hidden pluginId="ig-free-plugin-card" />
          </div>
        </div>
      )}
    </div>
  );
};

export default FootballHome;
