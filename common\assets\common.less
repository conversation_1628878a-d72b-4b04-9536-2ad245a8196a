.twinkleScore:after {
  animation-name: twinkle;
  animation-duration: 1.2s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  animation-fill-mode: forwards;
  animation-delay: 0s;
  content: "'";
  opacity: 0;
}

.bg-fff {
  background-color: #fff !important;
}

.color-999 {
  color: #999 !important;
}

.color-red {
  color: #c72a1d !important;
}

.color-fff {
  color: #fff !important;
}

.color-yellow {
  color: #feb82d !important;
}

.color-blue {
  color: #2196F3;
}

.color-333 {
  color: #333 !important;
}

.color-666 {
  color: #666;
}

.color-999 {
  color: #999;
}

.text-overflow {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bg-f4 {
  background-color: #f4f5f8;
}

.flex-row-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-1 {
  flex: 1 !important;
}

.flex-2 {
  flex: 2 !important;
}

.flex-3 {
  flex: 3 !important;
}

.flex-4 {
  flex: 4 !important;
}

.flex-5 {
  flex: 5 !important;
}

.flex-6 {
  flex: 6 !important;
}

.flex-7 {
  flex: 7 !important;
}

.flex-8 {
  flex: 8 !important;
}

* {
  background-repeat: no-repeat !important;
  background-position: center;
}

.w-18 {
  width: 18px !important;
  flex-shrink: 0;
}

.h-18 {
  height: 18px !important;
  flex-shrink: 0;
}

.f-s-12 {
  font-size: 12px !important;
}

.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.w {
  color: #5db400 !important;
}

.d {
  color: #ffba5a !important;
}
.l {
  color: #e74c5b !important;
}

.color-ff4747 {
 color: #ff4747!important;
}


.myBlue {
  color: #0f80da;
}

.iconfont {
  margin-left: 5px;
}

.color-c1272d {
  color: #c1272d !important;
}

.linkComp {
  margin-left: 5px;
  font-weight: 500;
  color: #333;
}

.not-allow {
  cursor: not-allowed !important;
}

.flex-bettween {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.className {
  opacity: 1;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.w100 {
  width: 100%;
}

.hidden {
  opacity: 0;
}

.html, body {
  // font-family: Rubik-Regular,Rubik, MicrosoftYaHei, MicrosoftYaHei-Bold, PingFang SC;
  font-family: "Urbanist-Regular", sans-serif !important;

  .ant-card-head-title {
    font-weight: 500;
    // font-family: Rubik-Medium,Rubik-Regular,Rubik, MicrosoftYaHei, MicrosoftYaHei-Bold, PingFang SC;
    font-family: "Urbanist-Light, Urbanist-Regular";
  }
}

.font-300 {
  font-weight: 300;
  // font-family: Rubik-Light,Rubik-Regular,Rubik, MicrosoftYaHei, MicrosoftYaHei-Bold, PingFang SC;
  font-family: "Urbanist-Light, Urbanist-Regular";
}

.font-500 {
  font-weight: 500;
  // font-family: Rubik-Medium,Rubik-Regular,Rubik, MicrosoftYaHei, MicrosoftYaHei-Bold, PingFang SC;
  font-family: "Urbanist-Light, Urbanist-Regular",
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px
}

::-webkit-scrollbar-thumb {
  background: #ccc
}

@keyframes twinkle {
  30% {
    opacity: 1
  }

  70% {
    opacity: 1
  }
}

@-webkit-keyframes twinkle {
  30% {
    opacity: 1
  }

  70% {
    opacity: 1
  }
}
.font-500 {
  font-family: Rubik-Medium, Rubik;
  font-weight: 500;
}
.flex-right{
  justify-content: flex-end;
}
.text-left{
  text-align: left !important;
}
.text-right{
  text-align: right !important;
}