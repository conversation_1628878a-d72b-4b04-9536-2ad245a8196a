// .list_item_box {
//   :global {
//     .adm-list-item-content-main {
//       padding: 12px 0;
//     }
//   }
// }

// .list_item_style {
//   // padding: 11px 12px;
//   display: flex;
//   flex-direction: row;
//   align-items: center;
//   justify-content: space-between;
// }

// .list_item_left {
//   display: flex;
//   align-items: center;
// }

// .list_item_avatar {
//   width: 58px;
//   height: 58px;
//   border: 2px solid #dbdbdb;
//   border-radius: 50%;
// }

// .left_item_name_box {
//   display: flex;
//   flex-direction: column;
//   padding-left: 20px;
// }

// .left_item_name {
//   font-size: 24px;
// }

// .left_item_date {
//   font-size: 20px;
//   color: rgba(153, 153, 153, 1);
// }

// .list_item_right {
//   display: flex;
//   flex-direction: column;
//   color: rgba(15, 128, 218, 1);
//   justify-content: flex-end;
// }

// .list_item_right_transferTeam {
//   font-size: 24px;
//   display: flex;
//   flex-direction: row;
//   align-items: center;
//   justify-content: flex-end;
// }

// .list_item_right_transferType {
//   font-size: 20px;
//   text-align: right;
// }

.competition-transfer-list-container {
  .adm-list-item, .adm-list-item-content {
    border: none;
    padding-right: 0px;
  }

  .adm-list-default, .adm-list-body {
    border: none;
    background: transparent;
  }

  .transfer-list-container {
    padding: 0px 10px;
    background: #121212;
    border-bottom: 1px solid #2c2c2c;

    .player-logo {
      width: 58px;
      height: 58px;
      border-radius: 50%;
      margin: 5px 10px;
      overflow: hidden;
      object-fit: cover;
    }

    .transfer-player-detail {
      display: flex;
      justify-content: space-between;
      width: 100%;
      padding-right: 10px;

      .transfer-player-info {
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .player-name,
        .transfer-date {
          font-size: 20px;
          color: #fff;
        }
      }

      .transfer-player-team {
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        .transfer-team-container {
          display: flex;
          align-items: center;

          .transfer-team-logo {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 0px 10px;
            overflow: hidden;
            background-size: cover;
          }

          .transfer-team-name {
            font-size: 20px;
            color: #fff;
          }
        }

        .transfer-type {
          font-size: 20px;
          color: #fff;
        }
      }
    }
  }
}
