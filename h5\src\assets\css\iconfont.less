.fs-12 {
  font-size: 24px;
}

.fs-14 {
  font-size: 28px;
}

.fs-16 {
  font-size: 32px;
}

.fs-18 {
  font-size: 36px;
}

.fs-20 {
  font-size: 40px;
}

.fs-22 {
  font-size: 44px;
}

.fs-24 {
  font-size: 48px;
}

.fs-26 {
  font-size: 52px;
}

.fs-36 {
  font-size: 72px;
}

.fs-11 {
  font-size: 24px;
  transform: scale(0.9);
  transform-origin: left;
}

.fs-11-center {
  font-size: 24px;
  transform: scale(0.9);
  transform-origin: center;
}

.fs-10 {
  font-size: 24px;
  transform: scale(0.84);
  transform-origin: left;
}

.fs-10-center {
  font-size: 24px;
  transform: scale(0.84);
  transform-origin: center;
}

.fs-9 {
  font-size: 24px;
  transform: scale(0.75);
  transform-origin: left;
}

.fs-9-center {
  font-size: 24px;
  transform: scale(0.75);
  transform-origin: center;
}

.fs-8 {
  font-size: 24px;
  transform: scale(0.67);
  transform-origin: left;
}

.fs-8-center {
  font-size: 24px;
  transform: scale(0.67);
  transform-origin: center;
}

.ft-300 {
  font-weight: 300;
}

.ft-500 {
  font-weight: 500;
}

.vue-recycle-scroller {
  position: relative;
}

.vue-recycle-scroller.direction-vertical:not(.page-mode) {
  overflow-y: auto;
}

.vue-recycle-scroller.direction-horizontal:not(.page-mode) {
  overflow-x: auto;
}

.vue-recycle-scroller.direction-horizontal {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.vue-recycle-scroller__slot {
  -webkit-box-flex: 1;
  -ms-flex: auto 0 0;
  flex: auto 0 0;
}

.vue-recycle-scroller__item-wrapper {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}

.vue-recycle-scroller.ready .vue-recycle-scroller__item-view {
  position: absolute;
  top: 0;
  left: 0;
  will-change: transform;
}

.vue-recycle-scroller.direction-vertical .vue-recycle-scroller__item-wrapper {
  width: 100%;
}

.vue-recycle-scroller.direction-horizontal .vue-recycle-scroller__item-wrapper {
  height: 100%;
}

.vue-recycle-scroller.ready.direction-vertical .vue-recycle-scroller__item-view {
  width: 100%;
}

.vue-recycle-scroller.ready.direction-horizontal .vue-recycle-scroller__item-view {
  height: 100%;
}

.resize-observer[data-v-b329ee4c] {
  border: none;
  background-color: transparent;
  opacity: 0;
}

.resize-observer[data-v-b329ee4c],
.resize-observer[data-v-b329ee4c] object {
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 100%;
  pointer-events: none;
  display: block;
  overflow: hidden;
}

html {
  -webkit-tap-highlight-color: transparent;
}

body {
  background: #000;
  margin: 0;
  font-family: "Urbanist-Regular", sans-serif !important;
  // font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, Helvetica, Segoe UI, Arial, Roboto, PingFang SC,
  // Hiragino Sans GB, Microsoft Yahei, sans-serif;
}

a {
  text-decoration: none;
}

button,
input,
textarea {
  color: inherit;
  font: inherit;
}

[class*='van-']:focus,
a:focus,
button:focus,
input:focus,
textarea:focus {
  outline: 0;
}

ol,
ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.van-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.van-multi-ellipsis--l2 {
  -webkit-line-clamp: 2;
}

.van-multi-ellipsis--l2,
.van-multi-ellipsis--l3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
}

.van-multi-ellipsis--l3 {
  -webkit-line-clamp: 3;
}

.van-clearfix:after {
  display: table;
  clear: both;
  content: '';
}

[class*='van-hairline']:after {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  top: -50%;
  right: -50%;
  bottom: -50%;
  left: -50%;
  border: 0 solid #ebedf0;
  transform: scale(0.5);
}

.van-hairline,
.van-hairline--bottom,
.van-hairline--left,
.van-hairline--right,
.van-hairline--surround,
.van-hairline--top,
.van-hairline--top-bottom {
  position: relative;
}

.van-hairline--top:after {
  border-top-width: 1px;
}

.van-hairline--left:after {
  border-left-width: 1px;
}

.van-hairline--right:after {
  border-right-width: 1px;
}

.van-hairline--bottom:after {
  border-bottom-width: 1px;
}

.van-hairline--top-bottom:after,
.van-hairline-unset--top-bottom:after {
  border-width: 1px 0;
}

.van-hairline--surround:after {
  border-width: 1px;
}

@-webkit-keyframes van-slide-up-enter {
  0% {
    transform: translate3d(0, 100%, 0);
  }
}

@keyframes van-slide-up-enter {
  0% {
    transform: translate3d(0, 100%, 0);
  }
}

@-webkit-keyframes van-slide-up-leave {
  to {
    transform: translate3d(0, 100%, 0);
  }
}

@keyframes van-slide-up-leave {
  to {
    transform: translate3d(0, 100%, 0);
  }
}

@-webkit-keyframes van-slide-down-enter {
  0% {
    transform: translate3d(0, -100%, 0);
  }
}

@keyframes van-slide-down-enter {
  0% {
    transform: translate3d(0, -100%, 0);
  }
}

@-webkit-keyframes van-slide-down-leave {
  to {
    transform: translate3d(0, -100%, 0);
  }
}

@keyframes van-slide-down-leave {
  to {
    transform: translate3d(0, -100%, 0);
  }
}

@-webkit-keyframes van-slide-left-enter {
  0% {
    transform: translate3d(-100%, 0, 0);
  }
}

@keyframes van-slide-left-enter {
  0% {
    transform: translate3d(-100%, 0, 0);
  }
}

@-webkit-keyframes van-slide-left-leave {
  to {
    transform: translate3d(-100%, 0, 0);
  }
}

@keyframes van-slide-left-leave {
  to {
    transform: translate3d(-100%, 0, 0);
  }
}

@-webkit-keyframes van-slide-right-enter {
  0% {
    transform: translate3d(100%, 0, 0);
  }
}

@keyframes van-slide-right-enter {
  0% {
    transform: translate3d(100%, 0, 0);
  }
}

@-webkit-keyframes van-slide-right-leave {
  to {
    transform: translate3d(100%, 0, 0);
  }
}

@keyframes van-slide-right-leave {
  to {
    transform: translate3d(100%, 0, 0);
  }
}

@-webkit-keyframes van-fade-in {
  0% {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes van-fade-in {
  0% {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@-webkit-keyframes van-fade-out {
  0% {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

@keyframes van-fade-out {
  0% {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

@-webkit-keyframes van-rotate {
  0% {
    transform: rotate(0);
  }

  to {
    transform: rotate(1turn);
  }
}

@keyframes van-rotate {
  0% {
    transform: rotate(0);
  }

  to {
    transform: rotate(1turn);
  }
}

.van-fade-enter-active {
  -webkit-animation: van-fade-in 0.3s ease-out both;
  animation: van-fade-in 0.3s ease-out both;
}

.van-fade-leave-active {
  -webkit-animation: van-fade-out 0.3s ease-in both;
  animation: van-fade-out 0.3s ease-in both;
}

.van-slide-up-enter-active {
  -webkit-animation: van-slide-up-enter 0.3s ease-out both;
  animation: van-slide-up-enter 0.3s ease-out both;
}

.van-slide-up-leave-active {
  -webkit-animation: van-slide-up-leave 0.3s ease-in both;
  animation: van-slide-up-leave 0.3s ease-in both;
}

.van-slide-down-enter-active {
  -webkit-animation: van-slide-down-enter 0.3s ease-out both;
  animation: van-slide-down-enter 0.3s ease-out both;
}

.van-slide-down-leave-active {
  -webkit-animation: van-slide-down-leave 0.3s ease-in both;
  animation: van-slide-down-leave 0.3s ease-in both;
}

.van-slide-left-enter-active {
  -webkit-animation: van-slide-left-enter 0.3s ease-out both;
  animation: van-slide-left-enter 0.3s ease-out both;
}

.van-slide-left-leave-active {
  -webkit-animation: van-slide-left-leave 0.3s ease-in both;
  animation: van-slide-left-leave 0.3s ease-in both;
}

.van-slide-right-enter-active {
  -webkit-animation: van-slide-right-enter 0.3s ease-out both;
  animation: van-slide-right-enter 0.3s ease-out both;
}

.van-slide-right-leave-active {
  -webkit-animation: van-slide-right-leave 0.3s ease-in both;
  animation: van-slide-right-leave 0.3s ease-in both;
}

.van-overlay {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
}

.van-info {
  position: absolute;
  top: 0;
  right: 0;
  box-sizing: border-box;
  min-width: 16px;
  padding: 0 3px;
  color: #fff;
  font-weight: 500;
  font-size: 24px;
  font-family: Avenir-Heavy, PingFang SC, Helvetica Neue, Arial, sans-serif;
  line-height: 14px;
  text-align: center;
  background-color: #ee0a24;
  border: 1px solid #fff;
  border-radius: 16px;
  transform: translate(50%, -50%);
  transform-origin: 100%;
}

.van-info--dot {
  width: 8px;
  min-width: 0;
  height: 8px;
  background-color: #ee0a24;
  border-radius: 100%;
}

.van-sidebar-item {
  position: relative;
  display: block;
  box-sizing: border-box;
  padding: 20px 12px;
  overflow: hidden;
  color: #323233;
  font-size: 14px;
  line-height: 20px;
  word-wrap: break-word;
  background-color: #f7f8fa;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-sidebar-item:active {
  background-color: #f2f3f5;
}

.van-sidebar-item__text {
  position: relative;
  display: inline-block;
}

.van-sidebar-item:not(:last-child):after {
  border-bottom-width: 1px;
}

.van-sidebar-item--select {
  color: #323233;
  font-weight: 500;
}

.van-sidebar-item--select,
.van-sidebar-item--select:active {
  background-color: #fff;
}

.van-sidebar-item--select:before {
  position: absolute;
  top: 50%;
  left: 0;
  width: 4px;
  height: 16px;
  background-color: #ee0a24;
  transform: translateY(-50%);
  content: '';
}

.van-sidebar-item--disabled {
  color: #c8c9cc;
  cursor: not-allowed;
}

.van-sidebar-item--disabled:active {
  background-color: #f7f8fa;
}

@font-face {
  font-weight: 400;
  font-family: vant-icon;
  font-style: normal;
  font-display: swap;
  src: url(https://img.yzcdn.cn/vant/vant-icon-6ae417.woff2) format('woff2'),
  url(https://img.yzcdn.cn/vant/vant-icon-6ae417.woff) format('woff'),
  url(https://img.yzcdn.cn/vant/vant-icon-6ae417.ttf) format('truetype');
}

.van-icon {
  position: relative;
  font: normal normal normal 14px/1 vant-icon;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}

.van-icon,
.van-icon:before {
  display: inline-block;
}

.van-icon-add-o:before {
  content: '\F000';
}

.van-icon-add-square:before {
  content: '\F001';
}

.van-icon-add:before {
  content: '\F002';
}

.van-icon-after-sale:before {
  content: '\F003';
}

.van-icon-aim:before {
  content: '\F004';
}

.van-icon-alipay:before {
  content: '\F005';
}

.van-icon-apps-o:before {
  content: '\F006';
}

.van-icon-arrow-down:before {
  content: '\F007';
}

.van-icon-arrow-left:before {
  content: '\F008';
}

.van-icon-arrow-up:before {
  content: '\F009';
}

.van-icon-ascending:before {
  content: '\F00B';
}

.van-icon-audio:before {
  content: '\F00C';
}

.van-icon-award-o:before {
  content: '\F00D';
}

.van-icon-award:before {
  content: '\F00E';
}

.van-icon-bag-o:before {
  content: '\F00F';
}

.van-icon-bag:before {
  content: '\F010';
}

.van-icon-balance-list-o:before {
  content: '\F011';
}

.van-icon-balance-list:before {
  content: '\F012';
}

.van-icon-balance-o:before {
  content: '\F013';
}

.van-icon-balance-pay:before {
  content: '\F014';
}

.van-icon-bar-chart-o:before {
  content: '\F015';
}

.van-icon-bars:before {
  content: '\F016';
}

.van-icon-bell:before {
  content: '\F017';
}

.van-icon-bill-o:before {
  content: '\F018';
}

.van-icon-bill:before {
  content: '\F019';
}

.van-icon-birthday-cake-o:before {
  content: '\F01A';
}

.van-icon-bookmark-o:before {
  content: '\F01B';
}

.van-icon-bookmark:before {
  content: '\F01C';
}

.van-icon-browsing-history-o:before {
  content: '\F01D';
}

.van-icon-browsing-history:before {
  content: '\F01E';
}

.van-icon-brush-o:before {
  content: '\F01F';
}

.van-icon-bulb-o:before {
  content: '\F020';
}

.van-icon-bullhorn-o:before {
  content: '\F021';
}

.van-icon-calender-o:before {
  content: '\F022';
}

.van-icon-card:before {
  content: '\F023';
}

.van-icon-cart-circle-o:before {
  content: '\F024';
}

.van-icon-cart-circle:before {
  content: '\F025';
}

.van-icon-cart-o:before {
  content: '\F026';
}

.van-icon-cart:before {
  content: '\F027';
}

.van-icon-cash-back-record:before {
  content: '\F028';
}

.van-icon-cash-on-deliver:before {
  content: '\F029';
}

.van-icon-cashier-o:before {
  content: '\F02A';
}

.van-icon-certificate:before {
  content: '\F02B';
}

.van-icon-chart-trending-o:before {
  content: '\F02C';
}

.van-icon-chat-o:before {
  content: '\F02D';
}

.van-icon-chat:before {
  content: '\F02E';
}

.van-icon-checked:before {
  content: '\F02F';
}

.van-icon-circle:before {
  content: '\F030';
}

.van-icon-clear:before {
  content: '\F031';
}

.van-icon-clock-o:before {
  content: '\F032';
}

.van-icon-clock:before {
  content: '\F033';
}

.van-icon-close:before {
  content: '\F034';
}

.van-icon-closed-eye:before {
  content: '\F035';
}

.van-icon-cluster-o:before {
  content: '\F036';
}

.van-icon-cluster:before {
  content: '\F037';
}

.van-icon-column:before {
  content: '\F038';
}

.van-icon-comment-circle-o:before {
  content: '\F039';
}

.van-icon-comment-circle:before {
  content: '\F03A';
}

.van-icon-comment-o:before {
  content: '\F03B';
}

.van-icon-comment:before {
  content: '\F03C';
}

.van-icon-completed:before {
  content: '\F03D';
}

.van-icon-contact:before {
  content: '\F03E';
}

.van-icon-coupon-o:before {
  content: '\F03F';
}

.van-icon-coupon:before {
  content: '\F040';
}

.van-icon-credit-pay:before {
  content: '\F041';
}

.van-icon-cross:before {
  content: '\F042';
}

.van-icon-debit-pay:before {
  content: '\F043';
}

.van-icon-delete:before {
  content: '\F044';
}

.van-icon-descending:before {
  content: '\F045';
}

.van-icon-description:before {
  content: '\F046';
}

.van-icon-desktop-o:before {
  content: '\F047';
}

.van-icon-diamond-o:before {
  content: '\F048';
}

.van-icon-diamond:before {
  content: '\F049';
}

.van-icon-discount:before {
  content: '\F04A';
}

.van-icon-down:before {
  content: '\F04B';
}

.van-icon-ecard-pay:before {
  content: '\F04C';
}

.van-icon-edit:before {
  content: '\F04D';
}

.van-icon-ellipsis:before {
  content: '\F04E';
}

.van-icon-empty:before {
  content: '\F04F';
}

.van-icon-enlarge:before {
  content: '\F0E4';
}

.van-icon-envelop-o:before {
  content: '\F050';
}

.van-icon-exchange:before {
  content: '\F051';
}

.van-icon-expand-o:before {
  content: '\F052';
}

.van-icon-expand:before {
  content: '\F053';
}

.van-icon-eye-o:before {
  content: '\F054';
}

.van-icon-eye:before {
  content: '\F055';
}

.van-icon-fail:before {
  content: '\F056';
}

.van-icon-failure:before {
  content: '\F057';
}

.van-icon-filter-o:before {
  content: '\F058';
}

.van-icon-fire-o:before {
  content: '\F059';
}

.van-icon-fire:before {
  content: '\F05A';
}

.van-icon-flag-o:before {
  content: '\F05B';
}

.van-icon-flower-o:before {
  content: '\F05C';
}

.van-icon-free-postage:before {
  content: '\F05D';
}

.van-icon-friends-o:before {
  content: '\F05E';
}

.van-icon-friends:before {
  content: '\F05F';
}

.van-icon-gem-o:before {
  content: '\F060';
}

.van-icon-gem:before {
  content: '\F061';
}

.van-icon-gift-card-o:before {
  content: '\F062';
}

.van-icon-gift-card:before {
  content: '\F063';
}

.van-icon-gift-o:before {
  content: '\F064';
}

.van-icon-gift:before {
  content: '\F065';
}

.van-icon-gold-coin-o:before {
  content: '\F066';
}

.van-icon-gold-coin:before {
  content: '\F067';
}

.van-icon-good-job-o:before {
  content: '\F068';
}

.van-icon-good-job:before {
  content: '\F069';
}

.van-icon-goods-collect-o:before {
  content: '\F06A';
}

.van-icon-goods-collect:before {
  content: '\F06B';
}

.van-icon-graphic:before {
  content: '\F06C';
}

.van-icon-home-o:before {
  content: '\F06D';
}

.van-icon-hot-o:before {
  content: '\F06E';
}

.van-icon-hot-sale-o:before {
  content: '\F06F';
}

.van-icon-hot-sale:before {
  content: '\F070';
}

.van-icon-hot:before {
  content: '\F071';
}

.van-icon-hotel-o:before {
  content: '\F072';
}

.van-icon-idcard:before {
  content: '\F073';
}

.van-icon-info-o:before {
  content: '\F074';
}

.van-icon-info:before {
  content: '\F075';
}

.van-icon-invition:before {
  content: '\F076';
}

.van-icon-label-o:before {
  content: '\F077';
}

.van-icon-label:before {
  content: '\F078';
}

.van-icon-like-o:before {
  content: '\F079';
}

.van-icon-like:before {
  content: '\F07A';
}

.van-icon-live:before {
  content: '\F07B';
}

.van-icon-location-o:before {
  content: '\F07C';
}

.van-icon-location:before {
  content: '\F07D';
}

.van-icon-lock:before {
  content: '\F07E';
}

.van-icon-logistics:before {
  content: '\F07F';
}

.van-icon-manager-o:before {
  content: '\F080';
}

.van-icon-manager:before {
  content: '\F081';
}

.van-icon-map-marked:before {
  content: '\F082';
}

.van-icon-medal-o:before {
  content: '\F083';
}

.van-icon-medal:before {
  content: '\F084';
}

.van-icon-more-o:before {
  content: '\F085';
}

.van-icon-more:before {
  content: '\F086';
}

.van-icon-music-o:before {
  content: '\F087';
}

.van-icon-music:before {
  content: '\F088';
}

.van-icon-new-arrival-o:before {
  content: '\F089';
}

.van-icon-new-arrival:before {
  content: '\F08A';
}

.van-icon-new-o:before {
  content: '\F08B';
}

.van-icon-new:before {
  content: '\F08C';
}

.van-icon-newspaper-o:before {
  content: '\F08D';
}

.van-icon-notes-o:before {
  content: '\F08E';
}

.van-icon-orders-o:before {
  content: '\F08F';
}

.van-icon-other-pay:before {
  content: '\F090';
}

.van-icon-paid:before {
  content: '\F091';
}

.van-icon-passed:before {
  content: '\F092';
}

.van-icon-pause-circle-o:before {
  content: '\F093';
}

.van-icon-pause-circle:before {
  content: '\F094';
}

.van-icon-pause:before {
  content: '\F095';
}

.van-icon-peer-pay:before {
  content: '\F096';
}

.van-icon-pending-payment:before {
  content: '\F097';
}

.van-icon-phone-circle-o:before {
  content: '\F098';
}

.van-icon-phone-circle:before {
  content: '\F099';
}

.van-icon-phone-o:before {
  content: '\F09A';
}

.van-icon-phone:before {
  content: '\F09B';
}

.van-icon-photo-o:before {
  content: '\F09C';
}

.van-icon-photo:before {
  content: '\F09D';
}

.van-icon-photograph:before {
  content: '\F09E';
}

.van-icon-play-circle-o:before {
  content: '\F09F';
}

.van-icon-play-circle:before {
  content: '\F0A0';
}

.van-icon-play:before {
  content: '\F0A1';
}

.van-icon-plus:before {
  content: '\F0A2';
}

.van-icon-point-gift-o:before {
  content: '\F0A3';
}

.van-icon-point-gift:before {
  content: '\F0A4';
}

.van-icon-points:before {
  content: '\F0A5';
}

.van-icon-printer:before {
  content: '\F0A6';
}

.van-icon-qr-invalid:before {
  content: '\F0A7';
}

.van-icon-qr:before {
  content: '\F0A8';
}

.van-icon-question-o:before {
  content: '\F0A9';
}

.van-icon-question:before {
  content: '\F0AA';
}

.van-icon-records:before {
  content: '\F0AB';
}

.van-icon-refund-o:before {
  content: '\F0AC';
}

.van-icon-replay:before {
  content: '\F0AD';
}

.van-icon-scan:before {
  content: '\F0AE';
}

.van-icon-search:before {
  content: '\F0AF';
}

.van-icon-send-gift-o:before {
  content: '\F0B0';
}

.van-icon-send-gift:before {
  content: '\F0B1';
}

.van-icon-service-o:before {
  content: '\F0B2';
}

.van-icon-service:before {
  content: '\F0B3';
}

.van-icon-setting-o:before {
  content: '\F0B4';
}

.van-icon-setting:before {
  content: '\F0B5';
}

.van-icon-share:before {
  content: '\F0B6';
}

.van-icon-shop-collect-o:before {
  content: '\F0B7';
}

.van-icon-shop-collect:before {
  content: '\F0B8';
}

.van-icon-shop-o:before {
  content: '\F0B9';
}

.van-icon-shop:before {
  content: '\F0BA';
}

.van-icon-shopping-cart-o:before {
  content: '\F0BB';
}

.van-icon-shopping-cart:before {
  content: '\F0BC';
}

.van-icon-shrink:before {
  content: '\F0BD';
}

.van-icon-sign:before {
  content: '\F0BE';
}

.van-icon-smile-comment-o:before {
  content: '\F0BF';
}

.van-icon-smile-comment:before {
  content: '\F0C0';
}

.van-icon-smile-o:before {
  content: '\F0C1';
}

.van-icon-smile:before {
  content: '\F0C2';
}

.van-icon-star-o:before {
  content: '\F0C3';
}

.van-icon-star:before {
  content: '\F0C4';
}

.van-icon-stop-circle-o:before {
  content: '\F0C5';
}

.van-icon-stop-circle:before {
  content: '\F0C6';
}

.van-icon-stop:before {
  content: '\F0C7';
}

.van-icon-success:before {
  content: '\F0C8';
}

.van-icon-thumb-circle-o:before {
  content: '\F0C9';
}

.van-icon-thumb-circle:before {
  content: '\F0CA';
}

.van-icon-todo-list-o:before {
  content: '\F0CB';
}

.van-icon-todo-list:before {
  content: '\F0CC';
}

.van-icon-tosend:before {
  content: '\F0CD';
}

.van-icon-tv-o:before {
  content: '\F0CE';
}

.van-icon-umbrella-circle:before {
  content: '\F0CF';
}

.van-icon-underway-o:before {
  content: '\F0D0';
}

.van-icon-underway:before {
  content: '\F0D1';
}

.van-icon-upgrade:before {
  content: '\F0D2';
}

.van-icon-user-circle-o:before {
  content: '\F0D3';
}

.van-icon-user-o:before {
  content: '\F0D4';
}

.van-icon-video-o:before {
  content: '\F0D5';
}

.van-icon-video:before {
  content: '\F0D6';
}

.van-icon-vip-card-o:before {
  content: '\F0D7';
}

.van-icon-vip-card:before {
  content: '\F0D8';
}

.van-icon-volume-o:before {
  content: '\F0D9';
}

.van-icon-volume:before {
  content: '\F0DA';
}

.van-icon-wap-home-o:before {
  content: '\F0DB';
}

.van-icon-wap-home:before {
  content: '\F0DC';
}

.van-icon-wap-nav:before {
  content: '\F0DD';
}

.van-icon-warn-o:before {
  content: '\F0DE';
}

.van-icon-warning-o:before {
  content: '\F0DF';
}

.van-icon-warning:before {
  content: '\F0E0';
}

.van-icon-weapp-nav:before {
  content: '\F0E1';
}

.van-icon-wechat:before {
  content: '\F0E2';
}

.van-icon-youzan-shield:before {
  content: '\F0E3';
}

.van-icon__image {
  width: 1em;
  height: 1em;
  -o-object-fit: contain;
  object-fit: contain;
}

.van-tabbar-item {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #646566;
  font-size: 12px;
  line-height: 1;
  cursor: pointer;
}

.van-tabbar-item__icon {
  position: relative;
  margin-bottom: 5px;
  font-size: 18px;
}

.van-tabbar-item__icon .van-icon {
  display: block;
  min-width: 1em;
}

.van-tabbar-item__icon img {
  display: block;
  height: 18px;
}

.van-tabbar-item--active {
  color: #1989fa;
}

.van-tabbar-item .van-info {
  margin-top: 2px;
}

.van-step {
  position: relative;
  flex: 1;
  color: #969799;
  font-size: 14px;
}

.van-step__circle {
  display: block;
  width: 5px;
  height: 5px;
  background-color: #969799;
  border-radius: 50%;
}

.van-step__line {
  position: absolute;
  background-color: #ebedf0;
  transition: background-color 0.3s;
}

.van-step--horizontal {
  float: left;
}

.van-step--horizontal:first-child .van-step__title {
  margin-left: 0;
  transform: none;
}

.van-step--horizontal:last-child {
  position: absolute;
  right: 1px;
  width: auto;
}

.van-step--horizontal:last-child .van-step__title {
  margin-left: 0;
  transform: none;
}

.van-step--horizontal:last-child .van-step__circle-container {
  right: -9px;
  left: auto;
}

.van-step--horizontal .van-step__circle-container {
  position: absolute;
  top: 30px;
  left: -8px;
  z-index: 1;
  padding: 0 8px;
  background-color: #fff;
  transform: translateY(-50%);
}

.van-step--horizontal .van-step__title {
  display: inline-block;
  margin-left: 3px;
  font-size: 12px;
  transform: translateX(-50%);
}

@media (max-width: 321px) {
  .van-step--horizontal .van-step__title {
    font-size: 11px;
  }
}

.van-step--horizontal .van-step__line {
  top: 30px;
  left: 0;
  width: 100%;
  height: 1px;
}

.van-step--horizontal .van-step__icon {
  display: block;
  font-size: 12px;
}

.van-step--horizontal .van-step--process {
  color: #323233;
}

.van-step--vertical {
  display: block;
  float: none;
  padding: 10px 10px 10px 0;
  line-height: 18px;
}

.van-step--vertical:not(:last-child):after {
  border-bottom-width: 1px;
}

.van-step--vertical:first-child:before {
  position: absolute;
  top: 0;
  left: -15px;
  z-index: 1;
  width: 1px;
  height: 20px;
  background-color: #fff;
  content: '';
}

.van-step--vertical .van-step__circle-container {
  position: absolute;
  top: 19px;
  left: -15px;
  z-index: 2;
  font-size: 12px;
  line-height: 1;
  transform: translate(-50%, -50%);
}

.van-step--vertical .van-step__line {
  top: 16px;
  left: -15px;
  width: 1px;
  height: 100%;
}

.van-step:last-child .van-step__line {
  width: 0;
}

.van-step--finish {
  color: #323233;
}

.van-step--finish .van-step__circle,
.van-step--finish .van-step__line {
  background-color: #07c160;
}

.van-step__icon,
.van-step__title {
  transition: color 0.3s;
}

.van-step__icon--active,
.van-step__title--active {
  color: #07c160;
}

.van-rate {
  display: inline-flex;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-rate__item {
  position: relative;
}

.van-rate__item:not(:last-child) {
  padding-right: 4px;
}

.van-rate__icon {
  display: block;
  width: 1em;
  color: #c8c9cc;
  font-size: 20px;
}

.van-rate__icon--half {
  position: absolute;
  top: 0;
  left: 0;
  width: 0.5em;
  overflow: hidden;
}

.van-rate__icon--full {
  color: #ee0a24;
}

.van-rate__icon--disabled {
  color: #c8c9cc;
}

.van-rate--disabled {
  cursor: not-allowed;
}

.van-rate--readonly {
  cursor: default;
}

.van-notice-bar {
  position: relative;
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 16px;
  color: #ed6a0c;
  font-size: 14px;
  line-height: 24px;
  background-color: #fffbe8;
}

.van-notice-bar__left-icon,
.van-notice-bar__right-icon {
  min-width: 24px;
  font-size: 16px;
}

.van-notice-bar__right-icon {
  text-align: right;
  cursor: pointer;
}

.van-notice-bar__wrap {
  position: relative;
  display: flex;
  flex: 1;
  align-items: center;
  height: 100%;
  overflow: hidden;
}

.van-notice-bar__content {
  position: absolute;
  white-space: nowrap;
  transition-timing-function: linear;
}

.van-notice-bar__content.van-ellipsis {
  max-width: 100%;
}

.van-notice-bar--wrapable {
  height: auto;
  padding: 8px 16px;
}

.van-notice-bar--wrapable .van-notice-bar__wrap {
  height: auto;
}

.van-notice-bar--wrapable .van-notice-bar__content {
  position: relative;
  white-space: normal;
  word-wrap: break-word;
}

.van-nav-bar {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  height: 46px;
  line-height: 1.5;
  text-align: center;
  background-color: #fff;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-nav-bar .van-icon {
  color: #1989fa;
}

.van-nav-bar__arrow {
  min-width: 1em;
  margin-right: 4px;
  font-size: 16px;
}

.van-nav-bar--fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
}

.van-nav-bar__title {
  max-width: 60%;
  margin: 0 auto;
  color: #323233;
  font-weight: 500;
  font-size: 16px;
}

.van-nav-bar__left,
.van-nav-bar__right {
  position: absolute;
  top: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  padding: 0 16px;
  font-size: 14px;
  cursor: pointer;
}

.van-nav-bar__left:active,
.van-nav-bar__right:active {
  opacity: 0.7;
}

.van-nav-bar__left {
  left: 0;
}

.van-nav-bar__right {
  right: 0;
}

.van-nav-bar__text {
  color: #1989fa;
}

.van-grid-item {
  position: relative;
  box-sizing: border-box;
}

.van-grid-item--square {
  height: 0;
}

.van-grid-item__icon {
  font-size: 28px;
}

.van-grid-item__icon-wrapper {
  position: relative;
}

.van-grid-item__text {
  color: #646566;
  font-size: 12px;
  line-height: 1.5;
  word-wrap: break-word;
}

.van-grid-item__icon + .van-grid-item__text {
  margin-top: 8px;
}

.van-grid-item__content {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  height: 100%;
  padding: 16px 8px;
  background-color: #fff;
}

.van-grid-item__content:after {
  z-index: 1;
  border-width: 0 1px 1px 0;
}

.van-grid-item__content--square {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
}

.van-grid-item__content--center {
  align-items: center;
  justify-content: center;
}

.van-grid-item__content--horizontal {
  flex-direction: row;
}

.van-grid-item__content--horizontal .van-grid-item__icon + .van-grid-item__text {
  margin-top: 0;
  margin-left: 8px;
}

.van-grid-item__content--surround:after {
  border-width: 1px;
}

.van-grid-item__content--clickable {
  cursor: pointer;
}

.van-grid-item__content--clickable:active {
  background-color: #f2f3f5;
}

.van-goods-action-icon {
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 48px;
  height: 100%;
  color: #646566;
  font-size: 10px;
  line-height: 1;
  text-align: center;
  background-color: #fff;
  cursor: pointer;
}

.van-goods-action-icon:active {
  background-color: #f2f3f5;
}

.van-goods-action-icon__icon {
  position: relative;
  width: 1em;
  margin: 0 auto 5px;
  color: #323233;
  font-size: 18px;
}

.van-checkbox {
  display: flex;
  align-items: center;
  overflow: hidden;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-checkbox--disabled {
  cursor: not-allowed;
}

.van-checkbox--label-disabled {
  cursor: default;
}

.van-checkbox--horizontal {
  margin-right: 12px;
}

.van-checkbox__icon {
  flex: none;
  height: 1em;
  font-size: 20px;
  line-height: 1em;
  cursor: pointer;
}

.van-checkbox__icon .van-icon {
  display: block;
  box-sizing: border-box;
  width: 1.25em;
  height: 1.25em;
  color: transparent;
  font-size: 0.8em;
  line-height: 1.25;
  text-align: center;
  border: 1px solid #c8c9cc;
  transition-duration: 0.2s;
  transition-property: color, border-color, background-color;
}

.van-checkbox__icon--round .van-icon {
  border-radius: 100%;
}

.van-checkbox__icon--checked .van-icon {
  color: #fff;
  background-color: #1989fa;
  border-color: #1989fa;
}

.van-checkbox__icon--disabled {
  cursor: not-allowed;
}

.van-checkbox__icon--disabled .van-icon {
  background-color: #ebedf0;
  border-color: #c8c9cc;
}

.van-checkbox__icon--disabled.van-checkbox__icon--checked .van-icon {
  color: #c8c9cc;
}

.van-checkbox__label {
  margin-left: 8px;
  color: #323233;
  line-height: 20px;
}

.van-checkbox__label--left {
  margin: 0 8px 0 0;
}

.van-checkbox__label--disabled {
  color: #c8c9cc;
}

.van-coupon {
  margin: 0 12px 12px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
}

.van-coupon:active {
  background-color: #f2f3f5;
}

.van-coupon__content {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  min-height: 84px;
  padding: 14px 0;
  color: #323233;
}

.van-coupon__head {
  position: relative;
  min-width: 96px;
  padding: 0 8px;
  color: #ee0a24;
  text-align: center;
}

.van-coupon__amount,
.van-coupon__condition,
.van-coupon__name,
.van-coupon__valid {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.van-coupon__amount {
  margin-bottom: 6px;
  font-weight: 500;
  font-size: 30px;
}

.van-coupon__amount span {
  font-weight: 400;
  font-size: 40%;
}

.van-coupon__amount span:not(:empty) {
  margin-left: 2px;
}

.van-coupon__condition {
  font-size: 12px;
  line-height: 16px;
  white-space: pre-wrap;
}

.van-coupon__body {
  position: relative;
  flex: 1;
  border-radius: 0 8px 8px 0;
}

.van-coupon__name {
  margin-bottom: 10px;
  font-weight: 700;
  font-size: 14px;
  line-height: 20px;
}

.van-coupon__valid {
  font-size: 12px;
}

.van-coupon__corner {
  position: absolute;
  top: 0;
  right: 16px;
  bottom: 0;
}

.van-coupon__description {
  padding: 8px 16px;
  font-size: 12px;
  border-top: 1px dashed #ebedf0;
}

.van-coupon--disabled:active {
  background-color: #fff;
}

.van-coupon--disabled .van-coupon-item__content {
  height: 74px;
}

.van-coupon--disabled .van-coupon__head {
  color: inherit;
}

.van-image {
  position: relative;
  display: inline-block;
}

.van-image--round {
  overflow: hidden;
  border-radius: 50%;
}

.van-image--round img {
  border-radius: inherit;
}

.van-image__error,
.van-image__img,
.van-image__loading {
  display: block;
  width: 100%;
  height: 100%;
}

.van-image__error,
.van-image__loading {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #969799;
  font-size: 14px;
  background-color: #f7f8fa;
}

.van-image__error-icon,
.van-image__loading-icon {
  font-size: 22px;
}

.van-radio {
  display: flex;
  align-items: center;
  overflow: hidden;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-radio--disabled {
  cursor: not-allowed;
}

.van-radio--label-disabled {
  cursor: default;
}

.van-radio--horizontal {
  margin-right: 12px;
}

.van-radio__icon {
  flex: none;
  height: 1em;
  font-size: 20px;
  line-height: 1em;
  cursor: pointer;
}

.van-radio__icon .van-icon {
  display: block;
  box-sizing: border-box;
  width: 1.25em;
  height: 1.25em;
  color: transparent;
  font-size: 0.8em;
  line-height: 1.25;
  text-align: center;
  border: 1px solid #c8c9cc;
  transition-duration: 0.2s;
  transition-property: color, border-color, background-color;
}

.van-radio__icon--round .van-icon {
  border-radius: 100%;
}

.van-radio__icon--checked .van-icon {
  color: #fff;
  background-color: #1989fa;
  border-color: #1989fa;
}

.van-radio__icon--disabled {
  cursor: not-allowed;
}

.van-radio__icon--disabled .van-icon {
  background-color: #ebedf0;
  border-color: #c8c9cc;
}

.van-radio__icon--disabled.van-radio__icon--checked .van-icon {
  color: #c8c9cc;
}

.van-radio__label {
  margin-left: 8px;
  color: #323233;
  line-height: 20px;
}

.van-radio__label--left {
  margin: 0 8px 0 0;
}

.van-radio__label--disabled {
  color: #c8c9cc;
}

.van-tag {
  display: inline-flex;
  align-items: center;
  padding: 0.2em 0.5em;
  color: #fff;
  font-size: 10px;
  line-height: normal;
  border-radius: 0.2em;
}

.van-tag:after {
  border-color: currentColor;
  border-radius: 0.4em;
}

.van-tag--default {
  background-color: #969799;
}

.van-tag--default.van-tag--plain {
  color: #969799;
}

.van-tag--danger {
  background-color: #ee0a24;
}

.van-tag--danger.van-tag--plain {
  color: #ee0a24;
}

.van-tag--primary {
  background-color: #1989fa;
}

.van-tag--primary.van-tag--plain {
  color: #1989fa;
}

.van-tag--success {
  background-color: #07c160;
}

.van-tag--success.van-tag--plain {
  color: #07c160;
}

.van-tag--warning {
  background-color: #ff976a;
}

.van-tag--warning.van-tag--plain {
  color: #ff976a;
}

.van-tag--plain {
  background-color: #fff;
}

.van-tag--mark {
  padding-right: 0.7em;
}

.van-tag--mark,
.van-tag--mark:after {
  border-radius: 0 999px 999px 0;
}

.van-tag--round,
.van-tag--round:after {
  border-radius: 999px;
}

.van-tag--medium {
  font-size: 12px;
}

.van-tag--large {
  font-size: 14px;
}

.van-tag__close {
  min-width: 1em;
  margin-left: 2px;
  cursor: pointer;
}

.van-card {
  position: relative;
  box-sizing: border-box;
  padding: 8px 16px;
  color: #323233;
  font-size: 12px;
  background-color: #fafafa;
}

.van-card:not(:first-child) {
  margin-top: 8px;
}

.van-card__header {
  display: flex;
}

.van-card__thumb {
  position: relative;
  flex: none;
  width: 88px;
  height: 88px;
  margin-right: 8px;
}

.van-card__thumb img {
  border-radius: 8px;
}

.van-card__content {
  position: relative;
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: space-between;
  min-width: 0;
  min-height: 88px;
}

.van-card__content--centered {
  justify-content: center;
}

.van-card__desc,
.van-card__title {
  word-wrap: break-word;
}

.van-card__title {
  max-height: 32px;
  font-weight: 500;
  line-height: 16px;
}

.van-card__desc {
  max-height: 20px;
  color: #646566;
}

.van-card__bottom,
.van-card__desc {
  line-height: 20px;
}

.van-card__price {
  display: inline-block;
  color: #323233;
  font-weight: 500;
  font-size: 12px;
}

.van-card__price-integer {
  font-size: 16px;
}

.van-card__price-decimal,
.van-card__price-integer {
  font-family: Avenir-Heavy, PingFang SC, Helvetica Neue, Arial, sans-serif;
}

.van-card__origin-price {
  display: inline-block;
  margin-left: 5px;
  color: #969799;
  font-size: 10px;
  text-decoration: line-through;
}

.van-card__num {
  float: right;
  color: #969799;
}

.van-card__tag {
  position: absolute;
  top: 2px;
  left: 0;
}

.van-card__footer {
  flex: none;
  text-align: right;
}

.van-card__footer .van-button {
  margin-left: 5px;
}

.van-cell {
  position: relative;
  display: flex;
  box-sizing: border-box;
  width: 100%;
  padding: 10px 16px;
  overflow: hidden;
  color: #323233;
  font-size: 14px;
  line-height: 24px;
  background-color: #fff;
}

.van-cell:after {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  right: 16px;
  bottom: 0;
  left: 16px;
  border-bottom: 1px solid #ebedf0;
  transform: scaleY(0.5);
}

.van-cell--borderless:after,
.van-cell:last-child:after {
  display: none;
}

.van-cell__label {
  margin-top: 4px;
  color: #969799;
  font-size: 12px;
  line-height: 18px;
}

.van-cell__title,
.van-cell__value {
  flex: 1;
}

.van-cell__value {
  position: relative;
  overflow: hidden;
  color: #969799;
  text-align: right;
  vertical-align: middle;
  word-wrap: break-word;
}

.van-cell__value--alone {
  color: #323233;
  text-align: left;
}

.van-cell__left-icon,
.van-cell__right-icon {
  min-width: 1em;
  height: 24px;
  font-size: 16px;
  line-height: 24px;
}

.van-cell__left-icon {
  margin-right: 5px;
}

.van-cell__right-icon {
  margin-left: 5px;
  color: #969799;
}

.van-cell--clickable {
  cursor: pointer;
}

.van-cell--clickable:active {
  background-color: #f2f3f5;
}

.van-cell--required {
  overflow: visible;
}

.van-cell--required:before {
  position: absolute;
  left: 8px;
  color: #ee0a24;
  font-size: 14px;
  content: '*';
}

.van-cell--center {
  align-items: center;
}

.van-cell--large {
  padding-top: 12px;
  padding-bottom: 12px;
}

.van-cell--large .van-cell__title {
  font-size: 16px;
}

.van-cell--large .van-cell__label {
  font-size: 14px;
}

.van-coupon-cell--selected {
  color: #323233;
}

.van-contact-card {
  padding: 16px;
}

.van-contact-card__value {
  margin-left: 5px;
  line-height: 20px;
}

.van-contact-card--add .van-contact-card__value {
  line-height: 40px;
}

.van-contact-card--add .van-cell__left-icon {
  color: #1989fa;
  font-size: 40px;
}

.van-contact-card:before {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  height: 2px;
  background: -webkit-repeating-linear-gradient(135deg,
  #ff6c6c,
  #ff6c6c 20%,
  transparent 0,
  transparent 25%,
  #1989fa 0,
  #1989fa 45%,
  transparent 0,
  transparent 50%);
  background: repeating-linear-gradient(-45deg,
  #ff6c6c,
  #ff6c6c 20%,
  transparent 0,
  transparent 25%,
  #1989fa 0,
  #1989fa 45%,
  transparent 0,
  transparent 50%);
  background-size: 80px;
  content: '';
}

.van-collapse-item {
  position: relative;
}

.van-collapse-item--border:after {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  top: 0;
  right: 16px;
  left: 16px;
  border-top: 1px solid #ebedf0;
  transform: scaleY(0.5);
}

.van-collapse-item__title .van-cell__right-icon:before {
  transform: rotate(90deg);
  transition: transform 0.3s;
}

.van-collapse-item__title:after {
  right: 16px;
  display: none;
}

.van-collapse-item__title--expanded .van-cell__right-icon:before {
  transform: rotate(-90deg);
}

.van-collapse-item__title--expanded:after {
  display: block;
}

.van-collapse-item__title--borderless:after {
  display: none;
}

.van-collapse-item__title--disabled {
  cursor: not-allowed;
}

.van-collapse-item__title--disabled,
.van-collapse-item__title--disabled .van-cell__right-icon {
  color: #c8c9cc;
}

.van-collapse-item__title--disabled:active {
  background-color: #fff;
}

.van-collapse-item__wrapper {
  overflow: hidden;
  transition: height 0.3s ease-in-out;
  will-change: height;
}

.van-collapse-item__content {
  padding: 12px 16px;
  color: #969799;
  font-size: 14px;
  line-height: 1.5;
  background-color: #fff;
}

.van-field__label {
  flex: none;
  box-sizing: border-box;
  width: 6.2em;
  margin-right: 12px;
  color: #646566;
  text-align: left;
  word-wrap: break-word;
}

.van-field__label--center {
  text-align: center;
}

.van-field__label--right {
  padding-right: 16px;
  text-align: right;
}

.van-field--disabled .van-field__label {
  color: #c8c9cc;
}

.van-field__value {
  overflow: visible;
}

.van-field__body {
  display: flex;
  align-items: center;
}

.van-field__control {
  display: block;
  box-sizing: border-box;
  width: 100%;
  min-width: 0;
  margin: 0;
  padding: 0;
  color: #323233;
  line-height: inherit;
  text-align: left;
  background-color: transparent;
  border: 0;
  resize: none;
}

.van-field__control::-moz-placeholder {
  color: #c8c9cc;
}

.van-field__control:-ms-input-placeholder {
  color: #c8c9cc;
}

.van-field__control::placeholder {
  color: #c8c9cc;
}

.van-field__control:disabled {
  color: #c8c9cc;
  background-color: transparent;
  cursor: not-allowed;
  opacity: 1;
  -webkit-text-fill-color: currentColor;
}

.van-field__control:-moz-read-only {
  cursor: default;
}

.van-field__control:read-only {
  cursor: default;
}

.van-field__control--center {
  justify-content: center;
  text-align: center;
}

.van-field__control--right {
  justify-content: flex-end;
  text-align: right;
}

.van-field__control--custom {
  display: flex;
  align-items: center;
  min-height: 24px;
}

.van-field__control[type='date'],
.van-field__control[type='datetime-local'],
.van-field__control[type='time'] {
  min-height: 24px;
}

.van-field__control[type='search'] {
  -webkit-appearance: none;
}

.van-field__button,
.van-field__clear,
.van-field__icon,
.van-field__right-icon {
  flex-shrink: 0;
}

.van-field__clear,
.van-field__right-icon {
  margin-right: -8px;
  padding: 0 8px;
  line-height: inherit;
}

.van-field__clear {
  color: #c8c9cc;
  font-size: 16px;
  cursor: pointer;
}

.van-field__left-icon .van-icon,
.van-field__right-icon .van-icon {
  display: block;
  min-width: 1em;
  font-size: 16px;
  line-height: inherit;
}

.van-field__left-icon {
  margin-right: 5px;
}

.van-field__right-icon {
  color: #969799;
}

.van-field__button {
  padding-left: 8px;
}

.van-field__error-message {
  color: #ee0a24;
  font-size: 12px;
  text-align: left;
}

.van-field__error-message--center {
  text-align: center;
}

.van-field__error-message--right {
  text-align: right;
}

.van-field__word-limit {
  margin-top: 4px;
  color: #646566;
  font-size: 12px;
  line-height: 16px;
  text-align: right;
}

.van-field--error .van-field__control::-moz-placeholder {
  color: #ee0a24;
  -webkit-text-fill-color: currentColor;
}

.van-field--error .van-field__control:-ms-input-placeholder {
  color: #ee0a24;
  -webkit-text-fill-color: currentColor;
}

.van-field--error .van-field__control,
.van-field--error .van-field__control::placeholder {
  color: #ee0a24;
  -webkit-text-fill-color: currentColor;
}

.van-field--min-height .van-field__control {
  min-height: 60px;
}

.van-search {
  display: flex;
  align-items: center;
  box-sizing: border-box;
  padding: 10px 12px;
  background-color: #fff;
}

.van-search__content {
  display: flex;
  flex: 1;
  padding-left: 8px;
  background-color: #f7f8fa;
  border-radius: 2px;
}

.van-search__content--round {
  border-radius: 999px;
}

.van-search__label {
  padding: 0 5px;
  color: #323233;
  font-size: 14px;
  line-height: 34px;
}

.van-search .van-cell {
  flex: 1;
  padding: 5px 8px 5px 0;
  background-color: transparent;
}

.van-search .van-cell__left-icon {
  color: #969799;
}

.van-search--show-action {
  padding-right: 0;
}

.van-search input::-webkit-search-cancel-button,
.van-search input::-webkit-search-decoration,
.van-search input::-webkit-search-results-button,
.van-search input::-webkit-search-results-decoration {
  display: none;
}

.van-search__action {
  padding: 0 8px;
  color: #323233;
  font-size: 14px;
  line-height: 34px;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-search__action:active {
  background-color: #f2f3f5;
}

.van-overflow-hidden {
  overflow: hidden !important;
}

.van-popup {
  position: fixed;
  max-height: 100%;
  overflow-y: auto;
  background-color: #fff;
  transition: transform 0.3s;
  -webkit-overflow-scrolling: touch;
}

.van-popup--center {
  top: 50%;
  left: 50%;
  transform: translate3d(-50%, -50%, 0);
}

.van-popup--center.van-popup--round {
  border-radius: 20px;
}

.van-popup--top {
  top: 0;
  left: 0;
  width: 100%;
}

.van-popup--top.van-popup--round {
  border-radius: 0 0 20px 20px;
}

.van-popup--right {
  top: 50%;
  right: 0;
  transform: translate3d(0, -50%, 0);
}

.van-popup--right.van-popup--round {
  border-radius: 20px 0 0 20px;
}

.van-popup--bottom {
  bottom: 0;
  left: 0;
  width: 100%;
}

.van-popup--bottom.van-popup--round {
  border-radius: 20px 20px 0 0;
}

.van-popup--left {
  top: 50%;
  left: 0;
  transform: translate3d(0, -50%, 0);
}

.van-popup--left.van-popup--round {
  border-radius: 0 20px 20px 0;
}

.van-popup--safe-area-inset-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.van-popup-slide-bottom-enter-active,
.van-popup-slide-left-enter-active,
.van-popup-slide-right-enter-active,
.van-popup-slide-top-enter-active {
  transition-timing-function: ease-out;
}

.van-popup-slide-bottom-leave-active,
.van-popup-slide-left-leave-active,
.van-popup-slide-right-leave-active,
.van-popup-slide-top-leave-active {
  transition-timing-function: ease-in;
}

.van-popup-slide-top-enter,
.van-popup-slide-top-leave-active {
  transform: translate3d(0, -100%, 0);
}

.van-popup-slide-right-enter,
.van-popup-slide-right-leave-active {
  transform: translate3d(100%, -50%, 0);
}

.van-popup-slide-bottom-enter,
.van-popup-slide-bottom-leave-active {
  transform: translate3d(0, 100%, 0);
}

.van-popup-slide-left-enter,
.van-popup-slide-left-leave-active {
  transform: translate3d(-100%, -50%, 0);
}

.van-popup__close-icon {
  position: absolute;
  z-index: 1;
  color: #c8c9cc;
  font-size: 22px;
  cursor: pointer;
}

.van-popup__close-icon:active {
  color: #969799;
}

.van-popup__close-icon--top-left {
  top: 16px;
  left: 16px;
}

.van-popup__close-icon--top-right {
  top: 16px;
  right: 16px;
}

.van-popup__close-icon--bottom-left {
  bottom: 16px;
  left: 16px;
}

.van-popup__close-icon--bottom-right {
  right: 16px;
  bottom: 16px;
}

.van-share-sheet__header {
  padding: 12px 16px 4px;
  text-align: center;
}

.van-share-sheet__title {
  margin-top: 8px;
  color: #323233;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
}

.van-share-sheet__description {
  display: block;
  margin-top: 8px;
  color: #969799;
  font-size: 12px;
  line-height: 16px;
}

.van-share-sheet__options {
  position: relative;
  display: flex;
  padding: 16px 0 16px 8px;
  overflow-x: auto;
  overflow-y: visible;
  -webkit-overflow-scrolling: touch;
}

.van-share-sheet__options--border:before {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  top: 0;
  right: 0;
  left: 16px;
  border-top: 1px solid #ebedf0;
  transform: scaleY(0.5);
}

.van-share-sheet__options::-webkit-scrollbar {
  height: 0;
}

.van-share-sheet__option {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-share-sheet__option:active {
  opacity: 0.7;
}

.van-share-sheet__icon {
  width: 48px;
  height: 48px;
  margin: 0 16px;
}

.van-share-sheet__name {
  margin-top: 8px;
  padding: 0 4px;
  color: #646566;
  font-size: 12px;
}

.van-share-sheet__option-description {
  padding: 0 4px;
  color: #c8c9cc;
  font-size: 12px;
}

.van-share-sheet__cancel {
  display: block;
  width: 100%;
  padding: 0;
  font-size: 16px;
  line-height: 48px;
  text-align: center;
  background: #fff;
  border: none;
  cursor: pointer;
}

.van-share-sheet__cancel:before {
  display: block;
  height: 8px;
  background-color: #f7f8fa;
  content: ' ';
}

.van-share-sheet__cancel:active {
  background-color: #f2f3f5;
}

.van-notify {
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 8px 16px;
  color: #fff;
  font-size: 14px;
  line-height: 20px;
  white-space: pre-wrap;
  text-align: center;
  word-wrap: break-word;
}

.van-notify--primary {
  background-color: #1989fa;
}

.van-notify--success {
  background-color: #07c160;
}

.van-notify--danger {
  background-color: #ee0a24;
}

.van-notify--warning {
  background-color: #ff976a;
}

.van-dropdown-item {
  position: fixed;
  right: 0;
  left: 0;
  z-index: 10;
  overflow: hidden;
}

.van-dropdown-item__icon {
  display: block;
  line-height: inherit;
}

.van-dropdown-item__option {
  text-align: left;
}

.van-dropdown-item__option--active,
.van-dropdown-item__option--active .van-dropdown-item__icon {
  color: #1989fa;
}

.van-dropdown-item--up {
  top: 0;
}

.van-dropdown-item--down {
  bottom: 0;
}

.van-dropdown-item__content {
  position: absolute;
  max-height: 80%;
}

.van-loading {
  color: #c8c9cc;
  font-size: 0;
}

.van-loading,
.van-loading__spinner {
  position: relative;
  vertical-align: middle;
}

.van-loading__spinner {
  display: inline-block;
  width: 30px;
  max-width: 100%;
  height: 30px;
  max-height: 100%;
  -webkit-animation: van-rotate 0.8s linear infinite;
  animation: van-rotate 0.8s linear infinite;
}

.van-loading__spinner--spinner {
  -webkit-animation-timing-function: steps(12);
  animation-timing-function: steps(12);
}

.van-loading__spinner--spinner i {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.van-loading__spinner--spinner i:before {
  display: block;
  width: 2px;
  height: 25%;
  margin: 0 auto;
  background-color: currentColor;
  border-radius: 40%;
  content: ' ';
}

.van-loading__spinner--circular {
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
}

.van-loading__circular {
  display: block;
  width: 100%;
  height: 100%;
}

.van-loading__circular circle {
  -webkit-animation: van-circular 1.5s ease-in-out infinite;
  animation: van-circular 1.5s ease-in-out infinite;
  stroke: currentColor;
  stroke-width: 3;
  stroke-linecap: round;
}

.van-loading__text {
  display: inline-block;
  margin-left: 8px;
  color: #969799;
  font-size: 14px;
  vertical-align: middle;
}

.van-loading--vertical {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.van-loading--vertical .van-loading__text {
  margin: 8px 0 0;
}

@-webkit-keyframes van-circular {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -40;
  }

  to {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -120;
  }
}

@keyframes van-circular {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -40;
  }

  to {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -120;
  }
}

.van-loading__spinner--spinner i:first-of-type {
  transform: rotate(30deg);
  opacity: 1;
}

.van-loading__spinner--spinner i:nth-of-type(2) {
  transform: rotate(60deg);
  opacity: 0.9375;
}

.van-loading__spinner--spinner i:nth-of-type(3) {
  transform: rotate(90deg);
  opacity: 0.875;
}

.van-loading__spinner--spinner i:nth-of-type(4) {
  transform: rotate(120deg);
  opacity: 0.8125;
}

.van-loading__spinner--spinner i:nth-of-type(5) {
  transform: rotate(150deg);
  opacity: 0.75;
}

.van-loading__spinner--spinner i:nth-of-type(6) {
  transform: rotate(180deg);
  opacity: 0.6875;
}

.van-loading__spinner--spinner i:nth-of-type(7) {
  transform: rotate(210deg);
  opacity: 0.625;
}

.van-loading__spinner--spinner i:nth-of-type(8) {
  transform: rotate(240deg);
  opacity: 0.5625;
}

.van-loading__spinner--spinner i:nth-of-type(9) {
  transform: rotate(270deg);
  opacity: 0.5;
}

.van-loading__spinner--spinner i:nth-of-type(10) {
  transform: rotate(300deg);
  opacity: 0.4375;
}

.van-loading__spinner--spinner i:nth-of-type(11) {
  transform: rotate(330deg);
  opacity: 0.375;
}

.van-loading__spinner--spinner i:nth-of-type(12) {
  transform: rotate(1turn);
  opacity: 0.3125;
}

.van-pull-refresh {
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-pull-refresh__track {
  position: relative;
  height: 100%;
  transition-property: transform;
}

.van-pull-refresh__head {
  position: absolute;
  left: 0;
  width: 100%;
  height: 50px;
  overflow: hidden;
  color: #969799;
  font-size: 14px;
  line-height: 50px;
  text-align: center;
  transform: translateY(-100%);
}

.van-number-keyboard {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #f2f3f5;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-number-keyboard--with-title {
  border-radius: 20px 20px 0 0;
}

.van-number-keyboard__header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: content-box;
  height: 30px;
  padding-top: 6px;
  color: #646566;
  font-size: 14px;
}

.van-number-keyboard__title {
  display: inline-block;
  font-weight: 400;
}

.van-number-keyboard__title-left {
  position: absolute;
  left: 0;
}

.van-number-keyboard__body {
  display: flex;
  padding: 6px 0 0 6px;
}

.van-number-keyboard__keys {
  display: flex;
  flex: 3;
  flex-wrap: wrap;
}

.van-number-keyboard__close {
  position: absolute;
  right: 0;
  height: 100%;
  padding: 0 16px;
  color: #576b95;
  font-size: 14px;
  background-color: transparent;
  border: none;
  cursor: pointer;
}

.van-number-keyboard__close:active {
  opacity: 0.7;
}

.van-number-keyboard__sidebar {
  display: flex;
  flex: 1;
  flex-direction: column;
}

.van-number-keyboard--unfit {
  padding-bottom: 0;
}

.van-key {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 56px;
  font-size: 28px;
  line-height: 1.5;
  background-color: #fff;
  border-radius: 8px;
  cursor: pointer;
}

.van-key--large {
  height: 100%;
}

.van-key--blue,
.van-key--delete {
  font-size: 16px;
}

.van-key--active {
  background-color: #ebedf0;
}

.van-key--blue {
  color: #fff;
  background-color: #1989fa;
}

.van-key--blue.van-key--active {
  background-color: #0570db;
}

.van-key__wrapper {
  flex: 1;
  flex-basis: 33%;
  box-sizing: border-box;
  padding: 0 6px 6px 0;
}

.van-key__wrapper--wider {
  flex-basis: 66%;
}

.van-key__delete-icon {
  width: 32px;
  height: 22px;
}

.van-key__collapse-icon {
  width: 30px;
  height: 24px;
}

.van-key__loading-icon {
  color: #fff;
}

.van-list__error-text,
.van-list__finished-text,
.van-list__loading {
  color: #969799;
  font-size: 14px;
  line-height: 50px;
  text-align: center;
}

.van-list__placeholder {
  height: 0;
  pointer-events: none;
}

.van-switch {
  position: relative;
  display: inline-block;
  box-sizing: content-box;
  width: 2em;
  font-size: 30px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 1em;
  cursor: pointer;
  transition: background-color 0.3s;
}

.van-switch,
.van-switch__node {
  height: 1em;
  background-color: #fff;
}

.van-switch__node {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 1em;
  border-radius: 100%;
  box-shadow: 0 3px 1px 0 rgba(0, 0, 0, 0.05), 0 2px 2px 0 rgba(0, 0, 0, 0.1), 0 3px 3px 0 rgba(0, 0, 0, 0.05);
  transition: transform 0.3s cubic-bezier(0.3, 1.05, 0.4, 1.05);
}

.van-switch__loading {
  top: 25%;
  left: 25%;
  width: 50%;
  height: 50%;
  line-height: 1;
}

.van-switch--on {
  background-color: #1989fa;
}

.van-switch--on .van-switch__node {
  transform: translateX(1em);
}

.van-switch--on .van-switch__loading {
  color: #1989fa;
}

.van-switch--disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.van-switch--loading {
  cursor: default;
}

.van-switch-cell {
  padding-top: 9px;
  padding-bottom: 9px;
}

.van-switch-cell--large {
  padding-top: 11px;
  padding-bottom: 11px;
}

.van-switch-cell .van-switch {
  float: right;
}

.van-button {
  position: relative;
  display: inline-block;
  box-sizing: border-box;
  height: 44px;
  margin: 0;
  padding: 0;
  font-size: 16px;
  line-height: 1.2;
  text-align: center;
  border-radius: 2px;
  cursor: pointer;
  transition: opacity 0.2s;
  -webkit-appearance: none;
}

.van-button:before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  background-color: #000;
  border-color: #000;
  border-radius: inherit;
  transform: translate(-50%, -50%);
  opacity: 0;
  content: ' ';
}

.van-button:active:before {
  opacity: 0.1;
}

.van-button--disabled:before,
.van-button--loading:before {
  display: none;
}

.van-button--default {
  color: #323233;
  background-color: #fff;
  border: 1px solid #ebedf0;
}

.van-button--primary {
  color: #fff;
  background-color: #07c160;
  border: 1px solid #07c160;
}

.van-button--info {
  color: #fff;
  background-color: #1989fa;
  border: 1px solid #1989fa;
}

.van-button--danger {
  color: #fff;
  background-color: #ee0a24;
  border: 1px solid #ee0a24;
}

.van-button--warning {
  color: #fff;
  background-color: #ff976a;
  border: 1px solid #ff976a;
}

.van-button--plain {
  background-color: #fff;
}

.van-button--plain.van-button--primary {
  color: #07c160;
}

.van-button--plain.van-button--info {
  color: #1989fa;
}

.van-button--plain.van-button--danger {
  color: #ee0a24;
}

.van-button--plain.van-button--warning {
  color: #ff976a;
}

.van-button--large {
  width: 100%;
  height: 50px;
}

.van-button--normal {
  padding: 0 15px;
  font-size: 14px;
}

.van-button--small {
  height: 32px;
  padding: 0 8px;
  font-size: 12px;
}

.van-button__loading {
  color: inherit;
  font-size: inherit;
}

.van-button--mini {
  height: 24px;
  padding: 0 4px;
  font-size: 10px;
}

.van-button--mini + .van-button--mini {
  margin-left: 4px;
}

.van-button--block {
  display: block;
  width: 100%;
}

.van-button--disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.van-button--loading {
  cursor: default;
}

.van-button--round {
  border-radius: 999px;
}

.van-button--square {
  border-radius: 0;
}

.van-button__content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.van-button__icon {
  min-width: 1em;
  font-size: 1.2em;
  line-height: inherit;
}

.van-button__icon + .van-button__text,
.van-button__loading + .van-button__text {
  margin-left: 5px;
}

.van-button--hairline {
  border-width: 0;
}

.van-button--hairline:after {
  border-color: inherit;
  border-radius: 4px;
}

.van-button--hairline.van-button--round:after {
  border-radius: 999px;
}

.van-button--hairline.van-button--square:after {
  border-radius: 0;
}

.van-submit-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 100;
  width: 100%;
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #fff;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-submit-bar__tip {
  padding: 8px 12px;
  color: #f56723;
  font-size: 12px;
  line-height: 1.5;
  background-color: #fff7cc;
}

.van-submit-bar__tip-icon {
  min-width: 18px;
  font-size: 12px;
  vertical-align: middle;
}

.van-submit-bar__tip-text {
  vertical-align: middle;
}

.van-submit-bar__bar {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  height: 50px;
  padding: 0 16px;
  font-size: 14px;
}

.van-submit-bar__text {
  flex: 1;
  padding-right: 12px;
  color: #323233;
  text-align: right;
}

.van-submit-bar__text span {
  display: inline-block;
}

.van-submit-bar__suffix-label {
  margin-left: 5px;
  font-weight: 500;
}

.van-submit-bar__price {
  color: #ee0a24;
  font-weight: 500;
  font-size: 12px;
}

.van-submit-bar__price--integer {
  font-size: 20px;
  font-family: Avenir-Heavy, PingFang SC, Helvetica Neue, Arial, sans-serif;
}

.van-submit-bar__button {
  width: 110px;
  height: 40px;
  font-weight: 500;
  border: none;
}

.van-submit-bar__button--danger {
  background: linear-gradient(90deg, #ff6034, #ee0a24);
}

.van-submit-bar--unfit {
  padding-bottom: 0;
}

.van-goods-action-button {
  flex: 1;
  height: 40px;
  font-weight: 500;
  font-size: 14px;
  border: none;
}

.van-goods-action-button--first {
  margin-left: 5px;
  border-top-left-radius: 999px;
  border-bottom-left-radius: 999px;
}

.van-goods-action-button--last {
  margin-right: 5px;
  border-top-right-radius: 999px;
  border-bottom-right-radius: 999px;
}

.van-goods-action-button--warning {
  background: linear-gradient(90deg, #ffd01e, #ff8917);
}

.van-goods-action-button--danger {
  background: linear-gradient(90deg, #ff6034, #ee0a24);
}

@media (max-width: 321px) {
  .van-goods-action-button {
    font-size: 13px;
  }
}

.van-dialog {
  position: fixed;
  top: 45%;
  left: 50%;
  width: 320px;
  overflow: hidden;
  font-size: 16px;
  background-color: #fff;
  border-radius: 16px;
  transform: translate3d(-50%, -50%, 0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  transition: 0.3s;
  transition-property: transform, opacity;
}

@media (max-width: 321px) {
  .van-dialog {
    width: 90%;
  }
}

.van-dialog__header {
  padding-top: 24px;
  font-weight: 500;
  line-height: 24px;
  text-align: center;
}

.van-dialog__header--isolated {
  padding: 24px 0;
}

.van-dialog__message {
  max-height: 60vh;
  padding: 24px;
  overflow-y: auto;
  font-size: 14px;
  line-height: 20px;
  white-space: pre-wrap;
  text-align: center;
  word-wrap: break-word;
  -webkit-overflow-scrolling: touch;
}

.van-dialog__message--has-title {
  padding-top: 12px;
  color: #646566;
}

.van-dialog__message--left {
  text-align: left;
}

.van-dialog__message--right {
  text-align: right;
}

.van-dialog__footer {
  overflow: hidden;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-dialog__footer--buttons {
  display: flex;
}

.van-dialog__footer--buttons .van-button {
  flex: 1;
}

.van-dialog .van-button {
  border: 0;
}

.van-dialog__confirm,
.van-dialog__confirm:active {
  color: #1989fa;
}

.van-dialog-bounce-enter {
  transform: translate3d(-50%, -50%, 0) scale(0.7);
  opacity: 0;
}

.van-dialog-bounce-leave-active {
  transform: translate3d(-50%, -50%, 0) scale(0.9);
  opacity: 0;
}

.van-contact-edit {
  padding: 16px;
}

.van-contact-edit__fields {
  overflow: hidden;
  border-radius: 4px;
}

.van-contact-edit__fields .van-field__label {
  width: 4em;
}

.van-contact-edit__switch-cell {
  margin-top: 10px;
  padding-top: 9px;
  padding-bottom: 9px;
  border-radius: 4px;
}

.van-contact-edit__buttons {
  padding: 32px 0;
}

.van-contact-edit .van-button {
  margin-bottom: 12px;
  font-size: 16px;
}

.van-toast {
  position: fixed;
  top: 50%;
  left: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: content-box;
  width: 88px;
  max-width: 70%;
  min-height: 88px;
  padding: 16px;
  color: #fff;
  font-size: 14px;
  line-height: 20px;
  white-space: pre-wrap;
  text-align: center;
  word-wrap: break-word;
  background-color: rgba(50, 50, 51, 0.88);
  border-radius: 8px;
  transform: translate3d(-50%, -50%, 0);
}

.van-toast--unclickable {
  overflow: hidden;
}

.van-toast--unclickable * {
  pointer-events: none;
}

.van-toast--html,
.van-toast--text {
  width: fit-content;
  min-width: 96px;
  min-height: 0;
  padding: 8px 12px;
}

.van-toast--html .van-toast__text,
.van-toast--text .van-toast__text {
  margin-top: 0;
}

.van-toast--top {
  top: 50px;
}

.van-toast--bottom {
  top: auto;
  bottom: 50px;
}

.van-toast__icon {
  font-size: 40px;
}

.van-toast__loading {
  padding: 4px;
  color: #fff;
}

.van-toast__text {
  margin-top: 8px;
}

.van-calendar {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
}

.van-calendar__popup.van-popup--bottom,
.van-calendar__popup.van-popup--top {
  height: 80%;
}

.van-calendar__popup.van-popup--left,
.van-calendar__popup.van-popup--right {
  height: 100%;
}

.van-calendar__popup .van-popup__close-icon {
  top: 11px;
}

.van-calendar__header {
  flex-shrink: 0;
  box-shadow: 0 2px 10px rgba(125, 126, 128, 0.16);
}

.van-calendar__header-subtitle,
.van-calendar__header-title,
.van-calendar__month-title {
  height: 44px;
  font-weight: 500;
  line-height: 44px;
  text-align: center;
}

.van-calendar__header-title {
  font-size: 16px;
}

.van-calendar__header-subtitle,
.van-calendar__month-title {
  font-size: 14px;
}

.van-calendar__weekdays {
  display: flex;
}

.van-calendar__weekday {
  flex: 1;
  font-size: 12px;
  line-height: 30px;
  text-align: center;
}

.van-calendar__body {
  flex: 1;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.van-calendar__days {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-calendar__month-mark {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 0;
  color: rgba(242, 243, 245, 0.8);
  font-size: 160px;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.van-calendar__day,
.van-calendar__selected-day {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.van-calendar__day {
  position: relative;
  width: 14.285%;
  height: 64px;
  font-size: 16px;
  cursor: pointer;
}

.van-calendar__day--end,
.van-calendar__day--multiple-middle,
.van-calendar__day--multiple-selected,
.van-calendar__day--start,
.van-calendar__day--start-end {
  color: #fff;
  background-color: #ee0a24;
}

.van-calendar__day--start {
  border-radius: 4px 0 0 4px;
}

.van-calendar__day--end {
  border-radius: 0 4px 4px 0;
}

.van-calendar__day--multiple-selected,
.van-calendar__day--start-end {
  border-radius: 4px;
}

.van-calendar__day--middle {
  color: #ee0a24;
}

.van-calendar__day--middle:after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: currentColor;
  opacity: 0.1;
  content: '';
}

.van-calendar__day--disabled {
  color: #c8c9cc;
  cursor: default;
}

.van-calendar__bottom-info,
.van-calendar__top-info {
  position: absolute;
  right: 0;
  left: 0;
  font-size: 10px;
  line-height: 14px;
}

@media (max-width: 350px) {
  .van-calendar__bottom-info,
  .van-calendar__top-info {
    font-size: 9px;
  }
}

.van-calendar__top-info {
  top: 6px;
}

.van-calendar__bottom-info {
  bottom: 6px;
}

.van-calendar__selected-day {
  width: 54px;
  height: 54px;
  color: #fff;
  background-color: #ee0a24;
  border-radius: 4px;
}

.van-calendar__footer {
  flex-shrink: 0;
  padding: 0 16px env(safe-area-inset-bottom);
}

.van-calendar__footer--unfit {
  padding-bottom: 0;
}

.van-calendar__confirm {
  height: 36px;
  margin: 7px 0;
}

.van-picker {
  position: relative;
  background-color: #fff;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-picker__toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
}

.van-picker__cancel,
.van-picker__confirm {
  height: 100%;
  padding: 0 16px;
  font-size: 14px;
  background-color: transparent;
  border: none;
  cursor: pointer;
}

.van-picker__cancel:active,
.van-picker__confirm:active {
  opacity: 0.7;
}

.van-picker__confirm {
  color: #576b95;
}

.van-picker__cancel {
  color: #969799;
}

.van-picker__title {
  max-width: 50%;
  font-weight: 500;
  font-size: 16px;
  line-height: 20px;
  text-align: center;
}

.van-picker__columns {
  position: relative;
  display: flex;
  cursor: grab;
}

.van-picker__loading {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1989fa;
  background-color: hsla(0, 0%, 100%, 0.9);
}

.van-picker__frame {
  top: 50%;
  right: 16px;
  left: 16px;
  z-index: 3;
  transform: translateY(-50%);
}

.van-picker__frame,
.van-picker__mask {
  position: absolute;
  pointer-events: none;
}

.van-picker__mask {
  top: 0;
  left: 0;
  z-index: 2;
  width: 100%;
  height: 100%;
  background-image: linear-gradient(180deg, hsla(0, 0%, 100%, 0.9), hsla(0, 0%, 100%, 0.4)),
  linear-gradient(0deg, hsla(0, 0%, 100%, 0.9), hsla(0, 0%, 100%, 0.4));
  background-repeat: no-repeat;
  background-position: top, bottom;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.van-picker-column {
  flex: 1;
  overflow: hidden;
  font-size: 16px;
}

.van-picker-column__wrapper {
  transition-timing-function: cubic-bezier(0.23, 1, 0.68, 1);
}

.van-picker-column__item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 4px;
  color: #000;
}

.van-picker-column__item--disabled {
  cursor: not-allowed;
  opacity: 0.3;
}

.van-address-edit {
  padding: 12px;
}

.van-address-edit__fields {
  overflow: hidden;
  border-radius: 8px;
}

.van-address-edit__fields .van-field__label {
  width: 4em;
}

.van-address-edit__default {
  margin-top: 12px;
  overflow: hidden;
  border-radius: 8px;
}

.van-address-edit__buttons {
  padding: 32px 4px;
}

.van-address-edit__buttons .van-button {
  margin-bottom: 12px;
}

.van-address-edit-detail {
  padding: 0;
}

.van-address-edit-detail__search-item {
  background-color: #f2f3f5;
}

.van-address-edit-detail__keyword {
  color: #ee0a24;
}

.van-address-edit-detail__finish {
  color: #1989fa;
  font-size: 12px;
}

.van-action-sheet {
  max-height: 80%;
  color: #323233;
}

.van-action-sheet__cancel,
.van-action-sheet__item {
  display: block;
  width: 100%;
  padding: 14px 16px;
  font-size: 16px;
  background-color: #fff;
  border: none;
  cursor: pointer;
}

.van-action-sheet__cancel:active,
.van-action-sheet__item:active {
  background-color: #f2f3f5;
}

.van-action-sheet__item {
  line-height: 22px;
}

.van-action-sheet__item--disabled,
.van-action-sheet__item--loading {
  color: #c8c9cc;
}

.van-action-sheet__item--disabled:active,
.van-action-sheet__item--loading:active {
  background-color: #fff;
}

.van-action-sheet__item--disabled {
  cursor: not-allowed;
}

.van-action-sheet__item--loading {
  cursor: default;
}

.van-action-sheet__subname {
  margin-top: 8px;
  color: #969799;
  font-size: 12px;
  line-height: 20px;
}

.van-action-sheet__gap {
  display: block;
  height: 8px;
  background-color: #f7f8fa;
}

.van-action-sheet__header {
  font-weight: 500;
  font-size: 16px;
  line-height: 44px;
  text-align: center;
}

.van-action-sheet__description {
  position: relative;
  padding: 20px 16px;
  color: #969799;
  font-size: 14px;
  line-height: 20px;
  text-align: center;
}

.van-action-sheet__description:after {
  position: absolute;
  box-sizing: border-box;
  content: ' ';
  pointer-events: none;
  right: 16px;
  bottom: 0;
  left: 16px;
  border-bottom: 1px solid #ebedf0;
  transform: scaleY(0.5);
}

.van-action-sheet__close {
  position: absolute;
  top: 0;
  right: 0;
  padding: 0 16px;
  color: #c8c9cc;
  font-size: 22px;
  line-height: inherit;
}

.van-action-sheet__close:active {
  color: #969799;
}

.van-radio-group--horizontal {
  display: flex;
  flex-wrap: wrap;
}

.van-contact-list {
  box-sizing: border-box;
  height: 100%;
  padding-bottom: 50px;
}

.van-contact-list__item {
  padding: 16px;
}

.van-contact-list__item-value {
  display: flex;
  align-items: center;
  padding-right: 32px;
  padding-left: 8px;
}

.van-contact-list__item-tag {
  flex: none;
  margin-left: 8px;
  padding-top: 0;
  padding-bottom: 0;
  line-height: 1.4em;
}

.van-contact-list__group {
  box-sizing: border-box;
  height: 100%;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}

.van-contact-list__edit {
  font-size: 16px;
}

.van-contact-list__bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  padding: 5px 16px;
  background-color: #fff;
}

.van-contact-list__add {
  height: 40px;
  line-height: 38px;
}

.van-address-list {
  box-sizing: border-box;
  height: 100%;
  padding: 12px 12px 100px;
}

.van-address-list__bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 999;
  box-sizing: border-box;
  width: 100%;
  padding: 0 16px env(safe-area-inset-bottom);
  background-color: #fff;
}

.van-address-list__add {
  height: 40px;
  margin: 5px 0;
  line-height: 38px;
}

.van-address-list__disabled-text {
  padding: 20px 0 16px;
  color: #969799;
  font-size: 14px;
  line-height: 20px;
}

.van-address-item {
  padding: 12px;
  background-color: #fff;
  border-radius: 8px;
}

.van-address-item:not(:last-child) {
  margin-bottom: 12px;
}

.van-address-item__value {
  padding-right: 44px;
}

.van-address-item__name {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 16px;
  line-height: 22px;
}

.van-address-item__tag {
  flex: none;
  margin-left: 8px;
  padding-top: 0;
  padding-bottom: 0;
  line-height: 1.4em;
}

.van-address-item__address {
  color: #323233;
  font-size: 13px;
  line-height: 18px;
}

.van-address-item--disabled .van-address-item__address,
.van-address-item--disabled .van-address-item__name {
  color: #c8c9cc;
}

.van-address-item__edit {
  position: absolute;
  top: 50%;
  right: 16px;
  color: #969799;
  font-size: 20px;
  transform: translateY(-50%);
}

.van-address-item .van-cell {
  padding: 0;
}

.van-address-item .van-radio__label {
  margin-left: 12px;
}

.van-address-item .van-radio__icon--checked .van-icon {
  background-color: #ee0a24;
  border-color: #ee0a24;
}

.van-cell-group {
  background-color: #fff;
}

.van-cell-group__title {
  padding: 16px 16px 8px;
  color: #969799;
  font-size: 14px;
  line-height: 16px;
}

.van-panel {
  background: #fff;
}

.van-panel__header-value {
  color: #ee0a24;
}

.van-panel__footer {
  padding: 8px 16px;
}

.van-checkbox-group--horizontal {
  display: flex;
  flex-wrap: wrap;
}

.van-circle {
  position: relative;
  display: inline-block;
  text-align: center;
}

.van-circle__layer {
  fill: none;
  stroke-linecap: round;
}

.van-circle__text {
  position: absolute;
  top: 50%;
  left: 0;
  box-sizing: border-box;
  width: 100%;
  padding: 0 4px;
  color: #323233;
  font-weight: 500;
  font-size: 14px;
  line-height: 18px;
  transform: translateY(-50%);
}

.van-col {
  float: left;
  box-sizing: border-box;
  min-height: 1px;
}

.van-col--1 {
  width: 4.16666667%;
}

.van-col--offset-1 {
  margin-left: 4.16666667%;
}

.van-col--2 {
  width: 8.33333333%;
}

.van-col--offset-2 {
  margin-left: 8.33333333%;
}

.van-col--3 {
  width: 12.5%;
}

.van-col--offset-3 {
  margin-left: 12.5%;
}

.van-col--4 {
  width: 16.66666667%;
}

.van-col--offset-4 {
  margin-left: 16.66666667%;
}

.van-col--5 {
  width: 20.83333333%;
}

.van-col--offset-5 {
  margin-left: 20.83333333%;
}

.van-col--6 {
  width: 25%;
}

.van-col--offset-6 {
  margin-left: 25%;
}

.van-col--7 {
  width: 29.16666667%;
}

.van-col--offset-7 {
  margin-left: 29.16666667%;
}

.van-col--8 {
  width: 33.33333333%;
}

.van-col--offset-8 {
  margin-left: 33.33333333%;
}

.van-col--9 {
  width: 37.5%;
}

.van-col--offset-9 {
  margin-left: 37.5%;
}

.van-col--10 {
  width: 41.66666667%;
}

.van-col--offset-10 {
  margin-left: 41.66666667%;
}

.van-col--11 {
  width: 45.83333333%;
}

.van-col--offset-11 {
  margin-left: 45.83333333%;
}

.van-col--12 {
  width: 50%;
}

.van-col--offset-12 {
  margin-left: 50%;
}

.van-col--13 {
  width: 54.16666667%;
}

.van-col--offset-13 {
  margin-left: 54.16666667%;
}

.van-col--14 {
  width: 58.33333333%;
}

.van-col--offset-14 {
  margin-left: 58.33333333%;
}

.van-col--15 {
  width: 62.5%;
}

.van-col--offset-15 {
  margin-left: 62.5%;
}

.van-col--16 {
  width: 66.66666667%;
}

.van-col--offset-16 {
  margin-left: 66.66666667%;
}

.van-col--17 {
  width: 70.83333333%;
}

.van-col--offset-17 {
  margin-left: 70.83333333%;
}

.van-col--18 {
  width: 75%;
}

.van-col--offset-18 {
  margin-left: 75%;
}

.van-col--19 {
  width: 79.16666667%;
}

.van-col--offset-19 {
  margin-left: 79.16666667%;
}

.van-col--20 {
  width: 83.33333333%;
}

.van-col--offset-20 {
  margin-left: 83.33333333%;
}

.van-col--21 {
  width: 87.5%;
}

.van-col--offset-21 {
  margin-left: 87.5%;
}

.van-col--22 {
  width: 91.66666667%;
}

.van-col--offset-22 {
  margin-left: 91.66666667%;
}

.van-col--23 {
  width: 95.83333333%;
}

.van-col--offset-23 {
  margin-left: 95.83333333%;
}

.van-col--24 {
  width: 100%;
}

.van-col--offset-24 {
  margin-left: 100%;
}

.van-count-down {
  color: #323233;
  font-size: 14px;
  line-height: 20px;
}

.van-tab__pane,
.van-tab__pane-wrapper {
  flex-shrink: 0;
  box-sizing: border-box;
  width: 100%;
}

.van-tab__pane-wrapper--inactive {
  height: 0;
  overflow: visible;
}

.van-sticky--fixed {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 99;
}

.van-tab {
  position: relative;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 0 4px;
  color: #646566;
  font-size: 14px;
  cursor: pointer;
}

.van-tab--active {
  color: #323233;
  font-weight: 500;
}

.van-tab--disabled {
  color: #c8c9cc;
  cursor: not-allowed;
}

.van-tab__text--ellipsis {
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.van-tab__text-wrapper,
.van-tabs {
  position: relative;
}

.van-tabs__wrap {
  overflow: hidden;
}

.van-tabs__wrap--page-top {
  position: fixed;
}

.van-tabs__wrap--content-bottom {
  top: auto;
  bottom: 0;
}

.van-tabs__wrap--scrollable .van-tab {
  flex: 0 0 22%;
}

.van-tabs__wrap--scrollable .van-tab--complete {
  flex: 1 0 auto;
}

.van-tabs__wrap--scrollable .van-tabs__nav {
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
}

.van-tabs__wrap--scrollable .van-tabs__nav::-webkit-scrollbar {
  display: none;
}

.van-tabs__nav {
  position: relative;
  display: flex;
  background-color: #fff;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-tabs__nav--line {
  box-sizing: content-box;
  height: 100%;
  padding-bottom: 15px;
}

.van-tabs__nav--card {
  box-sizing: border-box;
  height: 30px;
  margin: 0 16px;
  border: 1px solid #ee0a24;
  border-radius: 2px;
}

.van-tabs__nav--card .van-tab {
  color: #ee0a24;
  border-right: 1px solid #ee0a24;
}

.van-tabs__nav--card .van-tab:last-child {
  border-right: none;
}

.van-tabs__nav--card .van-tab.van-tab--active {
  color: #fff;
  background-color: #ee0a24;
}

.van-tabs__nav--card .van-tab--disabled {
  color: #c8c9cc;
}

.van-tabs__line {
  position: absolute;
  bottom: 15px;
  left: 0;
  z-index: 1;
  height: 3px;
  background-color: #ee0a24;
  border-radius: 3px;
}

.van-tabs__track {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  will-change: left;
}

.van-tabs__content--animated {
  overflow: hidden;
}

.van-tabs--line .van-tabs__wrap {
  height: 44px;
}

.van-tabs--card > .van-tabs__wrap {
  height: 30px;
}

.van-coupon-list {
  position: relative;
  height: 100%;
  background-color: #f7f8fa;
}

.van-coupon-list__field {
  padding: 5px 0 5px 16px;
}

.van-coupon-list__field .van-field__body {
  height: 34px;
  padding-left: 12px;
  line-height: 34px;
  background: #f7f8fa;
  border-radius: 17px;
}

.van-coupon-list__field .van-field__body::-moz-placeholder {
  color: #c8c9cc;
}

.van-coupon-list__field .van-field__body:-ms-input-placeholder {
  color: #c8c9cc;
}

.van-coupon-list__field .van-field__body::placeholder {
  color: #c8c9cc;
}

.van-coupon-list__field .van-field__clear {
  margin-right: 0;
}

.van-coupon-list__exchange-bar {
  display: flex;
  align-items: center;
  background-color: #fff;
}

.van-coupon-list__exchange {
  flex: none;
  height: 32px;
  font-size: 16px;
  line-height: 30px;
  border: 0;
}

.van-coupon-list .van-tabs__wrap {
  box-shadow: 0 6px 12px -12px #969799;
}

.van-coupon-list__list {
  box-sizing: border-box;
  padding: 16px 0 24px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.van-coupon-list__list--with-bottom {
  padding-bottom: 66px;
}

.van-coupon-list__bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 999;
  box-sizing: border-box;
  width: 100%;
  padding: 5px 16px;
  font-weight: 500;
  background-color: #fff;
}

.van-coupon-list__close {
  height: 40px;
}

.van-coupon-list__empty {
  padding-top: 60px;
  text-align: center;
}

.van-coupon-list__empty p {
  margin: 16px 0;
  color: #969799;
  font-size: 14px;
  line-height: 20px;
}

.van-coupon-list__empty img {
  width: 200px;
  height: 200px;
}

.van-divider {
  display: flex;
  align-items: center;
  margin: 16px 0;
  color: #969799;
  font-size: 14px;
  line-height: 24px;
  border: 0 solid #ebedf0;
}

.van-divider:after,
.van-divider:before {
  display: block;
  flex: 1;
  box-sizing: border-box;
  height: 1px;
  border-color: inherit;
  border-style: inherit;
  border-width: 1px 0 0;
}

.van-divider:before {
  content: '';
}

.van-divider--hairline:after,
.van-divider--hairline:before {
  transform: scaleY(0.5);
}

.van-divider--dashed {
  border-style: dashed;
}

.van-divider--content-center:before,
.van-divider--content-left:before,
.van-divider--content-right:before {
  margin-right: 16px;
}

.van-divider--content-center:after,
.van-divider--content-left:after,
.van-divider--content-right:after {
  margin-left: 16px;
  content: '';
}

.van-divider--content-left:before,
.van-divider--content-right:after {
  max-width: 10%;
}

.van-dropdown-menu {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-dropdown-menu__bar {
  position: relative;
  display: flex;
  height: 48px;
  background-color: #fff;
  box-shadow: 0 2px 12px rgba(100, 101, 102, 0.08);
}

.van-dropdown-menu__bar--opened {
  z-index: 11;
}

.van-dropdown-menu__item {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  min-width: 0;
  cursor: pointer;
}

.van-dropdown-menu__item:active {
  opacity: 0.7;
}

.van-dropdown-menu__item--disabled:active {
  opacity: 1;
}

.van-dropdown-menu__item--disabled .van-dropdown-menu__title {
  color: #969799;
}

.van-dropdown-menu__title {
  position: relative;
  box-sizing: border-box;
  max-width: 100%;
  padding: 0 8px;
  color: #323233;
  font-size: 15px;
  line-height: 22px;
}

.van-dropdown-menu__title:after {
  position: absolute;
  top: 50%;
  right: -4px;
  margin-top: -5px;
  border-color: transparent transparent #dcdee0 #dcdee0;
  border-style: solid;
  border-width: 3px;
  transform: rotate(-45deg);
  opacity: 0.8;
  content: '';
}

.van-dropdown-menu__title--active {
  color: #1989fa;
}

.van-dropdown-menu__title--active:after {
  border-color: transparent transparent currentColor currentColor;
}

.van-dropdown-menu__title--down:after {
  margin-top: -1px;
  transform: rotate(135deg);
}

.van-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  padding: 32px 0;
}

.van-empty__image {
  width: 160px;
  height: 160px;
}

.van-empty__image img {
  width: 100%;
  height: 100%;
}

.van-empty__description {
  margin-top: 16px;
  padding: 0 60px;
  color: #969799;
  font-size: 14px;
  line-height: 20px;
}

.van-empty__bottom {
  margin-top: 24px;
}

.van-goods-action {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  box-sizing: content-box;
  height: 50px;
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #fff;
}

.van-goods-action--unfit {
  padding-bottom: 0;
}

.van-grid {
  display: flex;
  flex-wrap: wrap;
}

.van-swipe {
  position: relative;
  overflow: hidden;
  cursor: grab;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-swipe__track {
  display: flex;
  height: 100%;
}

.van-swipe__track--vertical {
  flex-direction: column;
}

.van-swipe__indicators {
  position: absolute;
  bottom: 12px;
  left: 50%;
  display: flex;
  transform: translateX(-50%);
}

.van-swipe__indicators--vertical {
  top: 50%;
  bottom: auto;
  left: 12px;
  flex-direction: column;
  transform: translateY(-50%);
}

.van-swipe__indicators--vertical .van-swipe__indicator:not(:last-child) {
  margin-bottom: 6px;
}

.van-swipe__indicator {
  width: 6px;
  height: 6px;
  background-color: #ebedf0;
  border-radius: 100%;
  opacity: 0.3;
  transition: opacity 0.2s;
}

.van-swipe__indicator:not(:last-child) {
  margin-right: 6px;
}

.van-swipe__indicator--active {
  background-color: #1989fa;
  opacity: 1;
}

.van-swipe-item {
  position: relative;
  flex-shrink: 0;
  width: 100%;
  height: 100%;
}

.van-image-preview {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.van-image-preview__swipe {
  height: 100%;
}

.van-image-preview__swipe-item {
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.van-image-preview__cover {
  position: absolute;
  top: 0;
  left: 0;
}

.van-image-preview__image {
  width: 100%;
  transition-property: transform;
}

.van-image-preview__image--vertical {
  width: auto;
  height: 100%;
}

.van-image-preview__image img {
  -webkit-user-drag: none;
}

.van-image-preview__image .van-image__error {
  top: 30%;
  height: 40%;
}

.van-image-preview__image .van-image__error-icon {
  font-size: 36px;
}

.van-image-preview__image .van-image__loading {
  background-color: transparent;
}

.van-image-preview__index {
  position: absolute;
  top: 16px;
  left: 50%;
  color: #fff;
  font-size: 14px;
  line-height: 22px;
  text-shadow: 0 1px 1px #323233;
  transform: translate(-50%);
}

.van-image-preview__overlay {
  background-color: rgba(0, 0, 0, 0.9);
}

.van-image-preview__close-icon {
  position: absolute;
  z-index: 1;
  color: #c8c9cc;
  font-size: 22px;
  cursor: pointer;
}

.van-image-preview__close-icon:active {
  color: #969799;
}

.van-image-preview__close-icon--top-left {
  top: 16px;
  left: 16px;
}

.van-image-preview__close-icon--top-right {
  top: 16px;
  right: 16px;
}

.van-image-preview__close-icon--bottom-left {
  bottom: 16px;
  left: 16px;
}

.van-image-preview__close-icon--bottom-right {
  right: 16px;
  bottom: 16px;
}

.van-uploader {
  position: relative;
  display: inline-block;
}

.van-uploader__wrapper {
  display: flex;
  flex-wrap: wrap;
}

.van-uploader__wrapper--disabled {
  opacity: 0.5;
}

.van-uploader__input {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  cursor: pointer;
  opacity: 0;
}

.van-uploader__input-wrapper {
  position: relative;
}

.van-uploader__input:disabled {
  cursor: not-allowed;
}

.van-uploader__upload {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  width: 80px;
  height: 80px;
  margin: 0 8px 8px 0;
  background-color: #f7f8fa;
}

.van-uploader__upload:active {
  background-color: #f2f3f5;
}

.van-uploader__upload-icon {
  color: #dcdee0;
  font-size: 24px;
}

.van-uploader__upload-text {
  margin-top: 8px;
  color: #969799;
  font-size: 12px;
}

.van-uploader__preview {
  position: relative;
  margin: 0 8px 8px 0;
  cursor: pointer;
}

.van-uploader__preview-image {
  display: block;
  width: 80px;
  height: 80px;
  overflow: hidden;
}

.van-uploader__preview-delete {
  position: absolute;
  top: 0;
  right: 0;
  width: 14px;
  height: 14px;
  background-color: rgba(0, 0, 0, 0.7);
  border-radius: 0 0 0 12px;
}

.van-uploader__preview-delete-icon {
  position: absolute;
  top: -2px;
  right: -2px;
  color: #fff;
  font-size: 16px;
  transform: scale(0.5);
}

.van-uploader__mask,
.van-uploader__preview-cover {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.van-uploader__mask {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: rgba(50, 50, 51, 0.88);
}

.van-uploader__mask-icon {
  font-size: 22px;
}

.van-uploader__mask-message {
  margin-top: 6px;
  padding: 0 4px;
  font-size: 12px;
  line-height: 14px;
}

.van-uploader__loading {
  width: 22px;
  height: 22px;
  color: #fff;
}

.van-uploader__file {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background-color: #f7f8fa;
}

.van-uploader__file-icon {
  color: #646566;
  font-size: 20px;
}

.van-uploader__file-name {
  box-sizing: border-box;
  width: 100%;
  margin-top: 8px;
  padding: 0 4px;
  color: #646566;
  font-size: 12px;
  text-align: center;
}

.van-index-anchor {
  z-index: 1;
  box-sizing: border-box;
  padding: 0 16px;
  color: #323233;
  font-weight: 500;
  font-size: 14px;
  line-height: 32px;
  background-color: transparent;
}

.van-index-anchor--sticky {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  color: #07c160;
  background-color: #fff;
}

.van-index-bar__sidebar {
  position: fixed;
  top: 50%;
  right: 0;
  z-index: 2;
  display: flex;
  flex-direction: column;
  text-align: center;
  transform: translateY(-50%);
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-index-bar__index {
  padding: 0 4px 0 16px;
  font-weight: 500;
  font-size: 10px;
  line-height: 14px;
}

.van-index-bar__index--active {
  color: #07c160;
}

.van-pagination {
  display: flex;
  font-size: 14px;
}

.van-pagination__item,
.van-pagination__page-desc {
  display: flex;
  align-items: center;
  justify-content: center;
}

.van-pagination__item {
  flex: 1;
  box-sizing: border-box;
  min-width: 36px;
  height: 40px;
  color: #1989fa;
  background-color: #fff;
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-pagination__item:active {
  color: #fff;
  background-color: #1989fa;
}

.van-pagination__item:after {
  border-width: 1px 0 1px 1px;
}

.van-pagination__item:last-child:after {
  border-right-width: 1px;
}

.van-pagination__item--active {
  color: #fff;
  background-color: #1989fa;
}

.van-pagination__next,
.van-pagination__prev {
  padding: 0 4px;
  cursor: pointer;
}

.van-pagination__item--disabled,
.van-pagination__item--disabled:active {
  color: #646566;
  background-color: #f7f8fa;
  cursor: not-allowed;
  opacity: 0.5;
}

.van-pagination__page {
  flex-grow: 0;
}

.van-pagination__page-desc {
  flex: 1;
  height: 40px;
  color: #646566;
}

.van-pagination--simple .van-pagination__next:after,
.van-pagination--simple .van-pagination__prev:after {
  border-width: 1px;
}

.van-password-input {
  position: relative;
  margin: 0 16px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-password-input__error-info,
.van-password-input__info {
  margin-top: 16px;
  font-size: 14px;
  text-align: center;
}

.van-password-input__info {
  color: #969799;
}

.van-password-input__error-info {
  color: #ee0a24;
}

.van-password-input__security {
  display: flex;
  width: 100%;
  height: 50px;
  cursor: pointer;
}

.van-password-input__security:after {
  border-radius: 6px;
}

.van-password-input__security li {
  position: relative;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 20px;
  line-height: 1.2;
  background-color: #fff;
}

.van-password-input__security i {
  width: 10px;
  height: 10px;
  background-color: #000;
  border-radius: 100%;
  visibility: hidden;
}

.van-password-input__cursor,
.van-password-input__security i {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.van-password-input__cursor {
  width: 1px;
  height: 40%;
  background-color: #323233;
  -webkit-animation: van-cursor-flicker 1s infinite;
  animation: van-cursor-flicker 1s infinite;
}

@-webkit-keyframes van-cursor-flicker {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

@keyframes van-cursor-flicker {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

.van-progress {
  position: relative;
  height: 4px;
  background: #ebedf0;
  border-radius: 4px;
}

.van-progress__portion {
  position: absolute;
  left: 0;
  height: 100%;
  background: #1989fa;
  border-radius: inherit;
}

.van-progress__pivot {
  position: absolute;
  top: 50%;
  box-sizing: border-box;
  min-width: 3.6em;
  padding: 0 5px;
  color: #fff;
  font-size: 10px;
  line-height: 1.6;
  text-align: center;
  word-break: keep-all;
  background-color: #1989fa;
  border-radius: 1em;
  transform: translateY(-50%);
}

.van-row:after {
  display: table;
  clear: both;
  content: '';
}

.van-row--flex {
  display: flex;
}

.van-row--flex:after {
  display: none;
}

.van-row--justify-center {
  justify-content: center;
}

.van-row--justify-end {
  justify-content: flex-end;
}

.van-row--justify-space-between {
  justify-content: space-between;
}

.van-row--justify-space-around {
  justify-content: space-around;
}

.van-row--align-center {
  align-items: center;
}

.van-row--align-bottom {
  align-items: flex-end;
}

.van-sidebar {
  width: 85px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.van-tree-select {
  position: relative;
  display: flex;
  font-size: 14px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-tree-select__nav {
  flex: 1;
  overflow-y: auto;
  background-color: #f7f8fa;
  -webkit-overflow-scrolling: touch;
}

.van-tree-select__nav-item {
  padding: 14px 12px;
}

.van-tree-select__content {
  flex: 2;
  overflow-y: auto;
  background-color: #fff;
  -webkit-overflow-scrolling: touch;
}

.van-tree-select__item {
  position: relative;
  padding: 0 32px 0 16px;
  font-weight: 500;
  line-height: 48px;
  cursor: pointer;
}

.van-tree-select__item--active {
  color: #ee0a24;
}

.van-tree-select__item--disabled {
  color: #c8c9cc;
  cursor: not-allowed;
}

.van-tree-select__selected {
  position: absolute;
  top: 50%;
  right: 16px;
  margin-top: -8px;
  font-size: 16px;
}

.van-skeleton {
  display: flex;
  padding: 0 16px;
}

.van-skeleton__avatar {
  flex-shrink: 0;
  margin-right: 16px;
  background-color: #f2f3f5;
}

.van-skeleton__avatar--round {
  border-radius: 999px;
}

.van-skeleton__content {
  width: 100%;
}

.van-skeleton__avatar + .van-skeleton__content {
  padding-top: 8px;
}

.van-skeleton__row,
.van-skeleton__title {
  height: 16px;
  background-color: #f2f3f5;
}

.van-skeleton__title {
  margin: 0;
}

.van-skeleton__row:not(:first-child) {
  margin-top: 12px;
}

.van-skeleton__title + .van-skeleton__row {
  margin-top: 20px;
}

.van-skeleton--animate {
  -webkit-animation: van-skeleton-blink 1.2s ease-in-out infinite;
  animation: van-skeleton-blink 1.2s ease-in-out infinite;
}

.van-skeleton--round .van-skeleton__row,
.van-skeleton--round .van-skeleton__title {
  border-radius: 999px;
}

@-webkit-keyframes van-skeleton-blink {
  50% {
    opacity: 0.6;
  }
}

@keyframes van-skeleton-blink {
  50% {
    opacity: 0.6;
  }
}

.van-stepper {
  font-size: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.van-stepper__minus,
.van-stepper__plus {
  position: relative;
  box-sizing: border-box;
  width: 28px;
  height: 28px;
  margin: 0;
  padding: 0;
  color: #323233;
  vertical-align: middle;
  background-color: #f2f3f5;
  border: 0;
  cursor: pointer;
}

.van-stepper__minus:before,
.van-stepper__plus:before {
  width: 50%;
  height: 1px;
}

.van-stepper__minus:after,
.van-stepper__plus:after {
  width: 1px;
  height: 50%;
}

.van-stepper__minus:after,
.van-stepper__minus:before,
.van-stepper__plus:after,
.van-stepper__plus:before {
  position: absolute;
  top: 50%;
  left: 50%;
  background-color: currentColor;
  transform: translate(-50%, -50%);
  content: '';
}

.van-stepper__minus:active,
.van-stepper__plus:active {
  background-color: #e8e8e8;
}

.van-stepper__minus--disabled,
.van-stepper__plus--disabled {
  color: #c8c9cc;
  background-color: #f7f8fa;
  cursor: not-allowed;
}

.van-stepper__minus--disabled:active,
.van-stepper__plus--disabled:active {
  background-color: #f7f8fa;
}

.van-stepper__minus {
  border-radius: 4px 0 0 4px;
}

.van-stepper__minus:after {
  display: none;
}

.van-stepper__plus {
  border-radius: 0 4px 4px 0;
}

.van-stepper__input {
  box-sizing: border-box;
  width: 32px;
  height: 28px;
  margin: 0 2px;
  padding: 0;
  color: #323233;
  font-size: 14px;
  line-height: normal;
  text-align: center;
  vertical-align: middle;
  background-color: #f2f3f5;
  border-width: 1px 0;
  border-radius: 0;
  -webkit-appearance: none;
}

.van-stepper__input:disabled {
  color: #c8c9cc;
  background-color: #f2f3f5;
  -webkit-text-fill-color: currentColor;
  opacity: 1;
}

.van-stepper__input:-moz-read-only {
  cursor: default;
}

.van-stepper__input:read-only {
  cursor: default;
}

.van-stepper--round .van-stepper__input {
  background-color: transparent;
}

.van-stepper--round .van-stepper__minus,
.van-stepper--round .van-stepper__plus {
  border-radius: 100%;
}

.van-stepper--round .van-stepper__minus:active,
.van-stepper--round .van-stepper__plus:active {
  opacity: 0.7;
}

.van-stepper--round .van-stepper__minus--disabled,
.van-stepper--round .van-stepper__minus--disabled:active,
.van-stepper--round .van-stepper__plus--disabled,
.van-stepper--round .van-stepper__plus--disabled:active {
  opacity: 0.3;
}

.van-stepper--round .van-stepper__plus {
  color: #fff;
  background-color: #ee0a24;
}

.van-stepper--round .van-stepper__minus {
  color: #ee0a24;
  background-color: #fff;
  border: 1px solid #ee0a24;
}

.van-sku-container {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  min-height: 50%;
  max-height: 80%;
  overflow-y: visible;
  font-size: 14px;
  background: #fff;
}

.van-sku-body {
  flex: 1 1 auto;
  min-height: 44px;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;
}

.van-sku-body::-webkit-scrollbar {
  display: none;
}

.van-sku-header {
  display: flex;
  flex-shrink: 0;
  margin: 0 16px;
}

.van-sku-header__img-wrap {
  width: 96px;
  height: 96px;
  margin: 12px 12px 12px 0;
  overflow: hidden;
  border-radius: 4px;
}

.van-sku-header__goods-info {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 12px 20px 12px 0;
}

.van-sku-header-item {
  margin-top: 8px;
  color: #969799;
  font-size: 12px;
  line-height: 16px;
}

.van-sku__price-symbol {
  font-size: 16px;
  vertical-align: bottom;
}

.van-sku__price-num {
  font-weight: 500;
  font-size: 22px;
  vertical-align: bottom;
  word-wrap: break-word;
}

.van-sku__goods-price {
  color: #ee0a24;
}

.van-sku__price-tag {
  position: relative;
  display: inline-block;
  margin-left: 8px;
  padding: 0 5px;
  overflow: hidden;
  color: #ee0a24;
  font-size: 12px;
  line-height: 16px;
  border-radius: 8px;
}

.van-sku__price-tag:before {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: currentColor;
  opacity: 0.1;
  content: '';
}

.van-sku-group-container {
  padding-top: 12px;
}

.van-sku-group-container--hide-soldout .van-sku-row__item--disabled {
  display: none;
}

.van-sku-row {
  margin: 0 16px 12px;
}

.van-sku-row:last-child {
  margin-bottom: 0;
}

.van-sku-row__image-item,
.van-sku-row__item {
  position: relative;
  overflow: hidden;
  color: #323233;
  border-radius: 4px;
  cursor: pointer;
}

.van-sku-row__image-item:before,
.van-sku-row__item:before {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #f7f8fa;
  content: '';
}

.van-sku-row__image-item--active,
.van-sku-row__item--active {
  color: #ee0a24;
}

.van-sku-row__image-item--active:before,
.van-sku-row__item--active:before {
  background: currentColor;
  opacity: 0.1;
}

.van-sku-row__item {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 40px;
  margin: 0 12px 12px 0;
  font-size: 13px;
  line-height: 16px;
  vertical-align: middle;
}

.van-sku-row__item-img {
  z-index: 1;
  width: 24px;
  height: 24px;
  margin: 4px 0 4px 4px;
  -o-object-fit: cover;
  object-fit: cover;
  border-radius: 2px;
}

.van-sku-row__item-name {
  z-index: 1;
  padding: 8px;
}

.van-sku-row__item--disabled {
  color: #c8c9cc;
  background: #f2f3f5;
  cursor: not-allowed;
}

.van-sku-row__item--disabled .van-sku-row__item-img {
  opacity: 0.3;
}

.van-sku-row__image {
  margin-right: 0;
}

.van-sku-row__image-item {
  display: flex;
  flex-direction: column;
  width: 110px;
  margin: 0 4px 4px 0;
  border: 1px solid transparent;
}

.van-sku-row__image-item:last-child {
  margin-right: 0;
}

.van-sku-row__image-item-img {
  width: 100%;
  height: 110px;
}

.van-sku-row__image-item-img-icon {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 3;
  width: 18px;
  height: 18px;
}

.van-sku-row__image-item-name {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  height: 40px;
  padding: 4px;
  font-size: 12px;
  line-height: 16px;
}

.van-sku-row__image-item-name span {
  word-wrap: break-word;
}

.van-sku-row__image-item--active {
  border-color: currentColor;
}

.van-sku-row__image-item--disabled {
  color: #c8c9cc;
  cursor: not-allowed;
}

.van-sku-row__image-item--disabled:before {
  z-index: 2;
  background: #f2f3f5;
  opacity: 0.4;
}

.van-sku-row__title {
  padding-bottom: 12px;
}

.van-sku-row__title-multiple {
  color: #969799;
}

.van-sku-row__scroller {
  margin: 0 -16px;
  overflow-x: scroll;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
}

.van-sku-row__scroller::-webkit-scrollbar {
  display: none;
}

.van-sku-row__row {
  display: inline-flex;
  margin-bottom: 4px;
  padding: 0 16px;
}

.van-sku-row__indicator {
  width: 40px;
  height: 4px;
  background: #ebedf0;
  border-radius: 2px;
}

.van-sku-row__indicator-wrapper {
  display: flex;
  justify-content: center;
  padding-bottom: 16px;
}

.van-sku-row__indicator-slider {
  width: 50%;
  height: 100%;
  background-color: #ee0a24;
  border-radius: 2px;
}

.van-sku-stepper-stock {
  padding: 12px 16px;
  overflow: hidden;
  line-height: 30px;
}

.van-sku__stepper {
  float: right;
  padding-left: 4px;
}

.van-sku__stepper-title {
  float: left;
}

.van-sku__stepper-quota {
  float: right;
  color: #ee0a24;
  font-size: 12px;
}

.van-sku__stock {
  display: inline-block;
  margin-right: 8px;
  color: #969799;
  font-size: 12px;
}

.van-sku__stock-num--highlight {
  color: #ee0a24;
}

.van-sku-messages {
  padding-bottom: 32px;
}

.van-sku-messages__image-cell .van-cell__title {
  max-width: 6.2em;
  margin-right: 12px;
  color: #646566;
  text-align: left;
  word-wrap: break-word;
}

.van-sku-messages__image-cell .van-cell__value {
  overflow: visible;
  text-align: left;
}

.van-sku-messages__image-cell-label {
  color: #969799;
  font-size: 12px;
  line-height: 18px;
}

.van-sku-actions {
  display: flex;
  flex-shrink: 0;
  padding: 8px 16px;
}

.van-sku-actions .van-button {
  height: 40px;
  font-weight: 500;
  font-size: 14px;
  border: none;
  border-radius: 0;
}

.van-sku-actions .van-button:first-of-type {
  border-top-left-radius: 20px;
  border-bottom-left-radius: 20px;
}

.van-sku-actions .van-button:last-of-type {
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
}

.van-sku-actions .van-button--warning {
  background: linear-gradient(90deg, #ffd01e, #ff8917);
}

.van-sku-actions .van-button--danger {
  background: linear-gradient(90deg, #ff6034, #ee0a24);
}

.van-slider {
  position: relative;
  width: 100%;
  height: 2px;
  background-color: #ebedf0;
  border-radius: 999px;
  cursor: pointer;
}

.van-slider:before {
  position: absolute;
  top: -8px;
  right: 0;
  bottom: -8px;
  left: 0;
  content: '';
}

.van-slider__bar {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #1989fa;
  border-radius: inherit;
  transition: width 0.2s, height 0.2s;
}

.van-slider__button {
  width: 24px;
  height: 24px;
  background-color: #fff;
  border-radius: 50%;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.van-slider__button-wrapper {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translate3d(50%, -50%, 0);
  cursor: grab;
}

.van-slider--disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.van-slider--disabled .van-slider__button-wrapper {
  cursor: not-allowed;
}

.van-slider--vertical {
  display: inline-block;
  width: 2px;
  height: 100%;
}

.van-slider--vertical .van-slider__button-wrapper {
  top: auto;
  right: 50%;
  bottom: 0;
  transform: translate3d(50%, 50%, 0);
}

.van-slider--vertical:before {
  top: 0;
  right: -8px;
  bottom: 0;
  left: -8px;
}

.van-steps {
  overflow: hidden;
  background-color: #fff;
}

.van-steps--horizontal {
  padding: 10px 10px 0;
}

.van-steps--horizontal .van-steps__items {
  position: relative;
  display: flex;
  margin: 0 0 10px;
  padding-bottom: 22px;
}

.van-steps--vertical {
  padding: 0 0 0 32px;
}

.van-swipe-cell {
  position: relative;
  overflow: hidden;
  cursor: grab;
}

.van-swipe-cell__wrapper {
  transition-timing-function: cubic-bezier(0.18, 0.89, 0.32, 1);
  transition-property: transform;
}

.van-swipe-cell__left,
.van-swipe-cell__right {
  position: absolute;
  top: 0;
  height: 100%;
}

.van-swipe-cell__left {
  left: 0;
  transform: translate3d(-100%, 0, 0);
}

.van-swipe-cell__right {
  right: 0;
  transform: translate3d(100%, 0, 0);
}

.van-tabbar {
  z-index: 1;
  display: flex;
  box-sizing: content-box;
  width: 100%;
  height: 50px;
  padding-bottom: env(safe-area-inset-bottom);
  background-color: #fff;
}

.van-tabbar--fixed {
  position: fixed;
  bottom: 0;
  left: 0;
}

.van-tabbar--unfit {
  padding-bottom: 0;
}

.clearfix {
  clear: both;
}

.clearfix:after,
.clearfix:before {
  content: '';
  display: table;
}

.clearfix-row {
  clear: both;
  display: inline-block;
  vertical-align: top;
  width: 100%;
}

.clearfix-row:after,
.clearfix-row:before {
  content: '';
  display: table;
}

.box-shadow5 {
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.07);
}

@font-face {
  font-family: 'Urbanist-Regular', sans-serif;
  src: url(./Urbanist-Regular.64ab880a.ttf);
  // font-family: "Urbanist-Regular", sans-serif;
  // src: url(./Roboto-Regular.5d58a0a.ttf);
  font-display: swap;
}

@font-face {
  font-family: 'Urbanist-Medium', sans-serif;
  src: url(./Urbanist-Medium.5f9e5310.ttf);
  // font-family: Roboto-Medium;
  // src: url(./Roboto-Medium.d2d7f0c.ttf);
  font-display: swap;
}

.font-en {
  font-family: "Urbanist-Regular", sans-serif;
  // font-family: Roboto-Regular, MicrosoftYaHei, MicrosoftYaHei-Bold, PingFang SC;
}

.font-en .font-bold {
  font-weight: 700;
}

.van-tabs--line .van-tabs__wrap {
  height: 0.96rem;
}

.van-tab {
  line-height: 0.96rem;
  font-size: 0.37rem;
  font-family: "Urbanist-Regular", sans-serif;
  font-weight: 500;
  color: #333;
}

.van-tab--active .van-tab__text {
  font-family: Roboto-Medium !important;
  font-weight: 500;
}

.van-nav-bar__left {
  height: 100%;
  vertical-align: middle;
}

.van-nav-bar {
  background-color: #0f80da;
  height: 1.17rem;
  line-height: 1.17rem;
}

.van-nav-bar .van-icon {
  color: #fff;
}

.van-collapse-item__content {
  padding: 0;
  line-height: 0.4rem;
}

.van-overlay {
  z-index: 1000 !important;
}

.van-hairline--top-bottom:after,
.van-hairline-unset--top-bottom:after {
  border-width: 0 !important;
}

.van-tabs__line {
  border-radius: 0 !important;
}

.h2h .van-checkbox__icon--checked .van-icon {
  color: #bfbfbf !important;
  background-color: #f1f1f1 !important;
  border: 1px solid #bfbfbf !important;
}

.league .van-cell:not(:last-child):after {
  border-bottom: none !important;
}

.league .van-hairline--top:after {
  border-top: none !important;
}

.van-icon-arrow:before {
  content: '\F00A';
  opacity: 0.35;
}

.van-collapse-item__title {
  height: 0.86rem;
  display: flex;
  align-items: center;
  padding: 0 0.21rem 0 0.32rem;
}

.van-dropdown-menu {
  height: 0.53rem !important;
}

.van-dropdown-menu__item {
  border-right: 1px solid #e3e3e3;
}

.van-dropdown-menu__title {
  font-size: 0.35rem;
  font-family: "Urbanist-Regular", sans-serif;
  font-weight: 400;
}

.league .van-dropdown-item,
.week_round .van-dropdown-item {
  transform: translateY(-1px);
}

.league .van-dropdown-item .van-cell__value,
.week_round .van-dropdown-item .van-cell__value {
  flex: 0;
}

.league .van-dropdown-item .van-cell__title,
.week_round .van-dropdown-item .van-cell__title {
  text-align: center;
}

.league .van-dropdown-item__option,
.week_round .van-dropdown-item__option {
  border-bottom: 1px solid #eee;
}

.league .maleMenu .van-dropdown-menu__title,
.league .roundMenu .van-dropdown-menu__title,
.week_round .maleMenu .van-dropdown-menu__title,
.week_round .roundMenu .van-dropdown-menu__title {
  opacity: 0;
}

.league .maleMenu .van-dropdown-menu__item,
.league .roundMenu .van-dropdown-menu__item,
.week_round .maleMenu .van-dropdown-menu__item,
.week_round .roundMenu .van-dropdown-menu__item {
  border-right: none;
}

.van-popup {
  width: 9.57rem !important;
  left: 0.215rem;
  background-color: transparent;
}

.van-count-down {
  font-size: 0.53rem;
  color: #999;
  height: 0.53rem;
  line-height: 0.53rem;
}

.van-sticky--fixed {
  z-index: 999;
}

.van-icon__image,
.van-image__error-icon {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
}

.van-image--round .van-image__loading {
  border-radius: 50% !important;
}

.van-tabs__wrap--scrollable .van-tab {
  padding: 0 0.5rem;
}

.bk_competition /deep/ .van-tab__text,
.bk_players /deep/ .van-tab__text,
.bk_teams /deep/ .van-tab__text,
.ft_competition /deep/ .van-tab__text,
.ft_players /deep/ .van-tab__text,
.ft_teams /deep/ .van-tab__text,
.innerMatchInfo /deep/ .van-tab__text,
.ts_competition /deep/ .van-tab__text,
.ts_players /deep/ .van-tab__text,
.ts_teams /deep/ .van-tab__text {
  font-size: 0.32rem;
}

.bk_competition /deep/ .van-tabs--line .van-tabs__wrap,
.bk_players /deep/ .van-tabs--line .van-tabs__wrap,
.bk_teams /deep/ .van-tabs--line .van-tabs__wrap,
.ft_competition /deep/ .van-tabs--line .van-tabs__wrap,
.ft_players /deep/ .van-tabs--line .van-tabs__wrap,
.ft_teams /deep/ .van-tabs--line .van-tabs__wrap,
.innerMatchInfo /deep/ .van-tabs--line .van-tabs__wrap,
.ts_competition /deep/ .van-tabs--line .van-tabs__wrap,
.ts_players /deep/ .van-tabs--line .van-tabs__wrap,
.ts_teams /deep/ .van-tabs--line .van-tabs__wrap {
  height: 0.85rem;
}

.bk_competition /deep/ .van-tabs__nav--line,
.bk_players /deep/ .van-tabs__nav--line,
.bk_teams /deep/ .van-tabs__nav--line,
.ft_competition /deep/ .van-tabs__nav--line,
.ft_players /deep/ .van-tabs__nav--line,
.ft_teams /deep/ .van-tabs__nav--line,
.innerMatchInfo /deep/ .van-tabs__nav--line,
.ts_competition /deep/ .van-tabs__nav--line,
.ts_players /deep/ .van-tabs__nav--line,
.ts_teams /deep/ .van-tabs__nav--line {
  padding-bottom: 0;
}

.bk_competition /deep/ .van-tab,
.bk_players /deep/ .van-tab,
.bk_teams /deep/ .van-tab,
.ft_competition /deep/ .van-tab,
.ft_players /deep/ .van-tab,
.ft_teams /deep/ .van-tab,
.innerMatchInfo /deep/ .van-tab,
.ts_competition /deep/ .van-tab,
.ts_players /deep/ .van-tab,
.ts_teams /deep/ .van-tab {
  line-height: 0.85rem;
}

.bk_competition /deep/ .van-tabs.van-tabs--line,
.bk_players /deep/ .van-tabs.van-tabs--line,
.bk_teams /deep/ .van-tabs.van-tabs--line,
.ft_competition /deep/ .van-tabs.van-tabs--line,
.ft_players /deep/ .van-tabs.van-tabs--line,
.ft_teams /deep/ .van-tabs.van-tabs--line,
.innerMatchInfo /deep/ .van-tabs.van-tabs--line,
.ts_competition /deep/ .van-tabs.van-tabs--line,
.ts_players /deep/ .van-tabs.van-tabs--line,
.ts_teams /deep/ .van-tabs.van-tabs--line {
  box-shadow: 0 3px 3px rgba(0, 0, 0, 0.3);
  z-index: 999;
}

.bk_competition /deep/ .van-tabs__line,
.bk_players /deep/ .van-tabs__line,
.bk_teams /deep/ .van-tabs__line,
.ft_competition /deep/ .van-tabs__line,
.ft_players /deep/ .van-tabs__line,
.ft_teams /deep/ .van-tabs__line,
.innerMatchInfo /deep/ .van-tabs__line,
.ts_competition /deep/ .van-tabs__line,
.ts_players /deep/ .van-tabs__line,
.ts_teams /deep/ .van-tabs__line {
  bottom: 0;
}

.bk_players .stats .van-grid-item__content {
  padding: 0.267rem 0.213rem;
}

.van-tabs__nav--line {
  box-sizing: content-box !important;
}

.h2h /deep/ .van-checkbox__label {
  font-size: 0.27rem;
  font-family: Roboto;
  font-weight: 400;
  color: #989898 !important;
  line-height: unset;
}

.van-tab.van-tab--disabled {
  opacity: 0;
}

.van-collapse-item--border:after {
  border-top: none !important;
}

.h2h .van-checkbox__label {
  max-width: 4rem;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.van-tabbar {
  height: 1.28rem;
  background: #f9f9f9;
  box-shadow: 0 0.03rem 0 0 #e3e3e3;
}

.van-tabbar-item__icon {
  margin-bottom: 0;
}

.footerlink .van-collapse-item__title {
  height: 1.07rem;
  padding: 0;
  display: flex;
  align-items: center;
  border-top: 1px solid hsla(0, 0%, 100%, 0.15);
}

.footerlink .van-collapse-item__title .van-cell__title {
  height: 1.07rem;
  line-height: 1.07rem;
  padding-left: 0.32rem;
  font-size: 0.37rem;
  font-family: "Urbanist-Regular", sans-serif;
  font-weight: 500;
  color: #fff;
  box-sizing: border-box;
}

.footerlink .van-collapse-item__content,
.footerlink .van-collapse-item__title {
  background: #055493;
}

.van-multi-ellipsis--l2 {
  word-break: break-all;
}

html {
  font-family: "Urbanist-Regular", sans-serif;
  font-size: 10vw;
  background-color: #f1f1f1 !important;
  color: #333;
}

body,
html {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

*,
:after,
:before {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  text-decoration: none;
}

div,
span:not(.iconfont) {
  // font-family: "Urbanist-Regular", sans-serif;
  font-family: "Urbanist-Regular", sans-serif !important;
}

.w100 {
  width: 100%;
}

.h100 {
  height: 100%;
}

.both {
  clear: both;
}

.flex-column {
  flex-direction: column;
}

.scale8 {
  transform: scale(0.8);
}

.hide {
  display: none !important;
}

.position-s {
  position: static;
}

.position-r {
  position: relative;
}

.position-a {
  position: absolute;
}

@-webkit-keyframes Twinkle {
  0% {
    opacity: 0;
  }

  25% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }

  75% {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

@keyframes Twinkle {
  0% {
    opacity: 0;
  }

  25% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }

  75% {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

.Twinkle {
  animation: Twinkle 1s infinite;
}

.content-box {
  width: 100%;
  margin: 0 auto;
}

a {
  text-decoration: none !important;
}

a:hover {
  text-decoration: none;
}

.list_active2:active,
.list_active a:active {
  background-color: #eee !important;
}

.float-l {
  float: left;
}

.float-r {
  float: right;
}

.float-n {
  float: none;
}

.v-a-t {
  vertical-align: top;
}

.v-a-m {
  vertical-align: middle;
}

.v-a-b {
  vertical-align: bottom;
}

.flex {
  display: flex;
}

.display-block {
  display: block;
}

.display-i-l {
  display: inline-block;
}

.flex-col {
  flex-direction: column;
}

.align-start {
  align-items: flex-start;
}

.align-center {
  align-items: center;
}

.align-end {
  align-items: flex-end;
}

.justify-start {
  justify-content: flex-start !important;
}

.justify-center {
  justify-content: center;
}

.justify-end {
  justify-content: flex-end;
}

.justify-around {
  justify-content: space-around;
}

.justify-between {
  justify-content: space-between !important;
}

.flex-all-center {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cur-pointer {
  cursor: pointer;
}

.flex-1 {
  flex: 1;
}

.flex-2 {
  flex: 2;
}

.flex-3 {
  flex: 3;
}

.flex-4 {
  flex: 4;
}

.flex-5 {
  flex: 5;
}

.flex-6 {
  flex: 6;
}

.flex-7 {
  flex: 7;
}

.flex-8 {
  flex: 8;
}

.flex-9 {
  flex: 9;
}

.flex-10 {
  flex: 10;
}

.flex-wrap {
  flex-wrap: wrap;
}

.f-s-0 {
  flex-shrink: 0;
}

.bgcolor-fff {
  background-color: #fff !important;
}

.bgcolor-fafafa {
  background-color: #fafafa;
}

.color-000 {
  color: #000;
}

.color-333 {
  color: #333 !important;
}

.color-666 {
  color: #666;
}

.color-999 {
  color: #999;
}

.color-919bb0 {
  color: #919bb0;
}

.color-bbb {
  color: #bbb;
}

.color-ccc {
  color: #ccc;
}

.color-green {
  color: #52af2f !important;
}

.color-red {
  color: #e74c5b !important;
}

.color-c1272d {
  color: #c1272d;
}

.color-blue {
  color: #2196f3;
}

.color-1081db {
  color: #1081db;
}

.color-3b939e {
  color: #3b939e;
}

.color-yellow {
  color: #feb82d;
}

.ml-xs {
  margin-left: 8px;
}

.ml-xxs {
  margin-left: 4px;
}

.mr-xs {
  margin-right: 8px;
}

.mr-xxs {
  margin-right: 4px;
}

.mb-xs {
  margin-bottom: 10px;
}

.mb-xxs {
  margin-bottom: 4px;
}

.m-s {
  margin: 12px;
}

.mt-n {
  margin-top: 20px !important;
}

.mt-xs {
  margin-top: 10px;
}

.pr-n {
  padding-right: 20px;
}

.pr-s {
  padding-right: 10px;
}

.pr-xs {
  padding-right: 6px;
}

.pl-n {
  padding-left: 20px;
}

.pl-s {
  padding-left: 10px;
}

.pl-xs {
  padding-left: 6px;
}

.pb-s {
  padding-bottom: 10px;
}

.pb-xxs {
  padding-bottom: 4px;
}

.p-s {
  padding: 10px !important;
}

.pv-s {
  padding-top: 10px;
  padding-bottom: 10px;
}

.ph-s {
  padding-left: 10px;
  padding-right: 10px;
}

.border-box {
  box-sizing: border-box;
}

.fixed {
  position: fixed;
}

.relative {
  position: relative;
}

.w-bar-100 {
  width: 100%;
}

.h-bar-100 {
  height: 100%;
}

.o-h {
  overflow: hidden;
}

.w-o-h {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.hidden {
  display: none;
}

.opa4 {
  opacity: 0.4;
}

.m-auto {
  margin: auto;
}

.br-50 {
  border-radius: 50%;
}

.matchStatus2 .score {
  color: #e74c5b;
}

.of-cta {
  -o-object-fit: contain;
  object-fit: contain;
}

.s-0-8 {
  transform: scale(0.8);
}

.s-0-85 {
  transform: scale(0.85);
}

.s-0-9 {
  transform: scale(0.9);
}

.head .col.col-5,
.row .col.col-5 {
  width: 5%;
}

.head .col.col-10,
.row .col.col-10 {
  width: 10%;
}

.head .col.col-15,
.row .col.col-15 {
  width: 15%;
}

.head .col.col-20,
.row .col.col-20 {
  width: 20%;
}

.head .col.col-25,
.row .col.col-25 {
  width: 25%;
}

.head .col.col-30,
.row .col.col-30 {
  width: 30%;
}

.head .col.col-40,
.row .col.col-40 {
  width: 40%;
}

.head .col.col-50,
.row .col.col-50 {
  width: 50%;
}

.head .col.col-60,
.row .col.col-60 {
  width: 60%;
}

.head .col.col-70,
.row .col.col-70 {
  width: 70%;
}

.head .col.col-80,
.row .col.col-80 {
  width: 80%;
}

.head .col.col-90,
.row .col.col-90 {
  width: 90%;
}

@keyframes cycle {
  0% {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(1turn);
  }
}

@-webkit-keyframes cycle {
  0% {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(1turn);
  }
}

.w {
  color: #5db400;
}

.l {
  color: #e74c5b;
}

.d {
  color: #ffba5a;
}

.myBlue {
  color: #0f80da;
}

.matchLiveLoading {
  width: 0;
  height: 0;
  border: none;
  position: fixed;
  right: 0;
  top: 0;
}

.svgPostop {
  position: relative;
  top: 0.06rem;
  flex-shrink: 0;
}

.opacity0 {
  opacity: 0;
}

.font-medium {
  font-family: Roboto-Medium, Roboto;
}

.homeIndex .homeTab .van-tabs__wrap {
  overflow: visible;
}

.homeIndex .homeTab .van-tab__text {
  white-space: nowrap;
}

.homeIndex .homeTab .van-tab {
  padding: 0 0.4rem;
}

.homeIndex .homeTab .van-tabs__nav {
  background-color: transparent;
}

.homeIndex .lage {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: #f1f1f1;
}

.homeIndex .match-note {
  box-sizing: border-box;
  background-color: #fff4f4;
  line-height: 0.53rem;
  font-size: 0.27rem;
  color: rgba(222, 30, 48, 0.8);
  font-family: "Urbanist-Regular", sans-serif;
  height: 0.53rem;
  width: 100%;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  padding: 0 0.5rem;
}

.homeIndex .match-note:before {
  content: '';
  position: absolute;
  left: calc(50% - 0.107rem);
  bottom: 0.5rem;
  border: 0.213rem solid transparent;
  border-bottom-color: #fff4f4;
}

.homeIndex .today {
  color: #fff;
  font-size: 0.45rem;
  margin-left: 0.43rem;
  font-family: "Urbanist-Regular", sans-serif;
  font-weight: 400;
}

.homeIndex .scroller {
  width: 100%;
  height: 100%;
}

.homeIndex .sortByTime {
  width: 100%;
  height: 1.07rem;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.32rem;
  box-sizing: border-box;
  margin-top: 0.21rem;
}

.homeIndex .sortByTime .text {
  font-size: 0.37rem;
  font-family: Roboto-Medium, Roboto !important;
  font-weight: 500;
  color: #666;
}

.homeIndex .loading {
  top: 5.13rem;
  position: fixed;
  width: 100%;
}

.homeIndex .repalceLoad {
  width: 100%;
  height: calc(100vh - 2.13rem);
}

.homeIndex .toRoute {
  display: flex;
  align-items: center;
  font-size: 0.32rem;
  color: #999;
}

.homeIndex .toRoute .matchIng {
  color: #de1e30;
}

.homeIndex .user {
  width: 100%;
  display: flex;
  align-items: center;
}

.homeIndex .fade-enter-active,
.homeIndex .fade-leave-active {
  transition: all 0.2s ease;
}

.homeIndex .fade-enter,
.homeIndex .fade-leave-active {
  opacity: 0;
}

.homeIndex svg.icon {
  margin-left: 4px;
}

.homeIndex .scoreWeight {
  font-size: 0.32rem;
  font-family: "Urbanist-Regular", sans-serif;
  font-weight: 400;
  color: #333;
}

.homeIndex .scoreRed {
  font-size: 0.32rem;
  font-family: Roboto-Medium !important;
  font-weight: 500;
  color: #b90909;
}

.homeIndex .isVisible {
  flex: 1;
  visibility: visible;
}

.homeIndex .noVisible {
  flex: 1;
  visibility: hidden;
}

.homeIndex .list_title {
  display: flex;
  width: 100%;
  height: 0.75rem;
  align-items: center;
  background: #f1f1f1;
  border-bottom: 1px solid #e3e3e3;
  opacity: 0.95;
  position: relative;
}

.homeIndex .list_title .listImg {
  width: 0.37rem;
  height: 0.37rem;
  margin: 0 0.16rem;
  min-width: 0.37rem;
}

.homeIndex .list_title .topt {
  position: absolute;
  right: 0;
  height: 100%;
}

.homeIndex .list_title .countryText {
  font-size: 0.32rem;
  margin-right: 0.1rem;
  white-space: nowrap;
  font-family: "Urbanist-Regular", sans-serif;
  font-weight: 400;
  color: #999;
}

.homeIndex .list_title .compText {
  font-size: 0.32rem;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: pointer;
  font-family: Roboto-Medium, Roboto;
  color: #999;
}

.homeIndex .color-r {
  font-size: 0.32rem;
  font-family: "Urbanist-Regular", sans-serif;
  font-weight: 400;
  color: #b90909;
}

.homeIndex .padding5 {
  padding: 5px 0;
}

.homeIndex .mL90 {
  margin-left: 90px !important;
}

.homeIndex .lh {
  line-height: 18.5px;
}

.homeIndex .lh div {
  font-family: Roboto-Regular, Roboto !important;
  font-weight: 300;
}

.homeIndex .upcoming {
  width: 100%;
  height: 0.85rem;
  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAu4AAABABAMAAAC3uu9OAAAAG1BMVEXOzs7R0dHV1dXX19fZ2dnb29vc3Nzd3d3e3t5FXqR6AAAAfElEQVR42u3cQQ0AIBAEsYOcAfyLxAIe+GwIHQkVMFVKNBaDRM2dO3dx5y7u3HXRRMCdu7hzF3fu4s5d3LmLO3fu4s5d3LmLO3dx5y7u3LmLO3dx5y7u3MWdu7hzF3fu3MWdu7hzF3fu4s5d3MP1ZsCdu7hzF/e382XWTx0bpQbIkTZuuAAAAABJRU5ErkJggg==);
  background-size: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.homeIndex .upcoming .upcoming_icon {
  color: #1081db;
}

.homeIndex .upcoming .upcoming_text {
  font-size: 0.35rem;
  font-family: "Urbanist-Regular", sans-serif;
  font-weight: 400;
  color: #333;
  margin-left: 0.16rem;
}

.homeIndex .list {
  width: 100%;
  height: 1.28rem;
  background: #fff;
  border-bottom: 1px solid #e3e3e3;
}

.homeIndex .list:hover .collectBox {
  background: #dfeaf6;
}

.homeIndex .list .listBox {
  border-right: 1px solid #e3e3e3;
  height: 100%;
}

.homeIndex .list .team {
  width: 6rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 0.35rem;
  font-weight: 400;
  color: #333;
  padding-left: 0.213rem;
  height: 100%;
  box-sizing: border-box;
}

.homeIndex .list .teamScore {
  width: 1.4rem;
  height: 100%;
}

.homeIndex .list .icon-setting {
  display: flex;
  justify-content: flex-end;
  box-sizing: border-box;
  width: 1rem;
  border-right: 1px solid #e3e3e3;
  height: 100%;
  padding-right: 0.24rem;
}

.homeIndex .list .icon-setting span.iconfont {
  font-size: 14px;
  display: flex;
  align-items: center;
}

.homeIndex .list .icon-setting span.iconfont.icon-first {
  color: #2196f3;
}

.homeIndex .list .icon-setting span.iconfont.icon-video {
  color: #e74c5b;
}

.homeIndex .list .icon-setting span.iconfont.icon-live {
  color: #ffa830;
}

.homeIndex .desMatch {
  background: #fff;
  height: 40px;
  display: flex;
  align-items: center;
}

.homeIndex .br {
  border-right: 1px solid #e3e3e3;
}

.homeIndex .option {
  width: 34px;
}

.homeIndex .option .option-arrow {
  display: inline-block;
  font-size: 0.32rem;
  color: #919bb0;
  cursor: pointer;
}

.homeIndex .option .option-arrow.up {
  transform: rotate(180deg);
}

.homeIndex .option .option-arrow:hover {
  color: #0f80da;
}

.myriliIcon {
  font-size: 0.48rem !important;
  color: #666;
}

.riqi {
  position: absolute;
  left: 0;
  height: 0.48rem;
  text-align: center;
  line-height: 0.6rem;
  font-size: 0.32rem;
  transform: scale(0.8);
  color: #666;
  font-family: Roboto-Medium;
  font-weight: 500;
}

.league,
.riqi {
  width: 100%;
}

.league .box-padding {
  padding: 0 0.21rem 0 0.32rem;
  box-sizing: border-box;
}

.league .myImg {
  width: 0.42rem;
  height: 0.42rem;
  margin-right: 0.21rem;
}

.league .lineBor {
  border-bottom: 1px solid #eee;
}

.league .text {
  font-size: 0.32rem;
  font-family: Roboto-Bold, Roboto;
  font-weight: 700;
  color: #333;
}

.league .itemBox {
  width: 100%;
  height: 0.96rem;
  background: #fff;
  border-bottom: 1px solid #eee;
}

.league .itemBox .jt1 {
  font-size: 14px;
  width: 0.42rem;
  height: 0.42rem;
  color: #e3e3e3;
}

.league .itemBox .toBottom {
  transform: rotate(90deg);
}

.league .itemBox .toTop {
  transform: rotate(-90deg);
}

.league .popular {
  width: 100%;
  height: 0.74rem;
  line-height: 0.74rem;
  background: #f1f1f1;
  font-size: 0.32rem;
  font-family: "Urbanist-Regular", sans-serif;
  font-weight: 400;
  color: #999;
}

.league .otherList {
  width: 100%;
  padding-left: 0.8rem;
  box-sizing: border-box;
}

.league .otherList .itemHeight {
  height: 0.85rem;
}

.league .iconjiantou,
.league /deep/ .otherBox i {
  color: #e3e3e3;
  font-size: 14px;
}

.league /deep/ .otherBox i {
  font-family: iconfont !important;
}

.fade-enter-active,
.fade-leave-active {
  transition: all 0.2s linear;
  overflow: hidden;
}

.fade-enter {
  height: 0;
  overflow: hidden;
}

.fade-enter-to {
  overflow: hidden;
  height: 665px;
}

.fade-leave {
  height: 655px;
}

.fade-leave-to {
  height: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s ease;
}

.slide-fade-leave-active {
  display: none;
}

.slide-fade-enter,
.slide-fade-leave-to {
  transform: translateX(10px);
  opacity: 0;
}

.liveBox {
  display: flex;
  justify-content: center;
  color: #fff;
  font-size: 0.29rem;
  font-family: Roboto;
  font-weight: 300;
  height: 0.64rem;
  position: absolute;
  width: 8.72rem;
  bottom: 0.1rem;
}

.liveBox .liveSream {
  border: 1px solid hsla(0, 0%, 100%, 0.3);
  border-top-left-radius: 0.32rem;
  border-bottom-left-radius: 0.32rem;
}

.liveBox .liveSream,
.liveBox .matchLive {
  width: 2.85rem;
  height: 0.64rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.liveBox .matchLive {
  border: 1px solid hsla(0, 0%, 100%, 0.3);
  border-top-right-radius: 0.32rem;
  border-bottom-right-radius: 0.32rem;
  margin-left: 0.08rem;
}

.iconjt {
  font-size: 0.27rem;
  margin-left: 0.1rem;
  transform: scale(0.7);
}

.title_pop {
  width: 100%;
  height: 1.39rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 0.53rem;
  box-sizing: border-box;
}

.title_pop .text {
  font-size: 0.43rem;
  color: #333;
}

.tips_pop,
.title_pop .text {
  font-family: "Urbanist-Regular", sans-serif;
  font-weight: 400;
}

.tips_pop {
  width: 100%;
  color: hsla(0, 0%, 60%, 0.4);
  line-height: 0.8rem;
  text-align: center;
}

.AD,
.tips_pop {
  font-size: 0.27rem;
}

.AD {
  position: absolute;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 0.25);
  color: hsla(0, 0%, 100%, 0.8);
  padding: 0.02rem 0.18rem;
  transform: scale(0.75);
  transform-origin: 0 0;
}

.flex-a {
  flex: 1 0 2rem;
  color: hsla(0, 0%, 100%, 0.6);
  margin: 0 0.2rem;
  font-size: 0.37rem;
  text-align: center;
  border-bottom: 2px solid transparent;
  display: inline-block;
  height: 100%;
  line-height: 0.96rem;
  white-space: nowrap;
}

.flex-a.active {
  color: #fff;
  border-bottom: 2px solid #fff;
}

.tabItem1 {
  line-height: 0.96rem;
  padding: 0 0.4rem;
  font-size: 0.37rem;
  color: #000;
  border-bottom: 2px solid transparent;
  font-family: Urbanist-Regular;
}

.tabItem1.active {
  color: #0f80da;
  border-bottom: 2px solid #0f80da;
}

.tabItem2 {
  line-height: 0.96rem;
  padding: 0 0.4rem;
  font-size: 0.37rem;
  color: hsla(0, 0%, 100%, 0.6);
  border-bottom: 2px solid transparent;
  font-family: Urbanist-Regular;
}

.tabItem2.active {
  color: #fff;
  border-bottom: 2px solid #fff;
}

@font-face {
  font-family: iconfont;
  font-display: swap;
  src: url('./iconfont.ef0a764.woff2') format('woff2');
}

.iconfont {
  font-family: iconfont !important;
  font-size: 26px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  &.f2 {
    color: #c6cfd6 !important;
  }

  &.f3 {
    color: #fbaf7d !important;
  }
}

.icondianjing_5qiang:before {
  content: '\e89a';
}

.icondianjing_5sha:before {
  content: '\e89b';
}

.icondianjing_10sha:before {
  content: '\e89c';
}

.icondianjing_16qiang:before {
  content: '\e89d';
}

.icondianjing_baozha:before {
  content: '\e89e';
}

.icondianjing_10qiang:before {
  content: '\e89f';
}

.icondianjing_duyao:before {
  content: '\e8a0';
}

.icondianjing_jishi:before {
  content: '\e8a1';
}

.icondianjing_chaidan:before {
  content: '\e8a2';
}

.icondianjing_fangyuta:before {
  content: '\e8a3';
}

.icondianjing_yixie:before {
  content: '\e8a4';
}

.icondianjing_jinbi:before {
  content: '\e8a7';
}

.iconsport_esports:before {
  content: '\e8a5';
}

.iconjiangbeibeifen:before {
  content: '\e610';
}

.icondianjing_1qiang:before {
  content: '\e899';
}

.iconthesports:before {
  content: '\e60f';
}

.iconhandball_ball:before {
  content: '\e825';
}

.iconbadminton_ball:before {
  content: '\e824';
}

.iconsport_waterpolo:before {
  content: '\e823';
}

.iconsport_snooker:before {
  content: '\e822';
}

.iconsport_amfootball:before {
  content: '\e818';
}

.iconsport_badminton:before {
  content: '\e819';
}

.iconsport_baseball:before {
  content: '\e81a';
}

.iconsport_cricket:before {
  content: '\e81b';
}

.iconsport_basketball:before {
  content: '\e81c';
}

.iconsport_handball:before {
  content: '\e81d';
}

.iconsport_tabletennis:before {
  content: '\e81e';
}

.iconsport_icehockey:before {
  content: '\e81f';
}

.iconsport_football:before {
  content: '\e820';
}

.iconsport_tennis:before {
  content: '\e821';
}

.iconsport_volleyball:before {
  content: '\e814';
}

.icontabletennis_ball:before {
  content: '\e812';
}

.iconbaseball_ball_2:before {
  content: '\e811';
}

.iconbaseball_ball_2_white:before {
  content: '\e815';
}

.iconbaseball_ball_1:before {
  content: '\e810';
}

.iconbaseball_ball_1_white:before {
  content: '\e816';
}

.iconCricket_ball_4:before {
  content: '\e80f';
}

.iconcricket_ball_3:before {
  content: '\e80e';
}

.iconcricket_ball_2:before {
  content: '\e80d';
}

.iconHockey_Miss:before {
  content: '\e80b';
}

.iconHockey_goal:before {
  content: '\e80a';
}

.iconjiaolian:before {
  content: '\e60e';
}

.iconchangguan1:before {
  content: '\e60d';
}

.icongamestart:before {
  content: '\e809';
}

.iconAMfootball_Safaty:before {
  content: '\e808';
}

.iconAMfootball_PAT:before {
  content: '\e807';
}

.iconAMfootball_FG:before {
  content: '\e806';
}

.iconAMfootball_2-PTCONV:before {
  content: '\e805';
}

.iconAMfootball_TD:before {
  content: '\e804';
}

.iconkaishi:before {
  content: '\e60c';
}

.iconAMfootball:before {
  content: '\e803';
}

.iconminus1:before {
  content: '\e60b';
}

.iconrili2:before {
  content: '\e802';
}

.iconlogo1:before {
  content: '\e801';
}

.iconback1:before {
  content: '\e800';
}

.iconrili1:before {
  content: '\e7f8';
}

.iconlanguage:before {
  content: '\e7f9';
}

.iconallgameslan:before {
  content: '\e7fa';
}

.iconlive:before {
  content: '\e7fb';
}

.iconallgames:before {
  content: '\e7fc';
}

.iconleagus:before {
  content: '\e7fd';
}

.iconleagueslan:before {
  content: '\e7fe';
}

.iconlivelan:before {
  content: '\e7ff';
}

.iconshuangshouwopai:before {
  content: '\e60a';
}

.icondanshouwopai:before {
  content: '\e609';
}

.iconzuoshou:before {
  content: '\e608';
}

.iconyoushou:before {
  content: '\e607';
}

.iconzuoyoushou:before {
  content: '\e606';
}

.iconsearch1:before {
  content: '\e605';
}

.iconlock:before {
  content: '\e782';
}

.iconup:before {
  content: '\e786';
}

.icondown:before {
  content: '\e787';
}

.iconsearch:before {
  content: '\e604';
}

.iconplus:before {
  content: '\e603';
}

.iconmyminuss:before {
  content: '\e602';
}

.iconiconwangqiu:before {
  content: '\e601';
}

.iconassists:before {
  content: '\e600';
}

.icongoals2:before {
  content: '\e74c';
}

.icongoals3:before {
  content: '\e74d';
}

.icongoals1:before {
  content: '\e74e';
}

.iconjiantou2:before {
  content: '\e73b';
}

.iconAiScorelogo:before {
  content: '\e70a';
}

.iconlujing:before {
  content: '\e70b';
}

.iconlogo:before {
  content: '\e70c';
}

.iconxialajiantou:before {
  content: '\e702';
}

.iconyuyan1:before {
  content: '\e6ae';
}

.iconkoushao1:before {
  content: '\e6ad';
}

.iconInjured:before {
  content: '\e6aa';
}

.iconjiantou1:before {
  content: '\e6ab';
}

.iconstar:before {
  content: '\e6ac';
}

.iconcaipan:before {
  content: '\e6a9';
}

.iconicon-test:before {
  content: '\e691';
}

.iconCorner:before {
  content: '\e693';
}

.icontwoyellow-red:before {
  content: '\e695';
}

.iconredandyellow1:before {
  content: '\e696';
}

.icon-yellow-card:before {
  content: '\e697';
}

.icon-red-card:before {
  content: '\e698';
}

.iconredcard1:before {
  content: '\e699';
}

.iconyellowcard1:before {
  content: '\e69a';
}

.iconin:before {
  content: '\e69b';
}

.iconout:before {
  content: '\e69c';
}

.iconsubstitution:before {
  content: '\e69d';
}

.iconInjuryreplace:before {
  content: '\e69e';
}

.iconVenue:before {
  content: '\e69f';
}

.icondianji:before {
  content: '\e6a0';
}

.iconduizhang:before {
  content: '\e6a1';
}

.iconassist:before {
  content: '\e6a2';
}

.icon-own-goal:before {
  content: '\e6a3';
}

.icongoal:before {
  content: '\e6a4';
}

.iconPenalty:before {
  content: '\e6a5';
}

.iconPenaltySaved:before {
  content: '\e6a6';
}

.iconout1:before {
  content: '\e6a7';
}

.iconin1:before {
  content: '\e6a8';
}

.iconarrow-tow-left:before {
  content: '\e627';
}

.icondelete:before {
  content: '\e685';
}

.iconjinzhi:before {
  content: '\e686';
}

.icontranslate:before {
  content: '\e687';
}

.iconxiala:before {
  content: '\e688';
}

.iconkuohao:before {
  content: '\e689';
}

.iconwenhao1:before {
  content: '\e684';
}

.iconsend:before {
  content: '\e683';
}

.iconchangguan:before {
  content: '\e680';
}

.iconDNP:before {
  content: '\e67d';
}

.icontingsai:before {
  content: '\e681';
}

.icontingsaibeifen2:before {
  content: '\e682';
}

.iconwenhao:before {
  content: '\e67f';
}

.iconiconlanqiu:before {
  content: '\e67e';
}

.iconshipingzhibo:before {
  content: '\e67b';
}

.icondonghuazhibo:before {
  content: '\e67c';
}

.icontransfer-to:before {
  content: '\e7e3';
}

.iconsound-0:before {
  content: '\e655';
}

.iconjiantou:before {
  content: '\e66d';
}

.iconSign_Sele:before {
  content: '\e66e';
}

.iconduigou:before {
  content: '\e66f';
}

.iconguanzhong:before {
  content: '\e670';
}

.iconback:before {
  content: '\e671';
}

.iconshalou:before {
  content: '\e673';
}

.iconSign_Def:before {
  content: '\e674';
}

.icondata:before {
  content: '\e675';
}

.iconshaixuan:before {
  content: '\e676';
}

.iconrili:before {
  content: '\e677';
}

.iconhuaban:before {
  content: '\e679';
}

.iconhuaban1:before {
  content: '\e67a';
}
