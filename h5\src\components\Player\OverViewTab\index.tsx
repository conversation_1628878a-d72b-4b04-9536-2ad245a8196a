import { inject, observer } from 'mobx-react';
import React from 'react';
import LazyLoad from 'react-lazyload';

import PlayerBaseInfo from './PlayerBaseInfo';
import PlayerMatches from './PlayerMatches';
import PlayerPosition from './PlayerPosition';
import PlayerTransfer from './PlayerTransfer';

import './index.less';

interface Props {
  store?: any;
}

const PlayerOverViewTab: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: { Player },
    } = props;
    const { playerHeaderInfo } = Player;
    const {
      player: { parsedAbility = [], parsedPositions, parsedCharacteristics = [], ...playerInfo },
      team,
    } = playerHeaderInfo || {};

    return (
      <div className="playerOverviewContainer">
        <LazyLoad height={700} offset={10}>
          <PlayerBaseInfo player={playerInfo} team={team} parsedCharacteristics={parsedCharacteristics} parsedPositions={parsedPositions}/>
          {/* <PlayerPosition parsedCharacteristics={parsedCharacteristics} parsedPositions={parsedPositions} /> */}
        </LazyLoad>
        <LazyLoad height={200} offset={10}>
          {/* <PlayerMatches teamId={team.id} /> */}
          <PlayerTransfer />
        </LazyLoad>
      </div>
    );
  }),
);

export default PlayerOverViewTab;
