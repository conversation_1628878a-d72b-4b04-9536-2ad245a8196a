import { subscribeHomeData, unsubscribeHomeData } from 'iscommon/mqtt/basketball/home';

import AdBanner from '@/components/Common/AdBanner';
import AllGames from '@/components/Basketball/Home/AllGames';
import { isPluginMode } from '@/layouts/plugin';
import LeftGames from '@/components/Basketball/Home/LeftGames';
import PageBreadcrumb from '@/components/Common/PageBreadcrumb';
import PluginBasketball from '@/pages/Plugin/pluginBasketball';
import { resetHomeOddsTrendCacheMaps } from 'iscommon/const/home';
import styles from './index.less';
import { useCrumbList } from 'iscommon/hooks/content';
import { useEffect } from 'react';
import WidgetBox from '@/components/Common/Widget';

const BasketballHome = () => {
  const { crumbs } = useCrumbList();
  const pluginMode = isPluginMode(); // ✅ detect plugin mode

  useEffect(() => {
    window.gtag('event', 'pv', { platform: 'web', sport: 'basketball', page_type: 'home' });
    subscribeHomeData();

    return () => {
      unsubscribeHomeData();
      resetHomeOddsTrendCacheMaps();
    };
  }, []);

  return (
    <div className={styles.container}>
      <PageBreadcrumb crumbs={crumbs} />
      {pluginMode ? (
        <div id="ig-free-plugin-card">
          <PluginBasketball />
          <WidgetBox hidden pluginId="ig-free-plugin-card" />
        </div>
      ) : (
        <div className={styles.layout}>
          <LeftGames />
          <div className={styles.content} id="ig-free-plugin-card">
            <AdBanner position='homepage_v2' style={{ marginBottom: 10 }} />
            <AllGames />
            <WidgetBox hidden pluginId="ig-free-plugin-card" />
          </div>
        </div>
      )}
    </div>
  );
};

export default BasketballHome;
