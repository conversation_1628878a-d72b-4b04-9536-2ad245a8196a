// .win {
//   color: #52af2f;
// }

// .draw {
//   color: #feb82d;
// }

// .lose {
//   color: #c72a1d;
// }

// .normal {
//   color: #0f80da;
// }

// .team {
//   display: flex;
//   align-items: center;

//   &:first-child {
//     margin-bottom: 6px;
//   }

//   .logo {
//     width: 32px;
//     height: 32px;
//     margin-right: 14px;
//   }

//   .teamName {
//     flex: 1;
//     font-size: 24px;
    
//     color: #333333;
//     line-height: 24px;
//     padding-right: 10px;
//     white-space: nowrap;
//     overflow: hidden;
//     text-overflow: ellipsis;
//   }
// }

// .header {
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
//   width: 100%;
//   height: fit-content;
//   background: #000;
//   padding: 16px;
//   box-sizing: border-box;

//   .title {
//     flex: 1;
//     display: flex;
//     align-items: center;
//     height: 24px;
//     font-size: 24px;
    
//     font-weight: 500;
//     color: #343434;
//     line-height: 24px;
//     white-space: nowrap;
//     overflow: hidden;
//     text-overflow: ellipsis;
//   }
// }

// .stats {
//   display: flex;
//   align-items: center;
//   padding: 15px 24px;
//   line-height: 30px;
//   background-color: #fff;
// }

// .teamInfo {
//   display: flex;
//   align-items: center;
//   padding: 16px 24px 5px;
//   background-color: #fff;
// }

// .list {
//   background-color: #fff;
// }

// .showMore {
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   height: fit-content;
//   padding: 15px 0;
//   margin: 0 auto;
//   box-sizing: content-box;

//   .showMoreIcon {
//     width: 22px;
//     height: 22px;
//   }
// }

// .noData {
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   width: 100%;
//   height: 150px;
//   font-size: 32px;
//   color: #666;
// }

.match-battle-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;

  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .container-body {
    background: #1e1e1e;

    .badge-container {
      display: flex;
      justify-content: space-evenly;
      align-items: center;
      margin: 20px auto;
    
      .badge-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        
        .custom-badge {
          width: 108px;
          height: 48px;
          border-radius: 24px; 
          color: #fff; 
          display: flex;
          justify-content: center;
          align-items: center;
          font-weight: bold;
          font-size: 20px;
          margin-bottom: 5px
        }
      
        .label {
          font-size: 20px;
          color: #fff;
        }
      }
    }

    .match-collapse {
      background-color: #121212;
      border-radius: 8px;
      border: none;

      .panel-header {
        display: flex;
        flex-direction: row;
        align-items: center;

        .competition-logo {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin-right: 10px;
          overflow: hidden;
          background-size: cover;
        }

        .competition-name {
          font-size: 20px;
          color: #fff;
        }
      }

      .adm-list {
        --active-background-color: none;
        --border-inner: none; 
        --border-top: none; 
        --border-bottom: none;

        .adm-list-body {
          background: none;

          .adm-list-item {
            background: #1e1e1e;
          }
        }
      }
    }

    .match-details {
      width: 100%;
      height: fit-content;
      background: #121212;
      border-radius: 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      box-sizing: border-box;
      padding: 5px 16px;
      margin-bottom: 16px;
      transition: all 0.3s ease;

      .match-info {
        display: flex;
        flex-direction: column;
        margin-left: 10px;
        width: 100%;

        .team-score-row {
          display: flex;
          justify-content: space-between;
          align-items: center;
          color: #fff;
          padding: 10px 0px;

          .team-info {
            display: flex;
            align-items: center;
            flex: 1;
            overflow: hidden;

            .team-icon {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 40px;
              height: 40px;
              border-radius: 50%;
              margin-right: 10px;
              overflow: hidden;
              background-size: cover;
            }
  
            .team-name {
              color: #fff;
              font-size: 20px;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          .score-text {
            font-size: 20px;
            color: #fff;
          }
        }
      }
    
      // .scores {
      //   display: flex;
      //   flex-direction: column;
      //   align-items: center;
      //   padding: 10px 0px;

      //   .score-text {
      //     font-size: 20px;
      //     color: #fff;
      //     padding: 10px 0px;
      //   }
      // }
    
      .match-time {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 5px;

        .match-time-text {
          font-size: 18px;
          color: #fff;
        }
      }
    }
  }
}