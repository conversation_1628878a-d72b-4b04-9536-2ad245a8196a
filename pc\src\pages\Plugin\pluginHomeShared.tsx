import './pluginHome.less';
import './pluginShared.less';

import { Button, Carousel, Popover, Select } from 'antd';
import { useCallback, useEffect, useState } from "react";

import { GlobalConfig } from 'iscommon/const/globalConfig';
import moment from 'moment';
import { PlayCircleOutlined } from '@ant-design/icons';
import PopularCompetitions from './pluginPopular';

// Interface for sport-specific functions
interface PluginHomeProps {
  fetchMatches: () => Promise<{
    allMatches: any[];
    competitions: string[];
    liveCompetitionIds: Set<string>;
  }>;
  getLiveTimer: (match: any) => string;
  getHandicapAndOdds: (match: any) => {
    homeHandicap: string | null;
    awayHandicap: string | null;
    homeOdds: number | null;
    awayOdds: number | null;
  };
  sportName: string;
}

const PluginHomeShared: React.FC<PluginHomeProps> = ({ 
  fetchMatches, 
  getLiveTimer, 
  getHandicapAndOdds, 
  sportName 
}) => {
  const [allMatches, setAllMatches] = useState<any[]>([]);
  const [filteredMatches, setFilteredMatches] = useState<any[]>([]);
  const [competitionList, setCompetitionList] = useState<string[]>([]);
  const [selectedCompetition, setSelectedCompetition] = useState<string>('All');
  const [matchType, setMatchType] = useState<'live' | 'upcoming'>('live');
  const [liveCompetitionIds, setLiveCompetitionIds] = useState<Set<string>>(new Set());

  // Get search params for plugin mode detection
  const searchParams = new URLSearchParams(window.location.search);

  useEffect(() => {
    // Apply plugin-specific styling
    const pluginContainer = document.querySelector('.plugin-container') as HTMLElement;
    if (pluginContainer) {
      pluginContainer.style.width = '1200px';
      pluginContainer.style.margin = '0 auto';
    }
  }, [searchParams]);
  
  const loadMatches = useCallback(async () => {
    try {
      console.log(`🎯 ${sportName} Plugin: Loading matches...`);
      const { allMatches, competitions, liveCompetitionIds } = await fetchMatches();
      
      setAllMatches(allMatches);
      setFilteredMatches(allMatches);
      setCompetitionList(['All', ...competitions]);
      setLiveCompetitionIds(liveCompetitionIds);
      
      console.log(`🎯 ${sportName} Plugin: Loaded ${allMatches.length} matches`);
    } catch (error) {
      console.error(`Error fetching ${sportName} matches:`, error);
    }
  }, [fetchMatches, sportName]);

  useEffect(() => {
    loadMatches();
    const interval = setInterval(loadMatches, 1000); // Update every 1 second for real-time odds
    return () => clearInterval(interval);
  }, [loadMatches]);

  // Calculate match counts for button labels
  const getLiveMatchCount = () => {
    return allMatches.filter(m => m.type === 'live').length;
  };

  // Handle watch live click
  const handleWatchLive = async (match: any) => {
    const matchId = match.matchId || match.id
    const pathName = GlobalConfig.pathname;

    const url = `${location.origin}/${pathName}-match/${matchId}/MatchLive?isplugin=true&pluginName=${pathName}LivePlugin&lang=en`;
  
    window.open(url, '_blank');
  };

  function chunkIntoPairs(array: any) {
    const result = [];
    for (let i = 0; i < array.length; i += 2) {
      result.push(array.slice(i, i + 2));
    }
    return result;
  }

  // Filter matches based on type and competition
  useEffect(() => {
    let filtered = allMatches.filter(match => match.type === matchType);
    
    if (selectedCompetition !== 'All') {
      filtered = filtered.filter(match => match.competitionName === selectedCompetition);
    }
    
    setFilteredMatches(filtered);
  }, [allMatches, matchType, selectedCompetition]);

  const matchPairs = chunkIntoPairs(filteredMatches);

  return (
    <div className="plugin-container">
      <div className="plugin-header">
        <div className="filter-bar">
          <div className="filter-left">
            <Button
              className={matchType === 'live' ? 'active' : ''}
              style={{
                backgroundColor: matchType === 'live' ? 'red' : '#2c2c2c',
                borderColor: matchType === 'live' ? 'red' : '#2c2c2c',
                color: '#fff'
              }}
              onClick={() => setMatchType('live')}
            >
              Live{getLiveMatchCount() > 0 ? ` (${getLiveMatchCount()})` : ''}
            </Button>
            <Button
              className={matchType === 'upcoming' ? 'active' : ''}
              style={{
                backgroundColor: matchType === 'upcoming' ? 'red' : '#2c2c2c',
                borderColor: matchType === 'upcoming' ? 'red' : '#2c2c2c',
                color: '#fff'
              }}
              onClick={() => setMatchType('upcoming')}
            >
              Upcoming
            </Button>
          </div>
          <div className="filter-right">
            <Select
              value={selectedCompetition}
              onChange={setSelectedCompetition}
              style={{ width: 200 }}
              className="competition-select"
            >
              {competitionList.map(comp => (
                <Select.Option key={comp} value={comp}>{comp}</Select.Option>
              ))}
            </Select>
          </div>
        </div>
        
        <PopularCompetitions 
          liveCompetitionIds={liveCompetitionIds}
          onCompetitionSelect={setSelectedCompetition}
          selectedCompetition={selectedCompetition}
        />
      </div>

      <div className="plugin-content">
        {matchPairs.length === 0 ? (
          <div className="no-matches">No matches available</div>
        ) : (
          <Carousel 
            dots={true} 
            infinite={false}
            slidesToShow={1}
            slidesToScroll={1}
          >
            {matchPairs.map((pair, pairIndex) => (
              <div key={pairIndex} className="carousel-slide">
                <div className="match-container">
                  {pair.map((match: any, matchIndex: number) => (
                    <div key={match.matchId || matchIndex} className="match-item">
                      <div className="match-header">
                        <span className="competition-name">{match.competitionName}</span>
                        <Popover content={match.vlive === 1 ? 'Watch Live' : 'Watch Animation'} placement="top">
                          <Button
                            type="text"
                            icon={<PlayCircleOutlined />}
                            className="action-btn"
                            onClick={() => handleWatchLive(match)}
                          />
                        </Popover>
                      </div>
                      <div className="match-body">
                        <div className="match-info">
                          <div className="match-time">
                            {match.type === 'live' ? getLiveTimer(match) : moment.unix(match.matchTime).format('HH:mm')}
                          </div>
                        </div>
                        <div className="teams-section">
                          <div className="team-row">
                            <img 
                              src={match.homeTeam?.logo} 
                              alt={match.homeTeam?.name}
                              className="team-icon"
                            />
                            <span className="team-name">{match.homeTeam?.name}</span>
                          </div>
                          <div className="team-row">
                            <img 
                              src={match.awayTeam?.logo} 
                              alt={match.awayTeam?.name}
                              className="team-icon"
                            />
                            <span className="team-name">{match.awayTeam?.name}</span>
                          </div>
                        </div>
                        <div className='match-score-section'>
                          <span className="match-score">{match.calculatedHomeScore}</span> 
                          <span className='match-score'>{match.calculatedAwayScore}</span>
                        </div>
                        {(() => {
                          const { homeHandicap, awayHandicap, homeOdds, awayOdds } = getHandicapAndOdds(match);
                          // Display betting section if we have handicap data (including '0') or odds data
                          if (homeHandicap !== null && awayHandicap !== null) {
                            return (
                              <div className='betting-section'>
                                <div className='handicap-section'>
                                  <span className="handicap-value home-handicap">{homeHandicap}</span>
                                  <span className="handicap-value away-handicap">{awayHandicap}</span>
                                </div>
                                {homeOdds !== null && awayOdds !== null && (
                                  <div className='odds-section'>
                                    <span className="odds-value home-odds">{homeOdds}</span>
                                    <span className="odds-value away-odds">{awayOdds}</span>
                                  </div>
                                )}
                              </div>
                            );
                          }
                          return null;
                        })()}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </Carousel>
        )}
      </div>
    </div>
  )
}

export default PluginHomeShared;
