import './pluginHomeShared.less';
import './pluginShared.less';

import { Button, Carousel, Popover, Select } from 'antd';
import { useCallback, useEffect, useState } from "react";

import { GlobalConfig } from 'iscommon/const/globalConfig';
import moment from 'moment';
import { PlayCircleOutlined } from '@ant-design/icons';
import PopularCompetitions from './pluginPopular';

// Interface for sport-specific functions
interface PluginHomeProps {
  fetchMatches: () => Promise<{
    allMatches: any[];
    competitions: string[];
    liveCompetitionIds: Set<string>;
  }>;
  getLiveTimer: (match: any) => string;
  getHandicapAndOdds: (match: any) => {
    homeHandicap: string | null;
    awayHandicap: string | null;
    homeOdds: number | null;
    awayOdds: number | null;
  };
  sportName: string;
  displayMode?: 'full' | 'carousel-only'; // New prop to control what gets displayed
}

const PluginHomeShared: React.FC<PluginHomeProps> = ({ 
  fetchMatches, 
  getLiveTimer, 
  getHandicapAndOdds, 
  sportName,
  displayMode = 'full' // Default to showing everything
}) => {
  const [allMatches, setAllMatches] = useState<any[]>([]);
  const [filteredMatches, setFilteredMatches] = useState<any[]>([]);
  const [competitionList, setCompetitionList] = useState<string[]>([]);
  const [selectedCompetition, setSelectedCompetition] = useState<string>('All');
  const [matchType, setMatchType] = useState<'live' | 'upcoming'>('live');
  const [liveCompetitionIds, setLiveCompetitionIds] = useState<Set<string>>(new Set());

  // Get search params for plugin mode detection
  const searchParams = new URLSearchParams(window.location.search);

  useEffect(() => {
    // Apply plugin-specific styling
    const pluginContainer = document.querySelector('.plugin-container') as HTMLElement;
    if (pluginContainer) {
      pluginContainer.style.width = '1200px';
      pluginContainer.style.margin = '0 auto';
    }
  }, [searchParams]);
  
  // const loadMatches = useCallback(async () => {
  //   try {
  //     console.log(`🎯 ${sportName} Plugin: Loading matches...`);
  //     const { allMatches, competitions, liveCompetitionIds } = await fetchMatches();
      
  //     setAllMatches(allMatches);
  //     setFilteredMatches(allMatches);
  //     setCompetitionList(['All', ...competitions]);
  //     setLiveCompetitionIds(liveCompetitionIds);
      
  //     console.log(`🎯 ${sportName} Plugin: Loaded ${allMatches.length} matches`);
  //   } catch (error) {
  //     console.error(`Error fetching ${sportName} matches:`, error);
  //   }
  // }, [fetchMatches, sportName]);

  // useEffect(() => {
  //   loadMatches();
  //   const interval = setInterval(loadMatches, 1000); // Update every 1 second for real-time odds
  //   return () => clearInterval(interval);
  // }, [loadMatches]);

  useEffect(() => {
    let mounted = true;

    const fetchAllData = async () => {
      try {
        console.log(`🎯 ${sportName} Plugin: Fetching all data (odds + scores + times)...`);
        const { allMatches, competitions, liveCompetitionIds } = await fetchMatches();

        if (!mounted) return;

        setAllMatches(allMatches);
        setCompetitionList(['All', ...competitions]);
        setLiveCompetitionIds(liveCompetitionIds);

        console.log(`🎯 ${sportName} Plugin: Updated ${allMatches.length} matches`);
      } catch (error) {
        console.error(`Error fetching ${sportName} data:`, error);
      }
    };

    // Initial load
    fetchAllData();

    // Single interval for all data updates
    // Since getCompetitionList returns everything together (odds + scores + times),
    // it's more efficient to update everything at once rather than make duplicate API calls
    const updateInterval = displayMode === 'full' ? 3000 : null; // 3s for full mode, no auto-update for video mode

    let interval: NodeJS.Timeout | null = null;
    if (updateInterval) {
      interval = setInterval(fetchAllData, updateInterval);
    }

    return () => {
      mounted = false;
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [fetchMatches, displayMode, sportName]);

  // Calculate match counts for button labels
  const getLiveMatchCount = () => {
    return allMatches.filter(m => m.type === 'live').length;
  };

  // Handle watch live click
  const handleWatchLive = async (match: any) => {
    const matchId = match.matchId || match.id
    const pathName = GlobalConfig.pathname;

    const url = `${location.origin}/${pathName}-match/${matchId}/MatchLive?isplugin=true&pluginName=${pathName}LivePlugin&lang=en`;
  
    window.open(url, '_blank');
  };

  function chunkIntoPairs(array: any) {
    const result = [];
    for (let i = 0; i < array.length; i += 2) {
      result.push(array.slice(i, i + 2));
    }
    return result;
  }

  // Filter matches based on type and competition
  useEffect(() => {
    let filtered = allMatches.filter(match => match.type === matchType);
    
    if (selectedCompetition !== 'All') {
      filtered = filtered.filter(match => match.competitionName === selectedCompetition);
    }
    
    setFilteredMatches(filtered);
  }, [allMatches, matchType, selectedCompetition]);

  const matchPairs = chunkIntoPairs(filteredMatches);

  // Reusable carousel component
  const renderCarousel = () => (
    <Carousel dots={false} swipeToSlide draggable infinite={false} variableWidth>
      {matchPairs.map((pair, index) => {
        return (
        <div key={index} className="carousel-column">
          {pair.map((match: any) => {
            return (
              <div key={match.matchId} className="plugin-carousel-item">
                <div className="container-title">
                  <span className='title-text'>{match.competitionName}</span>
                  <Popover content={match.vlive === 1 ? 'Watch Live' : 'Watch Animation'} placement="top">
                    <Button
                      type="text"
                      icon={<PlayCircleOutlined />}
                      className="action-btn"
                      onClick={() => handleWatchLive(match)}
                    />
                  </Popover>
                </div>
                <div className="container-body">
                  <div className='horizontal-content'>
                    <div className='match-time-section'>
                      {(() => {
                        const isBasketball = GlobalConfig.pathname === 'basketball';
                        const isFootballHalfTime = !isBasketball && match.matchStatus === 3; // HT only for football
                        const isNotStarted = match.matchStatus === 1; // Not started is same for both sports

                        if (isFootballHalfTime) {
                          return (
                            <div className="match-time-container">
                              <span className="match-status">HT</span>
                            </div>
                          );
                        } else if (isNotStarted) {
                          return (
                            <div className="match-time">
                              <span className="hour-time">{moment.unix(match.matchTime).format('HH:mm')}</span>
                              <span className="date-time">{moment.unix(match.matchTime).format('DD/MM')}</span>
                            </div>
                          );
                        } else {
                          return (
                            <div className="match-score-container">
                              <span className="match-status live-timer">{getLiveTimer(match)}</span>
                            </div>
                          );
                        }
                      })()}
                    </div>
                    <div className='team-section'>
                      <div className='home-team'>
                        <img className="team-icon" src={match.homeTeam?.logo} alt={match.homeTeam?.name}/>
                        <span className='team-name'>{match.homeTeam?.name}</span>
                      </div>
                      <div className='away-team'>
                        <img className="team-icon" src={match.awayTeam?.logo} alt={match.awayTeam?.name}/>
                        <span className='team-name'>{match.awayTeam?.name}</span>
                      </div>
                    </div>
                    <div className='match-score-section'>
                      <span className="match-score">{match.calculatedHomeScore}</span> 
                      <span className='match-score'>{match.calculatedAwayScore}</span>
                    </div>
                  </div>
                  {(() => {
                    const { homeHandicap, awayHandicap, homeOdds, awayOdds } = getHandicapAndOdds(match);
                    // Display betting section if we have handicap data (including '0') or odds data
                    if (homeHandicap !== null && awayHandicap !== null) {
                      return (
                        <div className='betting-section'>
                          <div className='handicap-section'>
                            <div className="pill">
                              <span className="handicap-value home-handicap">{homeHandicap}</span>
                              <span className="label">HDP</span>
                              <span className="handicap-value away-handicap">{awayHandicap}</span>
                            </div>
                          </div>
                          {homeOdds !== null && awayOdds !== null && (
                            <div className='odds-section'>
                              <div className="pill">
                                <span className="odds-value home-odds">{homeOdds}</span>
                                <span className="label">Odd</span>
                                <span className="odds-value away-odds">{awayOdds}</span>
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    }
                    return null;
                  })()}
                </div>
              </div>
            )
          })}
        </div>
        )
      })}
    </Carousel>
  );

  return (
    <div className={`plugin-container ${displayMode === 'carousel-only' ? 'video-mode' : ''}`}>
      {displayMode === 'full' ? (
        // Full mode: Popular competitions + Right content with filter bar + carousel below
        <>
          <PopularCompetitions 
            liveCompetitionIds={liveCompetitionIds}
            onCompetitionClick={setSelectedCompetition}
            selectedCompetition={selectedCompetition}
          />

          <div className="right-content">
            <div className="filter-bar">
              <div className="button-group">
                <Button
                  className={matchType === 'live' ? 'active' : ''}
                  style={{
                    backgroundColor: matchType === 'live' ? 'red' : '#2c2c2c',
                    borderColor: matchType === 'live' ? 'red' : '#2c2c2c',
                    color: '#fff'
                  }}
                  onClick={() => setMatchType('live')}
                >
                  Live{getLiveMatchCount() > 0 ? ` (${getLiveMatchCount()})` : ''}
                </Button>
                <Button
                  className={matchType === 'upcoming' ? 'active' : ''}
                  style={{
                    backgroundColor: matchType === 'upcoming' ? 'red' : '#2c2c2c',
                    borderColor: matchType === 'upcoming' ? 'red' : '#2c2c2c',
                    color: '#fff'
                  }}
                  onClick={() => setMatchType('upcoming')}
                >
                  Upcoming
                </Button>
              </div>
              <Select
                className='custom-select-plugin'
                value={selectedCompetition}
                onChange={value => setSelectedCompetition(value)}
                placeholder="Select Competition"
                options={competitionList.map((item: string) => ({
                  key: item,
                  value: item,
                  label: item
                }))}
              />
            </div>
            
            {/* Carousel inside right-content for full mode */}
            {renderCarousel()}
          </div>
        </>
      ) : (
        // Carousel-only mode: Just the carousel
        <div className="carousel-only-mode">
          {renderCarousel()}
        </div>
      )}
    </div>
  )
}

export default PluginHomeShared;
