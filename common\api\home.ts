// @ts-nocheck

import { HomeGameTab, InProgressStatusEnum, PeriodStatusEnum, TimezoneList } from '../const/constant';

import { getTodayDateFilter } from '../utils';
import homeDataController from '../mqtt/football/homeDataController';
import igsRequest from 'iscommon/request/instance';

let controller = null;

// 04:00 ===> 0400
export const formatMatchTimeToNumber = (timeString = '') => {
  return Number(timeString.replace(/[\s|:]/g, ''));
};

const filterValidMatch = (matches) => {
  const validMatch = [];
  const eachMatchIds = [];
  let minTime = 0;
  for (let j = 0; j < matches.length; j++) {
    const matchItem = matches[j];
    const { matchStatus, matchTime, firstHalfKickOffTime, secondHalfKickOffTime, matchId } = matchItem;
    let isValid = true;
    // 筛选出正在进行中的比赛
    if (InProgressStatusEnum.includes(matchStatus)) {
      matchItem.inProgress = true;
      matchItem.period = true;
      matchItem.matchActiveTime = secondHalfKickOffTime || firstHalfKickOffTime;

      if (!matchItem.matchActiveTime) {
        isValid = false;
      }
      // 筛选出live的比赛， 包括中场
    } else if (PeriodStatusEnum.includes(matchStatus)) {
      matchItem.period = true;
    } else if (!matchTime) {
      // 非live的，如果没有matchTime，过滤掉
      isValid = false;
    }
    if (isValid) {
      minTime = minTime === 0 ? matchTime : Math.min(matchTime, minTime);
      validMatch.push(matchItem);
      eachMatchIds.push(matchId);
    }
  }

  return { validMatch, minTime, eachMatchIds };
};

const filterCompetitions = (result) => {
  try {
    let total = 0;
    let matchIds = [];
    const { competitions } = result;
    for (let i = 0; i < competitions.length; i++) {
      const { matches } = competitions[i];
      if (!matches || !matches.length) {
        competitions[i]['matches'] = [];
        continue;
      }
      const { validMatch, minTime, eachMatchIds } = filterValidMatch(matches);
      competitions[i]['matches'] = validMatch;
      total += validMatch.length;
      competitions[i]['minMatchTime'] = minTime;
      matchIds = matchIds.concat(eachMatchIds);
    }
    return { competitions, total, matchIds };
  } catch (e) {
    console.log('filterCompetitions', e);
    return { competitions: [], total: 0, matchIds: [] };
  }
};

export const getInitialData = async () => {
  const { competitions, total, matchIds } = filterCompetitions({
    competitions: initList
  });
  getMatchOddsAndSave({ matchIds });
  return { competitions, total };
}

export const getCompetitionList = async ({ dateFilter = '', listType = 0, skipOdds = true } = {}) => {
  // if (listType !== 0) {
  //   const now = Date.now()
  //   const {competitions, total} = getHomeListWithDate(dateFilter, listType)
  //   if (competitions) {
  //     console.log(Date.now() - now, '读取缓存')
  //     return {competitions, total}
  //   }
  // }
  if (controller) {
    controller.abort();
  }
  controller = new AbortController();
  const signal = controller.signal;
  return igsRequest
    .post(
      'competition/list',
      {
        listType, //列表类型，0-Live， 1-ALL, 2-Finished, 3-Scheduled
        dateFilter: listType !== 0 ? dateFilter.replace('UTC', '') : getTodayDateFilter(), // "2022-08-27"
        skipOdds,
      },
      {
        signal,
      },
    )
    .then((result) => {
      const { competitions, total, matchIds } = filterCompetitions(result);
      if (competitions.length === 0) {
        if (listType !== 0) {
          // setHomeListWithDate({competitions, total}, dateFilter, listType)
        }
      }
      // console.log(matchIds)
      getMatchOddsAndSave({ matchIds });
      return { competitions, total };
    })
    .finally(() => {
      // controller = null
    });
};

export const getUpComingList = async () => {
  return igsRequest
    .post('competition/list', {
      listType: HomeGameTab.Scheduled,
      skipOdds: true,
      dateFilter: getTodayDateFilter(),
    })
    .then((result) => {
      const { competitions, total, matchIds } = filterCompetitions(result);
      getMatchOddsAndSave({ matchIds });
      return { competitions, total };
    });
};

export const getMatchOddsAndSave = async ({ matchIds = [] }) => {
  if (matchIds.length > 0) {
    igsRequest.post('match/odds/last', { matchIds }).then(({ matchRecentOdds }) => {
      homeDataController.syncHomeOdds(matchRecentOdds);
    });
  }
};


export const getFeaturedList = async () => {
  const result = await igsRequest.post('featured');
  return result;
};