import { useEffect, useState } from 'react';

import { getCompetitionHots } from 'iscommon/api/smallball-common/home';
import { CategoryIcon } from 'iscommon/const/icon';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

import Loading from '@/components/Loading';

interface Hot {
  id: string;
  name: string;
  logo: string;
  path: string;
  active: boolean;
}

import './HotCompetitions.less';

const HotCompetitions = () => {
  const labelMaps = useTranslateKeysToMaps(['Popular']);

  const [hots, setHots] = useState<Hot[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    setLoading(true);
    getCompetitionHots().then((res) => {
      setHots(res);
      setLoading(false);
    });
  }, []);

  return (
    <>
      <p className="title">{labelMaps.Popular}</p>
      <Loading loading={loading} isEmpty={!hots.length}>
        {hots.map((hot) => {
          return (
            <div className="item" key={hot.id}>
              <div className="target-blank">
                <div className="left">
                  <img className="logo" src={(CategoryIcon as any)[hot.logo] || hot.logo} alt="" />
                  <span className="text">{hot.name}</span>
                </div>
                {/* <i className="icon iconfont iconjiantou fs-12" /> */}
              </div>
            </div>
          );
        })}
      </Loading>
    </>
  );
};

export default HotCompetitions;
