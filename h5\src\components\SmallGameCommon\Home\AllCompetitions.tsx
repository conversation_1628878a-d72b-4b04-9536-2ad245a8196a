import { useCallback, useEffect, useRef, useState } from 'react';

import { getCompetitionByCountryOrCategory, getCountriesAndCategories } from 'iscommon/api/smallball-common/home';
import GlobalUtils, { GlobalConfig, PageTabs, GlobalSportPathname } from 'iscommon/const/globalConfig';
import { CategoryIcon } from 'iscommon/const/icon';
import { translate } from 'iscommon/i18n/utils';

import Loading from '@/components/Loading';

import './AllCompetitions.less';

interface Item {
  id: string;
  name: string;
  logo: string;
  path: string;
  active: boolean;
  mouseActive: boolean;
  isCategory?: boolean;
  competitions: {
    id: string;
    name: string;
    logo: string;
    active: boolean;
    path: string;
  }[];
}

const AllCompetitions = () => {
  const [list, setList] = useState<Item[]>([]);
  const selectedId = useRef('');
  const [loading, setLoading] = useState<boolean>(false);

  const fetchCountriesAndCategories = async () => {
    setLoading(true);
    const data: any = (await getCountriesAndCategories()) as unknown as {
      countries: Item[];
      categories: Item[];
    };
    // const countries = data.countries || [];
    const countries = GlobalConfig.pathname == GlobalSportPathname.cricket ? [] : (data?.countries || []);
    const categories = data.categories
      ? data.categories.map((item: any) => {
          return {
            ...item,
            isCategory: true,
          };
        })
      : [];
    const mergeList = [...categories, ...countries];
    
    setList(mergeList);
    setLoading(false);
  };

  const fetchCompetitions = useCallback(async (params: any) => {
    return await getCompetitionByCountryOrCategory(params, GlobalConfig.pathname);
  }, []);

  const showInnerList = async (id: string, isCategory: boolean = false) => {
    let newFilterList: any[];
    // 点击相同的li
    if (selectedId.current === id) {
      newFilterList = list.map((item) => {
        return {
          ...item,
          active: false,
        };
      });
      selectedId.current = '';
      return setList(newFilterList);
    }
    selectedId.current = id;

    const selectedItem = list.filter((item) => item.id === id);
    if (selectedItem[0].competitions?.length) {
      newFilterList = list.map((item) => {
        return {
          ...item,
          active: item.id === id,
        };
      });
    } else {
      const competitions = await fetchCompetitions(isCategory ? { categoryId: id } : { countryId: id });
      newFilterList = list.map((item) => {
        return {
          ...item,
          active: item.id === id,
          competitions: item.id === id ? competitions : item.competitions,
        };
      });
    }
    setList(newFilterList);
  };

  useEffect(() => {
    fetchCountriesAndCategories();
  }, []);

  const goCompetition = useCallback((item) => {
    if (item.curStageId) {
      GlobalUtils.goToPage(PageTabs.competition, item.id);
    }
  }, []);

  return (
    <>
      <p className="title">{translate('h5_Leagues')}</p>
      <Loading loading={loading} isEmpty={list.length === 0}>
        {list.map((item, i) => {
          return (
            <div key={item.id + i}>
              <div className="item" onClick={() => showInnerList(item.id, item.isCategory)}>
                <div className="left">
                  <img className="logo" src={(CategoryIcon as any)[item.logo] || item.logo} alt="" />
                  <span className="text">{item.name}</span>
                </div>
              </div>
              {item.active && (
                <ul className="inner-list">
                  {item.competitions &&
                    item.competitions.map((competition, index) => {
                      return (
                        <div className="target-blank" key={competition.id + index}>
                          <li className="inner-item">
                            <img className="inner-logo" src={competition.logo} alt="" />
                            <span className="inner-name">{competition.name}</span>
                          </li>
                        </div>
                      );
                    })}
                </ul>
              )}
            </div>
          );
        })}
      </Loading>
    </>
  );
};

export default AllCompetitions;
