.football-featured-card-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  background: #1E1E1E;
  padding: 24px 8px;
  border-radius: 24px;
  margin-bottom: 10px;

  .custom-football-swiper {
    .adm-swiper-slide, 
    .adm-swiper-slide-placeholder {
      width: fit-content;
    }

    .adm-swiper-track {
      justify-items: flex-start;
    }

    .match-list-container,
    .player-list-container,
    .competition-list-container,
    .team-list-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 12px 16px; 
      margin-right: 8px;
      border-radius: 16px;
      background: #2c2c2c;
      height: 130px;
    }
  
    .match-list-container {
      justify-content: space-between;
  
      .home-team,
      .away-team {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-overflow: ellipsis;
        margin: 0px 4px;
  
        .team-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 60px;
          height: 60px;
          border-radius: 50%;
          margin: 0px 10px;
          overflow: hidden;
          background-size: cover;
        }
      }
  
      .match-time {
        display: flex;
        flex-direction: column;
        align-items: center;
  
        .hour-time {
          text-align: center;
          font-weight: 600;
          font-size: 24px;
          color: #fff;
        }
  
        .date-time {
          font-size: 20px;
          color: #C0C5C9;
        }
      }
  
      .match-score-container {
        display: flex;
        flex-direction: column;
        align-items: center;
  
        .match-score {
          text-align: center;
          font-weight: 600;
          font-size: 24px;
          color: #fff;
        }
  
        .match-status {
          font-size: 20px;
          color: #C0C5C9;
        }
  
        .match-time-live {
          color: #fff;
      
          &.ing {
            color: #c1272d;
          }
        }
      }
    }
  
    .player-list-container {
      justify-content: center;
      width: fit-content;
  
      .player-icon {
        margin-right: 8px;
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 50%;
      }
  
      .player-info {
        display: flex;
        flex-direction: column;
  
        .player-name {
          font-size: 22px;
          color: #fff;
          font-weight: 600;
        }
  
        .team-name {
          font-size: 18px;
          color: #C0C5C9;
        }
      }
    }
  
    .competition-list-container {
      justify-content: center;
      width: fit-content;
  
      .competition-icon {
        margin-right: 8px;
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 50%;
      }
  
      .competition-name {
        font-size: 22px;
        font-weight: 600;
        color: #fff;
      }
    }
  
    .team-list-container {
      justify-content: center;
      width: fit-content;
  
      .team-icon {
        margin: 0px 8px;
        width: 60px;
        height: 60px;
        object-fit: cover;
        border-radius: 50%;
      }
  
      .team-name {
        font-size: 22px;
        font-weight: 600;
        color: #fff;
      }
    }
  }
}

.custom-football-swiper.single-item-swiper .adm-swiper-track-inner {
  transform: translate3d(-20%, 0px, 0px) !important;
}