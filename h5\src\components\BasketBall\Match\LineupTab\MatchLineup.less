.backGreen {
  background: #5dad6e;
  width: 100%;
  overflow: hidden;
}

.teamInfo {
  height: 86px;
  padding: 0 0.25rem;
  display: flex;
  flex-direction: row;
  align-items: center;
  color: white;
}

.bgLine{
  position: absolute;
  bottom: 0;
  transform: rotate(180deg) translateX(50%);
  left: 50%;
}

.groundBox {
  margin-left: 20px;
  margin-right: 20px;
  // margin: 0 auto;
  height: 1682px;
  // width: 741px;
  border: 2px solid hsla(0, 0%, 100%, 0.3);
  position: relative;
  box-sizing: border-box;
}

.formationInfo{
  position: absolute;
  left: 50%;
  color: #fff;
  text-align: center;
  position: absolute;
  background: hsla(0,0%,100%,.6);
  border-radius: 20px;
  font-size: 16px;
  color: #333;
  margin: 0 auto;
  z-index: 99;
  padding: 2px 10px;
}
.t{
  top: 0;
  transform: translateX(-50%) translateY(-50%);
}
.f{
  bottom: 0;
  transform: translateX(-50%) translateY(50%);
}

.halfBox {
  height: 50%;
  position: relative;
  border-bottom: 2px solid hsla(0, 0%, 100%, 0.3);
  z-index: 100;
}

.square2 {
  width: 4.64rem;
  height: 1.81rem;
  border-top: 0 !important;
  margin: 0 auto;
  border: 2px solid hsla(0, 0%, 100%, 0.3);
}

.square1 {
  width: 2.35rem;
  height: 0.96rem;
  border-top: 0 !important;
  margin: 0 auto;
  border: 2px solid hsla(0, 0%, 100%, 0.3);
}

.circleBox {
  height: 0.93rem;
  overflow: hidden;
}

.circle {
  width: 2.45rem;
  height: 2.45rem;
  border-radius: 50%;
  position: relative;
  top: -1.52rem;
  margin: 0 auto;
  border: 2px solid hsla(0, 0%, 100%, 0.3);
}

.midCircle {
  width: 2.67rem;
  height: 2.67rem;
  box-sizing: border-box;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
  border: 2px solid hsla(0, 0%, 100%, 0.3);
}

.midInner {
  width: 0.2rem;
  height: 0.2rem;
  border-radius: 50%;
  background: hsla(0, 0%, 100%, 0.3);
}

.player_box {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  width: 182px;
  height: 100px;
}

.player_avatar_box {
  position: relative;
  width: 64px;
  height: 64px;
}

.player_img {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  position: relative;
}

.player_name {
  // max-width: 50px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: white;
  font-size: 32px;
  width: 182px;
  text-align: center;
  // position: absolute;
}

.player_shirtNumber {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #444644;
  position: absolute;
  top: 30%;
  left: -15px;
  color: white;
  font-size: 24px;

  // transform: translate(-50%, 0);
}

.team_img {
  width: 17px;
  height: 17px;
  position: absolute;
  top: 23px;
  left: 4px;
}

.rating_box {
  // width: 40px;
  // height: 25px;
  border-radius: 10px;
  color: white;
  position: absolute;
  top: 0;
  left: 50%;
  font-size: 24px;
  padding: 0 4px;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: translateY(-50%);
}

.color9above {
  background-color: rgb(61, 132, 37);
}

.color8above {
  background-color: rgb(114, 178, 52);
}

.color7above {
  background-color: rgb(195, 204, 25);
}

.leftTopStyle {
  position: absolute;
  top: -5px;
  left: -50px;
  width: 64px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.even {
  display: inline-flex;
  vertical-align: middle;
  float: right;
  justify-content: center;
  align-items: center;
  color: white;
}

.other_even {
  position: absolute;
  top: 30px;
  right: -25px;
  display: inline-flex;
  vertical-align: middle;
  float: right;
  justify-content: center;
  align-items: center;
  color: white;
}

.fs_12 {
  font-size: 24px;
}

.teamInfo_box {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.align_center {
  display: flex;
  align-items: center;
}
