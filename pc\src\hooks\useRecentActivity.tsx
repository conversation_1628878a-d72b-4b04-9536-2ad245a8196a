import {
  addCompetitionActivity,
  addLeagueActivity,
  addMatchActivity,
  addRecentActivity,
  addTeamActivity,
  RecentActivityItem
} from '../utils/recentActivity';

import { GlobalConfig } from 'iscommon/const/globalConfig';
import React from 'react';
import { useCallback } from 'react';

/**
 * Hook for tracking user click activity
 * Provides convenient methods to track different types of clicks
 */
export const useRecentActivity = () => {
  
  // Dispatch custom event to notify other components
  const notifyUpdate = useCallback(() => {
    window.dispatchEvent(new CustomEvent('recentActivityUpdated'));
  }, []);

  // Track team click
  const trackTeamClick = useCallback((team: {
    id: string;
    name: string;
    logo?: string;
    competitionName?: string;
  }) => {
    const sport = GlobalConfig.pathname; // 'football', 'basketball', etc.
    
    addTeamActivity({
      id: team.id,
      name: team.name,
      logo: team.logo,
      sport: sport,
      competitionName: team.competitionName
    });
    
    notifyUpdate();
    console.log(`🎯 Tracked team click: ${team.name} (${sport})`);
  }, [notifyUpdate]);

  // Track match click
  const trackMatchClick = useCallback((match: {
    id: string;
    homeTeam: { name: string; logo?: string };
    awayTeam: { name: string; logo?: string };
    competitionName?: string;
    matchTime?: number;
  }) => {
    const sport = GlobalConfig.pathname;
    
    addMatchActivity({
      id: match.id,
      homeTeam: match.homeTeam,
      awayTeam: match.awayTeam,
      competitionName: match.competitionName,
      matchTime: match.matchTime,
      sport: sport
    });
    
    notifyUpdate();
    console.log(`🎯 Tracked match click: ${match.homeTeam.name} vs ${match.awayTeam.name} (${sport})`);
  }, [notifyUpdate]);

  // Track competition click
  const trackCompetitionClick = useCallback((competition: {
    id: string;
    name: string;
    logo?: string;
  }) => {
    const sport = GlobalConfig.pathname;
    
    addCompetitionActivity({
      id: competition.id,
      name: competition.name,
      logo: competition.logo,
      sport: sport
    });
    
    notifyUpdate();
    console.log(`🎯 Tracked competition click: ${competition.name} (${sport})`);
  }, [notifyUpdate]);

  // Track league click
  const trackLeagueClick = useCallback((league: {
    id: string;
    name: string;
    logo?: string;
  }) => {
    const sport = GlobalConfig.pathname;
    
    addLeagueActivity({
      id: league.id,
      name: league.name,
      logo: league.logo,
      sport: sport
    });
    
    notifyUpdate();
    console.log(`🎯 Tracked league click: ${league.name} (${sport})`);
  }, [notifyUpdate]);

  // Track custom activity
  const trackCustomActivity = useCallback((item: Omit<RecentActivityItem, 'timestamp'>) => {
    addRecentActivity(item);
    notifyUpdate();
    console.log(`🎯 Tracked custom activity: ${item.type} - ${item.name}`);
  }, [notifyUpdate]);

  // Track navigation click (for links, buttons, etc.)
  const trackNavigationClick = useCallback((navigation: {
    id: string;
    name: string;
    url?: string;
    type?: string;
  }) => {
    const sport = GlobalConfig.pathname;
    
    addRecentActivity({
      id: navigation.id,
      type: 'league', // Default type for navigation
      name: navigation.name,
      url: navigation.url,
      sport: sport,
      metadata: {
        navigationType: navigation.type || 'link'
      }
    });
    
    notifyUpdate();
    console.log(`🎯 Tracked navigation click: ${navigation.name}`);
  }, [notifyUpdate]);

  return {
    trackTeamClick,
    trackMatchClick,
    trackCompetitionClick,
    trackLeagueClick,
    trackCustomActivity,
    trackNavigationClick
  };
};

/**
 * Higher-order component to automatically track clicks
 */


export const withClickTracking = <T extends object>(
  Component: React.ComponentType<T>,
  trackingConfig: {
    type: 'team' | 'match' | 'competition' | 'league' | 'player';
    getId: (props: T) => string;
    getName: (props: T) => string;
    getLogo?: (props: T) => string | undefined;
    getMetadata?: (props: T) => any;
  }
) => {
  const Wrapped = React.forwardRef<any, T>((props, ref) => {
    const { trackCustomActivity } = useRecentActivity();

    const handleClick = React.useCallback(() => {
      const sport = GlobalConfig.pathname;
      trackCustomActivity({
        id: trackingConfig.getId(props),
        type: trackingConfig.type as 'team' | 'match' | 'competition' | 'league' | 'player',
        name: trackingConfig.getName(props),
        logo: trackingConfig.getLogo?.(props),
        sport: sport,
        metadata: trackingConfig.getMetadata?.(props)
      });
    }, [props, trackCustomActivity]);

    // If Component is a function component, ref will be ignored, so we only pass ref if supported
    return (
      <span onClick={handleClick} style={{ display: 'contents' }}>
        {React.createElement(Component, { ...(props as any), ...(ref ? { ref } : {}) })}
      </span>
    );
  });
  Wrapped.displayName = `WithClickTracking(${Component.displayName || Component.name || 'Component'})`;
  return Wrapped;
};

export default useRecentActivity;
