import SectionTitle from '@/components/Competition/Overview/SectionTitle';
import classNames from 'classnames';
import { getTeamCompetitions } from 'iscommon/api/football-team';
import { getPlayerStatistics } from 'iscommon/api/player';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { PlayerTabH5 } from 'iscommon/mobx/modules/player';
import React, { useCallback, useEffect, useState } from 'react';
import { useParams } from 'umi';

import styles from './index.less';

interface Props {
  teamId: string;
}

const PlayerMatches: React.FC<Props> = () => {
  const { categoryId: playerId } = useParams<{ categoryId: string }>();
  const labelMaps = useTranslateKeysToMaps([
    'Matches',
    'TotalPlayed',
    'Started',
    'MinutesPerGame',
    'Goals',
    'GoalsPerGame',
    'Assists',
    'YellowCards',
    'RedCards',
    'Saves',
  ]);

  const [competition, setCompetition] = useState<any>({});
  const [season, setSeason] = useState<any>({});
  const [matchInfo, setMatchInfo] = useState<any>({});

  useEffect(() => {
    if (playerId) {
      getTeamCompetitions({ playerId, validKey: 'hasPlayerStats' }).then(async ({ competitions }: any) => {
        if (competitions?.length) {
          const currentCompetition = competitions[0];
          const currentSeason = currentCompetition.seasons.find((i: any) => i.isCurrent === 1);

          setCompetition(currentCompetition);
          setSeason(currentSeason);

          const res: any = await getPlayerStatistics({
            competitionId: currentCompetition.id,
            seasonId: currentSeason?.id,
            playerId,
          });

          if (res) {
            setMatchInfo(res);
          }
        }
      });
    }
  }, [playerId]);

  const goMatches = useCallback(() => {
    GlobalUtils.goToPage(PageTabs.player, playerId, {
      target: 'history',
      customTab: PlayerTabH5.matches,
    });
  }, [playerId]);

  return (
    <>
      <SectionTitle leftTitle={labelMaps.Matches} rightTitle="More" onClick={goMatches} />
      {/* <div className="playerOverviewTitle">{labelMaps.Matches}</div> */}
      <div className={styles.container}>
        <div className={styles.competition}>
          <div className={styles.competitionTitle}>
            <img className={styles.logo} src={competition.logo} alt="" />
            <span>{competition.name}</span>
          </div>
          <div className={styles.season}>{season?.year}</div>
        </div>
        <div className={styles.baseDesc}>
          <div className={styles.baseRow}>
            <div className={styles.label}>{labelMaps.TotalPlayed}</div>
            <div className={styles.value}>{matchInfo?.matches?.court || 0}</div>
          </div>
          <div className={styles.baseRow}>
            <div className={styles.label}>{labelMaps.Started}</div>
            <div className={styles.value}>{matchInfo?.matches?.first || 0}</div>
          </div>
          <div className={styles.baseRow}>
            <div className={styles.label}>{labelMaps.MinutesPerGame}</div>
            <div className={styles.value}>{matchInfo?.matches?.minutesPlayed || 0}</div>
          </div>
          <div className={classNames(styles.baseRow, styles.hl)}>
            <div className={styles.label}>{labelMaps.Goals}(PK)</div>
            <div className={styles.value}>{matchInfo?.attacking?.goals || 0}</div>
          </div>
          <div className={classNames(styles.baseRow, styles.hl)}>
            <div className={styles.label}>{labelMaps.Assists}</div>
            <div className={styles.value}>{matchInfo?.passes?.assist || 0}</div>
          </div>
          <div className={classNames(styles.baseRow, styles.hl)}>
            <div className={styles.label}>{labelMaps.YellowCards}</div>
            <div className={styles.value}>{matchInfo?.cards?.yellowCards || 0}</div>
          </div>
          <div className={classNames(styles.baseRow, styles.hl)}>
            <div className={styles.label}>{labelMaps.RedCards}</div>
            <div className={styles.value}>{matchInfo?.cards?.redCards || 0}</div>
          </div>
          <div className={classNames(styles.baseRow, styles.hl)}>
            <div className={styles.label}>{labelMaps.Saves}</div>
            <div className={styles.value}>{matchInfo.first || 0}</div>
          </div>
        </div>
      </div>
    </>
  );
};

export default PlayerMatches;
