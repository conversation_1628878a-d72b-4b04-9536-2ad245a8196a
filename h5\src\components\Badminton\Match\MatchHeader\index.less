.common-match-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 32px 0px;

  .home-team-container,
  .match-score-container,
  .away-team-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 30%;
    height: 30%;

    .team-logo {
      width: 80px;
      height: 80px;
      border-radius: 16px;
      overflow: hidden;
      background-size: cover;
      vertical-align: middle;
      margin-bottom: 10px;
    }

    .team-name {
      font-size: 24px;
      color: #fff;
      text-overflow: ellipsis;
      text-align: center;
    }
  }

  .score-container {
    display: flex;
    flex-direction: column;
    align-items: center;

    .match-score {
      display: flex;
      flex-direction: row;

      .score-text {
        font-size: 60px;
        font-weight: 700;
        color: #fff;
        padding: 0px 5px;

        &.in-progress {
          color: #c1272d;
        }
      }
    }

    .status-text {
      font-size: 24px;
      font-weight: 600;
      color: #C0C5C9;
    }

    .not-started {
      font-size: 60px;
      font-weight: 700;
      color: #fff;
    }

    .pk-time {
      font-size: 24px;
      font-weight: 600;
      color: #fff;

      &.in-progress {
        color: #c1272d;
      }
    }
  }
}