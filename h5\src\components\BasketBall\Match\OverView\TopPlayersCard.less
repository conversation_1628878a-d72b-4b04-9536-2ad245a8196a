.basketball-top-players-card-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .player-team-container {
    display: flex;
    justify-content: space-between;
    padding: 16px;
    background: #1e1e1e;

    .team-container {
      display: flex;
      align-items: center;

      .team-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin: 5px 10px;
        overflow: hidden;
        background-size: cover;
      }
      
      .team-name {
        font-size: 22px;
        color: #fff;
      }
    }
  }

  .container-body {
    background: #1e1e1e;

    .player-container {
      margin-top: 12px;
      display: flex;
      flex-direction: row;

      .player-item {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        flex: 1;

        .player-icon {
          width: 80px;
          height: 80px;
          border-radius: 16px;
          overflow: hidden;
          background-size: cover;
          vertical-align: middle;
          margin-bottom: 10px;
          object-fit: cover;
        }

        .player-name {
          font-size: 22px;
          color: #fff;
        }
      }

      .contrast-box-container {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 200px;
        color: #fff;

        .item-count {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          padding: 2px;
          border-radius: 8px;
          color: #fff;

          &.home-item {
            background: #0073E0;
          }

          &.away-item {
            background: #F79421;
          }
        }
      }
    }
  }
}