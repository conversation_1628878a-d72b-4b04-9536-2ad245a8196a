import { getCompetitionList, getUpComingList } from 'iscommon/api/esports/home';

import GameCategory from './GameCategory';
import SmallBallGameListContainer from './SGameListContainer';

const GameListContainer = (props: any) => {
  const { propsTab, isSortByTime = false } = props;

  return (
    <SmallBallGameListContainer
      {...props}
      renderCateGory={(item) => <GameCategory competition={item} />}
      getUpComingList={getUpComingList}
      getCompetitionList={getCompetitionList}
    />
  );
};

export default GameListContainer;
