import { inject, observer } from 'mobx-react';

import { translate } from 'iscommon/i18n/utils';

import './SingleMatchLine.less';

const Venue = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Match: {
          matchHeaderInfo: { venue },
        },
      },
    } = props;
    const venueText = translate('venue');
    if (!venue) return null;
    return (
      <div className="singleMatchLine">
        <div className="plugins" style={{ minHeight: 0, marginBottom: 0 }}>
          <div className="head bg-fff">
            <span className="ft-500">{venueText}</span>
          </div>
          <div className="des">
            <div className="des_key">
              <span className="icon iconfont iconchangguan myIcon"></span>
              <span className="Trafford">{venue?.name}</span>
            </div>
          </div>
        </div>
      </div>
    );
  }),
);

export default Venue;
