{"All": "すべて", "Live": "ライブ", "LiveH5": "ライブ", "MatchLive": "ライブ", "TimeSort": "時間によるソート", "SortByTime": "時間によるソート", "AllGames": "ゲーム", "Leagues": "リーグ", "h5_Leagues": "リーグ", "Today": "今日", "Cancel": "中止", "Popular": "人気", "Settings": "設定", "Language": "言語", "Overview": "概要", "LiveOverview": "概要", "Standings": "順位表", "Stats": "統計", "Transfer": "移籍", "Champions": "優勝者", "TeamChampions": "優勝者", "teamChampions": "優勝者", "Football": "サッカー", "Basketball": "バスケットボール", "Baseball": "野球", "Icehockey": "ホッケー", "Tennis": "テニス", "Volleyball": "バレー", "Esports": "eスポーツ", "Handball": "ハンドボール", "Cricket": "クリケット", "WaterPolo": "水球", "TableTennis": "卓球", "Snooker": "スヌーカー", "Badminton": "バドミントン", "BusinessCooperation": "事業提携", "TermsOfService": "利用規約", "PrivacyPolicy": "個人情報保護方針", "Players": "選手", "ForeignPlayers": "外国人選手数", "NumberOfTeams": "チーム数", "YellowCards": "イエローカード数", "RedCards": "レッドカード数", "Capacity": "観客収容人数", "City": "市", "Info": "情報", "Matches": "試合", "Team": "チーム", "Teams": "チーム", "Goals": "ゴール数", "Assists": "アシスト数", "assists": "アシスト数", "Home": "ホーム", "Away": "アウェー", "topScorers": "得点王", "TopScorers": "得点王", "homeTopScorers": "得点王", "season": "シーズン", "Season": "シーズン", "ShotsOnTarget": "枠内シュート数", "Clearances": "クリア数", "Tackles": "タックル", "keyPasses": "アシストパス成功率", "KeyPasses": "アシストパス成功率", "Fouls": "ファウル数", "totalFouls": "ファウル数", "WasFouled": "被ファウル数", "Penalty": "ペナルティ", "MinutesPlayed": "出場時間", "BasketballMinutesPlayed": "出場時間", "Interceptions": "ボールカット数", "Steals": "スティール数", "steals": "スティール数", "Passes": "通過", "Saves": "セーブ数", "BlockedShots": "ブロック数", "Signed": "契約済", "league": "", "offensiveData": "攻撃データ", "defenseData": "守備データ", "otherData": "その他データ", "ballPossession": "ボール支配率", "shotsPerGame": "試合平均シュート数", "ShotsPerGame": "試合平均シュート数", "keyPassesPerGame": "試合平均アシスト数", "accurateLongBallsPerGame": "試合平均パス数成功率", "accurateCrossesPerGame": "試合平均パス数成功率", "tacklesPerGame": "試合平均タックル数", "TacklesPerGame": "試合平均タックル数", "interceptionsPerGame": "試合平均インターセプト数", "InterceptionsPerGame": "試合平均インターセプト数", "clearancesPerGame": "試合平均クリア数", "ClearancesPerGame": "試合平均クリア数", "blockedShotsPerGame": "試合平均ブロック数", "turnoversPerGame": "試合平均ターンオーバー数", "foulsPerGame": "試合平均ファウル数", "scoringFrequencyFiveGoals": "", "Coach": "コーチ", "Goalkeeper": "ゴールキーパー", "Stadium": "スタジアム", "Login": "サインイン", "Corner": "コーナーキック", "ShotsOffTarget": "枠外シュート数", "H2H": "H2H", "Date": "日付", "OwnGoal": "オウンゴール", "PenaltyMissed": "ペナルティキック失敗", "SecondYellow": "2枚目のイエローカード", "Odds": "オッズ", "attacks": "攻撃", "Started": "試合開始", "Chat": "チャット", "Strengths": "長所", "Weaknesses": "弱点", "Group": "グループ", "Birthday": "生年月日", "Club": "クラブ", "MainPosition": "主要ポジション", "PassesAccuracy": "", "Preseason": "", "RegularSeason": "", "PointsPerGame": "試合平均ポイント数", "Glossary": "用語", "h5Glossary": "用語", "Career": "キャリア", "Bench": "控えメンバー", "ReboundsPerGame": "試合平均リバウンド数", "AssistsPerGame": "試合平均アシスト数", "OddsFormat": "オッズフォーマット", "Squad": "メンバー", "TotalMarketValue": "合計市場価格", "Rounds": "ラウンド数", "LowerDivision": "下位", "TeamStats": "チーム統計", "GoalsPk": "ゴール数(PK)", "Crosses": "クロス数", "CrossesAccuracy": "首尾よくボールをパス", "Dribble": "素晴らしいです", "DribbleSucc": "臨時成功", "LongBalls": "ロングボール数", "LongBallsAccuracy": "ロング通過成功率", "Duels": "ファウル数", "DuelsWon": "白紙の状態", "Dispossessed": "セーブ数", "Punches": "ゴールキーパーの攻撃成功", "RunsOut": "ペナルティエリア外でのプレー", "RunsOutSucc": "シュートのブロック数", "GoodHighClaim": "渡す精度", "Loan": "期限付き移籍", "EndOfLoan": "移籍期限の終了", "Unknown": "不明", "AverageAge": "平均年齢", "cornersPerGame": "コーナーキック", "goalsConceded": "失点数", "Defender": "ディフェンダー", "Discipline": "規律性", "Pass": "", "FB_Login": "Facebookでサインイン", "Google_Login": "Googleでサインイン", "Substitutes": "控え選手", "PenaltyKick": "", "ShareYourViews": "意見の共有", "Nodata": "データなし", "Foot": "足の長さ", "dangerousAttack": "", "venue": "試合会場", "playerStatistics": "選手統計", "TotalPlayed": "合計試合出場時間", "MinutesPerGame": "試合平均出場時間", "GoalsFrequency": "", "GoalsPerGame": "フィールドゴール", "Arrivals": "入団", "Departures": "退団", "LeftFoot": "左", "RightFoot": "右", "LatestTransfers": "最近の移籍", "DraftInfo": "ドラフト情報", "OK": "はい", "AsianHandicap": "", "TotalGoals": "", "AmFootballTotalGames": "", "TotalCorners": "", "OverBall": "オーバー", "Over": "オーバー", "h5Over": "オーバー", "UnderBall": "下", "Under": "下", "h5Under": "下", "OtherLeagues": "他の言語 [A-Z]", "GoalPopup": "", "FullStandings": "順位表", "teamWeek": "今週のチーム", "weekTop": "今週のチーム", "TeamOfTheWeek": "今週のチーム", "round": "ラウンド", "Released": "放出済", "Retirement": "", "Draft": "ドラフト", "TransferIn": "", "TransferOut": "", "MarketValue": "市場価格", "Salary": "サラリー", "Next": "次", "Position": "ポジション", "CTR": "最近の移籍記録", "FoulBall": "", "FreeKick": "", "GoalKick": "", "Midfield": "", "HalftimeScore": "", "InjuryTime": "", "OvertimeIsOver": "", "PenaltyKickEnded": "", "Last": "最後の", "Win": "勝利", "Draw": "引分け", "Lose": "敗戦", "Lineup": "", "Substitution": "選手交代", "Offsides": "オフサイド数", "Playoffs": "", "Qualifier": "", "GroupStage": "", "Rebounds": "リバウンド数", "rebounds": "リバウンド数", "OffensiveRebounds": "オフェンス・リバウンド数", "offensiveRebounds": "オフェンス・リバウンド数", "DefensiveRebounds": "ディフェンス・リバウンド数", "defensiveRebounds": "ディフェンス・リバウンド数", "Turnovers": "ターンオーバー数", "turnovers": "ターンオーバー数", "Blocks": "ブロック数", "blocks": "ブロック数", "BoxScore": "ボックススコア", "Foul": "ファウル", "FreeThrows": "フリースロー数", "freeThrowsScored": "フリースロー数", "fieldGoalsScored": "", "fieldGoalsTotal": "", "threePointersScored": "", "threePointersTotal": "", "freeThrowsTotal": "", "Finished": "終了しました", "Scheduled": "備品", "Favourite": "", "OddsMarkets": "オッズ市場", "Search": "", "Timezone": "", "SoccerGoalReminderSettings": "", "RemindersOnlyForRacesToWatch": "", "GoalSound": "", "LiveScore": "", "Schedule": "スケジュール", "Rugby": "", "FooterContentFootball": "IGScore Football LiveScoreは、2600以上のサッカーリーグ、カップ、トーナメントからの比類のないサッカーライブスコアとサッカー結果を提供します。プレミアリーグ、ラリーガ、セリエA、ブンデスリーガ、リーグ1、エレディビジー、ロシアプレミアリーグ、ブラジレイラン、MLSから、ライブスコア、ハーフタイムとフルタイムのサッカーの結果、ゴールスコアラーとアシスタント、カード、交代、試合統計、ライブストリームを入手できます。 igscore.netのスーパーリーグとチャンピオンシップ。 IG Scoreは、イングランドプレミアリーグ、スペインラリーガ、イタリアセリエA、ドイツなどの最も人気のあるサッカーリーグだけでなく、すべてのサッカーファンにライブスコア、サッカーライブスコア、サッカースコア、リーグテーブル、リーグ、カップ、トーナメントの試合を提供しています。ブンデスリーガ、フランスリーグ1だけでなく、南北アメリカ、アジア、アフリカなど、世界中のさまざまなサッカー国からも参加しています。私たちのサッカーライフスコアスコアカードはリアルタイムでライブで更新され、すべてのサッカーとサッカーリーグのすべての終了したサッカー試合のサッカーライブスコアの結果とともに、今日行われているすべてのサッカー試合のライフスコアの更新を最新の状態に保ちます。試合ページでは、サッカーのスコアカードを使用して、すべてのサッカー大会で以前にプレイしたすべての試合の過去の試合結果を表示できます。 igscore.netでサッカーのライブ結果をすべて入手してください！", "FooterContentBasketball": "IG ScoreバスケットボールLivescoreは、NBAリーグのライブスコア、結果、テーブル、統計、フィクスチャ、順位、過去の結果を四半期、ハーフタイム、または最終結果別に提供します。 IG Scoreは、世界中の200以上のバスケットボールコンテスト（NCAA、ABAリーグ、バルティックリーグ、ユーロリーグ、ナショナルバスケットボールリーグなど）のスコアサービスを提供しています。ここでは、ライブスコア、四半期結果、最終結果、ラインナップだけでなく、2ポイントおよび3ポイントの試行回数、フリースロー、射撃の割合、リバウンド、ターンオーバー、スチール、個人ファウル、試合履歴、およびプレーヤーの統計も確認できます。IGScore Basketball LiveScoreでは、バスケットボールをクリックするだけでオンラインで視聴でき、トップリーグの試合をオンラインでカバーできます。そこの試合ページには、チームの最新の試合に関するバスケットボールの統計がすべて掲載された表もあります。バスケットボールのスコアカードはリアルタイムでリアルタイムに更新され、今日行われているすべてのバスケットボールの結果を最新の状態に保ち、すべてのバスケットボールの試合でこれまでに行われたすべての試合の過去のゲーム結果を表示できます。 igscore.netですべてのNBAライブ結果を入手してください！ NBAライブスコア、NBAフィクスチャ、NBA順位表、チームページをフォローしてください！", "FooterContentAmFootball": "IGScore american football live score世界最大かつ最も人気のあるアメリカンフットボールリーグからのすべての結果とライブスコアを提供します -  NFL、そして通常のNFLシーズンが終了したら、NFL PlayoffsとSuperBowlのライブスコアに従ってください。 NFLに加えて、NCAA大学のアメリカンフットボールとカナダのCFLのライブスコール、結果、順位、スケジュールを提供します。", "FooterContentBaseball": "IGScore baseball live scoreは、世界で最も人気のある野球リーグ（USSSA野球、NCAA野球、MLB野球、MLBオールスターゲーム）のライブスコア、結果、順位を提供します。 また、日本プロリーグ、メキシカンリーグ、ドイツ1.ブンデスリーガ、NCAA、国際野球トーナメントのワールドベースボールクラシックのライブスコアも提供しています。 また、野球リーグの順位、過去の試合、イニングによる結果、IGScore baseball live scoreでの今後の野球の試合のスケジュールもいつでも確認できます。", "FooterContentIcehockey": "IGScore ice hockey live scoreは、アイスホッケーリーグ、カップ、トーナメントのリアルタイムのアイスホッケー結果スコアを提供します。 IGScore ice hockey live scoreは、NHL、SHL、KHLのホッケーのライブスコア、表、統計、備品、結果、スコアを提供します。また、フィンランドの全国ホッケーリーグ、スウェーデンのホッケーリーグ、スロバキアのホッケーリーグ、チェコのホッケーリーグ、リーグテーブル、ゴールスコアラーも提供します。 3分の1と最終的なアイスホッケーの結果がライブです。 アイスホッケーのレギュラーシーズンが終了した後、私たちはあなたにホッケーのライブスコア、順位、トップアイスホッケーイベントの結果-IIHF世界選手権スタンレーカップ、そして冬季オリンピックトーナメントからのホッケースコアを提供します。 IGScore ice hockey live scoreには、NHL、SHLなどの無料ホッケーライブストリームもあります。", "FooterContentTennis": "IGScore tennis live scoreは、デイビスやフェッドカップ、全仏オープンテニスなどのすべての最大のテニストーナメント、またはすべてのグランドスラムトーナメント（オーストラリアオープンテニス、全米オープンテニス、ローランド）のライフスコア、結果、ATPランキング、WTAランキング、備品、統計を提供します。 ギャロスとウィンブルドンは、シングルスとダブルスの女性と男性の両方。 また、どのテニスプレーヤーについても、彼がプレーした試合を個別に詳細に確認し、セットごとの結果と、どのトーナメントでその試合がプレーされたかを確認できます。 IGScore tennis live scoreは、対戦する2人のプレーヤー間の直接の結果、統計、ライブスコア、ライブストリームを提供します。", "FooterContentVolleyball": "IGScore volleyball live scoreは、イタリアセリエA1女子とイタリアセリエA1女子、ロシアバレーボールスーパーリガ、ポーランド男子バレーボール、トルコ1.リグなど、すべての重要な男子および女子バレーボールリーグの試合をカバーしています。 国内バレーボールリーグのほかに、FIVB世界選手権や欧州選手権などの主要なバレーボール国際トーナメントのライフスコア情報や、オリンピックのバレーボールライブスコアの結果も提供しています。 また、お気に入りのバレーボールチームの古い結果を確認したり、将来のバレーボールスケジュールを確認したり、IGScore volleyball live scoreでリーグバレーボールの順位を確認したりすることもできます。", "FooterContentEsports": "ESPORTS LIVE SCORES SERVICE IGScore LIVE SCORE ESPORTS LIVEスコア、スケジュール、結果、およびテーブルを提供します。 あなたのお気に入りのチームに従ってください。 eSportsライブスコアオンigscore.netライブスコアは自動的に更新され、手動で更新する必要はありません。 あなたの試合に続く「私のゲーム」でフォローしたいゲームを追加すると、結果と統計はさらに簡単になります。", "FooterContentHandball": "IGScore handball live scoreは、ドイツブンデスリーガ、スペインリーガアソバル、デンマークメンズハンドボルドリガエン、フランスD1などの最も人気のあるハンドボールリーグのハンドボールライブスコアとライブ結果を提供します。 また、ヨーロッパハンドボールチャンピオンズリーグ、SEHAリーグ、EHFハンドボールカップなどの重要なカップのライフスコア、結果、統計、順位表、テーブル、備品も提供しています。 IGScore handball live scoreでは、女性と男性の両方を対象とした、ヨーロッパ選手権や世界選手権などの国際チームハンドボールトーナメントのライブスコアと無料のライブストリームを見つけることができます。 いつでも、チームがプレイした過去10試合のハンドボールの結果と統計を確認できます。また、統計でプレイするようにスケジュールされているチーム間の直接のスコアも確認できます。", "FooterContentCricket": "IGScore cricket live scoreは、リアルタイムのクリケットの結果、クリケットの順位、およびクリケットの備品を追跡するために提供します。 これらはすべて、インディアンプレミアリーグ、チャンピオンズリーグトゥエンティ20、ビッグバッシュリーグ、カリビアンプレミアリーグ、フレンズライフT20、ICCクリケットワールドカップなど、最も人気のあるリーグとカップで利用できます。 IG Scoreのすべてのクリケットスコアは自動的に更新され、手動で更新する必要はありません。 それだけで、無料のクリケットのライブストリームを視聴し、世界中で最も興味深いクリケットの試合の最終結果について最新のオッズを確認するオプションがあります。", "FooterContentWaterPolo": "IGScore water polo live scoreは、クラブレベルでイタリアセリエA1、ハンガリーOB1、チャンピオンズ、アドリアティックリーグの水球ライブスコアと結果を提供し、国際レベルでは、IGScore water polo live scoreは水球世界選手権やヨーロッパ水球選手権などの主要なトーナメントを提供します。 。 ゴールごとのライブスコアと無料のライブストリームを提供します。", "FooterContentTableTennis": "IGScore table tennis live scoreは、ロシアの卓球、卓球オリンピックなど、すべての最大の卓球トーナメントのライフスコア、テーブル、結果、卓球のランキング、備品、統計を提供します。 また、どの卓球プレーヤーでも、彼がプレーした試合を個別に詳細に確認し、セットごとの結果と、どのトーナメントでその試合がプレーされたかを確認できます。 IGScore table tennis live scoreは、対戦を行う2人のプレーヤー間の直接の結果、統計、ライブスコア、およびライブストリームを提供します。", "FooterContentSnooker": "IGScore snooker live scoreは、すべてのスヌーカートーナメントのライブスコア、結果、順位を追跡する機能を提供します。 また、英国と世界選手権のライフスコア、スヌーカーのライフスコア、スヌーカーの備品、世界スヌーカーツアーなどの国際トーナメントの最終的なスヌーカーの結果も提供しています。 いつでも、予定されているスヌーカートーナメントのスケジュール、過去のスヌーカートーナメントの結果、およびすべてのプレーヤーの過去10試合を見ることができます。 さらに、プレイヤー間の過去の対戦を確認することができます。 IGScore snooker live scoreでは、無料のスヌーカーライブストリームでカバーされている試合のリストを見つけることができます。", "FooterContentBadminton": "IGScore badminton live scoreは、世界選手権、BWFスーパーシリーズ、オリンピックのバドミントンの結果などの国際トーナメントのバドミントンのライブ結果、順位、備品、統計を提供します。 また、バドミントンでプレーしたゲームの結果を確認したり、バドミントンのプレースケジュールを確認したり、IGScore badminton live scoreでプレーヤーからのバドミントンの結果を個別に確認したりすることもできます。", "ContactUs": "お問い合わせ", "UpdateLineups": "Update Lineups", "FreeLiveScoresWidget": "Free Livescores Widget", "PlayerSalary": "", "DivisionLevel": "イベントレベル", "Foreigners": "外国人選手の数", "LeagueInfo": "トーナメント", "TeamInfo": "チーム情報", "Venues": "", "MostValuablePlayer": "", "UpperDivision": "上位", "NewcomersFromUpperDivision": "", "NewcomersFromLowerDivision": "", "TitleHolder": "タイトル保持者", "MostTitle": "番号を勝つためにアップ", "Pts": "得点数", "FullStats": "", "Relegation": "", "Result": "結果", "Score": "得点", "PlayerStats": "", "fixtures": "", "topPlayers": "キープレーヤー", "Shots": "撮影", "SelectTeam": "", "SelectBasketBallTeam": "", "SelectAll": "すべてを選択", "SquadSize": "プレイヤーの数", "ViewAll": "すべてを見る", "penaltiesWon": "ペナルティキックによるゴール", "accuracyCrosses": "", "totalShorts": "", "accuracyLongBalls": "", "blockShots": "", "MatchesToday": "今日の試合", "Strikers": "前方に", "Midfielders": "ミッドフィルダー", "Year": "", "Loans": "", "TopValuablePlayers": "", "total": "", "Total": "", "Results": "", "Prev": "", "Apps": "外観", "ShotsPg": "アベレージングショット", "Possession": "", "TotalAndAve": "", "Suspended": "負傷または一時出場停止", "injuredOrSuspended": "負傷または一時出場停止", "Since": "ロスタイム", "Overall": "Overall", "Age": "年齢", "LastMatchFormations": "最後のゲームラインナップ", "Formation": "", "GoalDistribution": "目標の分布", "Favorite": "", "FoundedIn": "年に設立されました", "LocalPlayers": "地元選手数", "ShowNext": "次の試合を表示", "HideNext": "次の試合を非表示", "FIFAWorldRanking": "FIFA世界ランキング", "Register": "", "OP": "1X2", "Handicap": "", "h5Handicap": "", "TennisHandicap": "", "OU": "O/U", "CardUpgradeConfirmed": "カードアップグレード確定", "VAR": "", "LatestMatches": "最新の試合", "ScheduledMatches": "", "Only": "", "LeagueFilter": "", "WinProb": "", "ScoreHalf": "", "Animation": "", "FigureLegends": "", "YellowCard": "イエローカード", "RedCard": "レッドカード", "Chatroom": "チャットルーム", "Send": "送信", "Logout": "", "CurrentLeague": "", "ShirtNumber": "", "ContractUntil": "までの契約", "PlayerInfo": "PLAYER INFO", "Height": "身長", "Weight": "体重", "PlayerValue": "社会的地位", "View": "", "Time": "時間", "onTarget": "", "offTarget": "", "Played": "", "Rating": "", "Attacking": "攻撃", "Creativity": "独創性", "Defending": "守備", "Tactical": "戦術", "Technical": "技術", "Other": "", "Cards": "ペナルティカード", "AccuratePerGame": "正確な通過", "AccLongBalls": "准ロングパス", "AccCrosses": "正確な伝記", "SuccDribbles": "臨時成功", "TotalDuelsWon": "ファウル数", "YellowRedCards": "", "AiScoreRatings": "", "Pergame": "", "Player": "", "Upcoming": "", "FreeKicks": "", "Corners": "コーナーキック", "DaysUntil": "…までの日数", "In": "イン（選手交代）", "Out": "アウト（選手交代）", "NoStrengths": "特に長所はなし", "NoWeaknesses": "特に弱点はなし", "UpcomingMatches": "", "Pos": "", "Ht(cm)": "", "wt(kg)": "", "Exp": "経験値", "College": "", "Salary/Year": "", "Manager": "", "TotalPlayers": "プレイヤーの数", "Form": "", "Points": "得点", "GamesPlayed": "", "WinPercentage": "", "FieldGoalsMade": "", "FieldGoalsAttempted": "", "FieldGoalPercentage": "", "threePointFieldGoalsMade": "", "threePointFieldGoalsAttempted": "", "threePointFieldGoalsPercentage": "", "FieldGoaldsMade": "", "FreeThrowsAttempted": "", "FreeThrowPercentage": "", "BlockedFieldGoalAttempts": "", "PersonalFouls": "", "PersonalFoulsDrawn": "", "Efficiency": "", "PlusMinus": "", "Atlantic": "", "Central": "", "Southeast": "", "Northwest": "", "Pacific": "", "Southwest": "", "EasternConference": "", "WesternConference": "", "ToWin": "", "Spread": "展開する", "AmFootballHand": "展開する", "TotalPoints": "", "TotalPoint": "", "TableTennisTotalGames": "", "Wins": "勝利", "Losses": "敗戦", "WinningPercentage": "", "GamesBack": "", "HomeRecord": "", "AwayRecord": "", "DivisionRecord": "", "ConferenceRecord": "", "OpponentPointsPerGame": "", "AveragePointDifferential": "", "CurrentStreak": "", "RecordLast5Games": "", "Match": "対戦", "OnTheCourt": "コート上での", "Starters": "スターティングメンバー", "FieldGoals": "フィールドゴール数", "Timeout": "", "OpeningOdds": "", "PreMatchOdds": "", "PointPerGame": "", "PointsAllowed": "", "StartingLineups": "先発メンバー", "HandicapGames": "", "TennisHand": "", "TotalGames": "", "TennisTotalGames": "", "Defensive": "", "Offensive": "", "Points(PerGame)": "", "Rebounds(PerGame)": "", "Other(PerGame)": "他の（平均）", "Attists": "", "FirstServe": "", "FirstServePoints": "", "BreakPoints": "", "Aces": "サービスエース", "DoubleFaults": "ダブルフォルト", "Winner": "Winner", "HandicapSets": "", "TableTennisHand": "", "MoneyLine": "", "TableTennisHandicapGames": "", "HandicapRuns": "", "BaseballHand": "", "TotalRuns": "", "BaseballTotalGames": "", "DIFF": "", "BasketPergame": "", "HandicapPoints": "", "HandicapFrames": "", "TotalFrames": "", "SeeAll": ""}