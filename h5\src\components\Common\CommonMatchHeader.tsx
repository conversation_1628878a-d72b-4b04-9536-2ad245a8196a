import classNames from 'classnames';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { getGameVideoInfo } from 'iscommon/api/match';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { callAppUrl } from 'iscommon/utils';
import { getLiveUrl } from 'iscommon/utils/live';

import { CommonLiveOrVideoHeader } from '@/components/Common/LiveHeader';

import styles from './CommonMatchHeader.less';

const CommonMatchHeader: React.FC<any> = (props) => {
  const { children, matchId, background } = props;
  const labelMaps = useTranslateKeysToMaps(['Animation', 'LiveStream']);
  const [videoLink, setVideoLink] = useState<string>('');
  const [showHeader, setShowHeader] = useState<number>(0);

  const liveUrl = useMemo(() => getLiveUrl(matchId), [matchId]);

  const showAnimation = useCallback(() => {
    window.gtag('event', 'click_animation_button', { platform: 'mobile' });
    setShowHeader(1);
  }, []);

  const showLiveStream = useCallback(() => {
    window.gtag('event', 'click_stream_button', { platform: 'mobile' });
    callAppUrl('stream');
  }, []);

  useEffect(() => {
    getGameVideoInfo(matchId).then((d: any) => {
      if (d && d.link) {
        setVideoLink(d.link);
      }
    });
  }, [matchId]);

  return (
    <div className={styles.container}>
      {showHeader == 0 && (
        <>
          {children}
          <div className={styles.btnWrap}>
            {videoLink && (
              <div className={styles.btn} style={{ marginRight: 5 }} onClick={showLiveStream}>
                <i className={classNames('icon iconfont iconshipingzhibo', styles.img)} />
                {labelMaps.LiveStream}
              </div>
            )}
            <div className={styles.btn} onClick={showAnimation}>
              <i className={classNames('icon iconfont icondonghuazhibo', styles.img)} />
              {labelMaps.Animation}
            </div>
          </div>
        </>
      )}
      {showHeader === 1 && (
        <CommonLiveOrVideoHeader
          url={liveUrl}
          background={background}
          onPress={() => {
            setShowHeader(0);
          }}
        />
      )}
    </div>
  );
};

export default CommonMatchHeader;
