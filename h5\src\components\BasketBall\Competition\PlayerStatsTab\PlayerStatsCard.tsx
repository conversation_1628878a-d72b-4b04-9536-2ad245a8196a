import Loading from "@/components/Loading";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd-mobile";
import { getScopeSelector, getCompetitionStatsPlayer } from "iscommon/api/basketball/basketball-competition";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { CompetitionScopeMap } from "iscommon/mobx/basketball/modules/competition";
import { inject, observer } from "mobx-react";
import { useState, useEffect, useMemo } from "react";
import { DownOutline } from 'antd-mobile-icons'
import './PlayerStatsCard.less'
import PlayerStatsTable from "./PlayerStatsTable";

interface Props {
  store?: any;
}

const PlayerStatsCard: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: {
        Basketball: {
          Competitions: {
            competitionId,
            currentSeason: { id: seasonId },
          },
        },
      },
    } = props;

    const labelMaps = useTranslateKeysToMaps([
      'Stats',
      'Player',
      'Preseason',
      'RegularSeason',
      'Playoffs',
      'Season',
      'Qualifier',
      'GroupStage',
    ]);

    const perOrAllList = [
      { id: '1', label: 'All', value: 'All'},
      { id: '2', label: 'Per Game', value: 'Per Game' },
    ];

    const [curScope, setCurScope] = useState<number>();
    const [scopeList, setScopeList] = useState<any>([]);
    const [tablesList, setTablesList] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [perOrAllId, setPerOrAllId] = useState<string>('');
    const [stageVisible, setStageVisible] = useState(false);
    const [gameVisible, setGameVisible] = useState(false)
    const [selectedScope, setSelectedScope] = useState<string | undefined>(undefined);
    const [selectedLabel, setSelectedLabel] = useState<string>(perOrAllList[0].label);

    useEffect(() => {
      if (competitionId && seasonId) {
        getScopeSelector({
          competitionId,
          seasonId,
        }).then(({ curScope, scopes }: any) => {
          setScopeList(scopes || []);
          setCurScope(curScope);
        });
      }
    }, [competitionId, seasonId]);

    useEffect(() => {
      if (competitionId && seasonId && curScope) {
        setLoading(true); 
        getCompetitionStatsPlayer({
          competitionId,
          seasonId,
          scope: curScope,
        }).then(({ list }: any) => {
          if (list.length > 0) {
            setTablesList(list);
          }
          setLoading(false);
        });
      }
    }, [competitionId, curScope, seasonId]);

    const ptablesList = useMemo(() => {
      if (perOrAllId === '2') {
        return tablesList.map((item) => {
          if (item.matches > 0) {
            return {
              ...item,
              minutesPlayed: item.minutesPlayed ? (item.minutesPlayed / item.court).toFixed(1) : '-',
              points: (item.points / item.court).toFixed(1),
              fieldGoalsScored: (item.fieldGoalsScored / item.court).toFixed(1),
              fieldGoalsTotal: (item.fieldGoalsTotal / item.court).toFixed(1),
              threePointsScored: (item.threePointsScored / item.court).toFixed(1),
              threePointsTotal: (item.threePointsTotal / item.court).toFixed(1),
              freeThrowsScored: (item.freeThrowsScored / item.court).toFixed(1),
              freeThrowsTotal: (item.freeThrowsTotal / item.court).toFixed(1),
              offensiveRebounds: (item.offensiveRebounds / item.court).toFixed(1),
              defensiveRebounds: (item.defensiveRebounds / item.court).toFixed(1),
              rebounds: (item.rebounds / item.court).toFixed(1),
              assists: (item.assists / item.court).toFixed(1),
              steals: (item.steals / item.court).toFixed(1),
              blocks: (item.blocks / item.court).toFixed(1),
              turnovers: (item.turnovers / item.court).toFixed(1),
              totalFouls: (item.totalFouls / item.court).toFixed(1),
            };
          } else {
            return item;
          }
        });
      } else {
        return tablesList;
      }
    }, [perOrAllId, tablesList]);

    useEffect(() => {
      if (scopeList.length > 0 && !curScope) {
        const defaultScope = scopeList[0]; 
        const defaultScopeLabel = labelMaps[CompetitionScopeMap[defaultScope]];
        setSelectedScope(defaultScopeLabel);
        setCurScope(defaultScope); 
      }
    }, [scopeList, curScope]);

    const handleScopeConfirm = (selectedScopeValue: any) => {
      const scopeLabel = labelMaps[CompetitionScopeMap[selectedScopeValue[0]]];
      setSelectedScope(scopeLabel); 
      setCurScope(selectedScopeValue[0]);
      setStageVisible(false); 
    };

    const handleGameConfirm = (value: any) => {
      const selectedItem = perOrAllList.find((item: any) => item.id === value[0]);
      if (selectedItem) {
        setPerOrAllId(selectedItem.id); 
        setSelectedLabel(selectedItem.label);
      }
      setGameVisible(false); 
    };

    return (
      <div className="basketball-player-stats-card-container">
        <div className="container-title">{labelMaps.Player}</div>
        <div className="container-body">
          <div className="picker-container">
            <>
              <Button className='custom-btn stage' size="small" onClick={() => setStageVisible(true)}>
                <span className='btn-content'>
                  <span className="btn-value">{selectedScope}</span>
                  <DownOutline className='down-icon'/>
                </span>
              </Button>
              <Picker
                columns={[
                  scopeList.map((item: any) => ({
                    key: item,
                    label: labelMaps[CompetitionScopeMap[item]],
                    value: item
                  }))
                ]}
                visible={stageVisible}
                onConfirm={handleScopeConfirm}
                onClose={() => setStageVisible(false)}
              />
            </>
            <>
              <Button className='custom-btn game' size="small" onClick={() => setGameVisible(true)}>
                <span className='btn-content'>
                  <span className="btn-value">{selectedLabel}</span>
                  <DownOutline className='down-icon'/>
                </span>
              </Button>
              <Picker
                columns={[
                  perOrAllList.map((item: any) => ({
                    key: item.id,
                    value: item.id,
                    label: item.label,
                  }))
                ]}
                visible={gameVisible}
                onConfirm={handleGameConfirm}
                onClose={() => setGameVisible(false)}
              />
            </>
          </div>
          <Loading loading={loading} isEmpty={tablesList.length === 0}>
            <PlayerStatsTable data={ptablesList}/>
          </Loading>
        </div>
      </div>
    )
  }),
);

export default PlayerStatsCard;