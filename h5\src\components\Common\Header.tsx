import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { history, Link } from 'umi';

import GlobalUtils, { GlobalConfig, MenuBarList } from 'iscommon/const/globalConfig';
import { translate, useTranslateKeysToMaps } from 'iscommon/i18n/utils';

const LanuagePopup = React.lazy(() => import('./LanuagePopup'));
const NavBarList = React.lazy(() => import('./NavBarList'));

import { inject, observer } from 'mobx-react';
import './Header.less';

import { Badge, Button, Space, Swiper } from 'antd-mobile';
import { getOddsStatus } from 'iscommon/api/header';

interface Props {
  defaultTitle?: string;
  hasBack?: boolean;
  isHome?: boolean;
  subTitle?: string;
}

interface TabItem {
  sportType: string;
  active: boolean;
  index: number;
  name: string;
  iconfont: string;
  pathname: string;
}

const Header: React.FC<Props> = inject('store')(
  observer((props: any) => {
    const {
      store: { WebConfig, WebHome },
    } = props;
    const { hasBack = true, subTitle = '', isHome = false } = props;
    const [lngValue, setLngValue] = useState<string>(GlobalConfig.lng);

    const {
      store: {
        WebConfig: { currentSportPathname, liveCounts },
      },
    } = props;

    const games = useMemo<TabItem[]>(() => GlobalUtils.getMenuBarList() as any, []);
    const labelMaps = useTranslateKeysToMaps(games, 'name');

    // ✅ Local WebConfig State
    const [localWebConfig, setLocalWebConfig] = useState(() => {
      const storedConfig = localStorage.getItem("WebConfig");
      return storedConfig ? JSON.parse(storedConfig) : WebConfig;
    });

    // ✅ Restore WebConfig from localStorage on mount
    useEffect(() => {
      const storedConfig = localStorage.getItem("WebConfig");
      if (storedConfig) {
        setLocalWebConfig(JSON.parse(storedConfig));
      }
    }, []);
        
    // ✅ Fetch WebConfig from API & update state
    useEffect(() => {
      getOddsStatus().then((displays: any) => {

        if (!displays) {
          return;
        }

        // ✅ Update WebConfig dynamically
        Object.entries(displays).forEach(([key, value]) => {
          const setterFunction = WebConfig[`set${key.charAt(0).toUpperCase() + key.slice(1)}Status`];
          if (setterFunction) {
            setterFunction.call(WebConfig, value);
          }
        });

        // ✅ Save WebConfig in local state and localStorage
        setLocalWebConfig(displays);
        localStorage.setItem("WebConfig", JSON.stringify(displays));
      });
    }, []);


    const formatSportKey = (sportType: string): string => {
      // 🔹 Explicit mappings for exceptions
      const sportKeyMappings: Record<string, string> = {
        AmFootball: "showAmfootball",
        TableTennis: "showTabletennis",
        IceHockey: "showIcehockey",
        WaterPolo: "showWaterpolo",
      };
    
      // 🔹 If sportType is in the mappings, return mapped value, otherwise generate dynamically
      return sportKeyMappings[sportType] || `show${sportType}`;
    };

    // ✅ Filter menu based on WebConfig values
    const filteredMenuTabList = useMemo(() => {
      return games.filter((item) => {
        const sportKey = formatSportKey(item.sportType);
        const isVisible = localWebConfig[sportKey];
    
        return isVisible === true;
      });
    }, [games, localWebConfig]);

    const changeGame = useCallback((item: TabItem) => {
      const path = item.pathname.startsWith('/') ? item.pathname : '/' + item.pathname;
      window.location.href = `${window.location.origin}${path}`;
    }, []);
    
    const normalizePath = (path: string = '') => path.replace(/^\/+/, '').toLowerCase();
    
    const isChecked = (item: TabItem) => normalizePath(location.pathname).includes(normalizePath(item.pathname));

    const getGameCount = (item: any) => {
      const filterCount = liveCounts.find(
        (l: any) => l?.sportType?.toLocaleLowerCase() === item?.sportType?.toLocaleLowerCase()
      );
      return filterCount ? filterCount : null;
    };

    const RenderLiveCount = (active: boolean, item: { liveCount: number; }) => {
      if (!item || item.liveCount <= 0) return null;
      return (
        <Badge content={item.liveCount} className='header-live-count-badge' />
      );
    };

    const isPolicyPage = location.pathname.includes('/policy');

    return (
      <>
        <div className="common-header-container">
          {hasBack && !isPolicyPage && (
            <div className="backArea" onClick={() => {history.go(-1);}}>
              <span className="icon iconfont fs-12 myIcon iconback"></span>
            </div>
          )}
          <a className="header-left-aligned" href="/">
            <img className="header-logo" src={require('iscommon/assets/logo/logo-v2.png')} alt="igscore" loading="lazy"/>
          </a>
        </div>

        {/* {isHome && ( */}
          <div className="header-navbar-swiper">
            <Swiper slideSize={100} trackOffset={0} defaultIndex={0} loop={false} indicator={false} className='custom-header-swiper'>
            {filteredMenuTabList.map((item: TabItem) => {
              return (
                <Swiper.Item key={item.sportType}>
                  <Button className={`header-sport-btn ${isChecked(item) ? 'active' : ''}`} onClick={() => changeGame(item)}>
                    <Space direction='vertical' align='center' className='swiper-space'>
                      <i className={`iconfont sport-icon ${item.iconfont}`} />
                      <span className="sport-text">
                        {labelMaps[item.name]}
                      </span>
                        {RenderLiveCount(isChecked(item), getGameCount(item))}
                    </Space>
                  </Button>
                </Swiper.Item>
              );
            })}
            </Swiper>
          </div>
        {/* )} */}
      </>
    );
  }),
);

export default Header;

// const RenderTitle = inject('store')(
//   observer((props: any) => {
//     const {
//       store: {
//         WebConfig: { currentSportPathname },
//       },
//       isHome,
//       subTitle,
//       setGameVisible,
//       setLngVisible,
//       lngValue,
//     } = props;

//     const title = MenuBarList.filter((item) => item.pathname === currentSportPathname)[0]?.name || '';
//     const titleText = translate(title);

//     const onClick = (e: React.MouseEvent<HTMLSpanElement>, eventName: 'visit_download_page') => {
//       window.gtag('event', eventName, { platform: 'mobile' });
//       window.gtag('event', 'target_to_download_app', { platform: 'mobile', target_app: 'all' });
//     };

//     if (isHome) {
//       return (
//         <>
//           <div className="left">
//             <img
//               width={213}
//               height={40}
//               className="logo"
//               src={require('iscommon/assets/logo/logo-v2.png')}
//               alt="igscore"
//             />
//           </div>
//           <div className="center" onClick={() => setGameVisible(true)}>
//             <span className="name">{titleText}</span>
//             <span className="icon iconfont iconxialajiantou myfont" />
//           </div>
//           <div className="right">
//             <span className="appdownload" style={{ display: 'flex' }}>
//               <a href="/download/">
//                 <span style={{ display: 'flex' }} onClick={(e) => onClick(e, 'visit_download_page')}>
//                   <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 384 512">
//                     <path
//                       fill="#f69422"
//                       d="M16 64C16 28.7 44.7 0 80 0H304c35.3 0 64 28.7 64 64V448c0 35.3-28.7 64-64 64H80c-35.3 0-64-28.7-64-64V64zM224 448a32 32 0 1 0 -64 0 32 32 0 1 0 64 0zM304 64H80V384H304V64z"
//                     />
//                   </svg>
//                 </span>
//               </a>
//             </span>
//             <span className="icon iconfont fs-14 myIcon iconyuyan1" onClick={() => setLngVisible(true)} />
//             <span className="fs-12 language-text" style={{ marginLeft: 5 }} onClick={() => setLngVisible(true)}>
//               {LanguageList.find((item) => item.id === lngValue)?.text || 'English'}
//             </span>
//           </div>
//         </>
//       );
//     }

//     return (
//       <>
//         <div className="center-title flex-column">
//           <span className="name">{titleText}</span>
//           <span className="subTitle">{subTitle}</span>
//         </div>
//         <div className="center-right"></div>
//       </>
//     );
//   }),
// );

// const Header: React.FC<Props> = (props) => {
//   const { hasBack = true, subTitle = '', isHome = false } = props;
//   const [lngVisible, setLngVisible] = useState<boolean>(false);
//   const [lngValue, setLngValue] = useState<string>(GlobalConfig.lng);
//   const [gameVisible, setGameVisible] = useState(false);

//   return (
//     <>
//       <header className="header">
//         {hasBack && (
//           <div
//             onClick={() => {
//               history.go(-1);
//             }}
//             className="backArea"
//           >
//             <span className="icon iconfont fs-12 myIcon iconback"></span>
//           </div>
//         )}
//         <RenderTitle
//           isHome={isHome}
//           subTitle={subTitle}
//           setGameVisible={setGameVisible}
//           setLngVisible={setLngVisible}
//           lngValue={lngValue}
//         />
//       </header>
//       {lngVisible ? <LanuagePopup onHide={() => setLngVisible(false)} /> : null}
//       {gameVisible ? <NavBarList setGameVisible={setGameVisible} /> : null}
//     </>
//   );
// };

// export default Header;
