import React from 'react';

import emptyImg from '@/assets/images/empty.png';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

import styles from './CommonEmpty.less';

const CommonEmpty = () => {
  const labelMaps = useTranslateKeysToMaps(['Nodata']);
  return (
    <div className={styles.empty}>
      <img src={emptyImg} alt="empty" />
      <div className="color-999 fs-12">{labelMaps.Nodata}</div>
    </div>
  );
};

export default React.memo(CommonEmpty);
