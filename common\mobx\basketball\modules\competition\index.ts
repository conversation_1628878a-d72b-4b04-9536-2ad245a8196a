// @ts-nocheck
import _ from 'lodash';
import { makeAutoObservable } from 'mobx';

export const CompetitionsTab = {
  overview: 'Overview',
  fixtures: 'Fixture & Results',
  standings: 'Standings',
  stats: 'Stats',
  teams: 'Teams',
};

export const CompetitionsTabH5 = {
  overview: 'Overview',
  fixtures: 'Fixture & Results',
  standings: 'Standings',
  stats: 'Stats',
  teams: 'Teams',
  playerStats: 'PlayerStats',
  teamStats: 'TeamStats',
};

export const CompetitionKindMap = {
  1: 'RegularSeason',
  2: 'Playoffs',
  3: 'Preseason',
  5: 'In-Season Tournament',
  6: 'Play-In Tournament',
};

export const CompetitionScopeMap = {
  1: 'Season',
  2: 'Qualifier',
  3: 'GroupStage',
  4: 'Preseason',
  5: 'RegularSeason',
  6: 'Playoffs',
};

const DefaultProps = {
  competitionId: 0,
  /**
   * interface SeasonData {
   *   id: string;
   *   competitionId: string;
   *   year: string;
   *   isCurrent: boolean; // Whether the latest season，1-Yes，0-No
   *   hasPlayerStats: boolean; // Is there any player statistics，1-Yes，0-No
   *   hasTeamStats: boolean; // Are there team statistics，1-Yes，0-No
   *   hasTable: boolean; // Is there a standings，1-Yes，0-No
   *   startTime: number;
   *   endTime: number;
   * }
   */
  currentSeason: {
    id: '',
    competitionId: '',
    year: '',
    hasPlayerStats: 0,
    hasTeamStats: 0,
    isCurrent: 0,
    hasTable: 0,
    startTime: 0,
    endTime: 0,
  },
  competitionName: '',
  seasons: [],
  commonInfo: {
    competition: {
      logo: '',
      positions: null,
      curRound: 0,
      roundCount: 0,
      // Competition type，0-unknown，1-league，2-cup，3-friendly
      // 赛事类型，0-未知、1-联赛、2-杯赛、3-友谊赛
      type: 0,
      /**
       * interface TitleHolderTeam {
       *   id: string,
       *   name: string,
       *   logo: string,
       *   shortName: string,
       *   marketValueCurrency: string,
       * }
       */
      country: {
        id: '',
        name: '',
        logo: '',
      },
      titleHolderTeam: {}, // TitleHolderTeam
      titleHolderChampions: 0,
      /**
       * interface MostTitlesTeam {
       *   id: string,
       *   name: string,
       *   logo: string,
       *   shortName: string,
       *   marketValueCurrency: string,
       * }
       */
      mostTitlesTeams: [], // MostTitlesTeam[]
      mostTitleChampions: 0,
      /**
       * interface ComerItem {
       *   id: string,
       *   name: string,
       *   logo: string,
       *   shortName: string,
       *   marketValueCurrency: string,
       * }
       */
      newComersFromHigherDivision: [], // ComerItem[]
      newComersFromLowerDivision: [], // ComerItem[]
      /**
       * interface DivisionItem {
       *   id: string,
       *   name: string,
       *   logo: string,
       *   countryId: string,
       *   categoryId: string,
       *   type: number,
       * }
       */
      upperDivision: [], // DivisionItem[]
      lowerDivision: [], // DivisionItem[]
      /**
       * interface ValuePlayer {
       *   id: string,
       *   teamId: string,
       *   name: string,
       *   shortName: string,
       *   marketValue: number,
       *   marketValueCurrency: string,
       *   positions: null,
       *   logo: string,
       *   countryId: string,
       *   categoryId: string,
       *   type: number,
       * }
       */
    },
    mostMarketValuePlayer: {}, // ValuePlayer
    /**
     * interface PlayerStats {
     *   totalPlayers: number,
     *   foreignPlayers: number,
     *   teamCount: number,
     *   nationalPlayers: number,
     *   marketValue: number,
     *   marketValueCurrency: string,
     *   redCards: number,
     *   yellowCards: number,
     * }
     */
    competitionPlayerStatsDto: {}, // PlayerStats
  },
};

class Competitions {
  constructor() {
    this.reset();
    makeAutoObservable(this);
  }

  reset() {
    for (const key in DefaultProps) {
      this[key] = _.cloneDeep(DefaultProps[key]);
    }
  }

  changeCompetitionId(competitionId) {
    this.competitionId = competitionId;
  }

  changeCurrentSeason(currentSeason) {
    if (currentSeason) {
      this.currentSeason = currentSeason;
    }
  }

  changeCompetitionName(name) {
    this.competitionName = name;
  }

  changeSeasons(list = []) {
    this.seasons = list;
  }

  changeCommonInfo(data) {
    this.commonInfo = data;
  }
}

export default Competitions;
