import { getCompetitionList, getUpComingList } from 'iscommon/api/ice-hockey/home';

import SmallBallGameListContainer from '@/components/SmallGameCommon/Home/GameListContainer';
import GameCategory from './GameCategory';

const GameListContainer = (props: any) => {
  return (
    <SmallBallGameListContainer
      {...props}
      renderCateGory={(item) => <GameCategory competition={item} />}
      getUpComingList={getUpComingList}
      getCompetitionList={getCompetitionList}
    />
  );
};

export default GameListContainer;
