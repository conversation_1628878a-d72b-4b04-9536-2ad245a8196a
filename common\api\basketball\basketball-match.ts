// @ts-nocheck
import igsRequest from "iscommon/request/instance";
import { getBasketBallTotalScore } from "../../utils/dataUtils";

/**
 * @param {{ matchId: string }}
 * @returns {any}
 */
export const getMatchHeader = async ({ matchId } = {}) => {
  try {
    return await igsRequest.post("match/info", { matchId });
  } catch (e) {
    console.log("getMatchHeader error:", e);
    return null;
  }
};

/**
 * @param {{ teamId: string; size: number }}
 * @returns {Promise<any>}
 */
export const getMatchRecentList = async ({ teamId, size = 30 } = {}) => {
  try {
    const result = await igsRequest.post("match/analysis/recent", { teamId, size });
    for(let v of result.matches) {
      v['calculatedAwayScore'] = getBasketBallTotalScore(v.awayScores)
      v['calculatedHomeScore'] = getBasketBallTotalScore(v.homeScores)
    }
    return result
  } catch (e) {
    console.log("getMatchRecentList error:", e);
    return null;
  }
};

/**
 * @param {{ homeTeamId: string; awayTeamId: string; size: number }}
 * @returns {any}
 */
export const getMatchHistoryList = async ({
  homeTeamId,
  awayTeamId,
  size = 30,
} = {}) => {
  try {
    const result = await igsRequest.post("match/analysis/history", {
      homeTeamId,
      awayTeamId,
      size,
    });
    for(let v of result.matches) {
      v['calculatedAwayScore'] = getBasketBallTotalScore(v.awayScores)
      v['calculatedHomeScore'] = getBasketBallTotalScore(v.homeScores)
    }
    return result
  } catch (e) {
    console.log("getMatchRecentList error:", e);
    return null;
  }
};

/**
 * @param {{ teamId: string; size: number }}
 * @returns {any}
 */
export const getMatchFutureList = async ({ teamId, size = 5 } = {}) => {
  try {
    return await igsRequest.post("match/analysis/future", { teamId, size });
  } catch (e) {
    console.log("getMatchRecentList error:", e);
    return null;
  }
};

export const getMatchStandings = ({ matchId, type } = {}) => {
  return igsRequest
    .post("match/standings", {
      matchId,
      type,
    })
    .then((result) => {
      // console.log(result);
      return result;
    });
};

export const getMatchOdds = ({ matchId } = {}) => {
  return igsRequest
    .post("match/odds", {
      matchId
    })
    .then((result) => {
      return result;
    });
};

export const getMatchTeamStats = ({ matchId } = {}) => {
  return igsRequest
    .post("match/stats/team", {
      matchId
    })
    .then((result) => {
      return result;
    });
};

export const getMatchStatistics = ({ matchId } = {}) => {
  return igsRequest
    .post("match/stats/realtime", {
      matchId
    })
    .then((result) => {
      return result;
    });
};

export const getMatchPlayerStatistics = ({ matchId } = {}) => {
  return igsRequest
    .post("match/stats/player", {
      matchId
    })
    .then((result) => {
      return result;
    });
};

export const getKeyPlayers = ({ matchId } = {}) => {
  return igsRequest
    .post("match/stats/keyPlayers", {
      matchId
    })
    .then((result) => {
      return result;
    });
};


