import { HomeGameTab } from "iscommon/const/constant";
import { inject, observer } from "mobx-react";
import { useState, useEffect } from "react";
import './GameFooter.less'

const GameFooter = inject('store')(
  observer((props: any) => {

    const [activeTab, setActiveTab] = useState(HomeGameTab.Live);
    const { propsTab } = props;
    const { WebHome } = props.store;
    
    useEffect(() => {
      if (propsTab) {
        WebHome.switchHomeGameTab(propsTab);
      }
    }, [propsTab, WebHome]);

    const handleTabSwitch = (tab: any) => {
      setActiveTab(tab);
      WebHome.switchHomeGameTab(tab); 
    };

    return (
      <div className='game-list-container-footer'>
      <div className={`footer-item ${activeTab === HomeGameTab.Live ? 'active' : ''}`} onClick={() => handleTabSwitch(HomeGameTab.Live)}>
        <img className="footer-match-btn" src={require('iscommon/assets/images/courtSport.png')} alt="matches" loading="lazy"/>
        <span className="footer-text">Matches</span>
      </div>
      {/* <div className={`footer-item ${activeTab === HomeGameTab.Favourite ? 'active' : ''}`} onClick={() => handleTabSwitch(HomeGameTab.Favourite)}>
        <img className="footer-favorite-btn" src={require('iscommon/assets/images/football/rating.png')} alt="favorite" />
        <span className="footer-text">Favorites</span>
      </div> */}
      <div className={`footer-item ${activeTab === HomeGameTab.Leagues ? 'active' : ''}`} onClick={() => handleTabSwitch(HomeGameTab.Leagues)}>
        <img className="footer-league-btn" src={require('iscommon/assets/images/league.png')} alt="league" loading="lazy"/>
        <span className="footer-text">Leagues</span>
      </div>
      </div>
    )
  })
);

export default GameFooter;