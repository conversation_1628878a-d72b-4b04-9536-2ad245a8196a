interface Props {
  isFallback?: boolean;
  htmlString?: string;
}

export default (props: Props) => {
  if (props.htmlString) {
    return <div dangerouslySetInnerHTML={{ __html: props.htmlString }}></div>;
  }
  // @ts-ignore
  const __html = props.isFallback || !window.appReady ? window.skeletonBodyString : window.skeletonContentString;
  return <div style={{ width: '100%', height: '1200px' }} dangerouslySetInnerHTML={{ __html: __html }}></div>;
};
