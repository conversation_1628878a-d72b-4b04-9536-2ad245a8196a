import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";
import { FallbackCategoryImage } from "iscommon/const/icon";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { inject, observer } from "mobx-react";
import React from "react";
import { Link } from "umi";
import './TeamInfoCard.less'

interface Props {
  store?: any;
}

interface InfoItemProps {
  label: string;
  value?: string | number;
  className?: string;
}

const InfoItem = React.memo<InfoItemProps>((props) => {
  const { label, value, className } = props;
  return (
    <div key={label} className={`info-item ${className || ''}`}>
      <div className='info-label'>{label}</div>
      <div className='info-text'>{!value && value !== 0 ? '-' : value}</div>
    </div>
  );
});

const TeamInfoCard: React.FC<Props> = inject('store')(
  observer((props: Props) => {
    const {
      store: {
        Basketball: {
          Team: { teamHeaderInfo },
        },
      },
    } = props;

    const labelMaps = useTranslateKeysToMaps(['TeamInfo', 'Leagues', 'Manager', 'TotalPlayers', 'AverageAge']);

    return (
      <div className='basketball-team-info-card-container'>
        <div className='container-title'>{labelMaps.TeamInfo}</div>
        <div className='container-body'>
          <InfoItem label={labelMaps['Manager']} value={teamHeaderInfo.manager} />
          <InfoItem label={labelMaps['TotalPlayers']} value={teamHeaderInfo.totalPlayers} />
          <InfoItem label={labelMaps['AverageAge']} value={teamHeaderInfo.avgAge} />
          <InfoItem label={labelMaps.Leagues} value=' ' className="no-border"/>
          <div className='league-container'>
            {teamHeaderInfo?.competitions?.map((item: any) => 
              <Link to={GlobalUtils.getPathname(PageTabs.competition, item.id)} key={item.id} className='competition-card'>
                <img className='competition-icon' src={item.logo || FallbackCategoryImage} alt={item.name} loading="lazy"/>
                <span className='competition-name'>{item.shortName}</span>
              </Link>
            )}
          </div>
        </div>
      </div>
    );
  }),
);

export default TeamInfoCard;