import { inject, observer } from 'mobx-react';

import LiveAndVideo from '@/components/Common/LiveAndVideo/LiveAndVideo';
import MatchInfo from '@/components/Match/OverView/MatchInfo';
import ScoreTable from './ScoreTable';
import SmallballOverViewOdds from '@/components/SmallGameCommon/Match/OverView/OverViewOdds';

const MatchLive = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Smallball: { SmallballCommon }
      },
    } = props;
    const { matchHeaderInfo, matchId } = SmallballCommon;

    console.log('badminton overview')

  return (
    <div>
      <LiveAndVideo matchHeaderInfo={matchHeaderInfo} matchId={matchId}>
        {/* <SingleMatchLine />  */}
      </LiveAndVideo>
      <ScoreTable />
      <MatchInfo />
      {/* <SingleMatchLine />*/}
      {/* <SmallballOverViewOdds overViewMaps={overViewMaps} /> */}
    </div>
  );
}));

export default MatchLive;
