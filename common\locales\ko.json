{"All": "전체", "Live": "라이브", "LiveH5": "라이브", "MatchLive": "라이브", "TimeSort": "시간순으로 정렬", "SortByTime": "시간순으로 정렬", "AllGames": "전 경기", "Leagues": "리그", "h5_Leagues": "리그", "Today": "오늘", "Cancel": "취소", "Popular": "인기", "Settings": "설정", "Language": "언어", "Overview": "개요", "LiveOverview": "개요", "Standings": "스코어보드", "Stats": "통계", "Transfer": "이체", "Champions": "우승", "TeamChampions": "우승", "teamChampions": "우승", "Football": "축구", "Basketball": "농구", "Baseball": "야구", "Icehockey": "하키", "Tennis": "테니스", "Volleyball": "배구", "Esports": "E 스포츠", "Handball": "핸드볼", "Cricket": "크리켓", "WaterPolo": "수구", "TableTennis": "탁구", "Snooker": "스누커", "Badminton": "배드민턴", "BusinessCooperation": "비즈니스 협력", "TermsOfService": "서비스 계약", "PrivacyPolicy": "개인정보 보호정책", "Players": "선수", "ForeignPlayers": "외국 선수", "NumberOfTeams": "팀 수", "YellowCards": "옐로우 카드", "RedCards": "레드 카드", "Capacity": "용납인", "City": "도시", "Info": "정보", "Matches": "레이스", "Team": "팀", "Teams": "팀", "Goals": "골인", "Assists": "어시스트", "assists": "어시스트", "Home": "홈경기", "Away": "원정경기", "topScorers": "상위 득점자", "TopScorers": "상위 득점자", "homeTopScorers": "상위 득점자", "season": "시즌", "Season": "시즌", "ShotsOnTarget": "슛온골", "Clearances": "킥", "Tackles": "스틸", "keyPasses": "관건적 패스", "KeyPasses": "관건적 패스", "Fouls": "반칙", "totalFouls": "반칙", "WasFouled": "침범됨", "Penalty": "페널티", "MinutesPlayed": "출전 시간", "BasketballMinutesPlayed": "출전 시간", "Interceptions": "차단", "Steals": "스틸", "steals": "스틸", "Passes": "통과", "Saves": "세이브", "BlockedShots": "슛 블록", "Signed": "계약", "league": "", "offensiveData": "공격 자료", "defenseData": "방어 수치", "otherData": "기타 수치", "ballPossession": "볼 점유율", "shotsPerGame": "경기당 평균 슈팅수량", "ShotsPerGame": "경기당 평균 슈팅수량", "keyPassesPerGame": "경기당 평균 관건적패스수량", "accurateLongBallsPerGame": "경기당 평균 성공적 장거리패스수량", "accurateCrossesPerGame": "경기당 평균 성공적 크로스수량", "tacklesPerGame": "경기당 평균 스틸수량", "TacklesPerGame": "경기당 평균 스틸수량", "interceptionsPerGame": "경기당 평균 차단수량", "InterceptionsPerGame": "경기당 평균 차단수량", "clearancesPerGame": "경기당 평균 킥수량", "ClearancesPerGame": "경기당 평균 킥수량", "blockedShotsPerGame": "경기당 평균 숏블록수량", "turnoversPerGame": "경기당 평균 실수", "foulsPerGame": "경기당 평균 반칙수", "scoringFrequencyFiveGoals": "", "Coach": "코치", "Goalkeeper": "골기퍼", "Stadium": "운동관", "Login": "로그인", "Corner": "코너킥", "ShotsOffTarget": "슛오프골", "H2H": "H2H", "Date": "날짜", "OwnGoal": "자살골", "PenaltyMissed": "놓친 헤딩슛", "SecondYellow": "두번째 옐로우카드", "Odds": "배당율", "attacks": "공격", "Started": "선발", "Chat": "채팅", "Strengths": "장점", "Weaknesses": "단점", "Group": "그룹", "Birthday": "생일", "Club": "축구단", "MainPosition": "주요직책", "PassesAccuracy": "전달의 정확성", "Preseason": "", "RegularSeason": "", "PointsPerGame": "경기당 평균득점", "Glossary": "설명", "h5Glossary": "설명", "Career": "경력", "Bench": "예비인원", "ReboundsPerGame": "경기당 평균리바운드수", "AssistsPerGame": "경기당 평균 어시스트수", "OddsFormat": "배당 형식", "Squad": "라인업", "TotalMarketValue": "시가총액", "Rounds": "라운드 수", "LowerDivision": "하위리그전", "TeamStats": "팀자료", "GoalsPk": "골인(PK)", "Crosses": "크로스", "CrossesAccuracy": "성공적으로 공을 패스", "Dribble": "이상한", "DribbleSucc": "놀라운 성공", "LongBalls": "장거리 패스", "LongBallsAccuracy": "롱 패스 성공률", "Duels": "일대일 잡는", "DuelsWon": "일대일 스크래핑 성공", "Dispossessed": "깨끗한 경력", "Punches": "복싱 볼", "RunsOut": "골기퍼 출전", "RunsOutSucc": "골키퍼 공격 성공", "GoodHighClaim": "액세스 높은 볼 성공", "Loan": "임대", "EndOfLoan": "임대 종료", "Unknown": "알 수 없음", "AverageAge": "평균 나이", "cornersPerGame": "경기당 평균코너킥수량", "goalsConceded": "골 먹음", "Defender": "풀백", "Discipline": "법칙성", "Pass": "", "FB_Login": "Facebook으로 로그인", "Google_Login": "Google로 로그인", "Substitutes": "교체 라인업", "PenaltyKick": "", "ShareYourViews": "관점을 공유", "Nodata": "데이터 없음", "Foot": "발싸이즈", "dangerousAttack": "", "venue": "운동관", "playerStatistics": "선수 자료", "TotalPlayed": "출전횟수", "MinutesPerGame": "경기당 평균 출전시간", "GoalsFrequency": "", "GoalsPerGame": "필드 골", "Arrivals": "전입", "Departures": "전출", "LeftFoot": "좌", "RightFoot": "우", "LatestTransfers": "최근이체", "DraftInfo": "드래프트", "OK": "오케이", "AsianHandicap": "", "TotalGoals": "", "AmFootballTotalGames": "", "TotalCorners": "", "OverBall": "위에", "Over": "위에", "h5Over": "위에", "UnderBall": "아래에", "Under": "아래에", "h5Under": "아래에", "OtherLeagues": "기타 리그[A-Z]", "GoalPopup": "", "FullStandings": "전체 순위", "teamWeek": "주 최고 라인업", "weekTop": "주 최고 라인업", "TeamOfTheWeek": "주 최고 라인업", "round": "라운드", "Released": "계약 해체", "Retirement": "", "Draft": "선발", "TransferIn": "", "TransferOut": "", "MarketValue": "시장가치", "Salary": "급여", "Next": "다음", "Position": "포지션", "CTR": "현재 이체팝업창 수익", "FoulBall": "", "FreeKick": "", "GoalKick": "", "Midfield": "", "HalftimeScore": "", "InjuryTime": "", "OvertimeIsOver": "", "PenaltyKickEnded": "", "Last": "마지막", "Win": "승리", "Draw": "무승부", "Lose": "패배", "Lineup": "", "Substitution": "선수교체", "Offsides": "옵사이드", "Playoffs": "", "Qualifier": "", "GroupStage": "", "Rebounds": "리바운드", "rebounds": "리바운드", "OffensiveRebounds": "공격 백보드", "offensiveRebounds": "공격 백보드", "DefensiveRebounds": "방어 백보드", "defensiveRebounds": "방어 백보드", "Turnovers": "실수", "turnovers": "실수", "Blocks": "슛블록", "blocks": "슛블록", "BoxScore": "선수 통계", "Foul": "반칙", "FreeThrows": "페널티 킥", "freeThrowsScored": "페널티 킥", "fieldGoalsScored": "", "fieldGoalsTotal": "", "threePointersScored": "", "threePointersTotal": "", "freeThrowsTotal": "", "Finished": "종료", "Scheduled": "비품", "Favourite": "", "OddsMarkets": "승률 시장", "Search": "", "Timezone": "", "SoccerGoalReminderSettings": "", "RemindersOnlyForRacesToWatch": "", "GoalSound": "", "LiveScore": "", "Schedule": "시간표", "Rugby": "", "FooterContentFootball": "IGScore Football LiveScore는 2600 개 이상의 축구 리그, 컵 및 토너먼트에서 비할 데없는 축구 라이브 스코어 및 축구 결과를 제공합니다. Premier League, La Liga, Serie A, Bundesliga, Ligue 1, Eredivisie, Russian Premier League, Brasileirão, MLS에서 실시간 점수, 하프 타임 및 풀 타임 축구 결과, 골 득점자 및 어시스턴트, 카드, 교체, 경기 통계 및 실시간 스트림을 확인하세요. igscore.net의 Super Lig 및 Championship. IG Score는 잉글랜드 프리미어 리그, 스페인 라 리가, 이탈리아 세리에 A, 독일과 같은 가장 인기있는 축구 리그뿐만 아니라 모든 축구 팬들에게 리그, 컵 및 토너먼트에 대한 실시간 스코어, 축구 라이브 스코어, 축구 스코어, 리그 테이블 및 비품을 제공합니다. 분데스리가, 프랑스 리그 1, 북미 및 남미, 아시아 및 아프리카를 포함한 전 세계의 다양한 축구 국가 출신. 우리의 축구 라이브 스코어 카드는 실시간으로 업데이트되어 모든 축구 및 축구 리그의 모든 완료된 축구 경기에 대한 축구 라이브 스코어 결과와 함께 오늘 발생하는 모든 축구 경기 라이브 스코어 업데이트를 최신 상태로 유지합니다. 경기 페이지에서 축구 스코어 카드를 통해 모든 축구 대회에서 이전에 플레이 한 모든 경기의 과거 경기 결과를 볼 수 있습니다. igscore.net에서 모든 축구 실시간 결과를 확인하세요!", "FooterContentBasketball": "IGScore Basketball LiveScore는 NBA 리그 실시간 스코어, 결과, 테이블, 통계, 일정, 순위 및 분기 별, 하프 타임 또는 최종 결과 별 이전 결과를 제공합니다. 리그, 발트 리그, 유로 리그, 국가 농구 리그) 라이브 스코어, 쿼터 결과, 최종 결과 및 라인업뿐만 아니라 2 점 및 3 점 시도, 자유투, 슈팅 비율, 리바운드, 매출, 도둑질, 개인 파울, 경기 기록 및 선수 통계.IGScore Basketball 라이브 스코어에서는 클릭하여 온라인으로 농구를 볼 수 있으며, 최고 리그 경기의 온라인 범위를 제공하며, 경기 페이지에는 팀의 최신 게임에 대한 모든 농구 통계 테이블이 있습니다. 우리의 농구 스코어 카드는 실시간으로 업데이트되어 현재 진행중인 모든 농구 결과를 최신 상태로 유지하고 모든 농구 경기에서 이전에 플레이 한 모든 경기 일정에 대한 과거 게임 결과를 볼 수 있습니다 IG Score에서 NBA 라이브 결과를 모두 얻으십시오 NBA 라이브 스코어, NBA 일정, NBA 순위 및 팀 페이지를 팔로우하십시오!", "FooterContentAmFootball": "IGScore american football live score 세계에서 가장 크고 인기있는 미식 풋볼 리그에서 모든 결과와 라이브 스코어를 제공하고 NFL 정규 NFL 시즌이 끝나면 NFL Playoffs 및 Superbowl의 라이브 스코어를 따르십시오. NFL 외에도 우리는 또한 NCAA College American Football과 Canadian CFL을위한 LiveScores, 결과, 순위 및 일정을 제공합니다.", "FooterContentBaseball": "IGScore baseball live score은(는) 세계에서 가장 인기 있는 야구 리그인 USSSA 야구, NCAA 야구, MLB 야구, MLB 올스타 게임의 실시간 점수, 결과, 순위를 제공합니다. 또한 일본 프로 리그, 멕시코 리그, 독일 1.분데스리가, NCAA 및 국제 야구 토너먼트 World Baseball Classic의 라이브 스코어를 제공합니다. 또한 언제든지 야구 리그 순위, 이닝별 결과가 있는 과거 경기 및 다음 야구 경기 일정을 IGScore baseball live score에서 볼 수 있습니다.", "FooterContentIcehockey": "IGScore ice hockey live score는 아이스하키 리그, 컵 및 토너먼트에 대한 실시간 아이스하키 결과 점수를 제공합니다. IGScore ice hockey live score는 NHL, SHL, KHL의 하키 라이브스코어, 테이블, 통계, 일정, 결과 및 점수를 제공하며 국가 핀란드 하키 리그, 스웨덴 하키 리그, 슬로바키아 하키 리그, 체코 하키 리그, 리그 테이블, 득점자, 3분의 1 및 최종 아이스하키 결과가 생중계됩니다. 아이스하키 정규 시즌이 끝난 후, 우리는 당신에게 하키 라이브 스코어, 순위, 최고의 아이스하키 이벤트(IIHF 세계 챔피언십 스탠리 컵)의 결과와 동계 올림픽 토너먼트의 하키 스코어를 제공합니다. IGScore ice hockey live score에서는 NHL, SHL 등을 위한 무료 하키 라이브 스트림도 찾을 수 있습니다.", "FooterContentTennis": "IGScore tennis live score은 Davis 및 Fed Cup, 프랑스 오픈 테니스 또는 모든 그랜드 슬램 토너먼트(호주 오픈 테니스, US 오픈 테니스, Roland)와 같은 모든 가장 큰 테니스 토너먼트의 라이브스코어, 결과, ATP 순위 및 WTA 순위, 일정 및 통계를 제공합니다. Garros와 Wimbledon은 남녀 단식 및 복식 경기입니다. 또한 모든 테니스 선수의 경우 개별적으로 플레이한 경기와 세트별 결과 및 해당 경기가 어떤 토너먼트에서 진행되었는지 자세히 볼 수 있습니다. IGScore tennis live score은(는) 경기를 하는 두 플레이어 간의 일대일 결과, 통계, 실시간 점수 및 실시간 스트림을 제공합니다.", "FooterContentVolleyball": "IGScore volleyball live score는 이탈리아 Serie A1 및 Italy Seria A1 Women, Russian Superliga, Polish PlusLiga, Turkey 1. Lig 및 기타 여러 국가를 포함한 모든 중요한 남녀 국내 배구 리그를 제공합니다. 국내 배구 리그 외에도 FIVB 세계 선수권 대회 및 유럽 선수권 대회와 같은 주요 배구 국제 토너먼트의 라이브스코어 정보와 올림픽 게임의 배구 라이브스코어 결과를 제공합니다. 또한 IGScore volleyball live score에서 좋아하는 배구 팀의 이전 결과를 확인하고 향후 배구 일정을 확인하고 리그 배구 순위를 확인할 수 있습니다.", "FooterContentEsports": "ESPORTS LIVE SCORES의 LIVE 점수는 ESPORTS 라이브 스코어, 일정, 결과 및 테이블을 제공합니다. 바로 여기에 가장 좋아하는 팀을 따라 가십시오! igscore.net 라이브 스코어의 ESPORTS LIVE 점수가 자동으로 업데이트되고 수동으로 새로 고침 할 필요가 없습니다. 경기와 같은 \"내 게임\"에 따라 게임을 추가하려는 게임을 추가하면 결과 및 통계가 더욱 간단합니다.", "FooterContentHandball": "IGScore handball live score은 독일 분데스리가, 스페인 리가 아소발, 덴마크 남자 핸드볼리가엔, 프랑스 D1과 같은 가장 인기 있는 핸드볼 리그의 핸드볼 실시간 스코어와 실시간 결과를 제공합니다. 우리는 또한 유럽 핸드볼 챔피언스 리그, SEHA 리그 및 EHF 핸드볼 컵과 같은 중요한 컵의 라이브스코어, 결과, 통계, 순위, 테이블 및 일정을 제공합니다. IGScore handball live score에서 여성과 남성 모두를 위한 유럽 선수권 대회 및 세계 선수권 대회와 같은 국제 팀 핸드볼 토너먼트의 라이브스코어와 무료 라이브 스트림을 찾을 수 있습니다. 언제든지 자신의 팀이 플레이한 최근 10경기의 핸드볼 결과 및 통계를 확인할 수 있으며, 통계로 플레이할 예정인 팀 간의 일대일 스코어도 확인할 수 있습니다.", "FooterContentCricket": "IGScore cricket live score에서는 실시간 크리켓 결과, 크리켓 순위 및 크리켓 경기를 팔로우할 수 있습니다. 이 모든 것은 가장 인기 있는 리그와 컵에서 사용할 수 있습니다: 인디언 프리미어 리그, 챔피언스 리그 2020, 빅 배시 리그, 캐리비안 프리미어 리그, 프렌즈 라이프 T20, ICC 크리켓 월드컵. IG Score의 모든 크리켓 점수는 자동으로 업데이트되며 수동으로 새로 고칠 필요가 없습니다. 이 모든 것을 통해 무료 크리켓 라이브 스트림을 시청하고 전 세계에서 가장 흥미로운 크리켓 경기의 최종 결과에 대한 최신 배당률을 확인할 수 있는 옵션이 있습니다.", "FooterContentWaterPolo": "IGScore water polo live score은(는) 클럽 수준에서 이탈리아 세리에 A1, 헝가리 OB1, 챔피언 및 아드리아 해 리그의 수구 실시간 점수 및 결과를 제공하고, 국제 수준에서는 IGScore water polo live score에서 수구 세계 선수권 대회 및 유럽 수구 선수권 대회와 같은 주요 토너먼트를 제공합니다. . 목표 라이브스코어 및 무료 라이브 스트리밍으로 목표를 제공합니다.", "FooterContentTableTennis": "IGScore table tennis live score는 러시아 탁구, 탁구 올림픽과 같은 모든 가장 큰 탁구 토너먼트의 라이브스코어, 테이블, 결과, 탁구 순위, 일정 및 통계를 제공합니다. 또한 모든 탁구 선수에 대해 개별적으로 플레이한 경기와 세트별 결과 및 해당 경기가 진행된 토너먼트를 자세히 볼 수 있습니다. IGScore table tennis live score는 경기를 하는 두 플레이어 간의 일대일 결과, 통계, 실시간 점수 및 실시간 스트림을 제공합니다.", "FooterContentSnooker": "IGScore snooker live score은(는) 모든 스누커 토너먼트의 실시간 점수, 결과 및 순위를 확인할 수 있는 기능을 제공합니다. 또한 영국 및 세계 선수권 대회의 라이브스코어와 스누커 라이브스코어, 스누커 경기 일정 및 World Snooker 투어와 같은 국제 토너먼트의 최종 스누커 결과를 제공합니다. 언제든지 모든 선수에 대한 진행 예정인 스누커 토너먼트 일정, 과거 스누커 토너먼트 결과 및 최근 10경기를 볼 수 있습니다. 또한 플레이어 간의 과거 일대일 경기를 확인할 수 있습니다. IGScore snooker live score에서 무료 스누커 라이브 스트림이 포함된 경기 목록을 찾을 수 있습니다.", "FooterContentBadminton": "IGScore badminton live score은(는) 세계 선수권 대회, BWF 슈퍼 시리즈 및 올림픽 게임의 배드민턴 결과와 같은 국제 토너먼트의 배드민턴 실시간 결과, 순위, 일정 및 통계를 제공합니다. 또한 IGScore badminton live score에서 배드민턴 경기 결과를 확인하고 배드민턴 경기 일정과 선수별 배드민턴 경기 결과를 확인할 수 있습니다.", "ContactUs": "문의하기", "UpdateLineups": "Update Lineups", "FreeLiveScoresWidget": "Free Livescores Widget", "PlayerSalary": "", "DivisionLevel": "이벤트 수준", "Foreigners": "외국인 선수의 수", "LeagueInfo": "토너먼트", "TeamInfo": "팀 정보", "Venues": "", "MostValuablePlayer": "", "UpperDivision": "상위리그전", "NewcomersFromUpperDivision": "", "NewcomersFromLowerDivision": "", "TitleHolder": "디펜딩 챔피언", "MostTitle": "수를 승리까지", "Pts": "PTS", "FullStats": "", "Relegation": "", "Result": "결과", "Score": "점수", "PlayerStats": "", "fixtures": "", "topPlayers": "주요 선수", "Shots": "촬영", "SelectTeam": "", "SelectBasketBallTeam": "", "SelectAll": "전부 선택", "SquadSize": "플레이어 수", "ViewAll": "전체 보기", "penaltiesWon": "페널티킥 획득", "accuracyCrosses": "", "totalShorts": "", "accuracyLongBalls": "", "blockShots": "", "MatchesToday": "오늘 경기", "Strikers": "앞으로", "Midfielders": "미드 필더", "Year": "", "Loans": "", "TopValuablePlayers": "", "total": "", "Total": "", "Results": "", "Prev": "", "Apps": "형세", "ShotsPg": "평균화 샷", "Possession": "", "TotalAndAve": "", "Suspended": "부상 정보", "injuredOrSuspended": "부상 정보", "Since": "인저리 타임", "Overall": "Overall", "Age": "나이", "LastMatchFormations": "마지막 게임 라인업", "Formation": "", "GoalDistribution": "목표 배포", "Favorite": "", "FoundedIn": "년에 설립되었습니다", "LocalPlayers": "네이티브 선수", "ShowNext": "다음 경기 표시", "HideNext": "다음 경기 접기", "FIFAWorldRanking": "FIFA 세계랭킹", "Register": "", "OP": "1X2", "Handicap": "", "h5Handicap": "", "TennisHandicap": "", "OU": "O/U", "CardUpgradeConfirmed": "카드 업그레이드 확인", "VAR": "", "LatestMatches": "최근 경기", "ScheduledMatches": "", "Only": "", "LeagueFilter": "", "WinProb": "", "ScoreHalf": "", "Animation": "", "FigureLegends": "", "YellowCard": "옐로우 카드", "RedCard": "레드 카드", "Chatroom": "채팅룸", "Send": "전송", "Logout": "", "CurrentLeague": "", "ShirtNumber": "", "ContractUntil": "계약 만료", "PlayerInfo": "PLAYER INFO", "Height": "높이", "Weight": "몸무게", "PlayerValue": "사회적 지위", "View": "", "Time": "시각", "onTarget": "", "offTarget": "", "Played": "", "Rating": "", "Attacking": "진공", "Creativity": "창의성", "Defending": "방어", "Tactical": "전술의식", "Technical": "기술", "Other": "", "Cards": "페널티 카드", "AccuratePerGame": "정확한 전달", "AccLongBalls": "준 장거리 패스로", "AccCrosses": "정확한 전기", "SuccDribbles": "놀라운 성공", "TotalDuelsWon": "일대일 스크래핑 성공", "YellowRedCards": "", "AiScoreRatings": "", "Pergame": "", "Player": "", "Upcoming": "", "FreeKicks": "", "Corners": "코너킥", "DaysUntil": "일 남음", "In": "교체", "Out": "교체", "NoStrengths": "큰 장점이 없음", "NoWeaknesses": "큰 단점이 없음", "UpcomingMatches": "", "Pos": "", "Ht(cm)": "", "wt(kg)": "", "Exp": "EXP", "College": "", "Salary/Year": "", "Manager": "", "TotalPlayers": "플레이어 수", "Form": "", "Points": "득점", "GamesPlayed": "", "WinPercentage": "", "FieldGoalsMade": "", "FieldGoalsAttempted": "", "FieldGoalPercentage": "", "threePointFieldGoalsMade": "", "threePointFieldGoalsAttempted": "", "threePointFieldGoalsPercentage": "", "FieldGoaldsMade": "", "FreeThrowsAttempted": "", "FreeThrowPercentage": "", "BlockedFieldGoalAttempts": "", "PersonalFouls": "", "PersonalFoulsDrawn": "", "Efficiency": "", "PlusMinus": "", "Atlantic": "", "Central": "", "Southeast": "", "Northwest": "", "Pacific": "", "Southwest": "", "EasternConference": "", "WesternConference": "", "ToWin": "", "Spread": "전파", "AmFootballHand": "전파", "TotalPoints": "", "TotalPoint": "", "TableTennisTotalGames": "", "Wins": "승리", "Losses": "실리", "WinningPercentage": "", "GamesBack": "", "HomeRecord": "", "AwayRecord": "", "DivisionRecord": "", "ConferenceRecord": "", "OpponentPointsPerGame": "", "AveragePointDifferential": "", "CurrentStreak": "", "RecordLast5Games": "", "Match": "경기", "OnTheCourt": "현장에서", "Starters": "선발", "FieldGoals": "필드 골", "Timeout": "", "OpeningOdds": "", "PreMatchOdds": "", "PointPerGame": "", "PointsAllowed": "", "StartingLineups": "선발 라인업", "HandicapGames": "", "TennisHand": "", "TotalGames": "", "TennisTotalGames": "", "Defensive": "", "Offensive": "", "Points(PerGame)": "", "Rebounds(PerGame)": "", "Other(PerGame)": "기타 (평균)", "Attists": "", "FirstServe": "", "FirstServePoints": "", "BreakPoints": "", "Aces": "서브 직접득점", "DoubleFaults": "더블 실수", "Winner": "Winner", "HandicapSets": "", "TableTennisHand": "", "MoneyLine": "", "TableTennisHandicapGames": "", "HandicapRuns": "", "BaseballHand": "", "TotalRuns": "", "BaseballTotalGames": "", "DIFF": "", "BasketPergame": "", "HandicapPoints": "", "HandicapFrames": "", "TotalFrames": "", "SeeAll": ""}