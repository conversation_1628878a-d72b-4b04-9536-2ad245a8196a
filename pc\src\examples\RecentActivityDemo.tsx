import React from 'react';
import { <PERSON><PERSON>, Card, Space, Typography, Divider } from 'antd';
import { useRecentActivity } from '../hooks/useRecentActivity';
import RecentActivity from '../components/Common/RecentActivity';

const { Title, Text } = Typography;

const RecentActivityDemo: React.FC = () => {
  const { trackTeamClick, trackMatchClick } = useRecentActivity();

  // Simulate clicking on a football match
  const simulateFootballMatchClick = () => {
    const matchData = {
      matchId: 'match_001',
      homeTeam: { 
        id: 'team_001',
        name: 'Manchester United', 
        logo: 'https://logos.com/man-utd.png' 
      },
      awayTeam: { 
        id: 'team_002',
        name: 'Liverpool', 
        logo: 'https://logos.com/liverpool.png' 
      },
      competitionName: 'Premier League',
      matchTime: Date.now() / 1000
    };

    // Track the match
    trackMatchClick({
      id: matchData.matchId,
      homeTeam: matchData.homeTeam,
      awayTeam: matchData.awayTeam,
      competitionName: matchData.competitionName,
      matchTime: matchData.matchTime
    });

    // Track both teams individually
    trackTeamClick({
      id: matchData.homeTeam.id,
      name: matchData.homeTeam.name,
      logo: matchData.homeTeam.logo,
      competitionName: matchData.competitionName
    });

    trackTeamClick({
      id: matchData.awayTeam.id,
      name: matchData.awayTeam.name,
      logo: matchData.awayTeam.logo,
      competitionName: matchData.competitionName
    });
  };

  // Simulate clicking on a basketball match
  const simulateBasketballMatchClick = () => {
    const matchData = {
      matchId: 'match_002',
      homeTeam: { 
        id: 'team_003',
        name: 'Los Angeles Lakers', 
        logo: 'https://logos.com/lakers.png' 
      },
      awayTeam: { 
        id: 'team_004',
        name: 'Boston Celtics', 
        logo: 'https://logos.com/celtics.png' 
      },
      competitionName: 'NBA',
      matchTime: Date.now() / 1000
    };

    // Track the match
    trackMatchClick({
      id: matchData.matchId,
      homeTeam: matchData.homeTeam,
      awayTeam: matchData.awayTeam,
      competitionName: matchData.competitionName,
      matchTime: matchData.matchTime
    });

    // Track both teams individually
    trackTeamClick({
      id: matchData.homeTeam.id,
      name: matchData.homeTeam.name,
      logo: matchData.homeTeam.logo,
      competitionName: matchData.competitionName
    });

    trackTeamClick({
      id: matchData.awayTeam.id,
      name: matchData.awayTeam.name,
      logo: matchData.awayTeam.logo,
      competitionName: matchData.competitionName
    });
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>Recent Activity Demo</Title>
      <Text type="secondary">
        This demonstrates how clicking on matches in GameCategory will store team data in recent activity.
      </Text>

      <div style={{ display: 'flex', gap: '20px', marginTop: '20px' }}>
        {/* Left side - Demo buttons */}
        <div style={{ flex: 1 }}>
          <Card title="Simulate Match Clicks" style={{ height: 'fit-content' }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button 
                type="primary" 
                onClick={simulateFootballMatchClick}
                style={{ width: '100%' }}
              >
                🏈 Click Football Match (Man Utd vs Liverpool)
              </Button>
              
              <Button 
                type="primary" 
                onClick={simulateBasketballMatchClick}
                style={{ width: '100%' }}
              >
                🏀 Click Basketball Match (Lakers vs Celtics)
              </Button>

              <Divider />

              <Text strong>What happens when you click a match:</Text>
              <ul style={{ marginLeft: '20px', marginTop: '10px' }}>
                <li>✅ The match gets tracked as one item</li>
                <li>✅ Both teams get tracked as separate items</li>
                <li>✅ All items appear in Recent Activity</li>
                <li>✅ Data persists in localStorage</li>
                <li>✅ Maximum 10 items are kept</li>
              </ul>
            </Space>
          </Card>
        </div>

        {/* Right side - Recent Activity display */}
        <div style={{ flex: 1 }}>
          <RecentActivity 
            maxItems={10} 
            showClearButton={true}
          />
        </div>
      </div>

      <Card title="Integration Status" style={{ marginTop: '20px' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Text strong>✅ Integrated Components:</Text>
          <ul style={{ marginLeft: '20px' }}>
            <li>✅ <code>pc/src/components/Home/GameCategory.tsx</code> - Football matches</li>
            <li>✅ <code>pc/src/components/Basketball/Home/GameCategory.tsx</code> - Basketball matches</li>
            <li>✅ <code>pc/src/components/Home/LeftGames.tsx</code> - Recent Activity display</li>
            <li>✅ <code>pc/src/pages/Plugin/pluginHomeShared.tsx</code> - Plugin matches</li>
          </ul>

          <Text strong>🎯 How it works in GameCategory:</Text>
          <ol style={{ marginLeft: '20px' }}>
            <li>User clicks on any match in the home page</li>
            <li>The <code>handleMatchClick</code> function is triggered</li>
            <li>It tracks the match using <code>trackMatchClick()</code></li>
            <li>It tracks both teams using <code>trackTeamClick()</code></li>
            <li>Data is stored in localStorage</li>
            <li>Recent Activity component updates automatically</li>
          </ol>

          <Text strong>💾 Storage Details:</Text>
          <ul style={{ marginLeft: '20px' }}>
            <li>📍 <strong>Location:</strong> localStorage['user_recent_activity']</li>
            <li>📊 <strong>Limit:</strong> 10 most recent items</li>
            <li>🔄 <strong>Updates:</strong> Real-time across browser tabs</li>
            <li>🗑️ <strong>Cleared:</strong> When user clears browser data/cookies</li>
          </ul>
        </Space>
      </Card>
    </div>
  );
};

export default RecentActivityDemo;
