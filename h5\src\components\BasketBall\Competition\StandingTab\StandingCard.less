.basketball-standing-card-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .container-body {
    background: #1e1e1e;
    padding: 10px;

    .custom-common-standing-table {
      width: 100%;
      display: flex;
      flex-direction: column;
      border-collapse: collapse;
    
      .header-row, .table-row {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #2c2c2c;
        text-align: left;
        color: #fff;
        align-items: center;
      }
    
      .header-row {
        background-color: #1E1E1E;
        font-weight: bold;
      }
    
      .table-row {
        background-color: #121212;
      }
    
      .header-cell, .table-cell {
        flex: 0 0 60px;
        text-align: center;
        padding: 10px;
        font-size: 24px;
        align-content: center;
      }
    
      .team-cell {
        display: flex;
        align-items: center;
        flex: 0 0 300px;
        text-align: left;
        overflow: hidden;
    
        .team-logo {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin: 5px 10px 5px 0px;
          overflow: hidden;
          background-size: cover;
        }
    
        .team-name {
          font-size: 24px;
          color: #fff;
          text-overflow: ellipsis;
        }
      }
    }

    .custom-collapse {
      .adm-list-default, 
      .adm-list-body,
      .adm-list-item,
      .adm-list-item-content {
        background: transparent;
        border: none;
      }

      .adm-list {
        --border-inner: none;
      }

      .adm-list-item-content {
        padding-right: 0px;
      }

      .panel-header {
        display: flex;
        align-items: center;

        .panel-title {
          font-size: 22px;
          font-weight: bold;
          color: #fff;
          text-overflow: ellipsis;
        }
      }
    }
  }
}