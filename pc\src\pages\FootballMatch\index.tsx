import { inject, observer } from 'mobx-react';
import { subscribeMatchData, unsubscribeMatchData } from 'iscommon/mqtt/football/match';
import { useEffect, useMemo, useState } from 'react';

import { getMatchHeader } from 'iscommon/api/match';
import H2HTab from '@/components/Match/H2HTab';
import LineupTab from '@/components/Match/LineupTab';
import MatchHeader from '@/components/PageHeader/MatchHeader';
import MatchLive from '@/components/Match/Live';
import MatchOddsTab from '@/components/Match/OddsTab';
import { MatchTab } from 'iscommon/mobx/modules/match';
import PageBreadcrumb from '@/components/Common/PageBreadcrumb';
import PageHeader from '@/components/PageHeader';
import { PageTabs } from 'iscommon/const/globalConfig';
import StandingsTab from '@/components/Match/StandingsTab';
import { StatusCodeEnum } from 'iscommon/const/constant';
import styles from './index.less';
import TabList from '@/components/TabList/Index';
import { useCrumbList } from 'iscommon/hooks/content';
import { useMatchTimer } from 'iscommon/hooks/apiData';
import { useParams } from 'umi';

const FootballMatch = inject('store')(
  observer((props: any) => {
    const {
      store: { WebConfig, Match },
    } = props;
    const { serverTime, matchHeaderInfo } = Match;
    const { categoryId: matchId, tabId } = useParams<{ categoryId: string; tabId: string }>();
    const [showLineup, setShowLineup] = useState(false);
    const matchName = useMemo(
      () => `${matchHeaderInfo?.homeTeam?.name} vs ${matchHeaderInfo?.awayTeam?.name}, ${tabId.toLowerCase()}`,
      [matchHeaderInfo?.awayTeam?.name, matchHeaderInfo?.homeTeam?.name, tabId],
    );
    const { crumbs } = useCrumbList(PageTabs.match, '', matchName, [], {
      competition: {
        id: matchHeaderInfo?.competition?.id,
        name: matchHeaderInfo?.competition?.name,
        // target: '_blank',
      },
    });

    const lineupTab = { key: MatchTab.lineup, label: MatchTab.lineup, children: <LineupTab /> };
    const oddsTab = { key: MatchTab.odds, label: MatchTab.odds, children: <MatchOddsTab /> };

    const Tabs = useMemo(() => [
      { key: MatchTab.live,  label: matchHeaderInfo.statusId === 8 ? MatchTab.overview : MatchTab.live, children: <MatchLive /> },
      { key: MatchTab.h2h, label: MatchTab.h2h, children: <H2HTab /> },
      null,
      { key: MatchTab.standings, label: MatchTab.standings, children: <StandingsTab /> },
      null,
    ], [matchHeaderInfo.statusId]);

    const tabList: any[] = useMemo(() => {
      // const tabs = Tabs.map(tab => ({ ...tab }));
      let tabs = [...Tabs];
      if (showLineup) {
        tabs.splice(1, 0, lineupTab);
      }
      if (WebConfig.showOdds) {
        tabs.splice(2, 0, oddsTab);
      }
      return tabs.filter(Boolean);
    }, [WebConfig.showOdds, showLineup, Tabs]);

    // update server time every 60s
    useMatchTimer(serverTime, Match);

    // useMount(() => {
    //   window.gtag('event', 'pv', { platform: 'web', sport: 'football', page_type: 'match', page_id: matchId });
    //   // window.gtag('event', 'pv_football', { platform: 'web', football_page_type: 'match', page_id: matchId });
    //   Match.changeMatchId(matchId || '');
    //   getMatchHeader({ matchId }).then((res: any) => {
    //     if (res) {
    //       Match.changeMatchHeaderInfo({
    //         ...res,
    //         matchId,
    //       });
    //       setShowLineup(res.lineup === 1);
    //     }
    //   });
    // });
    useEffect(() => {
      if (!matchId) return;
    
      window.gtag('event', 'pv', { platform: 'web', sport: 'football', page_type: 'match', page_id: matchId });
      Match.changeMatchId(matchId);
    
      getMatchHeader({ matchId }).then((res: any) => {
        if (res) {
          Match.changeMatchHeaderInfo({
            ...res,
            matchId,
          });
          setShowLineup(res.lineup === 1);
        }
      });
    }, [matchId]);

    useEffect(() => {
      if (matchHeaderInfo.statusId !== -1 && matchHeaderInfo.statusId < StatusCodeEnum.End) {
        // 进行中的需要推送
        subscribeMatchData(matchId);
        return () => {
          unsubscribeMatchData(matchId);
        };
      }
      else if (matchHeaderInfo.statusId === 8) {
        // Match just ended: re-fetch match header info to update the overview tab
        getMatchHeader({ matchId }).then((res: any) => {
          if (res) {
            Match.changeMatchHeaderInfo({
              ...res,
              matchId,
            });
          }
        });
      }
    }, [matchHeaderInfo.statusId, matchId]);

    return (
      <div className={styles.container}>
        <PageBreadcrumb crumbs={crumbs} />
        <PageHeader>
          <MatchHeader />
        </PageHeader>
        {tabList.length > 0 && <TabList datasource={tabList} centered defaultTabId={MatchTab.live} />}
      </div>
    );
  }),
);

export default FootballMatch;
