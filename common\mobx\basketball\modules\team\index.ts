// @ts-nocheck
import { makeAutoObservable } from "mobx";

export const TeamTab = {
  overview: "Overview",
  stats: 'Stats',
  squad: "Squad",
};

export const TeamTabH5 = {
  overview: "Overview",
  stats: 'Stats',
  squad: "Squad",
};

export default class Team {
  constructor() {
    this.teamId = 1;
    this.teamHeaderInfo = {
      logo: "",
      name: "",
      countryId: "",
      totalPlayers: 0,
      foreignPlayers: 0,
      avgAge: 0,
      marketValue: 0,
      marketValueCurrency: "€",
      playerSalary: 0,
      /**
       * {
       *         id: "",
       *         name: "",
       *         logo: "",
       *         countryId: "",
       *         categoryId: "",
       *         type: 0,
       *       }
       */
      competitions: [],
      country: {
        id: "",
        name: "",
        logo: "",
      },
      currentSeason: {
        id: "",
        year: "",
      }
    };
    this.teamSeason = []
    makeAutoObservable(this);
  }

  changeTeamId(teamId) {
    this.teamId = teamId;
  }

  changeTeamHeaderInfo(data) {
    this.teamHeaderInfo = data;
  }

  changeTeamSeason(data) {
    this.teamSeason = data;
  }
}
