import './GameCategory.less';

import { CategoryIcon, FallbackPlayerImage } from 'iscommon/const/icon';
import { getBasketBallScoreList, getHomeBasketBallMatchTimeText } from 'iscommon/utils/dataUtils';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { Image, Typography } from 'antd';
import { inject, observer } from 'mobx-react';
import React, { useCallback, useMemo } from 'react';

import _ from 'lodash';
import { BasketBallStatusCodeEnum } from 'iscommon/const/basketball/constant';
import classNames from 'classnames';
import Empty from '@/components/Common/Empty';
import FavoriteIcon from '@/components/Common/FavoriteIcon';
import { Link } from 'umi';
import MatchLiveIcon from '@/components/Common/MatchLiveIcon';
import { momentTimeZone } from 'iscommon/utils';
import OddsTdValue from './OddsTdValue';
import { translate } from 'iscommon/i18n/utils';
import { useRecentActivity } from '../../../hooks/useRecentActivity';

const {Text} = Typography;

const PureMatchStatusText = React.memo(({ serverTime, matchStatus, remainSeconds, currentGameTab }: any) => {
  const { statusText, isIng } = useMemo(() => getHomeBasketBallMatchTimeText({ matchStatus, remainSeconds, serverTime, currentGameTab }),
    [matchStatus, remainSeconds, serverTime, currentGameTab],
  );

  return (
    <span className={classNames('match-status', { 'live': isIng, 'neutral': statusText === '-' })}>
      {statusText}
      <span className="match-status-text">
        {isIng ? "'" : ''}
      </span>
    </span>
  );
});

const MatchStatusText = inject('store')(
  observer(({ match, store }: any) => {
    const { matchStatus, remainSeconds } = match;
    const { Basketball: { WebHome: { serverTime } } } = store;
    return (
      <PureMatchStatusText
        serverTime={serverTime}
        matchStatus={matchStatus}
        remainSeconds={remainSeconds}
      />
    );
  })
);

// oddsTR
const OddsTr = () => {
  return (
    <div className="odds-title">
      <div className="tw">{translate('ToWin')}</div>
      <div className="sp">{translate('Spread')}</div>
      <div className="tp">{translate('TotalPoints')}</div>
    </div>
  );
};

const GameCompetitionTab: React.FC<any> = inject('store')(
  observer(({ competition, store }) => {
    const { competitionName, country, category, onlineCount, additionalCompetitionName } = competition;
    const { WebConfig } = store;

    return (
      <div className="game-category-basketball">
        <div className='game-category-left-aligned'>
          <FavoriteIcon isMatch={false} competition={competition} />
          {country && (
            <>
              <Image className='team-logo' src={country.logo} preview={false}/>
              <Text className='team-name'>{country.name}:&nbsp;</Text>
            </>
          )}
          {category && !country && (
            <>
              <Image className='team-logo' src={(CategoryIcon as any)[category.logo]} preview={false}/>
              <Text className='team-name'>{category.name}:&nbsp;</Text>
            </>
          )}
          <Text className='team-name'>{competitionName} &nbsp;</Text>
          {additionalCompetitionName && <Text className='team-name'>{additionalCompetitionName}</Text>}
        </div>
        <div className='game-category-right-aligned'>
          {onlineCount > 0 && (
            <div className="score">
              {onlineCount >= 9999 ? (
                <img className="hot" src={require('../../../../../common/assets/images/hot.png')} alt='hot' loading='lazy'/>
              ) : (
                <i className="iconfont icon-renshu"></i>
              )}
              {onlineCount >= 9999 ? '9999+' : onlineCount}
            </div>
          )}
          {WebConfig.showOdds && <OddsTr />}
        </div>
      </div>
    );
  }),
);

interface ScoreItem {
  h: number;
  w: number;
  isRed: boolean;
  compareStatus: number; // 1 home > away 2 home < away 0 =
}

const ScoreItem = ({ item, isTotal = false }: { item: any; isTotal?: boolean }) => {  
  const getColor = (status: number) => {
    if (item.isRed) return '#c1272d';
    if (status === 1 || status === 2) return '#fff';
    return '#fff';
  };

  return (
    <div className="score-item">
      <div className={classNames({ 'score-total': isTotal })} style={{ color: getColor(item.compareStatus === 1 ? 1 : 0) }}>{item.h}</div>
      <div className={classNames({ 'score-total': isTotal })} style={{ color: getColor(item.compareStatus === 2 ? 2 : 0) }}>{item.w}</div>
    </div>
  );
};

const ScoreText = ({ item }: any) => {
  const { matchStatus, homeScores, awayScores } = item;

  if (matchStatus === BasketBallStatusCodeEnum.NotStarted) {
    return <div className="score-summary">-</div>;
  }

  const scoreList = getBasketBallScoreList(matchStatus, homeScores, awayScores);

  return (
    <div className="score-summary">
      {scoreList.map((score: any, index: number) => (
        <ScoreItem key={index} item={score} isTotal={index === 0} />
      ))}
    </div>
  );
};

const MatchItem = React.memo(({ item, onMatchClick }: any) => {
  const path = useMemo(() => GlobalUtils.getPathname(PageTabs.match, item.matchId), [item.matchId]);
  const matchTime = useMemo(() => momentTimeZone(item.matchTime, 'HH:mm'), [item.matchTime]);

  const handleClick = () => {
    if (onMatchClick) {
      onMatchClick(item);
    }
  };

  return (
    <Link to={path} className="list-item" onClick={handleClick}>
      <div>
        <FavoriteIcon match={item} isMatch />
        <span className="time">{matchTime}</span>
        <MatchStatusText match={item} />
      </div>
      <div className="team-section">
        <div className="team-row">
          <img className="team-logo" src={item.homeTeam?.logo || FallbackPlayerImage} alt={item.homeTeam?.name}/>
          <span className="team-name">{item.homeTeam?.name}</span>
        </div>
        <div className="team-row">
          <img className="team-logo" src={item.awayTeam?.logo || FallbackPlayerImage} alt={item.awayTeam?.name}/>
          <span className="team-name">{item.awayTeam?.name}</span>
        </div>
      </div>
      <ScoreText item={item} />
      <div className="match-footer">
        <div className="live-icon-wrap">
          <MatchLiveIcon vlive={item.vlive} mlive={item.mlive} />
        </div>
        <OddsTdValue matchId={item.matchId} />
      </div>
    </Link>
  );
}, _.isEqual);

// 首页比赛列表，不包括 赛事头
export const HomeMatchList: React.FC<{ matches: any[], onMatchClick?: (item: any) => void }> = React.memo(({ matches, onMatchClick }) => {
  return matches.length === 0 ? <Empty /> : (
    <>
      {matches.map((match) => <MatchItem key={match.matchId} item={match} onMatchClick={onMatchClick} />)}
    </>
  );
}, _.isEqual);

const GameCategory: React.FC<{ competition: any }> = React.memo(({ competition }) => {
  const { trackTeamClick, trackMatchClick } = useRecentActivity();

  const handleMatchClick = useCallback((item: any) => {
    // Track the match itself
    trackMatchClick({
      id: item.matchId,
      homeTeam: {
        name: item.homeTeam?.name || 'Home Team',
        logo: item.homeTeam?.logo
      },
      awayTeam: {
        name: item.awayTeam?.name || 'Away Team',
        logo: item.awayTeam?.logo
      },
      competitionName: competition.name || competition.competitionName,
      matchTime: item.matchTime
    });

    // Track both teams individually so they appear as separate items in recent activity
    if (item.homeTeam?.id && item.homeTeam?.name) {
      trackTeamClick({
        id: item.homeTeam.id,
        name: item.homeTeam.name,
        logo: item.homeTeam.logo,
        competitionName: competition.name || competition.competitionName
      });
    }

    if (item.awayTeam?.id && item.awayTeam?.name) {
      trackTeamClick({
        id: item.awayTeam.id,
        name: item.awayTeam.name,
        logo: item.awayTeam.logo,
        competitionName: competition.name || competition.competitionName
      });
    }
  }, [trackTeamClick, trackMatchClick, competition]);

  if (!competition.matches?.length) return null;

  return (
    <>
      <GameCompetitionTab competition={competition} />
      <HomeMatchList matches={competition.matches} onMatchClick={handleMatchClick} />
    </>
  );
});

export default GameCategory;
