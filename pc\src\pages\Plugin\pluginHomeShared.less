.plugin-container {
  display: flex;
  flex-direction: row;
  padding: 20px 16px;
  background: #1a1a1a;

  // Right content for full mode (with filters)
  .right-content {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding: 0px 20px;

    .filter-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding: 12px;
      background: transparent;
      border: 1px solid #63717a;
      border-radius: 12px;
      max-width: 100%;
      overflow: visible;

      .button-group {
        display: flex;
        gap: 12px;

        .ant-btn {
          padding: 8px 20px;
          height: auto;
          border-radius: 8px;
          font-weight: 600;
          transition: all 0.3s ease;

          &:hover {
            opacity: 0.8;
          }
        }
      }

      .custom-select-plugin {
        width: 170px;
        margin: 0px 5px;

        .ant-select-selector {
          background: transparent !important;
          border-radius: 8px !important;
          color: #fff !important;
        }

        .ant-select-arrow .anticon {
          color: #fff !important;
        }
      }
    }
  }

  // Carousel content for both modes
  .carousel-content,
  .carousel-only-mode {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding: 0px 20px;
  }

  // Shared carousel styles for both carousel-content and carousel-only-mode
  .carousel-content,
  .carousel-only-mode {
    .ant-carousel {
      width: 100%;
      cursor: grab;
      border: 1px solid #63717a;
      padding: 12px 12px 0px 12px;
      border-radius: 12px;

      &:active {
        cursor: grabbing;
      }

      .slick-list {
        overflow: hidden;
      }
    }

    .ant-carousel .slick-list .slick-slide {
      pointer-events: auto;
    }

    .carousel-column {
      margin-right: 12px;
    }
  }
}

.ant-select-dropdown {
  background: #383838;
  border-radius: 16px;

  .ant-select-item-option-selected {
    background-color: lighten(#383838, 20%) !important;
    color: #fff
  }

  .ant-select-item-option-active {
    background-color: lighten(#383838, 15%) !important;
    color: #fff
  }
}

// Carousel-only mode styles for video context
.plugin-container {
  // When in carousel-only mode
  .carousel-only-mode {
    width: 100%;
    padding: 0;

    // Override some styles for video context
    .ant-carousel {
      width: 100%;
      margin-top: 0;
      border: none;
      padding: 0;
      background: transparent;
    }
  }

  // Shared carousel styles for both modes
  .carousel-content {
    width: 100%;
  }
}

// Container modifier for video context
.plugin-container.video-mode {
  padding: 0;
  background: transparent;
}
