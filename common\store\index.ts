// @ts-nocheck
export const saveStore = (key = '', data = '') => {
  localStorage.setItem(key, JSON.stringify(data));
};

export const getStore = (key = '', defaultValue = null) => {
  try {
    const res = JSON.parse(window.localStorage.getItem(key));
    return res || defaultValue;
  } catch (e) {
    return defaultValue;
  }
};

/* 生成组件随机id */
export const generateUuid = (prefix, length) => {
  const uuidStr = 'xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx';
  const len = !length ? uuidStr.length : length;
  let date = Date.now();
  const uuid = uuidStr.replace(/[xy]/g, (c) => {
    const r = (date + Math.random() * 16) % 16 | 0; // eslint-disable-line no-bitwise
    date = Math.floor(date / 16);
    return (c === 'x' ? r : (r & 0x7) | 0x8).toString(16); // eslint-disable-line no-bitwise
  });
  return `${!prefix ? '' : prefix}${uuid.slice(0, len)}`;
};

export const getOneLocalUniqueValue = (key, len = 0) => {
  const val = getStore(key, null);
  if (val) {
    return val;
  } else {
    const newVal = generateUuid(key, len);
    saveStore(key, newVal);
    return newVal;
  }
};
