.container {
  display: flex;
  width: 702px;
  margin: 0 auto 5px;
  padding: 8px 0;
  border-bottom: 1px solid #ddd;

  &:last-child {
    margin-bottom: 0;
    border-bottom: none;
  }

  .left {
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    width: 200px;
    padding-right: 10px;

    .leftText {
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      height: 24px;
      font-size: 24px;
      
      font-weight: 500;
      color: #999999;
      line-height: 1;

      &:first-child {
        margin-bottom: 14px;
      }
    }
  }

  .teamWrap {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    max-width: 50%;
  }

  .score {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 60px;

    .scoreText {
      height: 30px;
      font-size: 24px;
      font-weight: 500;
      color: #333333;
      line-height: 30px;
      font-family: Roboto-Medium !important;
    }
  }

  .result {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100px;

    .resultIcon {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      overflow: hidden;
      color: #fff;

      &.W {
        background-color: #52af2f;
      }

      &.D {
        background-color: #ffba5a;
      }

      &.L {
        background-color: #e74c5b;
      }
    }
  }
}

.team {
  display: flex;
  align-items: center;

  &:first-child {
    margin-bottom: 6px;
  }

  .logo {
    width: 32px;
    height: 32px;
    margin-right: 14px;
  }

  .teamName {
    flex: 1;
    font-size: 24px;
    
    color: #333333;
    line-height: 24px;
    padding-right: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
