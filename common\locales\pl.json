{"All": "Wszystkie", "Live": "NA ŻYWO", "LiveH5": "NA ŻYWO", "MatchLive": "NA ŻYWO", "TimeSort": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SortByTime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "AllGames": "WSZYSTKIE MECZE", "Leagues": "LIGI", "h5_Leagues": "LIGI", "Today": "D<PERSON>ś", "Cancel": "<PERSON><PERSON><PERSON>", "Popular": "<PERSON><PERSON>", "Settings": "Ustawienia", "Language": "Język", "Overview": "Przegląd", "LiveOverview": "Przegląd", "Standings": "<PERSON><PERSON><PERSON>", "Stats": "Stat.", "Transfer": "Transfer", "Champions": "Zwycięzcy", "TeamChampions": "Zwycięzcy", "teamChampions": "Zwycięzcy", "Football": "PIŁKA NOŻNA", "Basketball": "KOSZYKÓWKA", "Baseball": "Baseball", "Icehockey": "Hokej", "Tennis": "TENIS", "Volleyball": "SIATKÓWKA", "Esports": "ESPORTS", "Handball": "GRA W PIŁKĘ RĘCZNĄ", "Cricket": "KRYKIET", "WaterPolo": "WODNE POLO", "TableTennis": "<PERSON><PERSON>", "Snooker": "SNOOKER", "Badminton": "BADMINTON", "BusinessCooperation": "Business Cooperation", "TermsOfService": "Warunki korzystania z usługi", "PrivacyPolicy": "Polityka p<PERSON>watności", "Players": "ZAWODNICY", "ForeignPlayers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NumberOfTeams": "Liczba drużyn", "YellowCards": "Żółte kartki", "RedCards": "Czerwone kartki", "Capacity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mi<PERSON>", "City": "<PERSON><PERSON>", "Info": "Info", "Matches": "MECZE", "Team": "Zespół", "Teams": "Dr<PERSON><PERSON><PERSON><PERSON>", "Goals": "<PERSON><PERSON>", "Assists": "<PERSON><PERSON><PERSON>", "assists": "<PERSON><PERSON><PERSON>", "Home": "Dom", "Away": "Wyjazd", "topScorers": "Najleps<PERSON> str<PERSON>", "TopScorers": "Najleps<PERSON> str<PERSON>", "homeTopScorers": "Najleps<PERSON> str<PERSON>", "season": "Sezon", "Season": "Sezon", "ShotsOnTarget": "Strzały celne", "Clearances": "Wybicia", "Tackles": "Próby odbioru", "keyPasses": "Kluczowe podania", "KeyPasses": "Kluczowe podania", "Fouls": "<PERSON><PERSON><PERSON>", "totalFouls": "<PERSON><PERSON><PERSON>", "WasFouled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Penalty": "<PERSON><PERSON><PERSON> karny", "MinutesPlayed": "Rozegrane minuty", "BasketballMinutesPlayed": "Rozegrane minuty", "Interceptions": "Przechwy<PERSON>", "Steals": "Przechwy<PERSON>", "steals": "Przechwy<PERSON>", "Passes": "Przechodzący", "Saves": "Interwencje", "BlockedShots": "Zablokowane strzały", "Signed": "Zakontraktowany", "league": "", "offensiveData": "Dane <PERSON>ensywy", "defenseData": "Date obrony", "otherData": "<PERSON><PERSON> dane", "ballPossession": "Posiadanie piłki", "shotsPerGame": "Strzałów na mecz", "ShotsPerGame": "Strzałów na mecz", "keyPassesPerGame": "Kluczowe podania na mecz", "accurateLongBallsPerGame": "Acc. long balls per game", "accurateCrossesPerGame": "Acc. crosses per game", "tacklesPerGame": "Próby odbioru na mecz", "TacklesPerGame": "Próby odbioru na mecz", "interceptionsPerGame": "Przechwyty na mecz", "InterceptionsPerGame": "Przechwyty na mecz", "clearancesPerGame": "Interwencje na mecz", "ClearancesPerGame": "Interwencje na mecz", "blockedShotsPerGame": "Zablokowane strzały na mecz", "turnoversPerGame": "Straty na mecz", "foulsPerGame": "Faule na mecz", "scoringFrequencyFiveGoals": "", "Coach": "<PERSON><PERSON><PERSON>", "Goalkeeper": "<PERSON><PERSON><PERSON>", "Stadium": "Stadion", "Login": "<PERSON><PERSON><PERSON><PERSON>", "Corner": "<PERSON><PERSON><PERSON> r<PERSON>", "ShotsOffTarget": "Strzały niecelne", "H2H": "H2H", "Date": "Data", "OwnGoal": "<PERSON><PERSON>", "PenaltyMissed": "<PERSON><PERSON><PERSON> niewykorzystany", "SecondYellow": "Druga zółta", "Odds": "<PERSON><PERSON><PERSON>", "attacks": "<PERSON><PERSON>", "Started": "Od 1. minuty", "Chat": "<PERSON><PERSON><PERSON>", "Strengths": "<PERSON><PERSON><PERSON> strony", "Weaknesses": "<PERSON><PERSON><PERSON> strony", "Group": "Grupa", "Birthday": "Urod<PERSON>y", "Club": "Klub", "MainPosition": "Główna pozycja", "PassesAccuracy": "", "Preseason": "", "RegularSeason": "", "PointsPerGame": "Punktów na mecz", "Glossary": "Z<PERSON><PERSON><PERSON>", "h5Glossary": "Z<PERSON><PERSON><PERSON>", "Career": "<PERSON><PERSON><PERSON>", "Bench": "<PERSON> ławce", "ReboundsPerGame": "Zbiórki na mecz", "AssistsPerGame": "Asysty na mecz", "OddsFormat": "Format kursów", "Squad": "Składy", "TotalMarketValue": "Całkowita wartość rynkowa", "Rounds": "Liczba kolejek", "LowerDivision": "Niższa liga", "TeamStats": "Stat. Drużyny", "GoalsPk": "<PERSON><PERSON>(PK)", "Crosses": "K<PERSON><PERSON>", "CrossesAccuracy": "Podać piłkę powodzeniem", "Dribble": "Niezwykły", "DribbleSucc": "Niezwykły sukces", "LongBalls": "Długie podania", "LongBallsAccuracy": "Wskaźnik sukcesu długi upływ", "Duels": "<PERSON><PERSON><PERSON>", "DuelsWon": "Czyste prześcieradło", "Dispossessed": "Interwencje", "Punches": "<PERSON><PERSON><PERSON> atak sukces", "RunsOut": "Wyjścia z bramki", "RunsOutSucc": "Zablokowane strzały", "GoodHighClaim": "Przechodząc dokładność", "Loan": "Wypożyczenie", "EndOfLoan": "Koniec wypożyczenia", "Unknown": "<PERSON><PERSON><PERSON><PERSON>", "AverageAge": "<PERSON><PERSON><PERSON> wiek", "cornersPerGame": "Rożne na mecz", "goalsConceded": "Wpuszczone bramki", "Defender": "<PERSON><PERSON>ń<PERSON>", "Discipline": "Dyscyplina", "Pass": "", "FB_Login": "Kontynuuj za pośrednictwem Facebook", "Google_Login": "Kontynuuj za pośrednictwem Google", "Substitutes": "Rezerwa", "PenaltyKick": "", "ShareYourViews": "Podziel się swoją opinią", "Nodata": "<PERSON><PERSON> da<PERSON>", "Foot": "Stopa", "dangerousAttack": "", "venue": "Obiekt", "playerStatistics": "Statystyki zawodników", "TotalPlayed": "Rozegrane łącznie", "MinutesPerGame": "Minut na mecz", "GoalsFrequency": "", "GoalsPerGame": "<PERSON><PERSON>", "Arrivals": "Do<PERSON><PERSON><PERSON><PERSON><PERSON>", "Departures": "<PERSON><PERSON><PERSON><PERSON>", "LeftFoot": "<PERSON><PERSON>", "RightFoot": "Dobrze", "LatestTransfers": "Ostatnie transfery", "DraftInfo": "Informacje o projekcie", "OK": "OK", "AsianHandicap": "", "TotalGoals": "", "AmFootballTotalGames": "", "TotalCorners": "", "OverBall": "Powyżej", "Over": "Powyżej", "h5Over": "Powyżej", "UnderBall": "Poniżej", "Under": "Poniżej", "h5Under": "Poniżej", "OtherLeagues": "<PERSON><PERSON> ligi [A-Z]", "GoalPopup": "", "FullStandings": "Pełna klasyfikacja", "teamWeek": "Drużyna tygodnia", "weekTop": "Drużyna tygodnia", "TeamOfTheWeek": "Drużyna tygodnia", "round": "<PERSON><PERSON><PERSON><PERSON>", "Released": "Sprzedany", "Retirement": "", "Draft": "Draft", "TransferIn": "", "TransferOut": "", "MarketValue": "Wartość rynkowa", "Salary": "Wynagrodzenie", "Next": "Kolejny", "Position": "<PERSON><PERSON><PERSON>ja", "CTR": "Obecny transfer", "FoulBall": "", "FreeKick": "", "GoalKick": "", "Midfield": "", "HalftimeScore": "", "InjuryTime": "", "OvertimeIsOver": "", "PenaltyKickEnded": "", "Last": "Ostatnie", "Win": "<PERSON><PERSON><PERSON><PERSON>", "Draw": "<PERSON><PERSON><PERSON>", "Lose": "Porażki", "Lineup": "", "Substitution": "Zmian<PERSON>", "Offsides": "<PERSON><PERSON>", "Playoffs": "", "Qualifier": "", "GroupStage": "", "Rebounds": "Zbiórki", "rebounds": "Zbiórki", "OffensiveRebounds": "Zbiórka piłki w ataku", "offensiveRebounds": "Zbiórka piłki w ataku", "DefensiveRebounds": "Zbiórka piłki w obronie", "defensiveRebounds": "Zbiórka piłki w obronie", "Turnovers": "<PERSON><PERSON><PERSON>", "turnovers": "<PERSON><PERSON><PERSON>", "Blocks": "Bloki", "blocks": "Bloki", "BoxScore": "Składy", "Foul": "<PERSON><PERSON>", "FreeThrows": "<PERSON><PERSON><PERSON> wolne", "freeThrowsScored": "<PERSON><PERSON><PERSON> wolne", "fieldGoalsScored": "", "fieldGoalsTotal": "", "threePointersScored": "", "threePointersTotal": "", "freeThrowsTotal": "", "Finished": "Zakończony", "Scheduled": "Oprawy", "Favourite": "", "OddsMarkets": "<PERSON><PERSON><PERSON>", "Search": "", "Timezone": "", "SoccerGoalReminderSettings": "", "RemindersOnlyForRacesToWatch": "", "GoalSound": "", "LiveScore": "", "Schedule": "Harmonogram", "Rugby": "", "FooterContentFootball": "IGScore Football LiveScore zapewnia niezrównane wyniki piłkarskie na żywo oraz wyniki z ponad 2600 lig piłkarskich, pucharów i turniejów. Otrzymuj wyniki na żywo, wyniki połowy i całego meczu piłki nożnej, strzelców bramek i asystentów, kartki, zmiany, statystyki meczów i transmisje na żywo z Premier League, La Liga, Serie A, Bundesligi, Ligue 1, Eredivisie, Russian Premier League, Brasileirão, MLS, Super Liga i Mistrzostwa na igscore.net. IGScore oferuje wszystkim fanom piłki nożnej wyniki na żywo, wyniki na żywo z piłki nożnej, wyniki piłkarskie, tabele ligowe i mecze lig, pucharów i turniejów, a nie tylko z najpopularniejszych lig piłkarskich, takich jak Anglia Premier League, Hiszpania La Liga, Włochy Serie A, Niemcy Bundesliga, Francja Ligue 1, ale także z wielu krajów piłkarskich z całego świata, w tym z Ameryki Północnej i Południowej, Azji i Afryki. Nasze statystyki wyników na żywo z piłki nożnej są aktualizowane na żywo w czasie rzeczywistym, dzięki czemu jesteś na bieżąco ze wszystkimi aktualizacjami wyników meczów piłkarskich, które mają miejsce dzisiaj, wraz z wynikami piłkarskimi na żywo ze wszystkich zakończonych meczów piłkarskich dla każdej ligi piłkarskiej i piłkarskiej. Na stronie meczów, nasze karty wyników piłkarskich pozwalają na przeglądanie wyników poprzednich meczów dla wszystkich wcześniej rozegranych spotkań dla wszystkich rozgrywek piłkarskich. Zobacz wszystkie swoje wyniki piłkarskie na żywo na igscore.net!", "FooterContentBasketball": "IGScore Basketball LiveScore zapewnia wyniki na żywo ligi NBA, wyniki, tabele, statystyki, terminarze, tabele i poprzednie wyniki według kwartałów, wyników po pierwszej połowie lub końcowych. IGScore oferuje serwis wyników z ponad 200 zawodów koszykówki z całego świata (takich jak NCAA, ABA League, liga bałtycka, Euroleague, krajowe ligi koszykówki). Znajdziesz tutaj nie tylko wyniki na żywo, wyniki kwartalne, wyniki końcowe i składy, ale także liczbę prób 2- i 3-punktowych, r<PERSON>ty wolne, procent str<PERSON>, zbiórki, obroty, kradzieże, faule osobiste, historię meczów i statystyki zawodników .Na stronie IGScore Basketball wyniki na żywo możesz oglądać koszykówkę online, klikając ją, i zapewnia ona relacje online z meczów najlepszych lig. Na stronie meczowej będzie także tabela ze wszystkimi statystykami koszykówki na temat ostatnich meczów drużyn. nasze karty wyników koszykówki są aktualizowane na bieżąco w czasie rzeczywistym, abyś był na bieżąco z wszystkimi wynikami koszykówki, które mają miejsce dzisiaj, i pozwala ci zobaczyć wyniki poprzednich meczów dla wszystkich poprzednio rozgrywanych spotkań dla wszystkich rozgrywek koszykówki. Uzyskaj wszystkie wyniki na żywo NBA na igscore.net! Śledź wyniki na żywo NBA, mecze NBA, tabele NBA i strony drużyn!", "FooterContentAmFootball": "IGScore american football live score zapewnia wszystkie wyniki i wyniki na żywo z największej i najbardziej popularnej ligi futbolu amerykańskiego na świecie - NFL i kiedy skończy się regularny sezon NFL, postępuj zgodnie z wynikami na żywo Playoffs NFL i Superbowl. Oprócz NFL Zapewniamy również wyniki na żywo, wyniki, stoisko i harmonogramy dla NCAA College American Football i Canadian CFL.", "FooterContentBaseball": "IGScore baseball live score zapewnia wyniki na żywo, wyniki, tabele z najpopularniejszej światowej ligi baseballowej - USSSA Baseball, NCAA Baseball, MLB Baseball, gra MLB Allstar. Zapewniamy również wyniki na żywo dla ligi <PERSON>, ligi <PERSON>, niemieckiej 1.Bundesliga, NCAA oraz międzynarodowego turnieju baseballowego World Baseball Classic. Możesz także zobaczyć w dowolnym momencie tabele ligi baseballowej, poprzednie mecze z wynikami według inningów i harmonogramem nadchodzących meczów baseballowych na IGScore baseball live score.", "FooterContentIcehockey": "IGScore ice hockey live score zapewnia wyniki w czasie rzeczywistym dla hokejowych lig, pucharów i turniejów. IGScore ice hockey live score zapewnia wyniki hokejowe na żywo, tabele, statystyki, terminarze, wyniki i wyniki z NHL, SHL, KHL, a także zapewniamy krajowe ligi hokejowe w Finlandii, szwedzkie ligi hokejowe, słowacki<PERSON> ligi hokejowe, czeskie ligi hokejowe, tabele ligowe, str<PERSON><PERSON> bramek, tercje i końcowe wyniki w hokeju na lodzie na żywo. Po zakończeniu sezonu zasadniczego hokejowego oferujemy wyniki hokejowe na żywo, tabele i wyniki najważniejszych imprez hokejowych - Mistrzostwa Świata IIHF Stanley Cup, a także wyniki hokejowe z zimowych turniejów olimpijskich. Na IGScore ice hockey live score można również znaleźć bezpłatne hokejowe transmisje na żywo dla NHL, SHL i innych.", "FooterContentTennis": "IGScore tennis live score zapewnia wyniki na żywo, wyniki, rankingi ATP i rankingi WTA, terminarze i statystyki ze wszystkich najwięks<PERSON><PERSON> turniejów tenisowych, takich jak <PERSON> i <PERSON> Cup, French Open Tenis lub dla w<PERSON><PERSON><PERSON><PERSON><PERSON> turn<PERSON> w<PERSON> – Australian Open Tennis, US Open Tennis, <PERSON> i Wimbledon zarówno dla kobiet, jak i mężczyzn dla singli i deblów. Również dla każdego tenisisty możesz zobaczyć szczegóły jego rozegranych pojedynków i ich wyniki według seta oraz turnieju, w którym rozgrywany był ten mecz. IGScore tennis live score zapewnia bezpośrednie wyniki, statystyki, wyniki na żywo i transmisję na żywo między dwoma graczami, którzy grają mecz.", "FooterContentVolleyball": "IGScore volleyball live score oferuje relacje ze wszystkich ważnych krajowych lig siatkówki mężczyzn i kobiet, w tym włoskiej Serie A1 i włoskiej Seria A1 Kobiet, r<PERSON><PERSON><PERSON><PERSON>ligi, polskiej PlusLigi, Turkey 1. Lig i wielu innych. Oprócz krajowych lig siatkówki dostarczamy również informacje o wynikach na żywo z najważniejszych międzynarodowych turniejów siatkówki, takich jak Mistrzostwa Świata i Mistrzostwa Europy FIVB, a także wyniki siatkówki na żywo z igrzysk olimpijskich. Możesz również sprawdzić stare wyniki swojej ulubionej drużyny siatkówki, zobaczyć przyszłe harmonogramy siatkówki i sprawdzić ligową tabelę siatkówki na IGScore volleyball live score.", "FooterContentEsports": "Esports Live Service Service w IGScore Wynik na żywo oferuje wyniki eSports na żywo, harmonogramy, wyniki i tabele. Śledź swoje ulubione drużyny tutaj żyć! Wynik na żywo eSports na igscore.net Wynik na żywo jest automatycznie aktualizowany i nie musisz odświeżać go ręcznie. <PERSON>zię<PERSON> do<PERSON>wan<PERSON> gier, które chcesz śledzić w \"Moje gry\" podążając za swoimi meczami wyniki na żywo, wyniki i statystyki będą jeszcze prostsze.", "FooterContentHandball": "IGScore handball live score zapewnia wyniki na żywo piłki ręcznej i wyniki na żywo z najpopularniejszych lig piłki ręcznej, takich jak niemiecka Bundesliga, hiszpańska Liga Asobal, duńska Handboldligaen i francuska D1. Dostarczamy również wyniki na żywo, wyniki, statystyki, tabele, tabele i terminarze z ważnych pucharów, takich jak Europejska Liga Mistrzów w piłce ręcznej, liga SEHA i Puchar EHF w piłce ręcznej. Na IGScore handball live score znajdziesz wyniki na żywo i bezpłatne transmisje na żywo z międzynarodowych drużynowych turniejów piłki ręcznej, takich jak Mistrzostwa Europy i Mistrzostwa Świata, zarówno kobiet, jak i mężczyzn. W dowolnym momencie możesz sprawdzić wyniki piłki ręcznej i statystyki ostatnich 10 meczów rozegranych przez Twoją drużynę, a także wyniki bezpośrednie pomiędzy drużynami, które mają grać ze statystykami.", "FooterContentCricket": "IGScore cricket live score umożliwia śledzenie wyników krykieta w czasie rzeczywistym, klasyfikacji krykieta i rozgrywek krykieta. <PERSON><PERSON><PERSON>tko to jest dostępne dla najpopularniejszych lig i pucharów: Indian Premier League, Champions League Twenty20, Big Bash League, Caribbean Premier League, Friends Life T20 oraz ICC Cricket World Cup. Wszystkie wyniki krykieta w IGScore są automatycznie aktualizowane i nie ma potrzeby ręcznego odświeżania. Dzięki temu istnieje możliwość oglądania darmowej transmisji na żywo z krykieta i sprawdzania najnowszych kursów na ostateczny wynik najciekawszych meczów krykieta na całym świecie.", "FooterContentWaterPolo": "IGScore water polo live score zapewnia wyniki na żywo z piłki wodnej i wyniki z Włoch Serie A1, Węgier OB1, Mistrzów i Ligi Adriatyckiej na poziomie klubowym, natomiast na poziomie międzynarodowym IGScore water polo live score zapewnia dostęp do głównych turniejów, takich jak Mistrzostwa Świata w piłce wodnej i Mistrzostwa Europy w piłce wodnej . Zapewniamy Ci wynik na żywo gola po golu i darmową transmisję na żywo.", "FooterContentTableTennis": "IGScore table tennis live score zapewnia wyniki na żywo, tabele, wyniki, rankingi tenisa stołowego, terminarze i statystyki ze wszystkich największych turniejów tenisa stołowego, takich jak rosyjski tenis stołowy, olimpiada tenisa stołowego. Również dla każdego tenisisty stołowego możesz zobaczyć szczegóły rozegranych przez niego pojedynków oraz ich wyniki według seta i turnieju, w którym rozgrywany był ten mecz. IGScore table tennis live score zapewnia bezpośrednie wyniki, statystyki, wyniki na żywo i transmisję na żywo między dwoma graczami, którzy grają mecz.", "FooterContentSnooker": "IGScore snooker live score umożliwia śledzenie wyników na żywo, wyników i klasyfikacji ze wszystkich turniejów snookera. Zapewniamy również wyniki na żywo z Wielkiej Brytanii i Mistrzostw Świata, a także wyniki na żywo snookera, mecze snookera i końcowe wyniki snookera z międzynarodowych turniejów, takich jak World Snooker tour. W dowolnym momencie możesz zobaczyć harmonogram turniejów snookera, które mają się rozpocząć, wyniki z poprzednich turniejów snookera i ostatnie 10 gier dla każdego gracza. Dodatkowo możesz sprawdzić poprzednie mecze między graczami. Na IGScore snooker live score możesz znaleźć listę meczów, które są transmitowane na żywo z darmowego snookera.", "FooterContentBadminton": "IGScore badminton live score udostępnia wyniki na żywo w badmintona, tabele, terminarze i statystyki z międzynarodowych turniejów, takich jak Mistrzostwa Świata, Super Series BWF i wyniki badmintona z Igrzysk Olimpijskich. Możesz także sprawdzić wyniki rozegranych gier w badmintona, zobaczyć harmonogram gier badmintona i sprawdzić wyniki badmintona poszczególnych graczy na IGScore badminton live score.", "ContactUs": "Skontaktuj się z nami", "UpdateLineups": "Update Lineups", "FreeLiveScoresWidget": "Free Livescores Widget", "PlayerSalary": "", "DivisionLevel": "Level Event", "Foreigners": "Liczba zagranicznych graczy", "LeagueInfo": "Turnieje", "TeamInfo": "Informacje o drużynie", "Venues": "", "MostValuablePlayer": "", "UpperDivision": "Wyższa liga", "NewcomersFromUpperDivision": "", "NewcomersFromLowerDivision": "", "TitleHolder": "Aktualny mistrz", "MostTitle": "Do wygrania numer", "Pts": "PTS", "FullStats": "", "Relegation": "", "Result": "<PERSON><PERSON><PERSON>", "Score": "<PERSON><PERSON><PERSON>", "PlayerStats": "", "fixtures": "", "topPlayers": "Kluczowi gracze", "Shots": "Strzelanie", "SelectTeam": "", "SelectBasketBallTeam": "", "SelectAll": "Zaznacz wszystko", "SquadSize": "Liczba graczy", "ViewAll": "Zobacz wszystkie", "penaltiesWon": "Wykorzy<PERSON><PERSON> karne", "accuracyCrosses": "", "totalShorts": "", "accuracyLongBalls": "", "blockShots": "", "MatchesToday": "Mecze d<PERSON>j", "Strikers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Midfielders": "Pomocnik", "Year": "", "Loans": "", "TopValuablePlayers": "", "total": "", "Total": "", "Results": "", "Prev": "", "Apps": "Wystę<PERSON>", "ShotsPg": "Uśrednianie strzał", "Possession": "", "TotalAndAve": "", "Suspended": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lub <PERSON>aw<PERSON>zeni", "injuredOrSuspended": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> lub <PERSON>aw<PERSON>zeni", "Since": "<PERSON>zas <PERSON>", "Overall": "Overall", "Age": "<PERSON><PERSON>", "LastMatchFormations": "Ostatnia gra lineup", "Formation": "", "GoalDistribution": "Dystrybucja cel", "Favorite": "", "FoundedIn": "Został znaleziony w", "LocalPlayers": "Lok<PERSON><PERSON> gracze", "ShowNext": "Pokaż następne wydarzenia", "HideNext": "Ukryj następne wydarzenia", "FIFAWorldRanking": "Ranking FIFA", "Register": "", "OP": "1X2", "Handicap": "", "h5Handicap": "", "TennisHandicap": "", "OU": "O/U", "CardUpgradeConfirmed": "Potwierdzono aktualizację karty", "VAR": "", "LatestMatches": "Ostatnie mecze", "ScheduledMatches": "", "Only": "", "LeagueFilter": "", "WinProb": "", "ScoreHalf": "", "Animation": "", "FigureLegends": "", "YellowCard": "Żółta kartka", "RedCard": "Czerwona kartka", "Chatroom": "Pokój rozmów", "Send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Logout": "", "CurrentLeague": "", "ShirtNumber": "", "ContractUntil": "Umowa do", "PlayerInfo": "PLAYER INFO", "Height": "Wzrost", "Weight": "W<PERSON>", "PlayerValue": "Status społeczny", "View": "", "Time": "Czas", "onTarget": "", "offTarget": "", "Played": "", "Rating": "", "Attacking": "<PERSON><PERSON>", "Creativity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Defending": "<PERSON><PERSON><PERSON>", "Tactical": "Ta<PERSON><PERSON><PERSON>", "Technical": "Technika", "Other": "", "Cards": "<PERSON><PERSON><PERSON> kary", "AccuratePerGame": "Dokładny mijania", "AccLongBalls": "Długie podanie nadzwyczajny", "AccCrosses": "Dokładna biografia", "SuccDribbles": "Niezwykły sukces", "TotalDuelsWon": "<PERSON><PERSON><PERSON>", "YellowRedCards": "", "AiScoreRatings": "", "Pergame": "", "Player": "", "Upcoming": "", "FreeKicks": "", "Corners": "<PERSON><PERSON><PERSON> r<PERSON>", "DaysUntil": "Dni do", "In": "<PERSON><PERSON><PERSON><PERSON>", "Out": "<PERSON><PERSON><PERSON><PERSON>", "NoStrengths": "Brak wyróżniających cech", "NoWeaknesses": "Brak wyróżniających cech", "UpcomingMatches": "", "Pos": "", "Ht(cm)": "", "wt(kg)": "", "Exp": "EXP", "College": "", "Salary/Year": "", "Manager": "", "TotalPlayers": "Liczba graczy", "Form": "", "Points": "<PERSON><PERSON>", "GamesPlayed": "", "WinPercentage": "", "FieldGoalsMade": "", "FieldGoalsAttempted": "", "FieldGoalPercentage": "", "threePointFieldGoalsMade": "", "threePointFieldGoalsAttempted": "", "threePointFieldGoalsPercentage": "", "FieldGoaldsMade": "", "FreeThrowsAttempted": "", "FreeThrowPercentage": "", "BlockedFieldGoalAttempts": "", "PersonalFouls": "", "PersonalFoulsDrawn": "", "Efficiency": "", "PlusMinus": "", "Atlantic": "", "Central": "", "Southeast": "", "Northwest": "", "Pacific": "", "Southwest": "", "EasternConference": "", "WesternConference": "", "ToWin": "", "Spread": "Handicap", "AmFootballHand": "Handicap", "TotalPoints": "", "TotalPoint": "", "TableTennisTotalGames": "", "Wins": "<PERSON><PERSON><PERSON><PERSON>", "Losses": "Porażki", "WinningPercentage": "", "GamesBack": "", "HomeRecord": "", "AwayRecord": "", "DivisionRecord": "", "ConferenceRecord": "", "OpponentPointsPerGame": "", "AveragePointDifferential": "", "CurrentStreak": "", "RecordLast5Games": "", "Match": "meczu", "OnTheCourt": "Na boisku", "Starters": "Rozpoczynający", "FieldGoals": "Kopnięcia na bramke na mecz", "Timeout": "", "OpeningOdds": "", "PreMatchOdds": "", "PointPerGame": "", "PointsAllowed": "", "StartingLineups": "Rozpoczynające składy", "HandicapGames": "", "TennisHand": "", "TotalGames": "", "TennisTotalGames": "", "Defensive": "", "Offensive": "", "Points(PerGame)": "", "Rebounds(PerGame)": "", "Other(PerGame)": "Inne (uśrednianie)", "Attists": "", "FirstServe": "", "FirstServePoints": "", "BreakPoints": "", "Aces": "<PERSON><PERSON>", "DoubleFaults": "Podwójne błędy", "Winner": "Winner", "HandicapSets": "", "TableTennisHand": "", "MoneyLine": "", "TableTennisHandicapGames": "", "HandicapRuns": "", "BaseballHand": "", "TotalRuns": "", "BaseballTotalGames": "", "DIFF": "", "BasketPergame": "", "HandicapPoints": "", "HandicapFrames": "", "TotalFrames": "", "SeeAll": ""}