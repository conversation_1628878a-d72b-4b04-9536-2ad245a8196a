.home-game-category-container {
  display: flex;
  flex-direction: column;
  width: 100%;

  .game-category {
    display: flex;
    flex-direction: row;
    width: 100%;
    padding: 12px;
    align-items: center;
    justify-content: space-between;

    .game-category-left-aligned {
      display: flex;
      align-items: center;
  
      .category-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 10px;
        overflow: hidden;
        background-size: cover;
      }
  
      .competition-container {
        display: flex;
        flex: 1;
        flex-direction: column;
  
        .competition-name {
          color: #fff;
          font-size: 22px;
        }
  
        .country-name {
          color: #C0C5C9;
          font-size: 22px;
        }
      }
    }
  
    .game-category-right-aligned {
      .count-container {
        display: flex;
        flex-direction: row;
        align-items: center;

        .icon-size,
        .online-count {
          font-size: 28px;
          color: #fff;
        }
      }
    }
  }

  .match-list-container {
    width: 100%;
    height: fit-content;
    background: #121212;
    border-radius: 24px;
    display: flex;
    align-items: center;
    box-sizing: border-box;
    padding: 0 16px;
    cursor: pointer;
    margin-bottom: 16px;
    transition: all 0.3s ease;

    .match-time-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 20%;

      .match-status {
        font-size: 24px;
        text-align: center;
        flex: 1;
        color: #fff;

        &.live {
          color: #c1272d;
      
          .match-status-text {
            animation: Twinkle 1s infinite;
          }
        }

        &.neutral {
          color: #fff;
        }
      }
    }

    .match-team-container {
      display: flex;
      flex-direction: column;
      width: 60%;

      .match-team-info {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 10px 0px;

        .team-logo {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin-right: 10px;
          overflow: hidden;
          background-size: cover;
        }

        .team-name {
          font-size: 24px;
          font-weight: bold;
          color: #fff;
          text-overflow: ellipsis;
        }
      }
    }

    .match-odd-container {
      display: flex;
      flex-direction: column;
      width: 10%;

      .odd {
        font-size: 18px;
        color: #fff;
      }
    }

    .match-live-score-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 20%;
      margin-left: 10px;

      .match-score-container {
        display: flex;
        flex-direction: column;
        width: 50px;
  
        .match-score {
          font-size: 24px;
          font-weight: bold;
          color: #fff;
          padding: 10px 0px;
        }
      }
    }
  }
}

// @keyframes pulse {
//   0% {
//     opacity: 1;
//     transform: scale(1);
//   }
//   50% {
//     opacity: 0.6;
//     transform: scale(1.05);
//   }
//   100% {
//     opacity: 1;
//     transform: scale(1);
//   }
// }