import { inject, observer } from 'mobx-react';
import { Moment } from 'moment';
import { useCallback, useEffect, useLayoutEffect, useRef, useState } from 'react';

import { getCompetitionFilter, getCompetitionList } from 'iscommon/api/competition';
import { translate } from 'iscommon/i18n/utils';
import { momentTimeZone } from 'iscommon/utils';

import Loading from '@/components/Loading';

import { getCupFilterData, getCupNoRoundFilterData, getLeagueFilterData, Matches } from './filterMatches';
import ScheduleGame from './ScheduleGame';

import './index.less';

export const LeagueItem = ({ leagueItem, isCup = false }: any) => {
  if (leagueItem?.list.length === 0) return null;
  return (
    <>
      {leagueItem?.title && <div className="leagueTitle">{leagueItem.title}</div>}
      {leagueItem?.list?.map((item: any, i: number) => {
        if (isCup) {
          return (
            <ScheduleGame
              key={item.id}
              competition={item}
              isCup={isCup}
              preGroupNum={i !== 0 ? leagueItem?.list[i - 1].groupNum : 0}
            />
          );
        } else {
          return <ScheduleGame key={item.id} competition={item} />;
        }
      })}
    </>
  );
};

const Schedule = inject('store')(
  observer((props: any) => {
    const { Competitions } = props.store;
    const {
      competitionId,
      currentSeason: { id: seasonId },
    } = Competitions;

    const [list, setList] = useState<any[]>([]);
    const [matches, setMatches] = useState<Matches>([]);
    const [selectorData, setSelectorData] = useState<any>({});
    const [loading, setLoading] = useState(true);
    const timer = useRef<NodeJS.Timeout | null>(null);
    const leagueTitle = translate('league');
    const roundText = translate('round');

    const filterList = useCallback(() => {
      const { type, curRound, curStageId, stages } = selectorData;
      // type === 1 联赛 , 2 杯赛， 0 不展示title
      if (type === 1) {
        let res: any[] = [];
        let restMatches = [...matches];
        for (let i = 0; i < stages.length; i++) {
          res = res.concat(getLeagueFilterData(restMatches, stages[i], res.length, curRound, curStageId, roundText, leagueTitle));
        }
        setList(res);
      } else if (type === 2) {
        let noRoundRes: any[] = [];
        let res: any[] = [];
        for (let i = 0; i < stages.length; i++) {
          const itemStage = stages[i];
          const { roundCount, groupCount } = itemStage;
          if (roundCount === 0 && groupCount === 0) {
            noRoundRes = noRoundRes.concat(getCupNoRoundFilterData(matches, itemStage, curStageId));
          } else {
            res = res.concat(getCupFilterData(matches, itemStage, res.length, curRound, curStageId, roundText));
          }
        }
        setList(noRoundRes.concat(res));
      }
      setLoading(false);
    }, [leagueTitle, matches, roundText, selectorData]);

    const getFilter = useCallback(() => {
      getCompetitionFilter({
        competitionId,
        seasonId,
      }).then(({ type, curRound, curStageId, stages }: any) => {
        setSelectorData({ type, curRound, curStageId, stages });
      });
    }, [competitionId, seasonId]);

    const getListData = useCallback(() => {
      getCompetitionList({
        seasonId,
        competitionId,
        stageId: null,
        groupNum: 0,
        roundNum: 0,
      }).then(({ matches }: any) => {
        setMatches(matches);
      });
    }, [competitionId, seasonId]);

    useEffect(() => {
      if (matches.length > 0 && selectorData) {
        filterList();
      }
    }, [filterList, matches, selectorData]);

    useEffect(() => {
      if (seasonId) {
        Promise.all([getFilter(), getListData()]);
      }
    }, [getFilter, getListData, seasonId]);

    useLayoutEffect(() => {
      if (list.length) {
        const now = momentTimeZone(Date.now(), '') as Moment;
        let nextMatch: any = null;
        for (const item of list) {
          if (item?.list?.length) {
            nextMatch = item.list.find(({ matchTime }: any) => (momentTimeZone(matchTime, '') as Moment).isAfter(now, 'day'));
            if (nextMatch) break;
          }
        }
        if (nextMatch) {
          timer.current = setTimeout(() => {
            const targetClassId = `.classId_${momentTimeZone(nextMatch.matchTime * 1000, 'YYMMDD')}`;
            document.querySelector(targetClassId)?.scrollIntoView({ behavior: 'smooth' });
          }, 300);
        }
      }
      return () => {
        if (timer.current) {
          clearTimeout(timer.current);
        }
      };
    }, [list]);

    return (
      <Loading loading={loading} isEmpty={list.length === 0}>
        {/* {list.map((item: any, index) => (
          <LeagueItem key={index} leagueItem={item} isCup={selectorData.type === 2} />
        ))} */}
        <ScheduleGame />
      </Loading>
    );
  }),
);

export default Schedule;
