/* eslint-disable max-len */

import './styles.less';

import { GlobalConfig, langStoreKey } from 'iscommon/const/globalConfig';
import { inject, observer } from 'mobx-react';
import { useEffect, useState } from 'react';

import { getStore } from 'iscommon/store';
import styles from './edit.less';
import { translate } from 'iscommon/i18n/utils';

const pluginId = 'ig-free-plugin-card';

const HomeEditBottom = inject('store')(
  observer((props: any) => {
    const { WebConfig } = props.store;
    const { themeId, currentSportPathname, sliderWidth } = WebConfig;
    const contactUs = translate('ContactUs');
    const [textVal, setTextVal] = useState('');
    const [textIndex, setTextIndex] = useState(0);

    const getUrl = () => {
      const pathName = GlobalConfig.pathname;
      // @ts-ignore
      return `${location.origin}/${pathName}?isplugin=true&pluginName=${pluginId}&width=${sliderWidth}&lang=${getStore(langStoreKey, 'en')}`;
    };

    const openPreview = () => {
      window.open(getUrl());
    };
    const generateCode = () => {
      const domId = `i_w_${pluginId}`;
      const iframeString = `<iframe src='${getUrl()}' height='100%' width='1200px' border='0'></iframe>`;
      const text = `<div id="${domId}"></div><script language="javascript">document.getElementById("${domId}").innerHTML="${iframeString}";</script>
    <style>body{margin:0}#${pluginId}{display:flex;justify-content:center;height:100%}</style>`;
      setTextVal(text);
      setTextIndex(2);
    };

    const generateUrl = () => {
      setTextVal(getUrl());
      setTextIndex(1);
    };

    useEffect(() => {
      if (textIndex === 1) {
        generateUrl();
      } else if (textIndex === 2) {
        generateCode();
      }
    }, [themeId, currentSportPathname, textIndex]);

    return (
      <div className={`${styles.editTop} editcontainer`}>
        <div className="generate">GENERATE URL AND CODE</div>
        <div className="generateBox">
          <div className="generateItem" onClick={openPreview}>
            Widget Preview
          </div>
          <div className="generateItem" onClick={generateUrl}>
            Generate URL
          </div>
          <div className="generateItem" onClick={generateCode}>
            Generate Code
          </div>
        </div>
        <p className="generateText">
          Generate the widget and the code you need. Paste the code onto your website, or in your WordPress Widgets and you'll be
          on your way in no time.
        </p>
        <div className="el-textarea" style={{ marginTop: '12px' }}>
          <textarea
            autoComplete="off"
            value={textVal.replace(/\n/g, '')}
            rows={7}
            className="el-textarea__inner"
            style={{ minHeight: '33PX' }}
          ></textarea>
        </div>
        <div className="more">MORE CUSTOMIZED REQUIREMENTS?</div>
        <div className="moreContact" style={{ color: '#333', marginTop: '5px' }}>
          {contactUs}
        </div>
        <div className="moreContact" style={{ marginBottom: '100px' }}>
          <a href="mailto:<EMAIL>" target="_blank" style={{ color: '#1081DB' }} rel="noreferrer">
            <EMAIL>
          </a>
        </div>
      </div>
    );
  }),
);
export default HomeEditBottom;
