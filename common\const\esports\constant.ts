export const EsportsStatusCodeEnum = {
  Abnormal: 0,
  NotStarted: 1,
  End: 3,
  // POSTPONED: 14,
  DELAYED: 13,
  CANCELED: 12,
  INTERRUPTED: 11,
  CutInHalf: 14,
  TBD: 15,
};

export const EsportsMatchStatusCodeToText = {
  0: '-',
  1: '-',
  2: 'Gaming',
  3: 'FT',
  411: 'Gaming',
  412: 'Gaming',
  413: 'Gaming',
  414: 'Gaming',
  415: 'Gaming',
  416: 'Gaming',
  417: 'Gaming',
  418: 'Gaming',
  419: 'Gaming',
  420: 'Gaming',
  421: 'Gaming',
  432: 'Gaming',
  433: 'Gaming',
  434: 'Gaming',
  435: 'Gaming',
  436: 'Gaming',
  437: 'Gaming',
  438: 'Gaming',
  439: 'Gaming',
  440: 'Gaming',
  452: 'Gaming',
  453: 'Gaming',
  454: 'Gaming',
  455: 'Gaming',
  456: 'Gaming',
  457: 'Gaming',
  458: 'Gaming',
  459: 'Gaming',
  460: 'Gaming',
  461: 'Gaming',
  462: 'Gaming',
  463: 'Gaming',
  464: 'Gaming',
  465: 'Gaming',
  466: 'Gaming',
  467: 'Gaming',
  468: 'Gaming',
  469: 'Gaming',
  470: 'Gaming',
  11: 'INTERRUPTED',
  12: 'CANCELED',
  13: 'DELAYED',
  14: 'Cut in half',
  15: 'TBD',
};

// 2-9
export const EsportsInLiveStatusEnum = [
  2,
  411,
  412,
  413,
  414,
  415,
  416,
  417,
  418,
  419,
  420,
  421,
  432,
  433,
  434,
  435,
  436,
  437,
  438,
  439,
  440,
  452,
  453,
  454,
  455,
  456,
  457,
  458,
  459,
  460,
  461,
  462,
  463,
  464,
  465,
  466,
  467,
  468,
  469,
  470,
];

export const EsportsTypeList = [
  {
    id: 3,
    name: 'CS:GO',
    logo: 'https://www.igscore.net/static-images/categories/csgo.png',
    active: false,
  },
  {
    id: 2,
    name: 'DOTA2',
    logo: 'https://www.igscore.net/static-images/categories/dota.png',
    active: false,
  },
  {
    id: 1,
    name: 'LOL',
    logo: 'https://www.igscore.net/static-images/categories/lol.png',
    active: false,
  },
];

export const esportsTypeMap = {
  1: 'lol',
  2: 'dota',
  3: 'csgo',
};
