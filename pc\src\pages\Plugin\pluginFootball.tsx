import './pluginHomeShared.less'

import { getCompetitionList } from "iscommon/api/home";
import { getHomeCompetitionMatchTime } from 'iscommon/utils/dataUtils';
import PluginHomeShared from './pluginHomeShared';

interface PluginFootballProps {
  displayMode?: 'full' | 'carousel-only';
}

const PluginFootball: React.FC<PluginFootballProps> = ({ displayMode = 'full' }) => {
  // Football-specific data fetching logic
  const fetchMatches = async () => {
    try {
      // Fetch live football matches (listType: 0)
      const { competitions: liveCompetitions } = await getCompetitionList({ listType: 0, skipOdds: false });
      console.log('🏈 Football Plugin: live competitions', liveCompetitions);

      // Extract live competition IDs for sharing with PopularCompetitions
      const liveCompIds = new Set(liveCompetitions.map((comp: any) => comp.competitionId?.toString()).filter(Boolean)) as Set<string>;

      const liveMatches = liveCompetitions.flatMap((comp: any) =>
        (comp.matches || []).map((match: any) => ({
          ...match,
          competitionName: comp.competitionName,
          type: 'live',
        }))
      );

      // Prepare dateFilter (today and tomorrow in ISO string with +08:00)
      const today = new Date();
      const todayFilter = new Date(today.getTime() - today.getTimezoneOffset() * 60000)
        .toISOString()
        .replace('Z', '+08:00');

      const tomorrow = new Date(today);
      tomorrow.setDate(today.getDate() + 1);
      const tomorrowFilter = new Date(tomorrow.getTime() - tomorrow.getTimezoneOffset() * 60000)
        .toISOString()
        .replace('Z', '+08:00');

      // Fetch upcoming matches for today
      const { competitions: todayCompetitions } = await getCompetitionList({ listType: 2, dateFilter: todayFilter, skipOdds: false });

      // Fetch upcoming matches for tomorrow
      const { competitions: tomorrowCompetitions } = await getCompetitionList({ listType: 2, dateFilter: tomorrowFilter, skipOdds: false });

      // Combine both
      const allUpcomingCompetitions = [...todayCompetitions, ...tomorrowCompetitions];

      // Extract matches from both days
      const upcomingMatches = allUpcomingCompetitions.flatMap((comp: any) =>
        (comp.matches || []).map((match: any) => ({
          ...match,
          competitionName: comp.competitionName,
          type: 'upcoming',
        }))
      );

      const allMatches = [...liveMatches, ...upcomingMatches];

      // Extract unique competition names for dropdown
      const competitions = Array.from(new Set(allMatches.map(match => match.competitionName)));

      return {
        allMatches,
        competitions,
        liveCompetitionIds: liveCompIds
      };
    } catch (error) {
      console.error('Error fetching football matches:', error);
      return {
        allMatches: [],
        competitions: [],
        liveCompetitionIds: new Set<string>()
      };
    }
  };

  // Football-specific live timer logic
  const getLiveTimer = (match: any, currentTime?: number) => {
    // Use provided currentTime for real-time updates, fallback to current time
    const serverTime = currentTime || (Date.now() / 1000);

    // Football uses matchActiveTime and getHomeCompetitionMatchTime
    const { matchStatus, matchActiveTime, secondHalfKickOffTime } = match;
    const { statusText } = getHomeCompetitionMatchTime({
      serverTime,
      matchStatus,
      matchActiveTime,
      secondHalfKickOffTime,
      currentGameTab: 1,
    });
    return statusText;
  };

  // Extract and format handicap values and odds data from match odds (Football version)
  const getHandicapAndOdds = (match: any) => {
    try {
      // Get the asia odds from matchOdds
      const asiaOdds = match.matchOdds?.find((odds: any) => odds.oddsType === 'asia');
      if (!asiaOdds || !asiaOdds.handicap || !asiaOdds.oddsData) {
        return { 
          homeHandicap: null, 
          awayHandicap: null,
          homeOdds: null,
          awayOdds: null
        };
      }

      const handicap = asiaOdds.handicap; // e.g., "0/0.5" or "0"

      // Handle different handicap formats
      let hdpValue;
      
      if (handicap.includes('/')) {
        // Format: "0/0.5", "0/1.5", etc.
        const parts = handicap.split('/');
        if (parts.length !== 2) {
          return { 
            homeHandicap: null, 
            awayHandicap: null,
            homeOdds: null,
            awayOdds: null
          };
        }
        hdpValue = parseFloat(parts[1]); // 0.5, 1.5, etc.
      } else {
        // Format: "0", "1", "-0.5", etc. (direct handicap value)
        hdpValue = parseFloat(handicap);
      }

      // Format handicap values - display even when hdpValue is 0
      const homeHandicap = hdpValue === 0 ? '0' : (hdpValue > 0 ? `+${hdpValue}` : `${hdpValue}`);
      const awayHandicap = hdpValue === 0 ? '0' : (hdpValue > 0 ? `-${hdpValue}` : `+${Math.abs(hdpValue)}`);

      // Extract odds data[0] and data[2]
      const homeOdds = asiaOdds.oddsData[0] != null ? parseFloat(Number(asiaOdds.oddsData[0]).toFixed(2)) : null;
      const awayOdds = asiaOdds.oddsData[2] != null ? parseFloat(Number(asiaOdds.oddsData[2]).toFixed(2)) : null;
      
      return { homeHandicap, awayHandicap, homeOdds, awayOdds };
    } catch (error) {
      console.error('❌ Error processing football handicap and odds:', error);
      return { 
        homeHandicap: null, 
        awayHandicap: null,
        homeOdds: null,
        awayOdds: null
      };
    }
  };

  return (
    <PluginHomeShared
      fetchMatches={fetchMatches}
      getLiveTimer={getLiveTimer}
      getHandicapAndOdds={getHandicapAndOdds}
      sportName="football"
      displayMode={displayMode}
    />
  );
};

export default PluginFootball;
