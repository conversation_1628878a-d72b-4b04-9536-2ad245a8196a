import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { useMemo } from "react";
import './StatsTable.less'

const TeamStatsTable = ({ data }: { data: any[] }) => {

  const labelMaps = useTranslateKeysToMaps(['Players']);
  const commonTitleMaps = useMemo(() => [
    { label: 'PTS', value: 'points' },
    { label: 'FGM', value: 'fieldGoalsScored' },
    { label: 'FGA', value: 'fieldGoalsTotal' },
    { label: '3PM', value: 'threePointsScored' },
    { label: '3PA', value: 'threePointsTotal' },
    { label: 'FTM', value: 'freeThrowsScored' },
    { label: 'FTA', value: 'freeThrowsTotal' },
    { label: 'OREB', value: 'offensiveRebounds' },
    { label: 'DREB', value: 'defensiveRebounds' },
    { label: 'REB', value: 'rebounds' },
    { label: 'AST', value: 'assists' },
    { label: 'STL', value: 'steals' },
    { label: 'BLK', value: 'blocks' },
    { label: 'TOV', value: 'turnovers' },
  ], []);

  return (
    <div className="basketball-stats-table-container">
      <div className="custom-stats-table">
        <div className="table-header">
          <div className="header-cell">#</div>
          <div className="header-cell player-cell">{labelMaps.Players}</div>
          {commonTitleMaps.map((stat) => (
            <div className="header-cell" key={stat.value}>{stat.label}</div>
          ))}
        </div>

        <div className="table-body">
          {data.map((player, index) => (
            <div className="table-row" key={index}>
              <div className="table-cell">{player.player.shirtNumber}</div>

              <div className="table-cell player-cell">
                <img src={player.player.logo} alt={player.player.name} className="player-icon" />
                <div className="player-info">
                  <span className="player-name">{player.player.name}</span>
                  <span className="player-minutes">{player.minutesPlayed} min</span>
                </div>
              </div>

              {commonTitleMaps.map((stat) => (
                <div className="table-cell" key={stat.value}>
                  {player[stat.value] ? player[stat.value] : '0'}
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default TeamStatsTable;