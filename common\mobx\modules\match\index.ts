// @ts-nocheck
import { GlobalConfig } from 'iscommon/const/globalConfig';
import { makeAutoObservable } from 'mobx';
import { FallbackImage } from '../../../const/icon';

export const MatchTab = {
  overview: 'Overview',
  odds: 'Odds',
  lineup: 'Lineup',
  live: 'MatchLive',
  standings: 'Standings',
  h2h: 'H2H',
  boxScore: 'BoxScore',
};

export const MatchTabH5 = {
  overview: 'Overview',
  chat: 'Chat',
  boxScore: 'BoxScore',
  lineup: 'Lineup',
  odds: 'Odds',
  live: 'MatchLive',
  h2h: 'H2H',
  standings: 'Standings',
  statistics: 'Statistics',
};

export default class Match {
  constructor() {
    this.matchId = '';
    this.serverTime = GlobalConfig.serverTime;
    this.matchHeaderInfo = {
      matchId: -1,
      competition: {
        id: '',
        name: '',
        logo: FallbackImage,
        countryId: '',
        categoryId: '',
        type: 0,
      },
      homeTeam: {
        id: '',
        name: '',
        logo: FallbackImage,
        shortName: '',
        marketValueCurrency: '',
        competitionId: '',
      },
      awayTeam: {
        id: '',
        name: '',
        logo: FallbackImage,
        shortName: '',
        marketValueCurrency: '',
        competitionId: '',
      },
      venue: {
        name: '',
        city: '',
        capacity: 0,
      },
      matchTime: 0,
      aggScore: '',
      statusId: -1,
      homeScores: '',
      awayScores: '',
    };

    this.timeLineList = []
    this.statsList = []
    this.matchOdds = []
    this.shouldShowVideo = false

    makeAutoObservable(this);
  }

  changeMatchId(id = '') {
    this.matchId = id;
  }

  changeServerTime(time) {
    this.serverTime = time;
  }

  changeMatchHeaderInfo(data = {}) {
    this.matchHeaderInfo = data;
  }

  setTimeLineList(list = []) {
    this.timeLineList = list
  }

  setStatsList(list = []) {
    this.statsList = list.sort((a, b) => a.type - b.type)
  }

  setMatchOdds(matchOdds = []) {
    this.matchOdds = matchOdds
  }

  setShouldShowVideo(shouldShowVideo = '') {
    this.shouldShowVideo = shouldShowVideo
  }

}
