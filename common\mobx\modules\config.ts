// @ts-nocheck
import { history } from 'umi';
import { makeAutoObservable } from 'mobx';
import { DefaultUserData, themeColorMap } from '../../const/constant';
import GlobalUtils, { GlobalConfig, GlobalSportPathname } from '../../const/globalConfig';
import { getLocalUserData, saveLocalUserData } from '../../store/userStore';
import { simpleCloneObj } from '../../utils';

export default class WebConfig {
  constructor() {
    this.liveCounts = [];
    this.tempLiveData = null;
    this.currentSportPathname = GlobalConfig.pathname;
    this.userData = getLocalUserData() || DefaultUserData;
    this.showOdds = false;
    this.showVideo = false;
    this.showFootball = false;
    this.showBasketball = false;
    this.showBaseball = false;
    this.showTennis = false;
    this.showTabletennis = false;
    this.showVolleyball = false;
    this.showEsports = false;
    this.showAmfootball = false;
    this.showBadminton = false;
    this.showSnooker = false;
    this.showHandball = false;
    this.showIcehockey = false;
    this.showCricket = false;
    this.showWaterpolo = false;
    this.showChatTab = false;
    this.themeId = 't1'
    this.sliderWidth = 1200

    makeAutoObservable(this);
  }

  get currentThemeId() {
    return this.themeId
  }

  get currentLiveCount() {
    return this.liveCounts.filter((item) => item.sportType.toLocaleLowerCase() === GlobalConfig.pathname.toLocaleLowerCase())[0];
  }

  setThemeId(id) {
    this.themeId = id
  }

  setSliderWidth(w) {
    this.sliderWidth = w
  }

  setLiveCounts(liveCounts = []) {
    this.liveCounts = liveCounts;
    if (this.tempLiveData) {
      this.setOnTypeLiveCount(this.tempLiveData.liveCount, this.tempLiveData.totalCount, this.tempLiveData.type);
    }
  }

  setShowOddsStatus(result = false) {
    this.showOdds = result;
  }
  
  setShowVideoStatus(result = false) {
    this.showVideo = result;
  }
  
  setShowFootballStatus(result = false) {
    this.showFootball = result;
  }
  
  setShowBasketballStatus(result = false) {
    this.showBasketball = result;
  }
  
  setShowBaseballStatus(result = false) {
    this.showBaseball = result;
  }
  
  setShowTennisStatus(result = false) {
    this.showTennis = result;
  }
  
  setShowTabletennisStatus(result = false) {
    this.showTabletennis = result;
  }
  
  setShowVolleyballStatus(result = false) {
    this.showVolleyball = result;
  }
  
  setShowEsportsStatus(result = false) {
    this.showEsports = result;
  }
  
  setShowAmfootballStatus(result = false) {
    this.showAmfootball = result;
  }
  
  setShowBadmintonStatus(result = false) {
    this.showBadminton = result;
  }
  
  setShowSnookerStatus(result = false) {
    this.showSnooker = result;
  }
  
  setShowHandballStatus(result = false) {
    this.showHandball = result;
  }
  
  setShowIcehockeyStatus(result = false) {
    this.showIcehockey = result;
  }
  
  setShowCricketStatus(result = false) {
    this.showCricket = result;
  }
  
  setShowWaterpoloStatus(result = false) {
    this.showWaterpolo = result;
  }

  setShowChatTab(result = false) {
    this.showChatTab = result;
  }

  setOnTypeLiveCount(liveCount = 0, totalCount = 0, type = GlobalConfig.pathname) {
    const list = simpleCloneObj(this.liveCounts);
    const item = list.find((l) => l.sportType.toLocaleLowerCase() === type.toLocaleLowerCase());
    if (item) {
      if (liveCount > -1) {
        item.liveCount = liveCount;
      }
      if (totalCount > -1) {
        item.totalCount = totalCount;
      }
      this.liveCounts = list;
    } else {
      this.tempLiveData = {
        liveCount,
        totalCount,
        type,
      };
    }
  }

  initSportPathname(pathname = GlobalSportPathname.football) {
    this.currentSportPathname = pathname;
  }

  changeSportPathname(pathname = GlobalSportPathname.football) {
    GlobalUtils.setPathName(pathname);
    this.currentSportPathname = pathname;
    if (pathname === GlobalSportPathname.football) {
      history.push('/');
    } else {
      history.push(`/${pathname}/`);
    }
  }

  changePathnameWithTheme(pathname = GlobalSportPathname.football) {
    GlobalUtils.setPathName(pathname);
    this.currentSportPathname = pathname;
    history.push(`/${pathname}?livescorewidget=true`);
  }

  setGlobalUserData({ type = 'google', token, name, avatar, email }) {
    const userData = {
      type,
      token,
      name,
      avatar,
      email,
      isLogin: true,
    };
    this.userData = userData;
    saveLocalUserData(userData);
  }

  logout() {
    this.userData = DefaultUserData;
    saveLocalUserData(DefaultUserData);
  }
}
