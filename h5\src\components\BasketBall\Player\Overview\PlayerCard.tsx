import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { inject, observer } from "mobx-react";
import moment from "moment";
import { useMemo } from "react";
import { Link } from "umi";
import './PlayerCard.less'
import { FallbackImage } from "iscommon/const/icon";

const PlayerCard = inject('store')(
  observer((props: any) => {
    const {
      store: { 
        Basketball: { Player }
      }
    } = props;
    const { playerHeaderInfo } = Player;

    const labelMaps = useTranslateKeysToMaps([
      'Overview',
      'Birthday',
      'Salary',
      'Height',
      'Position',
      'Shirt',
      'Club',
      'Country',
    ])

    const birthday = useMemo(() => moment(playerHeaderInfo?.player?.birthday * 1000).format('YYYY-MM-DD'), [playerHeaderInfo?.player?.birthday]);

    return (
      <div className="player-base-info-container">
        <div className="container-title">{labelMaps.Overview}</div>
        <div className="container-body">
          <Link className='team-container' to={GlobalUtils.getPathname(PageTabs.team, playerHeaderInfo?.team?.id)}>
            <img className='team-icon' src={playerHeaderInfo?.team?.logo} alt={playerHeaderInfo?.team?.name} loading="lazy"/>
            <div className='team-detail'>
              <span className='team-name'>{playerHeaderInfo?.team?.name}</span>
              <span className='club-label'>{labelMaps.Club}</span>
            </div>
          </Link>
          <div className='player-info-container'>
            <div className="detail-item">
              <div className='detail-info'>
                <img className="detail-logo" src={playerHeaderInfo.player.countryDto?.logo || FallbackImage} loading="lazy"/>
                <span className="detail-title">{playerHeaderInfo.player.countryDto?.name}</span>
              </div>
              <span className="detail-value">{labelMaps.Country}</span>
            </div>
            <div className="detail-item">
              <span className="detail-title">{playerHeaderInfo.player.age} Years</span>
              <span className="detail-value">{birthday}</span>
            </div>
            <div className="detail-item">
              <span className="detail-title">${playerHeaderInfo.player.salary ? `${Math.round(playerHeaderInfo.player.salary / 1000000)}M` : '-'}</span>
              <span className="detail-value">{labelMaps.Salary}</span>
            </div>
            <div className="detail-item">
              <span className="detail-title">{playerHeaderInfo.player.height} cm</span>
              <span className="detail-value">{labelMaps.Height}</span>
            </div>
            <div className="detail-item">
              <span className="detail-title">{playerHeaderInfo.player.position}</span>
              <span className="detail-value">{labelMaps.Position}</span>
            </div>
            <div className="detail-item">
              <span className="detail-title">{playerHeaderInfo.player.shirtNumber}</span>
              <span className="detail-value">{labelMaps.Shirt}</span>
            </div>
          </div>
        </div>
      </div>
    );
  }),
);

export default PlayerCard;