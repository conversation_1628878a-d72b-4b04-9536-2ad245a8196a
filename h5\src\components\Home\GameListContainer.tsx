import './GameListContainer.less'

import { inject, observer } from 'mobx-react';
import { lazy, memo, useCallback, useEffect, useRef, useState } from 'react';
import { momentTimeZone, simpleCloneObj, sortByMatchTime } from 'iscommon/utils';

import FavoriteCompetitions from './FavoriteCompetitions';
import GameCategory from './GameCategory';
import { getCompetitionList } from 'iscommon/api/home';
import { GlobalConfig } from 'iscommon/const/globalConfig';
import { HomeGameTab } from 'iscommon/const/constant';
import { isEqual } from 'lodash';
import LazyLoad from 'react-lazyload';
import UmiLoading from '../Common/UmiLoading';
import UpComing from './UpComing';
import { useMatchTimer } from 'iscommon/hooks/apiData';

// @ts-ignore
















const AllCompetitionsContainer = lazy(() => import('./AllCompetitionsContainer'));

let refTimer: any = null;
const MaxCount = 8;
let MaxMatchItem = [4, 8];
const getMaxtItem = (list: any) => {
  try {
    if (list.length === 0) return [1, 2];
    let maxDisplayCount = 0,
      count = 0;
    for (let i = 0; i < list.length; i++) {
      const cItem = list[i];
      if (count < MaxCount) {
        count += cItem.matches.length;
        maxDisplayCount++;
      } else {
        break;
      }
    }
    return [Math.floor(maxDisplayCount / 1.5), maxDisplayCount];
  } catch (e) {
    return [4, 8];
  }
};

const RenderHomeCompetitions = memo(
  ({ list, scroll }: any) => {
    useEffect(() => {
      if (scroll) {
        window.scrollTo(0, 1);
        window.scrollTo(0, 0);
      }
    }, [scroll]);
    return (
      <div>
        {list.map((item: any, index: number) => {
          return <GameCategory key={item.competitionId + index} competition={item} />;
        })}
      </div>
    );
  },
  (pre, next) => {
    return isEqual(pre, next);
  },
);

const RenderLoading = memo(
  () => {
    return (
      <div style={{ transform: 'translateY(-34px)' }}>
        {/* @ts-ignore */}
        <UmiLoading htmlString={window.skeletonDetailString} />
      </div>
    );
  },
  () => true,
);

const GameListContainer = inject('store')(
  observer((props: any) => {
    const { propsTab, isSortByTime = false } = props;
    const { WebHome, WebConfig } = props.store;
    const { serverTime, currentGameTab: storeGameTab, date, h5StickIdList, homeCompetitions } = WebHome;

    const competitionsRef = useRef<any[]>([]);
    const isSortByTimeRef = useRef<boolean>(false);
    const [originList, setOriginList] = useState<any[]>([]);
    // const [fvids, setFvids] = useState([]);
    const [loading, setLoading] = useState<boolean>(true);
    const isMount = useRef<boolean>(true);

    const [activeTab, setActiveTab] = useState(HomeGameTab.Live);

    const currentGameTab = propsTab || storeGameTab;

    const setCompetitionsWithStick = useCallback(() => {
      if (originList.length > 0) {
        const prevList: any[] = [];
        const nextList: any[] = [];
        if (currentGameTab === HomeGameTab.All) {
          for (let i = 0; i < originList.length; i++) {
            const item = originList[i];
            if (h5StickIdList.indexOf(item.competitionId) > -1) {
              prevList.push(item);
            } else {
              nextList.push(item);
            }
          }
          WebHome.setHomeCompetitions([...prevList, ...nextList]);
        } else {
          WebHome.setHomeCompetitions(originList);
        }
      } else {
        WebHome.setHomeCompetitions([]);
      }
    }, [currentGameTab, h5StickIdList, originList, WebHome]);

    const setFormatCompetitionList = useCallback(() => {
      const list = simpleCloneObj(competitionsRef.current);
      if (isSortByTimeRef.current) {
        // order by time
        setOriginList(sortByMatchTime(list));
      } else {
        setOriginList(list);
      }
    }, []);

    const getListData = useCallback(() => {
      setTimeout(() => {
        getCompetitionList({
          dateFilter: momentTimeZone(date, 'YYYY-MM-DDTHH:mm:SS.SSS') + GlobalConfig.timezone,
          listType: currentGameTab,
        }).then(({ competitions, total }) => {
          competitionsRef.current = simpleCloneObj(competitions);
          setFormatCompetitionList();
          setLoading(false);

          if (currentGameTab === HomeGameTab.Live) {
            WebConfig.setOnTypeLiveCount(total);
          }
          if (currentGameTab === HomeGameTab.All) {
            WebConfig.setOnTypeLiveCount(0, total);
          }
        });
      }, 500);
    }, [date, currentGameTab, setFormatCompetitionList, WebConfig]);

    useEffect(() => {
      setCompetitionsWithStick();
    }, [h5StickIdList, originList, setCompetitionsWithStick]);

    useEffect(() => {
      if (propsTab) {
        WebHome.switchHomeGameTab(propsTab);
      }
    }, [propsTab, WebHome]);

    useEffect(() => {
      setOriginList([]);
      setLoading(true);
      if (refTimer) {
        clearTimeout(refTimer);
      }
      if (currentGameTab !== HomeGameTab.Leagues) {
        getListData();
      }
    }, [currentGameTab, date, getListData]);

    useEffect(() => {
      isSortByTimeRef.current = isSortByTime;
      setFormatCompetitionList();
    }, [isSortByTime, setFormatCompetitionList]);

    useEffect(() => {
      return () => {
        if (refTimer) {
          clearTimeout(refTimer);
        }
        isMount.current = false;
      };
    }, []);

    // useEffect(() => {
    //   window.scrollTo(0, 10);
    // }, []);

    const homeCompetitionsLength = homeCompetitions.length;

    useEffect(() => {
      if (homeCompetitionsLength === 0 && !loading) {
        window.scrollTo(0, 1);
        window.scrollTo(0, 0);
      }
    }, [loading, homeCompetitionsLength]);

    // update server time every 60s
    useMatchTimer(serverTime, WebHome, getListData);

    MaxMatchItem = getMaxtItem(homeCompetitions);

    return (
      <div>
        {loading && homeCompetitionsLength === 0 && currentGameTab !== 5 && <RenderLoading />}
        {(homeCompetitionsLength > 0 || (currentGameTab === HomeGameTab.Live && !loading)) && (
          <div className='game-list-container'>
            {homeCompetitionsLength > 0 && (
              <>
                <RenderHomeCompetitions list={homeCompetitions.slice(0, MaxMatchItem[0])} scroll />
                {currentGameTab !== 5 && homeCompetitionsLength > MaxMatchItem[0] && (
                  <LazyLoad height={800} offset={10}>
                    <RenderHomeCompetitions list={homeCompetitions.slice(MaxMatchItem[0], MaxMatchItem[1])} />
                  </LazyLoad>
                )}
                {currentGameTab !== 5 && homeCompetitionsLength > MaxMatchItem[1] && (
                  <LazyLoad height={800} offset={10}>
                    <RenderHomeCompetitions list={homeCompetitions.slice(MaxMatchItem[1], homeCompetitionsLength)} />
                  </LazyLoad>
                )}
              </>
            )}
            {!loading && currentGameTab === HomeGameTab.Live && (
              <LazyLoad height={200} offset={10}>
                <UpComing />
              </LazyLoad>
            )}
          </div>
        )}
        {currentGameTab === 5 && <AllCompetitionsContainer />}
        {currentGameTab === 4 && <FavoriteCompetitions />}
      </div>
    );
  }),
);

export default GameListContainer;
