import SmallballMatchOddsTab from '@/components/SmallGameCommon/Match/OddsTab';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
const OddTab = () => {
  const labels = useTranslateKeysToMaps(['Winner', 'TennisHandicap', 'OU', 'h5Handicap', 'h5Over', 'h5Under']);
  const labelMaps = {
    ToWin: labels.Winner,
    Spread: labels.TennisHandicap,
    TotalPoints: labels.OU,
    h5Handicap: 'Games',
    h5Over: labels.h5Over,
    Goals: 'Games',
    h5Under: labels.h5Under,
  };
  return <SmallballMatchOddsTab labelMaps={labelMaps} />;
};

export default OddTab;
