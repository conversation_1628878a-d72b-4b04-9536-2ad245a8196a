import './FeaturedCard.less'

import { Carousel, Image, Typography } from 'antd';
import { FallbackCategoryImage, FallbackImage, FallbackPlayerImage } from "iscommon/const/icon";
import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";
import { inject, observer } from "mobx-react";
import React, { useEffect, useMemo, useState } from "react";

import classNames from 'classnames';
import { getFeaturedList } from "iscommon/api/home";
import { getHomeCompetitionMatchTime } from "iscommon/utils/dataUtils";
import { Link } from "umi";
import moment from "moment";

const {Text} = Typography;

interface PureMatchStatusTextProps {
  currentGameTab: number;
  serverTime: number;
  matchStatus: number;
  matchActiveTime: number;
  secondHalfKickOffTime: number;
}

const PureMatchStatusText = React.memo<PureMatchStatusTextProps>((props) => {
  const { currentGameTab, serverTime, matchStatus, matchActiveTime, secondHalfKickOffTime } = props;
  const { statusText, isAni, isIng } = useMemo(
    () =>
      getHomeCompetitionMatchTime({
        serverTime,
        matchStatus,
        matchActiveTime,
        secondHalfKickOffTime,
        currentGameTab,
      }),
    [serverTime, matchStatus, matchActiveTime, secondHalfKickOffTime, currentGameTab],
  );

  console.log('status text', statusText)

  return (
    <span className={classNames('time-status', isAni && 'twinkleScore', isIng && 'ing', statusText === '-')}>
      {statusText}
    </span>
  );
});

interface MatchStatusTextProps {
  match: any;
  store?: any;
}

const MatchStatusText: React.FC<MatchStatusTextProps> = inject('store')(
  observer((props: any) => {
    const {
      match: { matchStatus, matchActiveTime, secondHalfKickOffTime },
      store: {
        WebHome: { currentGameTab, serverTime },
      },
    } = props;

    return (
      <PureMatchStatusText
        serverTime={serverTime}
        matchStatus={matchStatus}
        matchActiveTime={matchActiveTime}
        secondHalfKickOffTime={secondHalfKickOffTime}
        currentGameTab={currentGameTab}
      />
    );
  }),
);

const FeaturedCard = inject('store')(
  observer((props: any) => {
    const {
      WebHome : { homeCompetitions },
    } = props.store;
    const [competitionList, setCompetitionList] = useState<any[]>([]);
    const [matchList, setMatchList] = useState<any[]>([]);
    const [playerList, setPlayerList] = useState<any[]>([]);
    const [teamList, setTeamList] = useState<any[]>([]);

    useEffect(() => {
      getFeaturedList().then((res: any) => {
        const { competitionList, matchList, playerList, teamList } = res

        setMatchList(matchList)
        setCompetitionList(competitionList)
        setPlayerList(playerList)
        setTeamList(teamList)
      })
    }, [])

    const matchesWithActiveTime = homeCompetitions.flatMap((competition: any) =>
      competition.matches.map((match: any) => ({
        matchId: match.matchId,
        matchActiveTime: match.matchActiveTime,
      }))
    ); 

    const activeMatch = matchesWithActiveTime.find((match: any) =>
      matchList.some((item) => match.matchId === item.id)
    );

    const getFormattedScore = (scores: number[], isHome: boolean): React.ReactNode => {
      if (!Array.isArray(scores)) return null;
    
      const baseScore = scores[5] !== 0 ? scores[5] : scores[0];
      const penaltyScore = scores[6] !== 0 ? scores[6] : null;
    
      if (penaltyScore !== null) {
        return isHome ? (
          <>
            <span style={{ color: '#9e9e9e' }}>({penaltyScore})</span> {baseScore}
          </>
        ) : (
          <>
            {baseScore} <span style={{ color: '#9e9e9e' }}>({penaltyScore})</span>
          </>
        );
      }
    
      return <>{baseScore}</>;
    };

    return (
      <div className="football-featured-card-container">
        <Carousel dots={false} swipeToSlide draggable infinite={false} variableWidth>
          {matchList.map((item, index) => {
            return (
              <div className="carousel-item" key={`match-${index}`}>
                <Link className="match-list-container" to={GlobalUtils.getPathname(PageTabs.match, item.id)}>
                  <div className="home-team">
                    <Image className='team-icon' src={item?.homeTeam?.logo || FallbackImage} alt={item?.homeTeam?.name} preview={false}/>
                  </div>
                  {item.statusId === 8 || item.statusId === 3 ? (
                    <div className="match-score-container">
                      <span className="match-score">{getFormattedScore(item.homeScores, true)} - {getFormattedScore(item.awayScores, false)}</span>
                      <span className="match-status">{item.statusId === 8 ? 'FT' : 'HT'}</span>
                    </div>
                  ) : item.statusId === 1 ? (
                    <div className="match-time">
                      <div className="hour-time">{moment.unix(item.matchTime).format('HH:mm')}</div>
                      <div className="date-time">{moment.unix(item.matchTime).format('DD/MM/YY')}</div>
                    </div>
                  ) : (
                    <div className="match-score-container">
                      <div className="match-score">{item.homeScores[0]} - {item.awayScores[0]}</div>
                      <MatchStatusText match={{ matchStatus: item.statusId, secondHalfKickOffTime: item.secondHalfKickOffTime, matchActiveTime: activeMatch?.matchActiveTime }} />
                    </div>
                  )}
                  <div className="away-team">
                    <Image className='team-icon' src={item?.awayTeam?.logo || FallbackImage} alt={item?.awayTeam?.name} preview={false}/>
                  </div>
                </Link>
              </div>
            )
          })}

          {playerList.map((item, index) => {
            return (
              <div className="carousel-item" key={`match-${index}`}>
                <Link className="player-list-container" to={GlobalUtils.getPathname(PageTabs.player, item.player.id)}>
                  <Image className="player-icon" src={item?.player?.logo || FallbackPlayerImage} alt={item?.player?.name} preview={false}/>
                  <div className="player-info">
                    <Text className="player-name">{item?.player?.name}</Text>
                    <Text className="team-name">{item?.team?.name}</Text>
                  </div>
                </Link>
              </div>
            )
          })}

          {competitionList.map((item, index) => {
            return (
              <div className="carousel-item" key={`match-${index}`}>
                <Link className="competition-list-container" to={GlobalUtils.getPathname(PageTabs.competition, item.competition.id)}>
                  <Image className="competition-icon" src={item?.competition?.logo || FallbackCategoryImage} alt={item?.competition?.name} preview={false}/>
                  <Text className="competition-name">{item?.competition?.name}</Text>
                </Link>
              </div>
            )
          })}

          {teamList.map((item, index) => {
            return (
            <div className="carousel-item" key={`match-${index}`}>
              <Link className="team-list-container" to={GlobalUtils.getPathname(PageTabs.team, item.id)}>
                <Image className="team-icon" src={item?.logo || FallbackImage} alt={item?.name} preview={false}/>
                <Text className="team-name">{item?.name}</Text>
              </Link>
            </div>
            )
          })}
        </Carousel>
      </div>
    );
  }),
);

export default FeaturedCard;