{"All": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Live": "CANLI", "LiveH5": "CANLI", "MatchLive": "CANLI", "TimeSort": "<PERSON>ax<PERSON> görə sırala", "SortByTime": "<PERSON>ax<PERSON> görə sırala", "AllGames": "TAM OYUNLAR", "Leagues": "LIQALAR", "h5_Leagues": "LIQALAR", "Today": "<PERSON><PERSON><PERSON><PERSON>", "Cancel": "<PERSON>ə<PERSON>v etmək", "Popular": "<PERSON><PERSON><PERSON>", "Settings": "Parametrlər", "Language": "Dil", "Overview": "<PERSON><PERSON><PERSON><PERSON>", "LiveOverview": "<PERSON><PERSON><PERSON><PERSON>", "Standings": "<PERSON><PERSON> cədvəli", "Stats": "Statistika", "Transfer": "Transfer", "Champions": "Çempionlar", "TeamChampions": "Çempionlar", "teamChampions": "Çempionlar", "Football": "FUTBOL", "Basketball": "BASKETBOL", "Baseball": "Beysbol", "Icehockey": "Xokkey", "Tennis": "TENNIS", "Volleyball": "VOLLEYBOL", "Esports": "ESPORT", "Handball": "HƏNDBOL", "Cricket": "KRİKET", "WaterPolo": "VATERPOL", "TableTennis": "<PERSON><PERSON>", "Snooker": "SNOOKER", "Badminton": "BADMINTON", "BusinessCooperation": "Business Cooperation", "TermsOfService": "<PERSON>d<PERSON><PERSON><PERSON>", "PrivacyPolicy": "<PERSON><PERSON><PERSON><PERSON>", "Players": "OYUNÇULAR", "ForeignPlayers": "Xarici oyunçular", "NumberOfTeams": "Komandaların sayı", "YellowCards": "Sarı kartlar", "RedCards": "Qırmızı kartlar", "Capacity": "Azarkeş tutumu", "City": "Ş<PERSON><PERSON><PERSON><PERSON>", "Info": "Mə<PERSON>at", "Matches": "MATÇLAR", "Team": "<PERSON><PERSON><PERSON>", "Teams": "<PERSON><PERSON><PERSON><PERSON>", "Goals": "<PERSON><PERSON><PERSON>", "Assists": "Məhsuldar ötürmələr", "assists": "Məhsuldar ötürmələr", "Home": "<PERSON>z sahəsində", "Away": "Sə<PERSON><PERSON>rdə", "topScorers": "Ən çox qol vuranlar", "TopScorers": "Ən çox qol vuranlar", "homeTopScorers": "Ən çox qol vuranlar", "season": "Mövsüm", "Season": "Mövsüm", "ShotsOnTarget": "Hədə<PERSON>ə atış", "Clearances": "<PERSON>ol b<PERSON>mama<PERSON>", "Tackles": "Qarşısını almaq", "keyPasses": "Həll edici ötürmələr", "KeyPasses": "Həll edici ötürmələr", "Fouls": "<PERSON><PERSON><PERSON> pozuntuları", "totalFouls": "<PERSON><PERSON><PERSON> pozuntuları", "WasFouled": "Oyunçu üzərində qayda pozuntuları", "Penalty": "<PERSON><PERSON><PERSON>", "MinutesPlayed": "Oynanmış dəqiqələr", "BasketballMinutesPlayed": "Oynanmış dəqiqələr", "Interceptions": "Topu ələ keçirmələr", "Steals": "<PERSON><PERSON>", "steals": "<PERSON><PERSON>", "Passes": "Ke<PERSON>ə<PERSON>", "Saves": "Qurtarışlar", "BlockedShots": "<PERSON><PERSON><PERSON><PERSON> zərbələr", "Signed": "İmzalanmış", "league": "", "offensiveData": "Təhqiredici məlumatlar", "defenseData": "Müdafiə məlumatları", "otherData": "<PERSON><PERSON><PERSON><PERSON>ə<PERSON>", "ballPossession": "<PERSON><PERSON> sahib olmaq", "shotsPerGame": "<PERSON>ər oyun üzrə zərbələr", "ShotsPerGame": "<PERSON>ər oyun üzrə zərbələr", "keyPassesPerGame": "<PERSON>ər oyun üzrəa əsas ö<PERSON>ü<PERSON>lər", "accurateLongBallsPerGame": "<PERSON>ər oyun üzrə dəqiq uzun ötümələr", "accurateCrossesPerGame": "<PERSON>ər oyun üzrə dəqiq hava ötürmələri", "tacklesPerGame": "<PERSON>ər oyun üzrə qarşısını almalar", "TacklesPerGame": "<PERSON>ər oyun üzrə qarşısını almalar", "interceptionsPerGame": "<PERSON>ər oyun üzrə topu ələ keçirmələr", "InterceptionsPerGame": "<PERSON>ər oyun üzrə topu ələ keçirmələr", "clearancesPerGame": "<PERSON>ər oyun üzrə topu qurtarmalar", "ClearancesPerGame": "<PERSON>ər oyun üzrə topu qurtarmalar", "blockedShotsPerGame": "<PERSON>ər oyun üzrə bloklanan zərbələr", "turnoversPerGame": "<PERSON>ər oyun üzrə topu itirmələr", "foulsPerGame": "<PERSON>ər oyun üzrə qayda pozuntuları", "scoringFrequencyFiveGoals": "", "Coach": "<PERSON><PERSON>şq<PERSON><PERSON>", "Goalkeeper": "Qapıçı", "Stadium": "Stadion", "Login": "Daxil ol", "Corner": "<PERSON><PERSON><PERSON>", "ShotsOffTarget": "Hədə<PERSON>dən kənar atış", "H2H": "H2H", "Date": "<PERSON><PERSON>", "OwnGoal": "<PERSON>z qapısına qol", "PenaltyMissed": "Uğursuz on bir metrə", "SecondYellow": "İkinci sarı kart", "Odds": "<PERSON><PERSON><PERSON><PERSON>", "attacks": "Zərb<PERSON>lə<PERSON>", "Started": "Başladı", "Chat": "Çat", "Strengths": "<PERSON>st<PERSON><PERSON> cə<PERSON>ə<PERSON>", "Weaknesses": "<PERSON><PERSON><PERSON>", "Group": "Qrup", "Birthday": "<PERSON>", "Club": "Klub", "MainPosition": "<PERSON><PERSON><PERSON>", "PassesAccuracy": "", "Preseason": "", "RegularSeason": "", "PointsPerGame": "<PERSON>ər oyun üzrə xallar", "Glossary": "<PERSON><PERSON><PERSON><PERSON>", "h5Glossary": "<PERSON><PERSON><PERSON><PERSON>", "Career": "<PERSON><PERSON><PERSON>", "Bench": "<PERSON>vəz edən", "ReboundsPerGame": "<PERSON>ər oyun üzrə topa sahiblənmələr", "AssistsPerGame": "<PERSON>ər oyun üzrə məhsuldar ötürmələr", "OddsFormat": "Əmsal formatı", "Squad": "<PERSON><PERSON><PERSON>", "TotalMarketValue": "Ümumi bazar dəyəri", "Rounds": "<PERSON><PERSON><PERSON>", "LowerDivision": "Aşağı liqa", "TeamStats": "Komandanın statistikası", "GoalsPk": "<PERSON><PERSON><PERSON>(PK)", "Crosses": "<PERSON><PERSON>", "CrossesAccuracy": "Uğurla top keçmək", "Dribble": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DribbleSucc": "Fövqə<PERSON><PERSON> uğur", "LongBalls": "<PERSON><PERSON><PERSON>ü<PERSON>ələ<PERSON>", "LongBallsAccuracy": "Long keçən müvəffəqiyyət dərəcəsi", "Duels": "<PERSON><PERSON><PERSON> pozuntuları", "DuelsWon": "Clean hesabatı", "Dispossessed": "Qurtarışlar", "Punches": "Qapıçı hücum uğur", "RunsOut": "Qapıçı qaçışı", "RunsOutSucc": "<PERSON><PERSON><PERSON><PERSON> zərbələr", "GoodHighClaim": "<PERSON><PERSON><PERSON><PERSON> dəqiq<PERSON>", "Loan": "İcarə", "EndOfLoan": "İ<PERSON><PERSON>nin sonu", "Unknown": "<PERSON><PERSON><PERSON>", "AverageAge": "<PERSON><PERSON>", "cornersPerGame": "<PERSON>ər oyun üzrə künc zərbə<PERSON>əri", "goalsConceded": "Buraxılmış qollar", "Defender": "Müdafiəçi", "Discipline": "<PERSON><PERSON><PERSON>", "Pass": "", "FB_Login": "Facebook ilə davam edin", "Google_Login": "Google ilə davam edin", "Substitutes": "<PERSON>vəz edən", "PenaltyKick": "", "ShareYourViews": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON>", "Nodata": "M<PERSON><PERSON>at <PERSON>du<PERSON>", "Foot": "<PERSON><PERSON><PERSON>", "dangerousAttack": "", "venue": "<PERSON><PERSON><PERSON>", "playerStatistics": "Oyunçu statistikası", "TotalPlayed": "İştirak etdiyi oyunların cəmi", "MinutesPerGame": "<PERSON>ər oyun üzrə dəqiqələr", "GoalsFrequency": "", "GoalsPerGame": "Field qol", "Arrivals": "Komandaya gələnlər", "Departures": "<PERSON><PERSON><PERSON><PERSON> gedənlər", "LeftFoot": "Sol ", "RightFoot": "Sağ", "LatestTransfers": "<PERSON><PERSON> son <PERSON><PERSON><PERSON><PERSON>", "DraftInfo": "Plan məlumatı", "OK": "OK", "AsianHandicap": "", "TotalGoals": "", "AmFootballTotalGames": "", "TotalCorners": "", "OverBall": "<PERSON><PERSON>q", "Over": "<PERSON><PERSON>q", "h5Over": "<PERSON><PERSON>q", "UnderBall": "altında", "Under": "altında", "h5Under": "altında", "OtherLeagues": "<PERSON><PERSON><PERSON><PERSON>[<PERSON>-<PERSON>]", "GoalPopup": "", "FullStandings": "<PERSON> cədvəl", "teamWeek": "Həftənin komandası", "weekTop": "Həftənin komandası", "TeamOfTheWeek": "Həftənin komandası", "round": "<PERSON><PERSON>", "Released": "<PERSON><PERSON>", "Retirement": "", "Draft": "Q<PERSON>lam<PERSON>", "TransferIn": "", "TransferOut": "", "MarketValue": "<PERSON><PERSON>", "Salary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Next": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Position": "Mövq<PERSON>", "CTR": "Cari transfer qeydləri", "FoulBall": "", "FreeKick": "", "GoalKick": "", "Midfield": "", "HalftimeScore": "", "InjuryTime": "", "OvertimeIsOver": "", "PenaltyKickEnded": "", "Last": "<PERSON><PERSON><PERSON>", "Win": "Qalibiyyətlər", "Draw": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Lose": "Məğlubiyyətlər", "Lineup": "", "Substitution": "<PERSON>vəz edən", "Offsides": "Oyundan kənar vəziyyət", "Playoffs": "", "Qualifier": "", "GroupStage": "", "Rebounds": "<PERSON><PERSON> sahib olma<PERSON>", "rebounds": "<PERSON><PERSON> sahib olma<PERSON>", "OffensiveRebounds": "Hücumda topa sahib olmaq", "offensiveRebounds": "Hücumda topa sahib olmaq", "DefensiveRebounds": "Müdafiədə topa sahib olmaq", "defensiveRebounds": "Müdafiədə topa sahib olmaq", "Turnovers": "Top itirmələri", "turnovers": "Top itirmələri", "Blocks": "Bloklar", "blocks": "Bloklar", "BoxScore": "Oyunçu Statistikaları", "Foul": "<PERSON><PERSON><PERSON>", "FreeThrows": "Sərb<PERSON>st atış", "freeThrowsScored": "Sərb<PERSON>st atış", "fieldGoalsScored": "", "fieldGoalsTotal": "", "threePointersScored": "", "threePointersTotal": "", "freeThrowsTotal": "", "Finished": "<PERSON><PERSON>", "Scheduled": "Armatur", "Favourite": "", "OddsMarkets": "Odds markets", "Search": "", "Timezone": "", "SoccerGoalReminderSettings": "", "RemindersOnlyForRacesToWatch": "", "GoalSound": "", "LiveScore": "", "Schedule": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Rugby": "", "FooterContentFootball": "IGScore Football LiveScore sizə misilsiz futbol canlı skorları və 2600-dən çox futbol liqası, kubok və turnirdən futbol nəticələri təqdim edir. Premyer Liqa, La Liga, Serie A, Bundesliga, Ligue 1, Eredivisie, Rusiya Premyer Liqası, Brasileirão, MLS, canlı skorlar, yarı vaxt və tam zamanlı futbol nəticələri, qol vuran oyunçular və köməkçilər, kartlar, əvəzetmələr, matç statistikaları və canlı yayım alın. igscore.net-da Super Liqa və Çempionat. IGScore, yalnız İngiltərə Premyer Liqası, İspaniya La Liga, İtaliya Serie A, Almaniya kimi ən populyar futbol liqalarından deyil, bütün futbol azarkeşlərinə canlı skorları, futbol canlı skorları, futbol skorları, liqalar, kuboklar və turnirlər üçün fikstürləri təqdim edir. Bundesliga, Fransa Ligue 1, həm də Şimali və Cənubi Amerika, Asiya və Afrikadan daxil olmaqla dünyanın hər yerindəki bir çox futbol ölkəsindən. Futbol canlı skor kartlarımız bu gün baş verən bütün futbol matçı canlı skor yeniləmələri ilə yanaşı, hər futbol və futbol liqası üçün bitmiş bütün futbol matçları üçün futbol canlı skor nəticələri ilə sizi xəbərdar etmək üçün real vaxt rejimində canlı olaraq yenilənir. Qarşılaşma səhifəsində futbol hesab kartlarımız, hər futbol müsabiqəsi üçün əvvəllər oynanılan bütün qurğular üçün keçmiş oyun nəticələrini görüntüləməyə imkan verir. Bütün futbol nəticələrinizi igscore.net-da əldə edin!", "FooterContentBasketball": "IGScore Basketbol Livescore sizə NBA liqasının canlı puanları, nəticələri, cədvəlləri, statistikası, qurğular<PERSON>, durumları və dördüncü, yarımçıq və ya son nəticə ilə əvvəlki nəticələrini təqdim edir. IGScore dünyanın 200-dən çox basketbol yarışında (NCAA, ABA Liqası, Baltiklər liqası, Avroliqa, milli basketbol liqaları kimi) xal xidmətini təklif edir. Burada yalnız canlı puanları, rüb nəticələri, yekun nəticələr və qrup oyunlarını deyil, həm də 2 və 3-cü cəhdlərin sayını, sərbəst atışlar, atış faizləri, ribauntlar, dövriyyələr, o<PERSON><PERSON><PERSON><PERSON><PERSON>, fərdi pozuntular, matç tarixi və oyunçu statistikasını tapa bilərsiniz. .IGScore Basketbol canlı oyununda basketbolu sadəcə tıklayarak onlayn izləyə bilərsiniz və üst liqalardakı oyunların onlayn yayımını təmin edir. Oradakı matç səhifəsi, komandaların son oyunlarına dair bütün basketbol statistikaları ilə cədvəl olacaqdır. basketbol kartlarımız bu gün baş verən bütün basketbol nəticələri ilə tanış olmaq və hər basketbol yarışları üçün əvvəllər oynanan bütün qurğular üçün keçmiş oyun nəticələrini görmək üçün real vaxt rejimində yenilənir. igscore.net-da bütün NBA canlı nəticələrinizi əldə edin! NBA canlı oyunlarını, NBA armaturlarını, NBA durumlarını və komanda səhifələrini izləyin!", "FooterContentAmFootball": "IGScore american football live score D<PERSON><PERSON>dak<PERSON> ən böyük və ən populyar Amerika Futbol Liqasının ən böyük və ən populyar Amerika Futbol Liqasının bütün nəticələrini və canlı puanlarını təmin edir və NFL mövsümü başa çatdıqda, NFL Playoff və Superbowl canlı puanları izləyin. NFL-dən əlavə, biz sizə NCAA Kolleci Amerika futbolu və Kanada CFL üçün LiveScores, Nəticələr, Vəziyyətləri və Cədvəlləri də təqdim edəcəyik.", "FooterContentBaseball": "IGScore baseball live score sizə dünyanın ən populyar beysbol liqası - USSSA Baseball, NCAA Baseball, MLB Baseball, MLB Allstar oyunundan canlı skorları, nətic<PERSON>l<PERSON><PERSON>, vəziyyətləri təqdim edir. Yaponiya Professional Liqası, Meksika liqası, Alman 1.Bundesliga, NCAA və Dünya Beysbol Klassik beynəlxalq beysbol turniri üçün də canlı hesab təqdim edirik. İstənilən vaxt beysbol liqası turnir cədvəlini, atışlarla nəticələnən keçmiş oyunları və IGScore baseball live score tarixində qarşıdakı beysbol matçlarının təqvimini görə bilərsiniz.", "FooterContentIcehockey": "IGScore ice hockey live score, xokkey liqaları, kubokları və turnirləri üçün real vaxt buz xokkeyinin nəticələrini təmin edir. IGScore ice hockey live score sizə NHL, SHL, KHL -dən xokkey canlı cədvəli, cədv<PERSON>ll<PERSON><PERSON>, statistika, qurğular, nəticələr və skorlar təqdim edir və biz həmçinin milli Finlandiya xokkey liqaları, İsveç xokkey liqaları, Slovakiya xokkey liqaları, Çex xokkey liqaları, liqa cədvəlləri, qol vuranlar, buz xokkeyinin üçdə biri və yekun nəticələri canlıdır. Buz hokeyi müntəzəm mövsümü bitdikdən sonra xokkey canlı skorlarını, vəziyyətini və ən yaxşı buz xokkey yarışlarının nəticələrini- IIHF Dünya Çempionatı Stenli Kubokunu və Qış Olimpiya turnirindəki xokkey puanlarını təqdim edirik. IGScore ice hockey live score tarixində NHL, SHL və digərləri üçün pulsuz xokkey canlı yayımı da tapa bilərsiniz.", "FooterContentTennis": "IGScore tennis live score sizə canlı skorlar, n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ATP reytinqləri və WTA reytinqləri, Davis və Fed Cup, Fransız açıq tennis və ya bütün Böyük Dəbilqə turnirləri - Avstraliya açıq tennis, ABŞ açıq tennis, Roland kimi bütün ən böyük tennis turnirlərindən armatur və statistika təqdim edir. Garros və Wimbledon həm qadınlar, həm də kişilər tək və cütlüklər üçün. Həm də hər hansı bir tennisçi üçün ayrı -ayrılıqda oynadığı matçları və nəticələrini setə və hansı turnirdə oynandığını ətraflı görə bilərsiniz. IGScore tennis live score, matç oynayan iki oyunçu arasında baş -başa nəticələr, statistika, canlı skorlar və canlı yayım təmin edir.", "FooterContentVolleyball": "IGScore volleyball live score sizə İtaliya Seriyası A1 və İtaliya Seria A1 Qadınlar, Rusiya Superliqası, Polşa PlusLiga, Türkiyə 1. Liqa və digərləri daxil olmaqla bütün vacib kişi və qadın milli voleybol liqalarından xəbər verir. Milli voleybol liqaları ilə yanaşı, sizə FIVB Dünya Çempionatı və Avropa Çempionatı kimi böyük voleybol beynəlxalq turnirlərindən canlı məlumatlar, habelə Olimpiya oyunlarında voleybol canlı nəticələr veririk. Ən sevdiyiniz voleybol komandasının köhnə nəticələrini yoxlaya, gələcək voleybol cədvəllərinə baxa və IGScore volleyball live score üzərindəki liqa voleybolunun vəziyyətini yoxlaya bilərsiniz.", "FooterContentEsports": "Esports Canlı puanları IGScore Live Score esports canlı puanları, cədvəllər, nəticələr və cədvəllər təklif edir. Sevdiyiniz komandaları izləyin. ESPORTS Canlı hesabı igscore.net Canlı Score-da canlı hesab avtomatik olaraq yenilənir və əl ilə təzələnməyinizə ehtiyac yoxdur. Matçlarınızdan sonra \"Oyunlarım\" da izləmək istədiyiniz oyunlar ilə nəticələr, nəticələr və statistikalar daha da sadə olacaqdır.", "FooterContentHandball": "IGScore handball live score sizə Almaniya Bundesliqa, İspaniya Liga Asobal, Danimarka kişi Handboldligaen və Fransa D1 kimi ən populyar həndbol liqalarından həndbol canlı skorları və canlı nəticələr təqdim edir. Həm də Avropa həndbol Çempionlar Liqası, SEHA liqası və EHF həndbol Kuboku kimi vacib kuboklardan canlı nəticələr, nəticələr, statistika, sıralamalar, cədvəllər və qurğular təqdim edirik. IGScore handball live score tarixində həm qadınlar, həm də kişilər üçün Avropa Çempionatı və Dünya Çempionatı kimi beynəlxalq komandalar üçün həndbol turnirləri üçün canlı skorlar və pulsuz canlı yayım tapa bilərsiniz. İstənilən vaxt komandanızın oynadığı son 10 oyunun həndbol nəticələrini və statistikasını yoxlaya bilərsiniz və eyni zamanda statistika ilə oynamağı planlaşdırılan komandalar arasında baş -başa vura bilərsiniz.", "FooterContentCricket": "IGScore cricket live score real vaxt kriket nə<PERSON>ə<PERSON>, kriket vəziyyətini və kriket qurğularını izləməyinizi təmin edir. Bütün bunlar ən populyar liqalar və kuboklar üçün mövcuddur: Hindistan Premyer Liqası, Çempionlar Liqası Twenty20, Big Bash Liqası, Ka<PERSON>b dənizi Premyer Liqası, Friends Life T20 və ICC Kriket Dünya Kuboku üçün. IGScore -da bütün kriket puanları avtomatik olaraq yenilənir və əl ilə yeniləməyə ehtiyac yoxdur. Bütün bunlarla birlikdə, pulsuz kriket canlı yayımını izləmək və dünyanın ən maraqlı kriket matçlarının son nəticəsi üçün ən son əmsalları yoxlamaq imkanı var.", "FooterContentWaterPolo": "IGScore water polo live score, İtaliya Seriyası A1, Macarıstan OB1, Çempionlar və Adriatik liqasından klub səviyyəsində su polosu canlı skorları və nəticələri ilə təmin edir, beynəlxalq səviyyədə isə IGScore water polo live score su polosu üzrə Dünya Çempionatı və Avropa su polosu çempionatı kimi böyük turnirlər təqdim edir. . Sizə qolla canlı canlı yayım və pulsuz canlı yayım təqdim edirik.", "FooterContentTableTennis": "IGScore table tennis live score, rus stolüstü tennis, stolüstü tennis olimpiadaları kimi ən böyük stolüstü tennis turnirlərindən canlı nəticələr, masa, nə<PERSON><PERSON><PERSON><PERSON><PERSON>, stolüstü tennis sıralaması, qurğular və statistika təqdim edir. Həm də hər hansı bir stolüstü tennisçi üçün ayrı -ayrılıqda oynadığı matçları və nəticələrini setə görə və bu turnirin hansı turnirdə keçirildiyini ətraflı görə bilərsiniz. IGScore table tennis live score, matç oynayan iki oyunçu arasında baş -başa nəticələr, statistika, canlı skorlar və canlı yayım təmin edir.", "FooterContentSnooker": "IGScore snooker live score, bütün snooker turnirlərindən canlı hesabı, nəticələri və vəziyyətləri izləmək imkanı verir. Həm də İngiltərədən və Dünya Çempionatından can damarları, həmçinin snooker canlı skorları, snooker qurğuları və Dünya Snooker turu kimi beynəlxalq turnirlərin son snooker nəticələrini təqdim edirik. İstənilən vaxt getməli olduğunuz snooker turnirlərinin cədvəlini, keçmiş snooker turnirlərinin nəticələrini və hər oyunçu üçün son 10 oyunu görə bilərsiniz. Əlavə olaraq, oyunçular arasında baş -başa matçları yoxlaya bilərsiniz. IGScore snooker live score tarixində pulsuz snooker canlı yayımı ilə əhatə olunan matçların siyahısını tapa bilərsiniz.", "FooterContentBadminton": "IGScore badminton live score sizə badminton canlı nəticəl<PERSON>rini, v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, armaturlarını və Dünya Çempionatı, BWF Super Seriyası və Olimpiya Oyunlarından badminton nəticələri kimi beynəlxalq turnirlərin statistikasını təqdim edir. Badminton oyunlarının nəticələrini yoxlaya, badminton oyun cədvəlinə baxa və IGScore badminton live score oyunçuların badminton nəticələrini fərdi olaraq yoxlaya bilərsiniz.", "ContactUs": "Bizimlə əlaqə saxlayın", "UpdateLineups": "Update Lineups", "FreeLiveScoresWidget": "Free Livescores Widget", "PlayerSalary": "", "DivisionLevel": "Hadisə Level", "Foreigners": "Xarici o<PERSON>çu sayı", "LeagueInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TeamInfo": "<PERSON><PERSON><PERSON> haqqında məlumat", "Venues": "", "MostValuablePlayer": "", "UpperDivision": "Yüksək liqa", "NewcomersFromUpperDivision": "", "NewcomersFromLowerDivision": "", "TitleHolder": "Titul <PERSON>lə<PERSON>", "MostTitle": "Qaz<PERSON><PERSON><PERSON> qədər", "Pts": "<PERSON><PERSON><PERSON>", "FullStats": "", "Relegation": "", "Result": "<PERSON><PERSON><PERSON><PERSON>", "Score": "<PERSON><PERSON><PERSON>", "PlayerStats": "", "fixtures": "", "topPlayers": "<PERSON><PERSON><PERSON>", "Shots": "Atıcılıq", "SelectTeam": "", "SelectBasketBallTeam": "", "SelectAll": "Hamısını seçin", "SquadSize": "Oyunçuların sayı", "ViewAll": "Hamısını gö<PERSON>ər", "penaltiesWon": "11 metre uzre qalib gel<PERSON>k", "accuracyCrosses": "", "totalShorts": "", "accuracyLongBalls": "", "blockShots": "", "MatchesToday": "<PERSON><PERSON> gün u<PERSON>ğ<PERSON> gəlir", "Strikers": "<PERSON><PERSON><PERSON><PERSON>", "Midfielders": "Yarımmüdafiəçi", "Year": "", "Loans": "", "TopValuablePlayers": "", "total": "", "Total": "", "Results": "", "Prev": "", "Apps": "Xazar", "ShotsPg": "<PERSON><PERSON> shot", "Possession": "", "TotalAndAve": "", "Suspended": "Yaralanmış və ya kənarlaşdırılmış", "injuredOrSuspended": "Yaralanmış və ya kənarlaşdırılmış", "Since": "Injury vaxt", "Overall": "Overall", "Age": "Yaş", "LastMatchFormations": "<PERSON> oyun lineup", "Formation": "", "GoalDistribution": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ı", "Favorite": "", "FoundedIn": "Ci ildə təsis edilib", "LocalPlayers": "<PERSON><PERSON><PERSON>", "ShowNext": "<PERSON>öv<PERSON><PERSON><PERSON> gö<PERSON>ər", "HideNext": "Növbəti mat<PERSON>ı gizlə", "FIFAWorldRanking": "FIFA <PERSON><PERSON><PERSON>", "Register": "", "OP": "1X2", "Handicap": "", "h5Handicap": "", "TennisHandicap": "", "OU": "Üst/alt", "CardUpgradeConfirmed": "Təsdiq<PERSON>ən<PERSON>ş Yenilənmə", "VAR": "", "LatestMatches": "<PERSON><PERSON><PERSON>", "ScheduledMatches": "", "Only": "", "LeagueFilter": "", "WinProb": "", "ScoreHalf": "", "Animation": "", "FigureLegends": "", "YellowCard": "Sarı kart", "RedCard": "Qırmızı kart", "Chatroom": "Söhbət otağı", "Send": "Ötürmək", "Logout": "", "CurrentLeague": "", "ShirtNumber": "", "ContractUntil": "Müqa<PERSON>ənin bitmə tarixi", "PlayerInfo": "PLAYER INFO", "Height": "Boy", "Weight": "<PERSON><PERSON><PERSON>", "PlayerValue": "İctimai vəziyyət", "View": "", "Time": "vaxt", "onTarget": "", "offTarget": "", "Played": "", "Rating": "", "Attacking": "Hücum edir", "Creativity": "K<PERSON><PERSON>v", "Defending": "<PERSON><PERSON><PERSON><PERSON>", "Tactical": "Taktiki", "Technical": "Texniki", "Other": "", "Cards": "Penalty kartları", "AccuratePerGame": "Accurate keçən", "AccLongBalls": "Associate uzun pass", "AccCrosses": "Accurate tərc<PERSON><PERSON>yi-hal", "SuccDribbles": "Fövqə<PERSON><PERSON> uğur", "TotalDuelsWon": "<PERSON><PERSON><PERSON> pozuntuları", "YellowRedCards": "", "AiScoreRatings": "", "Pergame": "", "Player": "", "Upcoming": "", "FreeKicks": "", "Corners": "<PERSON><PERSON><PERSON>", "DaysUntil": "qədər gün", "In": "<PERSON><PERSON>l olma", "Out": "<PERSON><PERSON><PERSON>", "NoStrengths": "Əhəmiyyətli üstünlüyü yoxdur", "NoWeaknesses": "Əhəmiyyətli zəifliyi yoxdur", "UpcomingMatches": "", "Pos": "", "Ht(cm)": "", "wt(kg)": "", "Exp": "EXP", "College": "", "Salary/Year": "", "Manager": "", "TotalPlayers": "Oyunçuların sayı", "Form": "", "Points": "<PERSON><PERSON><PERSON>", "GamesPlayed": "", "WinPercentage": "", "FieldGoalsMade": "", "FieldGoalsAttempted": "", "FieldGoalPercentage": "", "threePointFieldGoalsMade": "", "threePointFieldGoalsAttempted": "", "threePointFieldGoalsPercentage": "", "FieldGoaldsMade": "", "FreeThrowsAttempted": "", "FreeThrowPercentage": "", "BlockedFieldGoalAttempts": "", "PersonalFouls": "", "PersonalFoulsDrawn": "", "Efficiency": "", "PlusMinus": "", "Atlantic": "", "Central": "", "Southeast": "", "Northwest": "", "Pacific": "", "Southwest": "", "EasternConference": "", "WesternConference": "", "ToWin": "", "Spread": "Yaymaq", "AmFootballHand": "Yaymaq", "TotalPoints": "", "TotalPoint": "", "TableTennisTotalGames": "", "Wins": "Qələbə", "Losses": "Məğlubiyyətlər", "WinningPercentage": "", "GamesBack": "", "HomeRecord": "", "AwayRecord": "", "DivisionRecord": "", "ConferenceRecord": "", "OpponentPointsPerGame": "", "AveragePointDifferential": "", "CurrentStreak": "", "RecordLast5Games": "", "Match": "<PERSON><PERSON>", "OnTheCourt": "Sa<PERSON><PERSON><PERSON>ə", "Starters": "Başlan<PERSON><PERSON><PERSON>", "FieldGoals": "<PERSON><PERSON>ə qolu", "Timeout": "", "OpeningOdds": "", "PreMatchOdds": "", "PointPerGame": "", "PointsAllowed": "", "StartingLineups": "Başlan<PERSON><PERSON><PERSON>", "HandicapGames": "", "TennisHand": "", "TotalGames": "", "TennisTotalGames": "", "Defensive": "", "Offensive": "", "Points(PerGame)": "", "Rebounds(PerGame)": "", "Other(PerGame)": "Dig<PERSON>r (orta)", "Attists": "", "FirstServe": "", "FirstServePoints": "", "BreakPoints": "", "Aces": "Bir zərbə ilə bal almaq", "DoubleFaults": "<PERSON><PERSON><PERSON> xəta", "Winner": "Winner", "HandicapSets": "", "TableTennisHand": "", "MoneyLine": "", "TableTennisHandicapGames": "", "HandicapRuns": "", "BaseballHand": "", "TotalRuns": "", "BaseballTotalGames": "", "DIFF": "", "BasketPergame": "", "HandicapPoints": "", "HandicapFrames": "", "TotalFrames": "", "SeeAll": ""}