import { Avatar } from 'antd-mobile';
import { changePlayerName, formatRateColor, sortRating } from 'iscommon/utils/dataUtils';
import React from 'react';
import styles from './LineupList.less';
const LineupList: React.FC = (props: any) => {
  const { homeSubstitute = [], awaySubstitute, matchLineupInfo = {} } = props;
  const { homeTeam = {}, awayTeam = {} } = matchLineupInfo;

  const iconH5Maps = {
    1: 'icongoal', //进球
    3: 'icon-yellow-card', //黄牌,
    4: 'icon-red-card', //红牌
    8: 'icon-Penalty', // 点球
    9: 'iconin', //换人
    15: 'icon-twoyellow-red', //两黄变红
    16: 'iconPenaltySaved', //点球未进
    17: 'icon-own-goal', //乌龙球
  };

  const renderSubstitute = (renderList = [], teamInfo = {}) => {
    return (
      <div className={styles.lineup_list_column}>
        <div className={styles.lineup_list_item}>
          <Avatar src={teamInfo?.logo} style={{ '--size': '20px', margin: '0 12.48px' }} />
          <span>{teamInfo?.name}</span>
        </div>
        {renderList.map((item: any) => (
          <div className={styles.lineup_list_item} key={item.name}>
            <div className={styles.line_item_left}>
              <span className={styles.shirtNumber}>{item.shirtNumber}</span>
              <div className={styles.player_avatar_box}>
                <Avatar src={item.logo} style={{ '--size': '30px', borderRadius: '50%', border: '1px solid #eee' }} />
                {item.rating != '0.0' && (
                  <span className={styles.player_rating} style={{ background: formatRateColor(item.rating) }}>
                    {item.rating}
                  </span>
                )}
              </div>
              <div className={styles.player_name_box}>
                <span className={styles.player_name}>{changePlayerName(item.name)}</span>
              </div>
            </div>
            <div className={styles.event_box}>
              {item.incidents &&
                Object.keys(item.incidents).map((val: any) =>
                  ['1', '3', '4', '9', '15'].includes(val) ? (
                    <div className={styles.event} key={val + Math.random()}>
                      {/* <div className={styles.align_center}> */}
                      <svg className="svg-icon" style={{ width: 16, height: 16 }}>
                        <use xlinkHref={`#${iconH5Maps[val]}`} className="h16"></use>
                      </svg>
                      {val === '9' ? <span className={styles.event_time}>{`${item.incidents[val][0]?.minute}'`}</span> : null}
                    </div>
                  ) : // </div>
                  null,
                )}
              {/* <svg aria-hidden="true" className={`icon ${styles.fs_12}`} style={{ width: 15, height: 15 }}>
                  <use xlinkHref="#icon-red-card"></use>
                </svg>
                <span>23'</span> */}
            </div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className={styles.container}>
      {renderSubstitute(sortRating(homeSubstitute), homeTeam)}
      {renderSubstitute(sortRating(awaySubstitute), awayTeam)}
    </div>
  );
};

export default LineupList;
