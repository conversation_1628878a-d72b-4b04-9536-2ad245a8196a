import { Popup } from 'antd-mobile';
import React, { useCallback, useEffect, useMemo, useRef } from 'react';

import GlobalUtils from 'iscommon/const/globalConfig';
import { translate } from 'iscommon/i18n/utils';

import { inject, observer } from 'mobx-react';
import './Header.less';

interface TabItem {
  sportType: string;
  active: boolean;
  index: number;
  name: string;
  iconfont: string;
  pathname: string;
  // liveCount: number;
  // totalCount: number;
}
const bodyStyle: React.CSSProperties = {
  height: '500px',
  overflow: 'auto',
  width: '95.6vw',
  left: 0,
  right: 0,
  borderRadius: '5px',
  margin: '0 auto',
  background: '#f1f1f1',
};

const RenderLiveCount = (active: boolean, item: { liveCount: number; totalCount: number }) => {
  if (!item) return null;
  return (
    <div className={`countStyle_normal ${active ? 'active' : ''}`}>
      {item.liveCount && <span style={{ color: 'rgb(222, 30, 48)' }}>{item.liveCount}</span>}
      {item.totalCount && <span className="accolor">/</span>}
      {item.totalCount && <span className="accolor">{item.totalCount}</span>}
    </div>
  );
};

const NavBarList = inject('store')(
  observer((props: any) => {
    const {
      store: { WebConfig },
      setGameVisible,
    } = props;
    const { liveCounts, currentSportPathname } = WebConfig;

    const timer = useRef<number>(null);

    const games = useMemo<TabItem[]>(() => GlobalUtils.getMenuBarList() as any, []);

    const changeGame = useCallback(
      (item: TabItem) => {
        setGameVisible(false);
        // @ts-ignore
        WebConfig.changeSportPathname(item.pathname);
      },
      [WebConfig, setGameVisible],
    );

    const isChecked = (item: any) => {
      return currentSportPathname === item.pathname;
    };

    const getGameCount = (item: any) => {
      // console.log(item, )
      // return {}
      const filterCount = liveCounts.filter(
        (l: any) => l?.sportType?.toLocaleLowerCase() === item?.pathname?.toLocaleLowerCase(),
      )[0];
      return filterCount ? filterCount : null;
    };

    useEffect(() => {
      return () => {
        if (timer.current) {
          clearTimeout(timer.current);
        }
      };
    }, []);

    return (
      <Popup visible bodyStyle={bodyStyle} position="top" destroyOnClose onMaskClick={() => setGameVisible(false)}>
        <div className="contentBox w100">
          <div className="w100">
            {games.map((item: TabItem) => {
              return (
                <div key={item.sportType} className={`ball ${isChecked(item) ? 'active' : ''}`} onClick={() => changeGame(item)}>
                  <div className="h100 flex align-center">
                    <i className={`icon iconfont ballType ${item.iconfont} ${item.active ? 'active' : ''}`} />
                    <span className={`type_text ${item.active ? 'active' : ''}`}>{translate(item.name)}</span>
                  </div>
                  {RenderLiveCount(isChecked(item), getGameCount(item))}
                </div>
              );
            })}
          </div>
        </div>
      </Popup>
    );
  }),
);

export default NavBarList;
