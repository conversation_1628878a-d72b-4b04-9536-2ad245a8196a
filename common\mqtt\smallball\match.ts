// @ts-nocheck
import MqttClientInstance from '../index';
import smallBallMatchDataController, {matchDataControllerType} from './matchDataController';
import { GlobalConfig } from '../../const/globalConfig';
import { MqttTopicEnum, MqttListenKeyEnum } from '../constants';
import store from '../../mobx';
// import moment from "moment";

export const subscribeMatchData = (currentMatch, matchId) => {
  // subscribe home match data
  MqttClientInstance.subscribe(MqttTopicEnum.singleMatch + matchId, MqttListenKeyEnum.allMatch, (topic, message) => {
    if(_.isArray(message)) {
      for(let v of message) {
        const data = v;
        // console.log('MqttClientInstance match', topic, data);
        if (data) {
          // GlobalConfig.serverTime = time;
          // WebHome.changeServerTime(time);
          for (let dataKey in matchDataControllerType) {
            if (data[dataKey] && typeof currentMatch[dataKey] === 'function') {
              currentMatch[dataKey](data);
            }
          }
        }
      }
    }
  });

  // subscribe match odds
  MqttClientInstance.subscribe(MqttTopicEnum.odds, MqttListenKeyEnum.odds, (topic, message = {}) => {
    // console.log('MqttClientInstance odds', topic, message);
    if (message && typeof message === 'object' && Object.keys(message).length) {
      smallBallMatchDataController.syncMatchOdds(message);
    }
  });
};

export const unsubscribeMatchData = (matchId) => {
  MqttClientInstance.unsubscribe(MqttTopicEnum.singleMatch + matchId);
  MqttClientInstance.unsubscribe(MqttListenKeyEnum.odds);
};
