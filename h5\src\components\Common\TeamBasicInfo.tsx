import { getTeamInfoData } from 'iscommon/api/football-team';
import { translate } from 'iscommon/i18n/utils';
import { useEffect, useState } from 'react';
import './TeamBasicInfo.less';

interface Props {
  teamId: number;
  teamHeaderInfo: any;
}

const TeamBasicInfo = (props: Props) => {
  const { teamId, teamHeaderInfo } = props;
  const [baseInfo, setBaseInfo] = useState<any>(null);

  const Coach = translate('Coach');
  const AverageAge = translate('AverageAge');
  const CTR = translate('CTR');
  const Stadium = translate('Stadium');
  const Capacity = translate('Capacity');
  const City = translate('City');

  useEffect(() => {
    getTeamInfoData({ teamId }).then((result) => {
      setBaseInfo(result);
    });
  }, [teamId]);

  if (!baseInfo) return null;
  return (
    <div className="teamBasicInfo">
      <div className="section">
        <div className="sectionItem">
          <span className="color-999">{Coach}</span>
          <span>{baseInfo?.coach?.name || '--'}</span>
        </div>
        <div className="sectionItem">
          <span className="color-999">{AverageAge}</span>
          <span>{teamHeaderInfo.avgAge || '--'}</span>
        </div>
        <div className="sectionItem">
          <span className="color-999">{CTR}</span>
          <span>--</span>
        </div>
      </div>
      <div className="section">
        <div className="sectionItem">
          <span className="color-999">{Stadium}</span>
          <span>{baseInfo?.venue?.name || '--'}</span>
        </div>
        <div className="sectionItem">
          <span className="color-999">{Capacity}</span>
          <span>{baseInfo?.venue?.capacity || '--'}</span>
        </div>
        <div className="sectionItem">
          <span className="color-999">{City}</span>
          <span>{baseInfo?.venue?.city || '--'}</span>
        </div>
      </div>
    </div>
  );
};

export default TeamBasicInfo;
