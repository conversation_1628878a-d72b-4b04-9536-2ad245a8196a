import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { MatchTabH5 } from 'iscommon/mobx/modules/match';
import { PlayerTabH5 } from 'iscommon/mobx/modules/player';
import { TeamTabH5 } from 'iscommon/mobx/modules/team';
import { ReactNode } from 'react';
import './Link.less';
interface Props {
  teamId?: string;
  playId?: string;
  matchId?: string;
  competitionId?: string;
  children: ReactNode;
  tab?: string;
  className?: string;
}

export const TeamLink = (props: Props) => {
  const { teamId, children, tab = TeamTabH5.overview, className = '' } = props;
  const to = GlobalUtils.getPathname(PageTabs.team, teamId, tab);
  return (
    <div
      className={`linkColor ${className}`}
      onClick={() => {
        location.href = to;
      }}
    >
      {children}
    </div>
  );
};

export const PlayerLink = (props: Props) => {
  const { playId, children, tab = PlayerTabH5.overview, className = '' } = props;
  const to = GlobalUtils.getPathname(PageTabs.player, playId, tab);
  return (
    <div
      className={`linkColor ${className}`}
      onClick={() => {
        location.href = to;
      }}
    >
      {children}
    </div>
  );
};

export const MatchLink = (props: Props) => {
  const { matchId, children, tab = MatchTabH5.overview, className = '' } = props;
  const to = GlobalUtils.getPathname(PageTabs.match, matchId, tab);

  return (
    <div
      className={`linkColor ${className}`}
      onClick={() => {
        location.href = to;
      }}
    >
      {children}
    </div>
  );
};
