// .header_box {
//   display: flex;
//   flex-direction: row;
//   justify-content: space-between;
//   align-items: center;
//   padding: 0 30px;
//   height: 120px;
// }

// .popup_title {
//   font-size: 30px;
//   color: #333;
//   font-weight: 400;
// }

// .content_container {
//   width: 100%;
//   position: relative;
//   flex: 1;
//   display: flex;
//   height: calc(100% - 120px);
//   flex-direction: column;
//   padding-top: 66px;
// }

// .content_header_box {
//   display: flex;
//   flex-direction: row;
//   width: 100%;
//   height: 66px;
//   background: #fff;
//   font-size: 20px;
//   color: #999;
//   align-items: center;
//   top: 0;
//   position: absolute;
// }

// .common_item {
//   flex: 1;
//   display: flex;
//   justify-content: center;
//   align-items: center;
// }

// .time_header_item {
//   width: 180px;
//   display: flex;
//   justify-content: center;
//   align-items: center;
// }

// .content_list_box {
//   flex:1;
//   overflow-y: scroll;
//   top: 66px;
//   display: flex;
//   flex-direction: column;
//   width: 100%;
// }

// .content_item_box {
//   display: flex;
//   flex-direction: row;
//   width: 100%;
//   background: #fff;
//   font-size: 20px;
//   color: #999;
//   border-bottom: 1px solid hsla(0, 0%, 89%, 0.6);
//   align-items: center;
//   height: 66px;
//   flex-shrink: 0;
// }

// // .content_item_box:not(:last-child) {
// //   border-bottom: 1px solid hsla(0, 0%, 89%, 0.6);
// // }

// .content_item {
//   flex: 1;
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   height: 100%;
// }

// .content_item:not(:last-child) {
//   border-right: 1px solid hsla(0, 0%, 89%, 0.6);
// }

.odds-popup-container {
  display: flex;
  flex-direction: column;
  background: transparent;
  height: 60%;
  padding: 10px;
  border-radius: 24px;

  .popup-container-header {
    background: #2c2c2c;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #fff;
    font-size: 30px;
    padding: 10px;
    border-radius: 24px 24px 0px 0px;

    .header-container {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;

      .header-icon {
        width: 12px;
        height: 12px;
      }
    }
  }

  .popup-container-body {
    // background: #1e1e1e;
    width: 100%;
    position: relative;
    flex: 1 1;
    display: flex;
    height: calc(100% - 100px);
    flex-direction: column;
    padding-top: 55px;

    .container-body-header {
      display: flex;
      flex-direction: row;
      width: 100%;
      height: 55px;
      background: #1e1e1e;
      font-size: 20px;
      color: #fff;
      align-items: center;
      top: 0;
      position: absolute;

      .header-container {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;

        .header-icon {
          width: 20px;
          height: 20px;
          color: #fff;
          display: flex;
          align-items: center;
        }
      }

      .header-container-time {
        width: 180px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }

    .container-body-list {
      flex: 1 1;
      overflow-y: scroll;
      top: 55px;
      display: flex;
      flex-direction: column;
      width: 100%;
      background: #121212;

      .body-list-cell {
        display: flex;
        flex-direction: row;
        width: 100%;
        // background: #fff;
        font-size: 20px;
        // font-family: Roboto-Regular;
        color: #fff;
        border-bottom: 1px solid #2c2c2c;
        align-items: center;
        height: 55px;
        flex-shrink: 0;

        .cell-content {
          flex: 1;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 100%;
        }

        .cell-content-time {
          width: 180px;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
  }
}