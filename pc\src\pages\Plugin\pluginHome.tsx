import './pluginHome.less'

import { Button, Carousel, Popover, Select } from 'antd';
import { useCallback, useEffect, useState } from "react";

import { getCompetitionList } from "iscommon/api/home";
import { getHomeCompetitionMatchTime } from 'iscommon/utils/dataUtils';
import { GlobalConfig } from 'iscommon/const/globalConfig';
import moment from 'moment';
import { PlayCircleOutlined } from '@ant-design/icons';
import PopularCompetitions from './pluginPopular';
import { themeColorMap as themeColorMapCommon } from 'iscommon/const/constant';
import { useLocation } from 'react-router-dom';

const PluginHome: React.FC = () => {
  const [allMatches, setAllMatches] = useState<any[]>([]);
  const [filteredMatches, setFilteredMatches] = useState<any[]>([]);
  const [competitionList, setCompetitionList] = useState<string[]>([]);
  const [selectedCompetition, setSelectedCompetition] = useState<string>('All');
  const [matchType, setMatchType] = useState<'live' | 'upcoming'>('live');
  const [liveCompetitionIds, setLiveCompetitionIds] = useState<Set<string>>(new Set());

  const [selectedMatch, setSelectedMatch] = useState<any>(null);

  // const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const theme = searchParams.get('theme') || 't1';
  const themeColorMap = themeColorMapCommon as any;

  // Ensure plugin container has proper width when theme is not in URL
  useEffect(() => {
    const pluginContainer = document.getElementById('plugin_container_body')?.parentElement;
    if (pluginContainer && !searchParams.get('theme')) {
      pluginContainer.style.width = '1200px';
      pluginContainer.style.margin = '0 auto';
    }
  }, [searchParams]);
  
  const fetchMatches  = useCallback(async () => {
    try {
      // Fetch live matches (listType: 0)
      const { competitions: liveCompetitions } = await getCompetitionList({ listType: 0 });

      // Extract live competition IDs for sharing with PopularCompetitions
      const liveCompIds = new Set(liveCompetitions.map((comp: any) => comp.competitionId?.toString()).filter(Boolean)) as Set<string>;
      setLiveCompetitionIds(liveCompIds);

      const liveMatches = liveCompetitions.flatMap((comp: any) =>
        (comp.matches || []).map((match: any) => ({
          ...match,
          competitionName: comp.competitionName,
          type: 'live',
        }))
      );
  
      // Prepare dateFilter (today in ISO string with +08:00)
      const today = new Date();
      const dateFilter = new Date(today.getTime() - today.getTimezoneOffset() * 60000)
        .toISOString()
        .replace('Z', '+08:00');
  
      // Fetch upcoming matches (listType: 2 with dateFilter)
      const { competitions: upcomingCompetitions } = await getCompetitionList({ listType: 2, dateFilter });
      const upcomingMatches = upcomingCompetitions.flatMap((comp: any) =>
        (comp.matches || []).map((match: any) => ({
          ...match,
          competitionName: comp.competitionName,
          type: 'upcoming',
        }))
      )
      .sort((a: any, b: any) => a.matchTime - b.matchTime)
  
      const combined = [...liveMatches, ...upcomingMatches];
      setAllMatches(combined);

    } catch (err) {
      console.error('Failed to fetch matches:', err);
    }
  }, []);

  useEffect(() => {
    const relevantMatches = allMatches.filter(m => m.type === matchType);
    const names = Array.from(new Set(relevantMatches.map(m => m.competitionName)));
    setCompetitionList(['All', ...names]);
  }, [matchType, allMatches]);

  useEffect(() => {
    fetchMatches();
    const interval = setInterval(fetchMatches, 3000);
    return () => clearInterval(interval);
  }, [fetchMatches]);

  useEffect(() => {
    const matches = allMatches.filter(m => 
      m.type === matchType && 
      (selectedCompetition === 'All' || m.competitionName === selectedCompetition)
    );
    setFilteredMatches(matches);
  }, [allMatches, matchType, selectedCompetition]);

  const getAsiaOdds = (match: any) => {
    const asiaOdds = (match.matchOdds || []).find((odds: any) => odds.oddsType === 'asia');
    return asiaOdds?.oddsData || null;
  };

  // Get proper live timer using the same logic as other components
  const getLiveTimer = (match: any) => {
    const { matchStatus, matchActiveTime, secondHalfKickOffTime } = match;
    const serverTime = Date.now() / 1000; // Current time in seconds

    const { statusText } = getHomeCompetitionMatchTime({
      serverTime,
      matchStatus,
      matchActiveTime,
      secondHalfKickOffTime,
      currentGameTab: 1,
    });

    return statusText;
  };

  // Handle watch live click
  const handleWatchLive = async (match: any) => {
    const sport = location.pathname.includes('basketball') ? 'basketball' : 'football';
    const matchId = match.matchId || match.id
    const pathName = GlobalConfig.pathname;
    console.log('pathname', GlobalConfig.pathname)
    console.log('location origin', location.pathname)

    const url = `${location.origin}/${pathName}-match/${matchId}/MatchLive/?isplugin=true&pluginName=${pathName}LivePlugin&lang=en`;
  
    window.open(url, '_blank');
  };

  const handlePopularCompetitionClick = (competitionName: string) => {
    setSelectedCompetition(competitionName);
  };

  function chunkIntoPairs(array: any) {
    const result = [];
    for (let i = 0; i < array.length; i += 2) {
      result.push(array.slice(i, i + 2));
    }
    return result;
  }

  const pairedMatches = chunkIntoPairs(filteredMatches);
  console.log('paired matches', pairedMatches)

  return (
    <div className="plugin-football-home">

      <PopularCompetitions
        selectedCompetition={selectedCompetition}
        onCompetitionClick={handlePopularCompetitionClick}
        liveCompetitionIds={liveCompetitionIds}
      />

      <div className='right-content'>
        <div className="filter-bar">
          <div className="button-group">
            <Button
              className={matchType === 'live' ? 'active' : ''}
              style={{
                backgroundColor: matchType === 'live' ? 'red' : '#2c2c2c',
                borderColor: matchType === 'live' ? 'red' : '#2c2c2c',
                color: '#fff'
              }}
              onClick={() => setMatchType('live')}
            >
              Live
            </Button>
            <Button
              className={matchType === 'upcoming' ? 'active' : ''}
              style={{
                backgroundColor: matchType === 'upcoming' ? 'red' : '#2c2c2c',
                borderColor: matchType === 'upcoming' ? 'red' : '#2c2c2c',
                color: '#fff'
              }}
              onClick={() => setMatchType('upcoming')}
            >
              Upcoming
            </Button>
          </div>
          <Select
            className='custom-select-plugin'
            value={selectedCompetition}
            onChange={value => setSelectedCompetition(value)}
            placeholder="Select Competition"
            options={competitionList.map((item: string) => ({
              key: item,
              value: item,
              label: item
            }))}
          />
        </div>

        <Carousel dots={false} swipeToSlide draggable infinite={false} variableWidth>
          {pairedMatches.map((pair, index) => {
            return (
            <div key={index} className="carousel-column">
            {pair.map((match: any) => {
              const asiaOdds = getAsiaOdds(match);
              return (
                <div key={match.matchId} className="carousel-item">
                  <div className="container-title">
                    <span className='title-text'>{match.competitionName}</span>
                    <Popover content={match.vlive === 1 ? 'Watch Live' : 'Watch Animation'} placement="top">
                      <Button
                        type="text"
                        icon={<PlayCircleOutlined />}
                        className="action-btn"
                        onClick={() => handleWatchLive(match)}
                      />
                    </Popover>
                  </div>
                  <div className="container-body">
                    <div className='horizontal-content'>
                      <div className='match-time-section'>
                        {match.matchStatus === 8 || match.matchStatus === 3 ? (
                          <div className="match-time-container">
                            <span className="match-status">{match.matchStatus === 8 ? 'FT' : 'HT'}</span>
                          </div>
                        ) : match.matchStatus === 1 ? (
                          <div className="match-time">
                            <span className="hour-time">{moment.unix(match.matchTime).format('HH:mm')}</span>
                            <span className="date-time">{moment.unix(match.matchTime).format('DD/MM')}</span>
                          </div>
                        ) : (
                          <div className="match-score-container">
                            <span className="match-status live-timer">{getLiveTimer(match)}</span>
                          </div>
                        )} 
                      </div>
                      <div className='team-section'>
                        <div className='home-team'>
                          <img className="team-icon" loading="lazy" src={match.homeTeam?.logo} alt={match.homeTeam.name}/>
                          <span className='team-name'>{match.homeTeam.name}</span>
                        </div>
                        <div className='away-team'>
                          <img className="team-icon" loading="lazy" src={match.awayTeam?.logo} alt={match.awayTeam.name}/>
                          <span className='team-name'>{match.awayTeam.name}</span>
                        </div>
                      </div>
                      <div className='match-score-section'>
                        <span className="match-score">{match.calculatedHomeScore}</span> 
                        <span className='match-score'>{match.calculatedAwayScore}</span>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
            </div>)
          })}
        </Carousel>
      </div>
    </div>
  )
}

export default PluginHome;