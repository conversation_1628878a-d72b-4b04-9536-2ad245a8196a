import './pluginHome.less';
import './pluginShared.less';

import { Button, Carousel, Popover, Select } from 'antd';
import { getHomeBasketBallMatchTimeText, getHomeCompetitionMatchTime } from 'iscommon/utils/dataUtils';
import { useCallback, useEffect, useState } from "react";

import { getCompetitionList } from "iscommon/api/basketball/home";
import { GlobalConfig } from 'iscommon/const/globalConfig';
import moment from 'moment';
import { PlayCircleOutlined } from '@ant-design/icons';
import PopularCompetitions from './pluginPopular';

const PluginHome: React.FC = () => {
  const [allMatches, setAllMatches] = useState<any[]>([]);
  const [filteredMatches, setFilteredMatches] = useState<any[]>([]);
  const [competitionList, setCompetitionList] = useState<string[]>([]);
  const [selectedCompetition, setSelectedCompetition] = useState<string>('All');
  const [matchType, setMatchType] = useState<'live' | 'upcoming'>('live');
  const [liveCompetitionIds, setLiveCompetitionIds] = useState<Set<string>>(new Set());

  // const location = useLocation();
  const searchParams = new URLSearchParams(location.search);

  // Ensure plugin container has proper width when theme is not in URL
  useEffect(() => {
    const pluginContainer = document.getElementById('plugin_container_body')?.parentElement;
    if (pluginContainer && !searchParams.get('theme')) {
      pluginContainer.style.width = '1200px';
      pluginContainer.style.margin = '0 auto';
    }
  }, [searchParams]);
  
  const fetchMatches  = useCallback(async () => {
    try {
      // Fetch live matches (listType: 0) - using basketball API
      const apiParams = { listType: 0 };
      console.log('🔍 Plugin: Fetching live matches with params (basketball API):', apiParams);

      const response = await getCompetitionList(apiParams);
      console.log('🔍 Plugin: Full API response:', response);
      console.log('🔍 Plugin: Raw response type:', typeof response);
      console.log('🔍 Plugin: Response keys:', Object.keys(response));
      console.log('🔍 Plugin: First competition raw:', JSON.stringify(response.competitions[0], null, 2));

      const { competitions: liveCompetitions } = response;
      console.log('🔍 Plugin: live competitions received:', {
        count: liveCompetitions.length,
        competitions: liveCompetitions.map((comp: any) => ({
          id: comp.competitionId,
          name: comp.competitionName,
          matchCount: comp.matches?.length || 0,
          matches: comp.matches?.map((m: any) => ({ id: m.matchId, homeTeam: m.homeTeam?.name, awayTeam: m.awayTeam?.name })) || []
        }))
      });

      // Extract live competition IDs for sharing with PopularCompetitions
      const liveCompIds = new Set(liveCompetitions.map((comp: any) => comp.competitionId?.toString()).filter(Boolean)) as Set<string>;
      setLiveCompetitionIds(liveCompIds);

      const liveMatches = liveCompetitions.flatMap((comp: any) =>
        (comp.matches || []).map((match: any) => ({
          ...match,
          competitionName: comp.competitionName,
          type: 'live',
        }))
      );
  
      // Prepare dateFilter (today in ISO string with +08:00)
      const today = new Date();
      const dateFilter = new Date(today.getTime() - today.getTimezoneOffset() * 60000)
        .toISOString()
        .replace('Z', '+08:00');
  
      // Fetch upcoming matches (listType: 2 with dateFilter) - using basketball API
      const { competitions: upcomingCompetitions } = await getCompetitionList({ listType: 2, dateFilter });
      const upcomingMatches = upcomingCompetitions.flatMap((comp: any) =>
        (comp.matches || []).map((match: any) => ({
          ...match,
          competitionName: comp.competitionName,
          type: 'upcoming',
        }))
      )
      .sort((a: any, b: any) => a.matchTime - b.matchTime)
  
      const combined = [...liveMatches, ...upcomingMatches];
      setAllMatches(combined);

    } catch (err) {
      console.error('Failed to fetch matches:', err);
    }
  }, []);

  useEffect(() => {
    const relevantMatches = allMatches.filter(m => m.type === matchType);
    const names = Array.from(new Set(relevantMatches.map(m => m.competitionName)));
    setCompetitionList(['All', ...names]);
  }, [matchType, allMatches]);

  useEffect(() => {
    fetchMatches();
    const interval = setInterval(fetchMatches, 1000); // Update every 1 second for real-time odds
    return () => clearInterval(interval);
  }, [fetchMatches]);

  useEffect(() => {
    const matches = allMatches.filter(m =>
      m.type === matchType &&
      (selectedCompetition === 'All' || m.competitionName === selectedCompetition)
    );

    setFilteredMatches(matches);
  }, [allMatches, matchType, selectedCompetition]);

  // Extract and format handicap values and odds data from match odds
  const getHandicapAndOdds = (match: any) => {
    try {
      // Get the asia odds from matchOdds
      const asiaOdds = match.matchOdds?.find((odds: any) => odds.oddsType === 'asia');
      if (!asiaOdds || !asiaOdds.handicap || !asiaOdds.oddsData) {
        return {
          homeHandicap: null,
          awayHandicap: null,
          homeOdds: null,
          awayOdds: null
        };
      }

      const handicap = asiaOdds.handicap; // e.g., "0/0.5"
      const oddsData = asiaOdds.oddsData; // Array with odds values

      console.log('🎯 Processing handicap and odds:', { handicap, oddsData });

      // Handle different handicap formats
      let hdpValue;

      if (handicap.includes('/')) {
        // Format: "0/0.5", "0/1.5", etc.
        const parts = handicap.split('/');
        if (parts.length !== 2) {
          return {
            homeHandicap: null,
            awayHandicap: null,
            homeOdds: null,
            awayOdds: null
          };
        }
        hdpValue = parseFloat(parts[1]); // 0.5, 1.5, etc.
      } else {
        // Format: "0", "1", "-0.5", etc. (direct handicap value)
        hdpValue = parseFloat(handicap);
      }

      console.log('🎯 Parsed handicap value:', hdpValue);

      // Format handicap values - display even when hdpValue is 0
      const homeHandicap = hdpValue === 0 ? '0' : (hdpValue > 0 ? `+${hdpValue}` : `${hdpValue}`);
      const awayHandicap = hdpValue === 0 ? '0' : (hdpValue > 0 ? `-${hdpValue}` : `+${Math.abs(hdpValue)}`);

      // Extract odds data[0] and data[2]
      const homeOdds = oddsData[0] != null ? parseFloat(Number(oddsData[0]).toFixed(2)) : null;
      const awayOdds = oddsData[2] != null ? parseFloat(Number(oddsData[2]).toFixed(2)) : null;

      console.log('🎯 Formatted handicaps and odds:', {
        homeHandicap,
        awayHandicap,
        homeOdds,
        awayOdds
      });

      return { homeHandicap, awayHandicap, homeOdds, awayOdds };
    } catch (error) {
      console.error('❌ Error processing handicap and odds:', error);
      return {
        homeHandicap: null,
        awayHandicap: null,
        homeOdds: null,
        awayOdds: null
      };
    }
  };

  // Get proper live timer using sport-specific logic
  const getLiveTimer = (match: any) => {
    const serverTime = Date.now() / 1000; // Current time in seconds

    // Use sport-specific logic based on GlobalConfig.pathname
    if (GlobalConfig.pathname === 'basketball') {
      // Basketball uses remainSeconds and getHomeBasketBallMatchTimeText
      const { matchStatus, remainSeconds } = match;
      const { statusText } = getHomeBasketBallMatchTimeText({
        matchStatus,
        remainSeconds,
        serverTime,
        currentGameTab: 1,
      });
      return statusText;
    } else {
      // Football and other sports use matchActiveTime and getHomeCompetitionMatchTime
      const { matchStatus, matchActiveTime, secondHalfKickOffTime } = match;
      const { statusText } = getHomeCompetitionMatchTime({
        serverTime,
        matchStatus,
        matchActiveTime,
        secondHalfKickOffTime,
        currentGameTab: 1,
      });
      return statusText;
    }
  };

  // Handle watch live click
  const handleWatchLive = async (match: any) => {
    const matchId = match.matchId || match.id
    const pathName = GlobalConfig.pathname;

    const url = `${location.origin}/${pathName}-match/${matchId}/MatchLive?isplugin=true&pluginName=${pathName}LivePlugin&lang=en`;
  
    window.open(url, '_blank');
  };

  const handlePopularCompetitionClick = (competitionName: string) => {
    setSelectedCompetition(competitionName);
  };

  const getLiveMatchCount = () => {
    return allMatches.filter(m => m.type === 'live').length;
  };

  function chunkIntoPairs(array: any) {
    const result = [];
    for (let i = 0; i < array.length; i += 2) {
      result.push(array.slice(i, i + 2));
    }
    return result;
  }

  const pairedMatches = chunkIntoPairs(filteredMatches);

  return (
    <div className="plugin-football-home">

      <PopularCompetitions
        selectedCompetition={selectedCompetition}
        onCompetitionClick={handlePopularCompetitionClick}
        liveCompetitionIds={liveCompetitionIds}
      />

      <div className='right-content'>
        <div className="filter-bar">
          <div className="button-group">
            <Button
              className={matchType === 'live' ? 'active' : ''}
              style={{
                backgroundColor: matchType === 'live' ? 'red' : '#2c2c2c',
                borderColor: matchType === 'live' ? 'red' : '#2c2c2c',
                color: '#fff'
              }}
              onClick={() => setMatchType('live')}
            >
              Live{getLiveMatchCount() > 0 ? ` (${getLiveMatchCount()})` : ''}
            </Button>
            <Button
              className={matchType === 'upcoming' ? 'active' : ''}
              style={{
                backgroundColor: matchType === 'upcoming' ? 'red' : '#2c2c2c',
                borderColor: matchType === 'upcoming' ? 'red' : '#2c2c2c',
                color: '#fff'
              }}
              onClick={() => setMatchType('upcoming')}
            >
              Upcoming
            </Button>
          </div>
          <Select
            className='custom-select-plugin'
            value={selectedCompetition}
            onChange={value => setSelectedCompetition(value)}
            placeholder="Select Competition"
            options={competitionList.map((item: string) => ({
              key: item,
              value: item,
              label: item
            }))}
          />
        </div>

        <Carousel dots={false} swipeToSlide draggable infinite={false} variableWidth>
          {pairedMatches.map((pair, index) => {
            return (
            <div key={index} className="carousel-column">
            {pair.map((match: any) => {
              return (
                <div key={match.matchId} className="plugin-carousel-item">
                  <div className="container-title">
                    <span className='title-text'>{match.competitionName}</span>
                    <Popover content={match.vlive === 1 ? 'Watch Live' : 'Watch Animation'} placement="top">
                      <Button
                        type="text"
                        icon={<PlayCircleOutlined />}
                        className="action-btn"
                        onClick={() => handleWatchLive(match)}
                      />
                    </Popover>
                  </div>
                  <div className="container-body">
                    <div className='horizontal-content'>
                      <div className='match-time-section'>
                        {(() => {
                          const isBasketball = GlobalConfig.pathname === 'basketball';
                          const isFootballHalfTime = !isBasketball && match.matchStatus === 3; // HT only for football
                          const isNotStarted = match.matchStatus === 1; // Not started is same for both sports

                          if (isFootballHalfTime) {
                            return (
                              <div className="match-time-container">
                                <span className="match-status">HT</span>
                              </div>
                            );
                          } else if (isNotStarted) {
                            return (
                              <div className="match-time">
                                <span className="hour-time">{moment.unix(match.matchTime).format('HH:mm')}</span>
                                <span className="date-time">{moment.unix(match.matchTime).format('DD/MM')}</span>
                              </div>
                            );
                          } else {
                            return (
                              <div className="match-score-container">
                                <span className="match-status live-timer">{getLiveTimer(match)}</span>
                              </div>
                            );
                          }
                        })()}
                      </div>
                      <div className='team-section'>
                        <div className='home-team'>
                          <img className="team-icon" loading="lazy" src={match.homeTeam?.logo} alt={match.homeTeam.name}/>
                          <span className='team-name'>{match.homeTeam.name}</span>
                        </div>
                        <div className='away-team'>
                          <img className="team-icon" loading="lazy" src={match.awayTeam?.logo} alt={match.awayTeam.name}/>
                          <span className='team-name'>{match.awayTeam.name}</span>
                        </div>
                      </div>
                      <div className='match-score-section'>
                        <span className="match-score">{match.calculatedHomeScore}</span>
                        <span className='match-score'>{match.calculatedAwayScore}</span>
                      </div>
                    </div>
                    {(() => {
                      const { homeHandicap, awayHandicap, homeOdds, awayOdds } = getHandicapAndOdds(match);
                      // Display betting section if we have handicap data (including '0') or odds data
                      if (homeHandicap !== null && awayHandicap !== null) {
                        return (
                          <div className='betting-section'>
                            <div className="handicap-section">
                              <div className="pill">
                                <span className="handicap-value home-handicap">{homeHandicap}</span>
                                <span className="label">HDP</span>
                                <span className="handicap-value away-handicap">{awayHandicap}</span>
                              </div>
                            </div>
                            {homeOdds !== null && awayOdds !== null && (
                              <div className="odds-section">
                                <div className="pill">
                                  <span className="odds-value home-odds">{homeOdds}</span>
                                  <span className="label">Odd</span>
                                  <span className="odds-value away-odds">{awayOdds}</span>
                                </div>
                              </div>
                            )}
                          </div>
                        );
                      }
                      return null;
                    })()}
                  </div>
                </div>
              );
            })}
            </div>)
          })}
        </Carousel>
      </div>
    </div>
  )
}

export default PluginHome;