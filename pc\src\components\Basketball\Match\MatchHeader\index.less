.container {
  flex: 1;
  display: flex;
  flex-direction: column;
  // background: #fff;
}

.competition {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 36px;
  padding-top: 20px;
  font-size: 12px;
  color: #333333;
  line-height: 12px;
  background: #1E1E1E;

  .collect {
    margin-left: 20px;
  }
}

.header {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 24px 0;

  .team {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    align-items: center;

    &.right {
      justify-content: flex-start;
    }

    .teamName {
      padding: 0 20px;
      font-size: 24px;
      font-weight: 500;
      color: #333333;
      line-height: 24px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .teamNum {
      width: 110px;
      font-family: "Urbanist-Bold", sans-serif;
      font-size: 40px;
      font-weight: 500;
      color: #333333;
      line-height: 40px;
      text-align: center;
    }
  }

  .pk {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 205px;

    .vs {
      height: 40px;
      line-height: 1;
      text-align: center;
      font-family: "Urbanist-Bold", sans-serif;
      font-size: 40px;
      font-weight: 700;
      color: #333;
    }

    .progress {
      display: flex;
      justify-content: center;
      font-size: 12px;
      color: #c72a1d;

      .pkTime {
        margin-right: 3px;
      }

      .timeSymbol {
        animation: Twinkle 1s infinite;
      }
    }

    .result {
      font-size: 14px;
      font-family: "Urbanist-Regular", sans-serif;
      color: #bbb;
    }
  }
}

@keyframes Twinkle {
  0% {
    opacity: 0
  }

  25% {
    opacity: 1
  }

  50% {
    opacity: 0
  }

  75% {
    opacity: 1
  }

  to {
    opacity: 0
  }
}

// .match-header-container {
//   flex: 1;
//   display: flex;
//   flex-direction: column;
//   background: #1E1E1E;
//   border-radius: 16px 16px 0px 0px;
//   height: 230px;

//   .competition {
//     display: flex;
//     justify-content: center;
//     align-items: center;
//     width: 100%;
//     height: 36px;
//     padding-top: 20px;
//     font-size: 12px;
//     color: #fff;
//     line-height: 12px;
//     background: #1E1E1E;
  
//     .collect {
//       margin-left: 20px;
//     }
//   }

//   .match-result-container {
//     flex: 1;
//     display: flex;
//     justify-content: center;
//     align-items: center;
//     padding: 24px 0;

//     .team-logo {
//       width: 80px;
//       height: 88px;
//       object-fit: fill;
//       margin: 0px 16px;
//       border-radius: 50%;
//     }

//     .team-name {
//       color: #fff;
//       font-size: 18px;
//       font-weight: bold;
//     }

//     .team-score {
//       // width: 110px;
//       // font-family: Rubik-Bold;
//       font-size: 32px;
//       font-weight: 500;
//       color: #fff;
//       line-height: 32px;
//       text-align: center;
//     }

//     .score-container {
//       display: flex;
//       align-items: center;
//       flex-direction: column;

//       .not-started {
//         font-size: 32px;
//         font-weight: bold;
//       }

//       .score-result {
//         display: flex;
//         flex-direction: row;

//         .progress {
//           display: flex;
//           justify-content: center;
//           font-size: 16px;
//           color: #FF3131;
//         }
      
//         .pk-time {
//           margin-right: 3px;
//           color: #fff;
//         }

//         .result {
//           font-size: 16px;
//           color: #fff;
//         }
//       }
//     }
//   }
// }

.match-header-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: #1E1E1E;
  border-radius: 16px 16px 0px 0px;
  height: 230px;

  &.not-started {
    .match-result-container {
      .team-score {
        font-size: 32px;
        font-weight: bold;
      }
      .score-container .not-started {
        font-size: 32px;
        font-weight: bold;
        color: #fff;
      }
    }
  }

  &.in-progress {
    .match-result-container {
      .team-score.color-red {
        color: #c1272d;
      }
      .pk-time.color-red {
        color: #c1272d;
        margin-top: 8px;
      }
    }
  }

  &.ended {
    .match-result-container {
      .team-score {
        font-size: 32px;
        font-weight: 500;
        color: #fff;
      }
      .pk-time {
        font-size: 16px;
        color: #fff;
        margin-top: 8px;
      }
    }
  }

  .competition {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 36px;
    padding-top: 20px;
    font-size: 12px;
    color: #fff;
    line-height: 12px;
    background: #1E1E1E;
  
    .collect {
      margin-left: 20px;
    }
  }

  .match-result-container {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 24px 0;

    .team-container {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      &.right {
        justify-content: flex-start;
      }

      .team-logo {
        width: 80px;
        height: 88px;
        object-fit: fill;
        margin: 0px 16px;
        border-radius: 50%;
      }
  
      .team-name {
        color: #fff;
        font-size: 18px;
        font-weight: bold;
      }
    }

    .team-score {
      font-size: 32px;
      font-weight: 500;
      color: #fff;
      line-height: 32px;
      text-align: center;
      margin: 0px 5px;
    }

    .score-container {
      display: flex;
      flex-direction: column;
      align-items: center;

      .not-started {
        font-size: 32px;
        font-weight: bold;
      }

      .match-score {
        display: flex;
        flex-direction: row;
      }

      .pk-time {
        font-size: 16px;
        margin-top: 8px;
        color: #fff;

        &.color-red {
          color: #c1272d;
        }
      }
    }
  }
}