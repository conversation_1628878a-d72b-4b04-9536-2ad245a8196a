.plugin-football-home {
  display: flex;
  flex-direction: row;
  padding: 20px 16px;
  background: #1a1a1a;

  .right-content {
    flex: 1;
    min-width: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    padding: 0px 20px;

    .filter-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      padding: 12px;
      background: transparent;
      border: 1px solid #63717a;
      border-radius: 12px;
      max-width: 100%;
      overflow: visible;

      .button-group {
        display: flex;
        gap: 12px;

        .ant-btn {
          padding: 8px 20px;
          height: auto;
          border-radius: 8px;
          font-weight: 600;
          transition: all 0.3s ease;

          &:hover {
            opacity: 0.8;
          }
        }
      }

      .custom-select-plugin {
        width: 170px;
        margin: 0px 5px;

        .ant-select-selector {
          background: transparent !important;
          border-radius: 8px !important;
          color: #fff !important;
        }

        .ant-select-arrow .anticon {
          color: #fff !important;
        }
      }
    }

    .ant-carousel {
      width: 100%;
      cursor: grab;
      border: 1px solid #63717a;
      padding: 12px;
      border-radius: 12px;

      &:active {
        cursor: grabbing;
      }
      
      .slick-list {
        overflow: hidden; 
      }
    }

    .ant-carousel .slick-list .slick-slide {
      pointer-events: auto;
    }

    .carousel-column {
      // display: flex;
      // flex-direction: column;
      // justify-content: space-between;
      width: 250px !important;
      margin-right: 12px;

      .carousel-item {
        margin-bottom: 12px;
        border-radius: 16px;
        background: #2c2c2c;
        color: #fff;

        .container-title {
          font-weight: 600;
          padding: 6px;
          border-radius: 16px 16px 0px 0px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 1px solid #63717a;

          .title-text {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .action-btn {
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            border: 1px solid #fff;
            transition: all 0.3s ease;
            color: #fff;

            &:hover {
              background: rgba(255, 255, 255, 0.2);
              border-color: rgba(255, 255, 255, 0.4);
              transform: translateY(-2px);
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            }

            .anticon {
              font-size: 12px;
            }
          }
        }

        .container-body {
          position: relative;
          display: flex;
          flex-direction: column;
          padding: 6px;
          border-radius: 0px 0px 16px 16px;
          color: #fff;

          &::before,
          &::after {
            content: '';
            position: absolute;
            height: 2px;
            width: 60%;
            background: linear-gradient(90deg, transparent, #03cfea, transparent);
            animation: neon-glow 3s linear infinite;
            pointer-events: none;
          }

          &::before {
            top: 2px;
            left: 0;
            transform: translateY(-50%);
          }
        
          &::after {
            bottom: 2px;
            right: 0;
            transform: translateY(50%) rotate(180deg);
          }

          .horizontal-content {
            display: flex;
            align-items: center;
            width: 100%;

            .match-time-section {
              width: 36px;
              display: flex;
              justify-content: center;
              align-items: center;
              margin-right: 5px;

              .match-time {
                display: flex;
                flex-direction: column;
                text-align: center;
                font-size: 12px;
              }

              .match-status {
                font-size: 12px;
                font-weight: 600;
                color: red;
              }

              .live-timer {
                animation: twinkle 2.5s infinite;
              }
            }

            .team-section {
              display: flex;
              flex-direction: column;
              gap: 12px;
              justify-content: center;
              width: 180px;

              .home-team,
              .away-team {
                display: flex;
                align-items: center;
                gap: 6px;

                .team-icon {
                  width: 24px;
                  height: 24px;
                  border-radius: 50%;
                  object-fit: cover;
                }

                .team-name {
                  font-size: 14px;
                  color: #fff;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
              }
            }

            .match-score-section {
              display: flex;
              flex-direction: column;
              gap: 12px;

              .match-score {
                text-align: center;
                font-weight: 500;
                font-size: 14px;
                color: #fff;
                width: 20px;
              }
            }
          }

          .action-buttons {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            margin-top: 8px;

            .action-btn {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 28px;
              height: 28px;
              border-radius: 50%;
              border: 1px solid #fff;
              transition: all 0.3s ease;
              color: #fff;

              &:hover {
                background: rgba(255, 255, 255, 0.2);
                border-color: rgba(255, 255, 255, 0.4);
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
              }

              .anticon {
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
}

.ant-select-dropdown {
  background: #383838;
  border-radius: 16px;

  .ant-select-item-option-selected {
    background-color: lighten(#383838, 20%) !important;
    color: #fff
  }

  .ant-select-item-option-active {
    background-color: lighten(#383838, 15%) !important;
    color: #fff
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

@keyframes neon-glow {
  0% {
    opacity: 0;
    transform: scaleX(0.5) translateX(-50%);
  }
  50% {
    opacity: 1;
    transform: scaleX(1) translateX(0%);
  }
  100% {
    opacity: 0;
    transform: scaleX(0.5) translateX(50%);
  }
}