// .container {
//   display: flex;
//   flex-direction: column;
//   width: 100%;
// }

// .select_data_container {
//   width: 100%;
//   height: 72px;
//   display: flex;
//   align-items: center;
//   background-color: #fff;
//   margin-top: 16px;
//   padding: 0 13px;
// }

// .select_team_style {
//   display: flex;
//   flex-direction: row;
//   justify-content: space-between;
//   flex: 1;
//   align-items: center;
// }

// .team_box {
//   display: flex;
//   flex-direction: row;
// }

// .select_year_style {
//   display: flex;
//   flex-direction: row;
//   justify-content: space-between;
//   align-items: center;
//   padding-left: 0.32rem;
//   // margin-left: 0.32rem;
//   position: relative;
//   width: 312px;
// }

// .select_year_style:nth-child(2):before {
//   border-left: 1px solid #e3e3e3;
//   margin-top: -0.26rem;
//   position: absolute;
//   height: 0.53rem;
//   content: '';
//   left: 1px;
//   top: 50%;
// }

// .transfer_type_style {
//   padding: 15px 20px;
//   display: flex;
//   flex-direction: row;
//   align-items: center;
// }

// .competition_name {
//   max-width: 680px;
//   overflow: hidden; //超出的文本隐藏
//   text-overflow: ellipsis; //溢出用省略号显示
//   white-space: nowrap; //溢出不换行
//   margin-left: 24px;
// }

// .stats_container {
//   display: flex;
//   flex-direction: column;
//   width: 100%;
//   color: #999;
// }

// .list_item {
//   height: 100%;
//   padding: 0 20px;
//   height: 60px;
//   display: flex;
//   align-items: center;
//   color: #333;
//   justify-content: space-between;
// }

// .list_item:not(:first-child) {
//   background: white;
// }

// .color_333 {
//   color: #333;
// }

// .color_999 {
//   color: #999;
// }

.team-standing-tab-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;

  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;

    .container-title-right-aligned {
      display: flex;
      justify-content: flex-end; 
      align-items: center;

      .button {
        background-color: #121212;
        color: #fff;
        border: none;
        margin-right: 8px;
        padding: 4px 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        border-radius: 16px;
        font-size: 24px;
      
        &.active {
          background-color: #fff;
          color: #000;
        }
      
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .container-body {
    background: #1e1e1e;
    padding: 10px;

    .custom-standing-table {
      width: 100%;
      display: flex;
      flex-direction: column;
      border-collapse: collapse;
    
      .header-row, .table-row {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #2c2c2c;
        text-align: left;
        color: #fff;
      }

      .header-row {
        background-color: #1E1E1E;
        font-weight: bold;
      }

      .table-row {
        background-color: #121212;
      }

      .header-cell, .table-cell {
        flex: 0.5;
        text-align: center;
        padding: 10px;
        font-size: 24px;
        align-content: center;
      }

      .header-cell:first-child, 
      .table-cell:first-child {
        flex: 0.5;
      }

      .header-cell:nth-child(2), 
      .table-cell:nth-child(2) {
        flex: 3;  
      }

      .team-cell {
        display: flex;
        align-items: center;
        flex: 3;
        text-align: left;

        .team-logo {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin: 5px 10px;
          overflow: hidden;
          background-size: cover;
        }
  
        .team-name {
          font-size: 24px;
          color: #fff;
        }
      }
    }
  }
}