.basketball-player-stats-table-container {
  margin-top: 16px;
  width: 100%;
  overflow-x: scroll;

  .custom-stats-table {
    width: 100%;
    display: flex;
    flex-direction: column;
    border-collapse: collapse;
    margin-bottom: 10px;

    .table-header {
      display: flex;
      color: #fff;
      background-color: #2c2c2c;
      font-weight: bold;
      border-bottom: 1px solid #121212;

      .header-cell {
        padding: 8px;
        text-align: center;
        background-color: #2c2c2c;
      }

      .player-cell {
        position: sticky; 
        left: 0;
        z-index: 11; 
        width: 300px;
        flex-shrink: 0;
        background-color: inherit;
        text-align: left;
      }

      .header-cell:not(.player-cell) {
        padding: 8px;
        flex: 1;
        min-width: 10%;
      }
    }

    .table-body {
      .table-row {
        display: flex;
        align-items: center;
        background-color: #121212;
        border-bottom: 1px solid #2c2c2c;
        color: #fff;

        .table-cell {
          text-align: center;
          padding: 8px;
          background-color: #121212;
        }

        .player-cell {
          display: flex;
          flex-direction: row;
          align-items: center;
          position: sticky;
          left: 0;
          z-index: 10;
          background-color: inherit;
          padding: 8px;
          width: 300px;
          flex-shrink: 0;

          .player-icon {
            width: 58px;
            height: 58px;
            border-radius: 50%;
            margin: 5px 10px;
            background-size: cover;
          }

          .player-info {
            display: flex;
            flex-direction: column;
            justify-content: space-between;

            .player-name {
              font-size: 22px;
              font-weight: 600;
              color: #fff;
              text-overflow: ellipsis;
              overflow: hidden;
              text-align: left;
            }

            .player-team-detail {
              display: flex;
              align-items: center;

              .team-icon {
                width: 35px;
                height: 35px;
                border-radius: 50%;
                margin-right: 10px;
                background-size: cover;
              }

              .team-name {
                font-size: 18px;
                color: #fff;
                text-overflow: ellipsis;
                overflow: hidden;
              }
            }
          }

          .team-name {
            font-size: 20px;
            font-weight: 600;
            color: #fff;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: left;
          }
        }

        .table-cell:not(.player-cell) {
          padding: 8px;
          flex: 1;
          min-width: 10%;
        }
      }
    }

    .pagination {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 20px 0px;
      color: #fff;
    
      button {
        margin: 0 10px;
        padding: 5px 10px;
        background-color: #383838;
        color: white;
        border: none;
        cursor: pointer;
        
        &:disabled {
          background-color: #383838;
          cursor: not-allowed;
        }
      }
    }
  }
}