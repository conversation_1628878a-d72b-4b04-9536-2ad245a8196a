// @ts-nocheck
import _ from 'lodash';

import store from 'iscommon/mobx';
import { getArrayFromString } from 'iscommon/utils';

let tempScorePool = {}
const tempScorePoolLength = 30
let delayScoreTimer = null
const timeSeconds = 3000

export const startTimer = () => {
  if(delayScoreTimer) clearTimeout(delayScoreTimer)
  delayScoreTimer = setTimeout(() => {
    console.log('定时补偿更新', '更新一次score数据', Object.keys(tempScorePool).length)
    delaySyncHomeScore()
  }, timeSeconds)
}

const delaySyncHomeScore = () => {

  // 定时补偿更新
  startTimer()

  if(Object.keys(tempScorePool).length === 0) return
  
  // 批量更新
  const tempListMap = JSON.parse(JSON.stringify(tempScorePool))
  tempScorePool = {}

  const { Basketball: {WebHome} } = store;
  const homeCompetitions = _.cloneDeep(WebHome.homeCompetitions);
  const allStatusMap = _.cloneDeep(WebHome.latestMatchStatus);
  if (!homeCompetitions || homeCompetitions.length === 0) {
    return false;
  }

  // ["1l4rjnhj9kn9m7v", 8, remainSeconds, [1, 0, 0, 0, -1, 0, 0], [1, 0, 0, 0, -1, 0, 0], 0, ""]
  for (let c of homeCompetitions) {
    for (let match of c.matches) {
      const { matchId } = match;
      if (tempListMap[matchId]) {
        const {score} = tempListMap[matchId]
        if(!score || score.length <= 0) continue
        // calculatedAwayScore, calculatedHomeScore homeScores awayScores matchStatus
        const matchStatus = score[1];
        const homeScores = getArrayFromString(score[3]);
        const awayScores = getArrayFromString(score[4]);

        match.matchStatus = matchStatus;
        allStatusMap[matchId] = matchStatus
        match.homeScores = homeScores;
        match.awayScores = awayScores;
        match.remainSeconds = Math.max(score[2], 0);
        break;
      }
    }
  }
  WebHome.setHomeCompetitions(homeCompetitions);
  WebHome.setLatestMatchStatus({allStatusMap});

}

const syncHomeScore = (data) => {
  const { id } = data;
  // console.log('score', data)
  tempScorePool[id] = data
  if(Object.keys(tempScorePool).length >= tempScorePoolLength) {
    console.log('数据积压直接更新', '更新一次score数据', Object.keys(tempScorePool).length)
    delaySyncHomeScore()
  }
};

// noinspection JSUnusedLocalSymbols
const syncIncidents = (data) => {
  // console.log('incidents', data)
  // incidents = [
  //   {type: 9, position: 1, time: 15},
  //   {type: 1, position: 1, time: 25},
  //   {type: 3, position: 1, time: 30}
  // ]
};

// the stats data is same as the scores data
// noinspection JSUnusedLocalSymbols
const syncStats = (data) => {
  // console.log('stats', data)
  // stats = [
  //   {type: 3, home: 2, away: 2},
  //   {type: 23, home: 74, away: 68},
  //   {type: 2, home: 3, away: 5}
  // ]
  // const { WebHome } = store;
  // const { id, stats = [] } = data;
  // if (WebHome.homeCompetitions && WebHome.homeCompetitions.length && stats.length > 0) {
  //   let cornerStat = null;
  //   let redCardStat = null;
  //   let htStat = null;
  //   for (const stat of stats) {
  //       if (stat.type === 2) {
  //           cornerStat = stat;
  //       } else if (stat.type === 4) {
  //           redCardStat = stat;
  //       } else if (stat.type === 13) {
  //           htStat = stat;
  //       }
  //   }
  //   if (cornerStat || redCardStat || htStat) {
  //     const homeCompetitions = _.cloneDeep(WebHome.homeCompetitions);
  //     for (const league of homeCompetitions) {
  //       for (const match of league.matches) {
  //         const { matchId } = match;
  //         if (matchId === id) {
  //           if (htStat) {
  //             match.homeScores[1] = htStat.home;
  //             match.awayScores[1] = htStat.away;
  //           }
  //           if (redCardStat) {
  //             match.homeScores[2] = redCardStat.home;
  //             match.awayScores[2] = redCardStat.away;
  //           }
  //           if (cornerStat) {
  //             match.homeScores[4] = cornerStat.home;
  //             match.awayScores[4] = cornerStat.away;
  //           }
  //           break;
  //         }
  //       }
  //     }
  //     WebHome.setHomeCompetitions(homeCompetitions);
  //   }
  // }
};

const mixinCompetitionOdds = (matchRecentOdds, oldOdds = {}, oldStatus = {}) => {
  // console.log(matchRecentOdds, oldOdds, 'matchRecentOdds')
  const cloneList = _.cloneDeep(oldOdds);
  const allStatusMap = _.cloneDeep(oldStatus)
  for(let matchId in matchRecentOdds) {
    const homeValidOdds = matchRecentOdds[matchId] ? matchRecentOdds[matchId].filter(item => item.companyId == 2) : []
    if(cloneList[matchId] && Array.isArray(homeValidOdds)) {
      if(homeValidOdds.length > 0) {
        const {matchStatus} = homeValidOdds[0]
        allStatusMap[matchId] = matchStatus
        for(let h of homeValidOdds) {
          const { oddsType } = h;
          for (let i = 0; i < cloneList[matchId].length; i++) {
            const item = cloneList[matchId][i];
            if (oddsType === item.oddsType) {
              cloneList[matchId][i] = {
                ...h,
                lastOddsData: item.oddsData
              };
            }
          }
        }
      }
    } else {
      cloneList[matchId] = homeValidOdds;
    }
  }
  return {cloneList, allStatusMap};
}

const syncHomeOdds = (matchRecentOdds) => {
  const {  Basketball: {WebHome}  } = store;
  const { homeCompetitionOdds, latestMatchStatus } = WebHome;
  const {cloneList, allStatusMap} = mixinCompetitionOdds(matchRecentOdds, homeCompetitionOdds, latestMatchStatus);
  WebHome.setHomeOdds(cloneList);
  WebHome.setLatestMatchStatus({allStatusMap});
};

export const homeDataControllerType = {
  score: 'score',
  stats: 'stats',
  incidents: 'incidents',
  syncHomeOdds: 'syncHomeOdds',
};

const homeDataController = {
  [homeDataControllerType.score]: syncHomeScore,
  [homeDataControllerType.stats]: syncStats,
  [homeDataControllerType.incidents]: syncIncidents,
  [homeDataControllerType.syncHomeOdds]: syncHomeOdds,
};

export default homeDataController;
