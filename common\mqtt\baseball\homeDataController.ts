// @ts-nocheck
import _ from 'lodash';

import store from 'iscommon/mobx';
import { getArrayFromString } from 'iscommon/utils';


const delaySyncHomeScore = (data) => {

  const {id, score} = data

  if(!score || score.length === 0) return

  const { Smallball: { SmallballCommon: WebHome} } = store;
  const homeCompetitions = _.cloneDeep(WebHome.homeCompetitions);
  const allStatusMap = _.cloneDeep(WebHome.latestMatchStatus);
  if (!homeCompetitions || homeCompetitions.length === 0) {
    return false;
  }

//   Score field description
// [
//   "dn1m16sk04vproe",//Match id
//   437,//Match status, please refer to Status code->Match state
//   1,//Batting team (stick), 1. Home team, 2. Away team, 0. Unknown
//   {
//       "ft": [//Regular score
//           "0",//Home team score
//           "0" //Away team score
//       ],
//       "p1": [//p* - Single inning score(* - Number of inning, 1、2、3...)
//           "0",//Home team score
//           "0" //Away team score
//       ],
//       "p2": [
//           "0",
//           "0"
//       ],
//       "p3": [
//           "0",
//           "0"
//       ],
//       "h": [//h - Hit, the total number of hits
//           "1",
//           "1"
//       ]
//   }
// ]
  for (let c of homeCompetitions) {
    for (let match of c.matches) {
      const { matchId } = match;
      if (matchId === id) {
        // calculatedAwayScore, calculatedHomeScore homeScores awayScores matchStatus
        const matchStatus = score[1];
        match.matchStatus = matchStatus;
        allStatusMap[matchId] = matchStatus
        match.scores = score[3];
        match.servingSide = score[2];
        break;
      }
    }
  }
  WebHome.setHomeCompetitions(homeCompetitions);
  WebHome.setLatestMatchStatus({allStatusMap});

}

const syncHomeScore = (data) => {
  delaySyncHomeScore(data)
};

export const homeDataControllerType = {
  score: 'score',
};

const homeDataController = {
  [homeDataControllerType.score]: syncHomeScore,
};

export default homeDataController;
