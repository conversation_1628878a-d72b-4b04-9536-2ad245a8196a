.game-category { 
  width: 100%;
  border-radius: 16px;
  padding: 0px 16px;
  align-items: center;
  justify-content: space-between;
  display: flex;
  transition: all 0.3s ease;
  font-size: 13px;
  margin: 16px 0px;

  .competition-header-left {
    .teamLogo {
      width: 20px;
      height: 20px;
      object-fit: cover;
      margin: 0 6px 0 3px;
      border-radius: 50%;
    }
  
    &:hover {
      transform: translateX(5px);
    }
  
    .textWhite {
      color: #fff;
    }
  
    .countryName {
      color: #999999;
    }
  
    .score {
      width: 56px;
      justify-content: flex-end;
      color: #fff;
  
      .hot {
        width: 14px;
        height: 14px;
        margin-right: 2px;
      }
    }
  }

  .competition-header-right {
    display: flex;
    align-items: center;

    .oddList {
      display: flex;
      flex-direction: row;
      width: 170px;
      background-color: transparent;

      &.oddTitle {
        background: transparent;
      }

      .oddItem {
        flex: 1;
        display: flex;            // ✅ Make it a flex container
        justify-content: center;  // ✅ Center horizontally
        align-items: center;  
        color: #fff;

        &.handicap {
          flex: auto;
          width: 100%;
          color: #f60;
        }
      }
    }

    .score {
      width: 56px;
      justify-content: center;
      color: #fff;
  
      .hot {
        width: 14px;
        height: 14px;
        margin-right: 2px;
      }
    }
  }
}

.game-category-football {
  width: 100%;
  height: 80px;
  background: #121212;
  border-radius: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  box-sizing: border-box;
  padding: 0px 16px;
  color: #333;
  cursor: pointer;
  margin-bottom: 12px;
  transition: all 0.3s ease;

  div {
    display: flex;
    height: 100%;
    align-items: center;
  }

  .teamLogo {
    width: 20px;
    height: 20px;
    object-fit: cover;
    margin: 0 6px 0 3px;
    border-radius: 50%;
  }

  &:hover {
    transform: translateX(5px);
  }

  .textWhite {
    color: #fff;
  }

  .countryName {
    color: #999999;
  }

  .oddList {
    width: 170px;
    background-color: transparent;

    &.oddTitle {
      background: transparent;
    }

    .oddItem {
      flex: 1;
      justify-content: center;
      color: #fff;

      &.handicap {
        flex: auto;
        width: 100%;
        color: #f60;
      }
    }
  }

  .score {
    width: 56px;
    justify-content: flex-end;
    color: #fff;

    .hot {
      width: 14px;
      height: 14px;
      margin-right: 2px;
    }
  }

  &.listItem {
    height: 80px;
    // background: #fff;
    background: transparent;
    // border-bottom: 1px solid #eee;

    &:hover {
      background-color: #ECF5FC;

      .oddList {
        background: #ECF5FC;
      }
    }
  }

  .time {
    width: 64px;

    &.ing {
      color: #c1272d;
    }
  }

  .timeStatus {
    width: 80px;
    color: #fff;

    &.ing {
      color: #c1272d;
    }
  }

  .vsCountry {
    overflow: hidden;
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    margin-right: 45px;

    .teamName {
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      flex: 1;       
      min-width: 0;
      margin-right: 5px;
      color: #fff;
      
      &.right {
        text-align: right;
      }
    }

    .vsCountryItem {
      flex: 1;
      overflow: hidden;
      margin-left: 10px;
      color: #fff;

      &.left {
        justify-content: flex-end;
      }

      &.right {
        justify-content: flex-start;
        margin-right: 10px;
        margin-left: 0;
      }

      .card {
        text-indent: -0.5px;
        text-align: center;
        border-radius: 2px;
        line-height: 12px;
        font-size: 12px;
        width: 10px;
        color: #fff;
        margin: 0 1px;

        &.red {
          background-color: #c1272d;
        }

        &.yellow {
          background-color: #ffa830
        }
      }
    }
  }

  .currentScore {
    flex: 1;
    font-weight: 500;
    color: #fff;
    justify-content: flex-start;
    flex-basis: 240px;
    flex-shrink: 0;
    .scoreItem{
      display: flex;
      flex-direction: column;
      justify-content: center;
      color: #999;
      width: 134px;
      .tennisIcon{
        width: 16px;
        height: 16px;
      }
      >div{
        width: 34px;
        text-align: center;
        position: relative;
        >span{
          display: inline-block;
          width: 16px;
          height: 16px;
          text-align: left;
          position: absolute;
          top: -2px;
          right: 10px;
          font-size: 12px;
        }
      }
      .spt{
        color: rgb(83, 176, 48)
      }
      .bigScore{
        font-weight: bolder!important;
        width: 134px;
        text-align: center;
        margin: 0 12px 0 4px;
      }
    }
  }

  .vsscore {
    display: flex;
    align-items: center;
    margin-right: 4px;
    justify-content: flex-end;
    color: #999;

    .vshc {
      width: 110px;
    }

    .iconWrap {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      width: 26px;
    }

    .icon-donghuazhibo {
      color: #ffa830;
    }

    .icon-corner {
      margin-left: 3px;
    }

    .icon-shipinzhibo {
      color: #c72a1d
    }

    .vsscoreht {
      display: flex;
      align-items: center;
      justify-content: flex-start;

      &.halfBox {
        width: 50px;
      }

      &.cornerBox {
        width: 60px;
      }
    }

    .textLeft {
      margin-left: -20px;
    }
  }

  .vsScoreText {
    overflow: hidden;
    flex-direction: column;
    align-items: flex-start;
    width: 20%;
    margin-right: 45px;
    color: #fff;
  }
}

.spaceBetween {
  display: flex;
  justify-content: space-between;
}

.paddingTop {
  padding-top: 3px;
}

// .vsCountry {
//   display: flex;
//   flex-direction: column;
//   justify-content: space-between;
//   align-items: flex-start;
//   padding: 10px;
//   background-color: transparent; /* Black background */
//   color: #fff; /* White text */
// }

// .teamColumn {
//   display: flex;
//   flex-direction: column;
//   justify-content: space-between;
// }

// .teamInfo {
//   display: flex;
//   align-items: center;
//   margin-right: 10px;
// }

// .teamLogo {
//   width: 24px;
//   height: 24px;
//   margin-right: 10px;
//   border-radius: 50%;
//   object-fit: cover;
// }

// .teamName {
//   font-weight: bold;
//   margin-right: 5px;
//   color: #fff;
// }

// .teamPosition {
//   margin-right: 10px;
//   color: #999; /* Grey color for position */
// }

// .scoreColumn {
//   display: flex;
//   flex-direction: column;
//   justify-content: space-between;
// }

// .currentScore {
//   flex: 1;
//   font-weight: 500;
//   color: #999;
//   justify-content: flex-start;
//   flex-basis: 240px;
//   flex-shrink: 0;
//   .scoreItem{
//     display: flex;
//     flex-direction: column;
//     justify-content: center;
//     color: #999;
//     width: 134px;
//     .tennisIcon{
//       width: 16px;
//       height: 16px;
//     }
//     >div{
//       width: 34px;
//       text-align: center;
//       position: relative;
//       >span{
//         display: inline-block;
//         width: 16px;
//         height: 16px;
//         text-align: left;
//         position: absolute;
//         top: -2px;
//         right: 10px;
//         font-size: 12px;
//       }
//     }
//     .spt{
//       color: rgb(83, 176, 48)
//     }
//     .bigScore{
//       font-weight: bolder!important;
//       width: 134px;
//       text-align: center;
//       margin: 0 12px 0 4px;
//     }
//   }
// }