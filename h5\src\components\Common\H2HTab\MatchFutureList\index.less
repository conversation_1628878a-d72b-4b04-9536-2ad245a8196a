.win {
  color: #52af2f;
}

.draw {
  color: #feb82d;
}

.lose {
  color: #c72a1d;
}

.normal {
  color: #0f80da;
}

.content {
  background-color: #fff;
  margin-bottom: 24px;
}

.team {
  display: flex;
  align-items: center;

  &:first-child {
    margin-bottom: 6px;
  }

  .logo {
    width: 32px;
    height: 32px;
    margin-right: 14px;
  }

  .teamName {
    flex: 1;
    font-size: 24px;
    
    color: #333333;
    line-height: 24px;
    padding-right: 10px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 54px;
  background: #000;
  padding: 10px 24px;
  box-sizing: border-box;

  .title {
    display: flex;
    align-items: center;
    height: 24px;
    font-size: 24px;
    
    font-weight: 500;
    color: #343434;
    line-height: 24px;
  }
}

.stats {
  display: flex;
  align-items: center;
  padding: 15px 24px;
  line-height: 30px;
  background-color: #fff;
}

.teamInfo {
  display: flex;
  align-items: center;
  padding: 16px 24px 5px;
  background-color: #fff;
}

.showMore {
  font-weight: 500;
  width: 100%;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 0;
}

.days {
  align-self: center;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #0f80da;
}

.noData {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 150px;
  font-size: 32px;
  color: #666;
}
