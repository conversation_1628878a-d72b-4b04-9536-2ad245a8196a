import './FeaturedCard.less'

import { FallbackCategoryImage, FallbackImage, FallbackPlayerImage } from "iscommon/const/icon";
import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";
import { inject, observer } from "mobx-react";
import { useEffect, useState } from "react";

import { getFeaturedList } from "iscommon/api/home";
import { getHomeCompetitionMatchTime } from "iscommon/utils/dataUtils";
import { Link } from "umi";
import moment from "moment";
import { Swiper } from "antd-mobile";

const MatchStatusText = inject('store')(
  observer((props: any) => {
    const {
      match: { matchStatus, matchActiveTime, secondHalfKickOffTime },
      store: {
        WebHome: { currentGameTab, serverTime },
      },
    } = props;
    const { statusText, isAni, isIng } = getHomeCompetitionMatchTime({
      serverTime,
      matchStatus,
      matchActiveTime,
      secondHalfKickOffTime,
      currentGameTab,
    });

    return (
      <span className={`match-time-live ${isAni ? 'twinkleScore' : ''} ${isIng ? 'ing' : ''}`}>
        {statusText}
      </span>
    );
  }),
);

const FeaturedCard = inject('store')(
  observer((props: any) => {
    const {
      WebHome : { homeCompetitions },
      WebConfig: { currentLiveCount, showOdds },
    } = props.store;
    const [competitionList, setCompetitionList] = useState([]);
    const [matchList, setMatchList] = useState([]);
    const [playerList, setPlayerList] = useState([]);
    const [teamList, setTeamList] = useState([]);

    useEffect(() => {
      getFeaturedList().then((res: any) => {
        const { competitionList = [], matchList = [], playerList = [], teamList = []} = res

        setMatchList(matchList)
        setCompetitionList(competitionList)
        setPlayerList(playerList)
        setTeamList(teamList)
      })
    }, [])

    const matchesWithActiveTime = homeCompetitions.flatMap((competition) =>
      competition.matches.map((match) => ({
        matchId: match.matchId,
        matchActiveTime: match.matchActiveTime,
      }))
    ); 

    const activeMatch = matchesWithActiveTime.find((match) =>
      matchList.some((item) => match.matchId === item.id)
    );

    const getFormattedScore = (scores: number[], isHome: boolean): React.ReactNode => {
      if (!Array.isArray(scores)) return null;
    
      const baseScore = scores[5] !== 0 ? scores[5] : scores[0];
      const penaltyScore = scores[6] !== 0 ? scores[6] : null;
    
      if (penaltyScore !== null) {
        return isHome ? (
          <>
            <span style={{ color: '#9e9e9e' }}>({penaltyScore})</span> {baseScore}
          </>
        ) : (
          <>
            {baseScore} <span style={{ color: '#9e9e9e' }}>({penaltyScore})</span>
          </>
        );
      }
    
      return <>{baseScore}</>;
    };
    
    const renderMatchItems = () =>
      matchList.map((item) => (
        <Swiper.Item key={`match-${item.id}`}>
          <Link className="match-list-container" to={GlobalUtils.getPathname(PageTabs.match, item.id)}>
            <div className="home-team">
              <img className="team-icon" src={item?.homeTeam?.logo || FallbackImage} alt={item?.homeTeam?.name} loading="lazy"/>
            </div>
            {item.statusId === 8 || item.statusId === 3 ? (
              <div className="match-score-container">
                <span className="match-score">{getFormattedScore(item.homeScores, true)} - {getFormattedScore(item.awayScores, false)}</span>
                <span className="match-status">{item.statusId === 8 ? 'FT' : 'HT'}</span>
              </div>
            ) : item.statusId === 1 ? (
              <div className="match-time">
                <div className="hour-time">{moment.unix(item.matchTime).format('HH:mm')}</div>
                <div className="date-time">{moment.unix(item.matchTime).format('DD/MM/YY')}</div>
              </div>
            ) : (
              <div className="match-score-container">
                <span className="match-score">{item.homeScores[0]} - {item.awayScores[0]}</span>
                <MatchStatusText match={{ matchStatus: item.statusId, secondHalfKickOffTime: item.secondHalfKickOffTime, matchActiveTime: activeMatch?.matchActiveTime }}/>
              </div>
            )}
            <div className="away-team">
              <img className="team-icon" src={item?.awayTeam?.logo || FallbackImage} alt={item?.awayTeam?.name} loading="lazy"/>
            </div>
          </Link>
        </Swiper.Item>
      ));

    const renderPlayerItems = () =>
      playerList.map((item) => (
        <Swiper.Item key={`player-${item.player.id}`}>
          <Link
            className="player-list-container"
            to={GlobalUtils.getPathname(PageTabs.player, item.player.id)}
          >
            <img
              className="player-icon"
              src={item?.player?.logo || FallbackPlayerImage}
              alt={item?.player?.name}
            />
            <div className="player-info">
              <span className="player-name">{item?.player?.name}</span> 
              <span className="team-name">{item?.team?.name}</span>
            </div>
          </Link>
        </Swiper.Item>
      ));

    const renderCompetitionItems = () =>
      competitionList.map((item) => (
        <Swiper.Item key={`competition-${item.competition.id}`}>
          <Link
            className="competition-list-container"
            to={GlobalUtils.getPathname(PageTabs.competition, item.competition.id)}
          >
            <img
              className="competition-icon"
              src={item?.competition?.logo || FallbackCategoryImage}
              alt={item?.competition?.name}
            />
            <span className="competition-name">{item?.competition?.name}</span>
          </Link>
        </Swiper.Item>
      ));

    const renderTeamItems = () => 
      teamList.map((item) => (
        <Swiper.Item key={`team-${item.id}`}>
          <Link
            className="team-list-container"
            to={GlobalUtils.getPathname(PageTabs.team, item.id)}
          >
            <img
              className="team-icon"
              src={item?.logo || FallbackImage}
              alt={item?.name}
            />
            <span className="team-name">{item?.name}</span>
          </Link>
        </Swiper.Item>
      ))

      const totalItems = renderMatchItems().length + 
                        renderPlayerItems().length + 
                        renderCompetitionItems().length + 
                        renderTeamItems().length;

    return (
      <div className="football-featured-card-container">
        <Swiper 
          key={totalItems}
          loop={false} 
          slideSize={50} 
          trackOffset={10} 
          stuckAtBoundary={true} 
          indicator={false} 
          className={`custom-football-swiper ${totalItems === 1 ? 'single-item-swiper' : ''}`}>
          {[
            ...renderMatchItems(),
            ...renderPlayerItems(),
            ...renderCompetitionItems(),
            ...renderTeamItems(),
          ]}
        </Swiper>
      </div>
    );
  }),
);

export default FeaturedCard;