// @ts-nocheck
import _ from 'lodash';
import moment from 'moment';
import { GlobalConfig } from '../const/globalConfig';

export default _;

export const sortByMatchTime = (list = []) => {
  return list.sort((a, b) => {
    return a.minMatchTime - b.minMatchTime;
  });
};

export const momentTimeZone = (time = Date.now(), format = 'YYYY-MM-DD HH:mm:ss') => {
  let fixedTime = time;
  if (typeof time === 'number') {
    if (fixedTime < 17000000000) {
      fixedTime = fixedTime * 1000;
    }
  }
  const m = moment(fixedTime).utcOffset(GlobalConfig.timezone.replace('UTC', ''));
  if (format) {
    return m.format(format);
  }
  return m;
};

export const getTodayDateFilter = () => {
  return (momentTimeZone(Date.now(), 'YYYY-MM-DDTHH:MM:SS.SSS') + GlobalConfig.timezone).replace('UTC', '')
}

export const filterCompetitionsWithFavorite = (list, favoriteMatches, favoriteCompetitions) => {
  let filterList = [];
  // 先过滤掉出所有的 competitions
  for (let { competitionId } of favoriteCompetitions) {
    const index = list.findIndex((item) => item.competitionId === competitionId);
    if (index > -1) {
      filterList.push(simpleCloneObj(list[index]));
      list.splice(index, 1);
    }
  }
  for (let match of favoriteMatches) {
    const { competitionId, matchId } = match;
    const comp = list.find((item) => item.competitionId === competitionId);
    // list里面存在这个 competitionId 说明当前 competitionId 没有被直接收藏
    if (comp) {
      // 找当前 matchId 比赛数据
      const matchIndex = comp.matches.findIndex((item) => item.matchId === matchId);
      if (matchIndex > -1) {
        // 新的match数据替换
        const newMatch = simpleCloneObj(comp.matches[matchIndex]);

        const filteItem = filterList.find((item) => item.competitionId === competitionId);
        if (filteItem) {
          // 添加match到 已有的 收藏competition
          filteItem.matches.push(newMatch);
        } else {
          // 没有的话，创建新的 收藏competition
          const competition = simpleCloneObj(comp);
          const newMatch = competition.matches.find((item) => item.matchId === matchId);
          if (newMatch) {
            competition['matches'] = [match];
          }
          filterList.push(competition);
        }

        comp.matches.splice(matchIndex, 1);
      }
    }
  }
  return filterList;
};

export const getArrayFromString = (arrString = '') => {
  if (_.isArray(arrString)) return arrString;
  try {
    return JSON.parse(arrString);
  } catch (e) {
    return [];
  }
};

export const genValidObj = (obj = {}) => {
  return Object.keys(obj).reduce((acc, cur) => {
    if (obj[cur] !== undefined && obj[cur] !== null && obj[cur] !== '') {
      acc[cur] = obj[cur];
    }
    return acc;
  }, {});
};

export const getLastSixYear = (count = 6) => {
  const current = momentTimeZone(Date.now(), 'YYYY');
  let yearList = [{ label: current, value: current }];
  for (let i = 0; i < count - 1; i++) {
    const value = moment()
      .subtract(i + 1, 'y')
      .format('YYYY');
    yearList.push({
      label: value,
      value,
    });
  }
  return yearList;
};

// 1:降序 2:升序
export const compare = (property = '', sortType = 1) => {
  return (a, b) => {
    const value1 = a[property] || 0;
    const value2 = b[property] || 0;
    return Number(sortType) === 1 ? value2 - value1 : value1 - value2;
  };
};

export const simpleCloneObj = (data) => {
  return JSON.parse(JSON.stringify(data));
};

export const getRemainTimeString = (time) => {
  try {
    return Math.ceil(time / 60);
  } catch (e) {
    return null;
  }
};

export const startWith = (str = '', prefix = '') => {
  if (typeof str !== 'string' || typeof prefix !== 'string') return false;
  if (prefix === '') return true;
  return str.match(new RegExp(`^${prefix}`));
};

export const endWith = (str = '', prefix = '') => {
  if (typeof str !== 'string' || typeof prefix !== 'string') return false;
  if (prefix === '') return true;
  return str.match(new RegExp(`${prefix}$`));
};

export const isPlugin = () => {
  return location.href.indexOf("isplugin") > -1
}
export const isLivePlugin = () => {
  return location.href.indexOf("livescorewidget") > -1
}
export const isLiveEditModel = () => {
  return location.href.indexOf("livescorewidget") > -1
}

const ua = navigator.userAgent;

/**
 * @param {string} position stream: live stream button, chat: chat room button
 */
export const callAppUrl = (position = '') => {
  const isAndroid = ua.indexOf("Android") > -1 || ua.indexOf("Linux") > -1;
  window.gtag('event', 'target_to_download_app', { platform: 'mobile', target_position: position, target_app: isAndroid ? 'android' : 'ios' });
  if(isAndroid) {
    // location.href = 'https://play.google.com/store/apps/details?id=com.ball.igscore'
    // location.href = 'https://' + window.location.hostname + '/download/igscore-app.apk'
    const link = document.createElement('a');
    link.download = 'igscore-app';
    link.href = '/download/igscore-app.apk';
    link.click();
  } else {
    location.href = 'https://apps.apple.com/app/ig-score-live-sports-scores/id6446317264'
  }
}
