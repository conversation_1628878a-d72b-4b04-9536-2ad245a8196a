import africa from 'iscommon/assets/categories/africa.png';
import americas from 'iscommon/assets/categories/americas.png';
import asia from 'iscommon/assets/categories/asia.png';
import atp from 'iscommon/assets/categories/atp.png';
import australia from 'iscommon/assets/categories/australia.png';
import austria from 'iscommon/assets/categories/austria.png';
import beach from 'iscommon/assets/categories/beach.png';
import belarus from 'iscommon/assets/categories/belarus.png';
import billieJKC from 'iscommon/assets/categories/billie-jean-king-cup.png';
import canada from 'iscommon/assets/categories/canada.png';
import challenger from 'iscommon/assets/categories/challenger.png';
import czech from 'iscommon/assets/categories/czech.png';
import czechRepublic from 'iscommon/assets/categories/czech-republic.png';
import davisCup from 'iscommon/assets/categories/davis-cup.png';
import denmark from 'iscommon/assets/categories/denmark.png';
import england from 'iscommon/assets/categories/england.png';
import europe from 'iscommon/assets/categories/europe.png';
import exhibition from 'iscommon/assets/categories/exhibition.png';
import finland from 'iscommon/assets/categories/finland.png';
import france from 'iscommon/assets/categories/france.png';
import germany from 'iscommon/assets/categories/germany.png';
import hungary from 'iscommon/assets/categories/hungary.png';
import international from 'iscommon/assets/categories/international.png';
import italy from 'iscommon/assets/categories/italy.png';
import itfMen from 'iscommon/assets/categories/itf-men.png';
import itfWomen from 'iscommon/assets/categories/itf-women.png';
import juniors from 'iscommon/assets/categories/juniors.png';
import kazakhstan from 'iscommon/assets/categories/kazakhstan.png';
import latvia from 'iscommon/assets/categories/latvia.png';
import legends from 'iscommon/assets/categories/legends.png';
import newZealand from 'iscommon/assets/categories/new-zealand.png';
import norway from 'iscommon/assets/categories/norway.png';
import oceania from 'iscommon/assets/categories/oceania.png';
import others from 'iscommon/assets/categories/others.png';
import poland from 'iscommon/assets/categories/poland.png';
import russia from 'iscommon/assets/categories/russia.png';
import simulatedReality from 'iscommon/assets/categories/simulated-reality.png';
import simulatedRealityWomen from 'iscommon/assets/categories/simulated-reality-women.png';
import slovakia from 'iscommon/assets/categories/slovakia.png';
import sweden from 'iscommon/assets/categories/sweden.png';
import switzerland from 'iscommon/assets/categories/switzerland.png';
import usa from 'iscommon/assets/categories/usa.png';
import utr from 'iscommon/assets/categories/utr.png';
import wheelchairs from 'iscommon/assets/categories/wheelchairs.png';
import wta from 'iscommon/assets/categories/wta.png';
import wta125 from 'iscommon/assets/categories/wta-125.png';

export const ICON_FONT = {
  Football: 'icon-zuqiu-weixuanzhong',
  Basketball: 'icon-lanqiu-weixuanzhong',
  AmFootball: 'icon-ganlanqiu-weixuanzhong',
  Baseball: 'icon-bangqiu-weixuanzhong',
  Icehockey: 'icon-bingqiu-weixuanzhong',
  Tennis: 'icon-a-wangqiuweixuanzhong',
  Volleyball: 'icon-paiqiu-weixuanzhong',
  Esports: 'icon-dianjing-weixuanzhong',
  Handball: 'icon-shouqiu-weixuanzhong',
  Cricket: 'icon-banqiu-weixuanzhong',
  WaterPolo: 'icon-shuiqiu-weixuanzhong',
  TableTennis: 'icon-pingpangqiu-weixuanzhong',
  Snooker: 'icon-sinuoke-weixuanzhong',
  Badminton: 'icon-yumaoqiu-weixuanzhong',
  ShangLa: 'icon-shangla',
  XiaLa: 'icon-xiala',
  Search: 'icon-sousuo',
  YiShouCang: 'icon-yishoucang',
};

export const H5_ICON_FONT = {
  Football: 'iconsport_football',
  Basketball: 'iconsport_basketball',
  AmFootball: 'iconsport_amfootball',
  Baseball: 'iconsport_baseball',
  Icehockey: 'iconsport_icehockey',
  Tennis: 'iconsport_tennis',
  Volleyball: 'iconsport_volleyball',
  Esports: 'iconsport_esports',
  Handball: 'iconsport_handball',
  Cricket: 'iconsport_cricket',
  WaterPolo: 'iconsport_waterpolo',
  TableTennis: 'iconsport_tabletennis',
  Snooker: 'iconsport_snooker',
  Badminton: 'iconsport_badminton',
  ShangLa: 'icon-shangla',
  XiaLa: 'icon-xiala',
  Search: 'icon-sousuo',
  YiShouCang: 'icon-yishoucang',
};

export const CategoryIcon = {
  International: international,
  Europe: europe,
  Americas: americas,
  Asia: asia,
  Africa: africa,
  Oceania: oceania,
  Beach: beach,
  ATP: atp,
  'Billie Jean King Cup': billieJKC,
  Challenger: challenger,
  'Davis Cup': davisCup,
  Exhibition: exhibition,
  'ITF Men': itfMen,
  'ITF Women': itfWomen,
  Juniors: juniors,
  Legends: legends,
  'Simulated Reality': simulatedReality,
  'Simulated Reality Women': simulatedRealityWomen,
  UTR: utr,
  WTA: wta,
  'WTA 125': wta125,
  Wheelchairs: wheelchairs,
  Canada: canada,
  USA: usa,
  Germany: germany,
  Others: others,
  Australia: australia,
  Austria: austria,
  Belarus: belarus,
  Czech: czech,
  'Czech Republic': czechRepublic,
  Denmark: denmark,
  England: england,
  Finland: finland,
  France: france,
  Kazakhstan: kazakhstan,
  Latvia: latvia,
  'New Zealand': newZealand,
  Norway: norway,
  Poland: poland,
  Russia: russia,
  Slovakia: slovakia,
  Sweden: sweden,
  Switzerland: switzerland,
  Hungary: hungary,
  Italy: italy,
};

export const FootballIcon = {
  2: 'corner.png', //角球
  5: 'foulBall.png', //越位
  6: 'freeKick.png', //任意球
  7: 'goalKick.png', //球门球
  10: 'start.png', //比赛开始
  11: 'midfield.png', //中场
  12: 'end.png', //结束
  13: 'halftimeScore.png', //半场比分
  19: 'injuryTime.png', //伤停补时
  21: 'shotsOnTarget.png', //射正
  22: 'shotsOffTarget.png', //射偏
  23: 'attacks.png', //进攻
  24: 'dangerousAttack.png', //危险进攻
  25: 'ballPossession.png', //控球率
  26: 'overtimeIsOver.png', //加时结束
  27: 'penaltyKickEnded.png', //点球大战结束
  28: 'VAR(VideoAssistantReferee)',
  29: 'Penalty(PenaltyShootOut)', //点球未进
  30: 'PenaltyMissed(PenaltyShootOut)', //点球未进
};

export const FallbackImage =
  'data:image/png;base64,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';

export const FallbackPlayerImage =
  'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAMAAAC7IEhfAAAAq1BMVEUAAADz+//y+//////z+//y+v/z+v/z+//z+//z+v/0+//z/P/4///z///y+v/c5/T79e7u9v54krz269/m7/nj7vjs9fzf6vbr8fjp8Pfp8vvt6uiDm8G8xdewvNKfsMt8lr7z9fXO2OePpMbu7/Dy7uvn5uemt9Gks8yUqMmZq8jz+Pvc5vDY4u/18On17ubi4uXE0eXw6eLEzNq2xdrY2uDN0t3W09S+ws6rjEyiAAAADnRSTlMAgPML6NnTv6qlclIjFjy9diYAAAH+SURBVDjLnZXpeqsgEEDV7NsgCGLUGNM0a3vb3rZ3ef8nKxBwoPvX80vhMAMIYxQyn05GgyQZjCbTefQhi3gICqoAxTBevKv14gQgF8QicoAk7r31Zn2gXAu8zPOSC/1IoT977cUA3IRxmGEcIA7TjoFKHSGACiIpjP30Y8gJwWio6taxn5fqsRfEYXt6ps6UJMfsM+3ZHn5ihsapklCY2Qn2zZvhV8ssdd5lh37PJuYktx5DanAmvyRfJECEbTywdmus9ZGxKzdpQpOFDUjt4Jo9pNVTs3pI07/s3IUUJuQQA96yVdpxZNQtkMBQnRfIux28qlNkz3gXsoR5NNUDnLhOPWqXSKecRhOQohMbXzxx6JYDk2gExDVssp0vVtkGJzmKBihmWRqQ3duOktBBlKipfiRmYNDLTT4V7zzRT31zH4p3N15qXIxmta6cpR4BvMUE2wPP7MmJdbsBf3uCDQfZ1vuL17ADgL/h+AkNv1m72qe7P2e2NY34Ce2hQLNmhrVpw0OBx8xR/T9uz80/N0E8ZnhwDWWVGa5LgODghleB82JnxUKU3lUILxcVxXJ5fRHV01Lg5QquK5WqV5JH7VVEvRQSr6tXALSnq9Tto/YUBS8EFgAsKbqDBKgEWFK8IiWd55BLLFJh2XtFWPbCQopgIf1Zaf662H/79/ECjIFUYeI1OcEAAAAASUVORK5CYII=';

export const FallbackCategoryImage = 'https://www.igscore.net/static-images/categories/fallbackcategory.png';
