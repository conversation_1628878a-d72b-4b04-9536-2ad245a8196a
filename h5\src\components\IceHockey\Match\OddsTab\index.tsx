import SmallballMatchOddsTab from '@/components/SmallGameCommon/Match/OddsTab';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
const OddTab = () => {
  const labels = useTranslateKeysToMaps(['MoneyLine', 'TennisHandicap', 'OU', 'h5Handicap', 'h5Over', 'h5Under', 'total']);
  const labelMaps = {
    ToWin: labels.MoneyLine,
    Spread: labels.TennisHandicap,
    TotalPoints: labels.OU,
    h5Handicap: labels.TennisHandicap,
    h5Over: labels.h5Over,
    Goals: labels.total,
    h5Under: labels.h5Under,
  };
  return <SmallballMatchOddsTab labelMaps={labelMaps} />;
};

export default OddTab;
