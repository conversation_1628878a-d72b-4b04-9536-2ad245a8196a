import { Nav<PERSON><PERSON>, Picker } from 'antd-mobile';
import { DownFill, LeftOutline } from 'antd-mobile-icons';
import classNames from 'classnames';
import { inject, observer } from 'mobx-react';
import React, { useCallback, useMemo, useState } from 'react';
import { history } from 'umi';

import styles from './Index.less';

interface Props {
  showSelector?: boolean;
  renderTitle?: () => React.ReactNode;
  requestHeader?: (seasonId?: string) => Promise<void>;
  className?: string;
  style?: React.CSSProperties;
}

interface SeasonSelectorProps {
  requestHeader: Props['requestHeader'];
  store?: any;
}

const SeasonSelector: React.FC<SeasonSelectorProps> = inject('store')(
  observer((props) => {
    const {
      requestHeader,
      store: { Competitions },
    } = props;
    const { currentSeason, seasons = [] } = Competitions;

    const [visible, setVisible] = useState(false);

    const columns = useMemo(() => {
      const list = (seasons || []).map((item: any) => ({
        ...item,
        value: item.id,
        label: item.year,
      }));
      return [list];
    }, [seasons]);

    const onConfirm = useCallback(
      ([id]: (string | null)[]) => {
        const season = columns[0].find((i: any) => i.id === id);
        if (season) {
          Competitions.changeCurrentSeason(season);
          requestHeader?.(season?.id);
        }
      },
      [Competitions, columns, requestHeader],
    );

    return (
      <>
        <div className={styles.navRight} onClick={() => setVisible(true)}>
          <div className={styles.season}>{currentSeason.year}</div>
          <DownFill fontSize={12} color="#fff" />
        </div>
        <Picker
          visible={visible}
          columns={columns}
          value={[currentSeason.id]}
          onClose={() => setVisible(false)}
          onConfirm={onConfirm as any}
        />
      </>
    );
  }),
);

const PageHeader: React.FC<Props> = (props) => {
  const { showSelector = true, renderTitle = null, requestHeader, className, style, children } = props;

  return (
    <div className={classNames(styles.container, className)} style={style}>
      {/* <NavBar
        className={styles.navbar}
        backArrow={<LeftOutline color="rgba(255, 255, 255, 0.6)" fontSize={24} />}
        right={showSelector ? <SeasonSelector requestHeader={requestHeader} /> : null}
        onBack={() => history.go(-1)}
      >
        {renderTitle?.()}
      </NavBar>*/}
      {renderTitle?.()}
      {children} 
    </div>
  );
};

export default PageHeader;
