// import * as echarts from 'echarts';
// import 'echarts/lib/chart/line';
// import moment from 'moment';
// import { useCallback, useEffect } from 'react';
// import './TeamResult.less';

// interface Props {
//   lastSixMatch: any[];
//   teamId: string;
// }

// const TeamResult = (props: Props) => {
//   const { lastSixMatch, teamId } = props;
//   const initChart = useCallback(() => {
//     // 基于准备好的dom，初始化echarts实例
//     let myChart = echarts.init(document.getElementById('main'));
//     // if( myChart === undefined){
//     // console.log('lastSixMatch', lastSixMatch);

//     const reverseMatch = lastSixMatch.reverse();
//     const score = reverseMatch.map((item) => {
//       return `${item.homeScores[0]}-${item.awayScores[0]}`;
//     });
//     const date = reverseMatch.map((item) => {
//       return moment(item.matchTime * 1000).format('MM/DD');
//     });
//     const result = reverseMatch.map((item) => {
//       const img = item.homeTeam?.id === teamId ? item.awayTeam?.logo : item.homeTeam?.logo;
//       if (item.homeScores[0] === item.awayScores[0]) {
//         return {
//           value: 'D',
//           symbol: `image://${img}`,
//           symbolSize: 18,
//         };
//       }
//       if (item.homeScores[0] > item.awayScores[0]) {
//         return item.homeTeam?.id === teamId
//           ? {
//               value: 'W',
//               symbol: `image://${img}`,
//               symbolSize: 18,
//             }
//           : {
//               value: 'L',
//               symbol: `image://${img}`,
//               symbolSize: 18,
//             };
//       } else {
//         return item.homeTeam?.id === teamId
//           ? {
//               value: 'L',
//               symbol: `image://${img}`,
//               symbolSize: 18,
//             }
//           : {
//               value: 'W',
//               symbol: `image://${img}`,
//               symbolSize: 18,
//             };
//       }
//     });

//     const option = {
//       tooltip: {},
//       legend: {
//         data: [],
//       },
//       grid: {
//         x: 40,
//         x2: 40,
//         y: 30,
//         y2: 20,
//       },
//       xAxis: [
//         {
//           type: 'category',
//           boundaryGap: true,
//           data: score, // 比分
//           axisLine: {
//             show: false,
//           },
//           axisTick: {
//             alignWithLabel: true,
//             show: false,
//           },
//         },
//         {
//           type: 'category',
//           boundaryGap: true,
//           data: date, // 时间
//           axisLine: {
//             show: false,
//           },
//           axisTick: {
//             alignWithLabel: true,
//             show: false,
//           },
//         },
//       ],
//       yAxis: {
//         type: 'category',
//         boundaryGap: true,
//         data: ['L', 'D', 'W'],
//         axisLine: {
//           show: false,
//         },
//         axisTick: {
//           alignWithLabel: true,
//           show: false,
//         },
//         splitLine: {
//           show: true,
//         },
//         splitArea: {
//           show: true,
//         },
//       },
//       series: [
//         {
//           type: 'line',
//           lineStyle: {
//             color: '#ddd',
//           },
//           data: result,
//         },
//       ],
//     };
//     // 绘制图表，option设置图表格式及源数据
//     myChart.setOption(option);
//   }, [lastSixMatch, teamId]);

//   useEffect(() => {
//     initChart();
//   }, [initChart, lastSixMatch]);

//   return <div className="teamResult" id="main"></div>;
// };

// export default TeamResult;
