import { DownFill, LeftOutline } from 'antd-mobile-icons';
import { ReactNode, useCallback, useEffect, useMemo, useRef, useState } from 'react';

import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { translate } from 'iscommon/i18n/utils';

import './DataDetail.less';

interface Item {
  playerName: string;
  teamName: string;
  logo: string;

  [key: string]: any;
}

type ValueKey = any;

export interface DataProps {
  title: string;
  subTitleOptions: { label: string; value: ValueKey }[];
  sectionLeftText?: string;
  sectionRightText?: string;
  list: Item[];
  withFloatButton?: boolean;
  currentProperty: any;
  detailType: string;

  renderValue?(item: Item, isTotal: boolean, key: ValueKey): string | ReactNode;
}

const DataDetail = (props: DataProps) => {
  const { title, subTitleOptions = [], list, sectionLeftText, currentProperty, detailType } = props;

  const cancelText = translate('Cancel');
  const currentContent = useRef<any>(null);
  const [height, setHeight] = useState<number>(0);
  const [filter, setFilter] = useState<{ label: any; value: ValueKey }>(subTitleOptions[0]);
  const [visible, setVisible] = useState<boolean>(false);

  const sortList = useMemo(
    () => list.filter((i) => i[filter.value]).sort((a, b) => b[filter.value] - a[filter.value]),
    [list, filter],
  );

  const [listData, setListData] = useState(sortList);

  const close = useCallback(() => {
    const parentNode = currentContent.current.parentElement;
    parentNode.parentNode.removeChild(parentNode);
    const className = document.body.getAttribute('class');
    // @ts-ignore
    document.body.setAttribute('class', className?.replace('g-overflow-hidden', ''));
  }, []);

  const showPicker = useCallback(() => {
    setVisible(true);
  }, []);

  const goPage = useCallback(
    (isPlayer: boolean, id: string) => {
      close();
      if (isPlayer) {
        GlobalUtils.goToPage(PageTabs.player, id);
      } else {
        GlobalUtils.goToPage(PageTabs.team, id);
      }
    },
    [close],
  );

  useEffect(() => {
    setListData(sortList);
  }, [filter, sortList]);

  useEffect(() => {
    if (currentProperty) {
      setFilter(currentProperty);
    }
  }, [currentProperty]);

  useEffect(() => {
    setHeight(document.body.offsetHeight);
  }, []);

  return (
    <>
      <div ref={currentContent} className="dataDetail" style={{ height: `${height}px` }}>
        <div className="dheader">
          <div className="left" onClick={close}>
            <LeftOutline color="#fff" style={{ transform: 'scale(1.2)' }} />
          </div>
          <div className="dtitle">
            <div className="upTitle">{title}</div>
            {subTitleOptions.length > 0 && (
              <div className="subtitle" onClick={showPicker}>
                <div style={{ marginRight: 4 }}>
                  <DownFill color="#fff" />
                </div>
                {filter.label}
              </div>
            )}
          </div>
          <div className="left"></div>
        </div>
        <div className="main">
          <div className="tableHeader">
            <span className="sort">#</span>
            <div className="content">
              <div>{sectionLeftText}</div>
              {/* <div>{sectionRightText}</div> */}
              <div>{filter.label}</div>
            </div>
          </div>
          <ul>
            {listData.map((item: any, index: any) => {
              return (
                <li
                  className="sectionitem"
                  onClick={() => {
                    goPage(detailType === 'Player', detailType === 'Player' ? item.player.id : item.team.id);
                  }}
                  key={detailType === 'Player' ? item.player?.id : item.team?.id}
                >
                  <span className="sort">{index + 1}</span>
                  <div className="content">
                    <div className="player">
                      <img className="logo" src={detailType === 'Player' ? item.player?.logo : item.team?.logo} alt={item.player?.name || item.team?.name} loading="lazy" />
                      <div className="name">
                        <span>{detailType === 'Player' ? item.player?.name : item.team?.name}</span>
                        {detailType === 'Player' ? <span className="teamname">{item.team?.name}</span> : null}
                      </div>
                    </div>
                    {/* <div>{renderValue(item, isTotal, filter.value)}</div> */}
                    <div style={{ color: '#0f80da' }}>{item[filter.value]}</div>
                  </div>
                </li>
              );
            })}
          </ul>
        </div>
        {/* {withFloatButton && (
          <div className="asbbtn" onClick={changeType}>
            {isTotal ? totalText : 'PG'}
          </div>
        )} */}
      </div>
      {visible ? (
        <div className="dataDetail picker" style={{ height: `${height}px` }}>
          <div className="btngroup">
            {subTitleOptions.map((item) => (
              <div
                onClick={() => {
                  setFilter(item);
                  setVisible(false);
                }}
                className={`btnli ${item.value === filter.value ? 'active' : ''}`}
                key={item.value}
              >
                {item.label}
              </div>
            ))}
          </div>
          <div className="btngroup" onClick={() => setVisible(false)}>
            <div className="btnli">{cancelText}</div>
          </div>
        </div>
      ) : null}
    </>
  );
};

export default DataDetail;
