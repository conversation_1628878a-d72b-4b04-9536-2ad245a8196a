import { useState, useEffect, useRef } from "react";

import { TimesUnits } from "../const/constant";

const defaultTimeStr = {
  d: "00",
  h: "00",
  m: "00",
  s: "00",
};

// let timerId
export function useCountDown(initCount = 0) {
  const [count, setCount] = useState(initCount);
  const [timeData, setTimeData] = useState(defaultTimeStr);
  const timerId = useRef(null);

  const clearTime = () => {
    if (timerId.current) clearTimeout(timerId.current);
  };
  useEffect(() => {
    return () => {
      clearTime();
    };
  }, []);

  useEffect(() => {
    clearTime();
    if (count >= 1000) {
      run();
    }
  }, [count]);

  useEffect(() => {
    if (count <= 0) {
      setTimeData(defaultTimeStr);
    } else {
      const day = Math.floor(count / TimesUnits.day);
      const h = Math.floor((count - day * TimesUnits.day) / TimesUnits.h);
      const m = Math.floor(
        (count - day * TimesUnits.day - h * TimesUnits.h) / TimesUnits.m
      );
      const s = Math.floor(
        (count - day * TimesUnits.day - h * TimesUnits.h - m * TimesUnits.m) /
          TimesUnits.s
      );
      setTimeData({
        d: day,
        h: ("0" + h).slice(-2),
        m: ("0" + m).slice(-2),
        s: ("0" + s).slice(-2),
      });
    }
  }, [count]);

  function run() {
    timerId.current = setTimeout(() => {
      setCount((pre) => pre - 1000);
    }, 1000);
  }

  function setCountDown(initCount = 0) {
    if (Number(initCount) > 0) {
      setCount(initCount);
    }
  }

  return [timeData, setCountDown];
}
