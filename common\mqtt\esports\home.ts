// @ts-nocheck
import MqttClientInstance from '../index';
import homeDataController, { homeDataControllerType} from './homeDataController';
import { GlobalConfig } from '../../const/globalConfig';
import { MqttTopicEnum, MqttListenKeyEnum } from '../constants';
import store from '../../mobx';
import _ from 'lodash';

export const subscribeHomeData = () => {
  // subscribe home match data
  MqttClientInstance.subscribe(MqttTopicEnum.allMatch, MqttListenKeyEnum.allMatch, (topic, message) => {
    if(_.isArray(message)) {
      for(let v of message) {
        const data = v;
        // console.log('MqttClientInstance match', topic, data);
        if (data) {
          // GlobalConfig.serverTime = time;
          // WebHome.changeServerTime(time);
          for (let dataKey in homeDataControllerType) {
            if (data[dataKey] && typeof homeDataController[dataKey] === 'function') {
              homeDataController[dataKey](data);
            }
          }
        }
      }
    }
  });
};

export const unsubscribeHomeData = () => {
  MqttClientInstance.unsubscribe(MqttListenKeyEnum.footballMatch);
};
