
import { basketballStatsKeys } from 'iscommon/const/basketball/constant';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { inject, observer } from 'mobx-react';

import './Statistics.less'
import { FallbackImage } from 'iscommon/const/icon';
import { useCallback } from 'react';

const keysList = Object.keys(basketballStatsKeys);

const Statistics = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Basketball: { Match },
      },
    } = props;

    const { teamStats } = Match;
    const { keyPlayers = {} } = Match;
    const { homeTeam, awayTeam } = keyPlayers;
    const labelMaps = useTranslateKeysToMaps(['Stats', 'Match'].concat(keysList));

    const renderProgress = useCallback((isHome = true, progress = 50) => {
      return (
        <div className="progress-container">
          <div
            className={`current-progress ${isHome ? 'home-progress' : 'away-progress'}`}
            style={{
              width: `${progress}%`,
              marginLeft: isHome ? 'auto' : '0',
              marginRight: isHome ? '0' : 'auto',
              justifyContent: isHome ? 'flex-end' : 'flex-start',
              display: 'flex',
              alignItems: 'center',
            }}
          />
        </div>
      );
    }, []);

    if (!teamStats || teamStats.length === 0) return null;

    const home = teamStats[0];
    const away = teamStats[1];


    const renderItem = ({ formatVal = (p, k) => p[k], index = 0, key = '' }) => {

      let homeValue = formatVal(home, key);
      let awayValue = formatVal(away, key);

      let homePercentage;
      let awayPercentage;

      if ([0, 2, 4].includes(index)) {
        homeValue = homeValue.toString();
        awayValue = awayValue.toString();
      
        const [homeScored, homeTotal] = homeValue.includes('/')
          ? homeValue.split('/').map(Number)
          : [parseFloat(homeValue), 1];
        const [awayScored, awayTotal] = awayValue.includes('/')
          ? awayValue.split('/').map(Number)
          : [parseFloat(awayValue), 1];
      
        homePercentage = (homeScored / homeTotal) * 100;
        awayPercentage = (awayScored / awayTotal) * 100;
      } else if ([1, 3, 5].includes(index)) {
        homePercentage = typeof homeValue === 'string' && homeValue.includes('%')
          ? parseFloat(homeValue.replace('%', ''))
          : parseFloat(homeValue);
      
        awayPercentage = typeof awayValue === 'string' && awayValue.includes('%')
          ? parseFloat(awayValue.replace('%', ''))
          : parseFloat(awayValue);
      
        homePercentage = homePercentage; 
        awayPercentage = awayPercentage;
      } else {
        const total = parseFloat(homeValue) + parseFloat(awayValue);
        homePercentage = (parseFloat(homeValue) / total) * 100;
        awayPercentage = (parseFloat(awayValue) / total) * 100;
      }

      return (
        <div key={index} className='stats-data-container'>
          <div className='stats-item'>
            <span className='data'>{homeValue}</span>
            {labelMaps[keysList[index]]}
            <span className='data'>{awayValue}</span>
          </div>

          <div className='bar-container'>
            {renderProgress(false, homePercentage)}
            {renderProgress(true, awayPercentage)}
          </div>
        </div>
      );
    };

    const list = [
      { formatVal: (o) => { return `${o.fieldGoalsScored}/${o.fieldGoalsTotal}`; }, },
      { formatVal: (o) => { return `${((o.fieldGoalsScored / o.fieldGoalsTotal) * 100).toFixed(1)}%`; }, },
      { formatVal: (o) => { return `${o.threePointersScored}/${o.threePointersTotal}`; }, },
      { formatVal: (o) => { return `${((o.threePointersScored / o.threePointersTotal) * 100).toFixed(1)}%`; }, },
      { formatVal: (o) => { return `${o.freeThrowsScored}/${o.freeThrowsTotal}`; }, },
      { formatVal: (o) => { return `${((o.freeThrowsScored / o.freeThrowsTotal) * 100).toFixed(1)}%`; }, },
      { key: 'rebounds', },
      { key: 'offensiveRebounds', },
      { key: 'defensiveRebounds', },
      { key: 'assists', },
      { key: 'blocks', },
      { key: 'steals', },
      { key: 'turnovers', },
      { key: 'totalFouls', },
    ];

    return (
      <div className='basketball-statistics-container'>
        <div className='container-title'>{labelMaps.Stats}</div>
        <div className='player-team-container'>
          <div className='team-container'>
            <img src={homeTeam?.team.logo || FallbackImage} className='team-icon'/>
            <span className='team-name'>{homeTeam?.team.name}</span>
          </div>
          <div className='team-container'>
            <span className='team-name'>{awayTeam?.team.name}</span>
            <img src={awayTeam?.team.logo || FallbackImage} className='team-icon'/>
          </div>
        </div>
        <div className='container-body'>
          {list.map((item, index) => {
            return renderItem({
              formatVal: item.formatVal,
              index,
              key: item.key,
            });
          })}
        </div>
      </div>
    );
  }),
);

export default Statistics;