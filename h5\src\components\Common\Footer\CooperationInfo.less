.text-center {
  text-align: center;
}

.clearfix-row {
  clear: both;
  display: inline-block;
  vertical-align: top;
  width: 100%;
}

.clearfix-row:after,
.clearfix-row:before {
  content: '';
  display: table;
}

.text-item {
  transition: all 0.3s ease;
  transform-style: preserve-3d;
  color: hsla(0, 0%, 100%, 0.6);
  position: relative;
  padding: 0 8px;
  flex: 1;
}

.flex {
  display: flex;
}

.mb-16 {
  margin-bottom: 16px;
}

.mt-10 {
  margin-top: 10px;
}

.color-w {
  color: #fff !important;
}

.line {
  width: 400px;
  height: 1px;
  background: #fff;
  opacity: 0.15;
  margin: 0 auto;
}

.justify-center {
  justify-content: center;
}

.align-center {
  align-items: center;
}

.flex {
  display: flex;
}

.mytransition {
  transform-style: preserve-3d;
  display: inline-block;
  cursor: pointer;
  font-size: 24px;
  font-weight: 400;
}

.thesportsIcon {
  width: 93px;
  height: 13px;
  margin-right: 8px;
  background: red;
}

svg:not(:root) {
  overflow: hidden;
}

.thesports {
  color: hsla(0, 0%, 100%, 0.6);
  cursor: pointer;
}

.text-item:after {
  background-color: hsla(0, 0%, 100%, 0.2);
  position: absolute;
  display: block;
  height: 24px;
  content: '';
  width: 1px;
  top: 5px;
  left: 0;
}

.text-item:first-child::after {
  width: 0;
}

.h-24 {
  height: 52px;
}

.ml-15 {
  margin-left: 38px;
}

.mt-24 {
  margin-top: 36px;
}

.mb-24 {
  margin-bottom: 36px;
}
