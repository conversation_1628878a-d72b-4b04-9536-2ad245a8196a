import { FallbackCategoryImage } from "iscommon/const/icon";
import { inject, observer } from "mobx-react";
import './TeamHeader.less'

interface Props {
  store?: any;
}

const TeamHeader: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: { 
        Basketball: { Team },
      }
    } = props;
    const { teamHeaderInfo } = Team;

    return (
      <div className='basketball-team-header-container'>
        <div className='team-container'>
          <img className='team-icon' src={teamHeaderInfo?.logo || FallbackCategoryImage} alt={teamHeaderInfo?.name} loading="lazy"/>
          <div className='team-info'>
            <div className='team-name-container'>
              <span className='team-name'>{teamHeaderInfo?.name}</span>
            </div>
            <div className='team-detail'>
              <img className='country-icon' src={teamHeaderInfo?.country?.logo || FallbackCategoryImage} alt={teamHeaderInfo?.country?.name} loading="lazy"/>
              <span className='country-name'>{teamHeaderInfo?.country?.name}</span>
            </div>
          </div>
        </div>
      </div>
    );
  }),
);

export default TeamHeader;