import { List } from 'antd-mobile';
import { useMemo } from 'react';

import { translate, useTranslateKeysToMaps } from 'iscommon/i18n/utils';

import { showDataDetail } from '@/utils/dataDetail';

import styles from './OtherStats.less';

const OtherStats = (props: any) => {
  const { data = {}, type = 'Player', allList = [] } = props;
  const labelMaps = useTranslateKeysToMaps(['PlayerStats', 'TeamStats', 'Players', 'Team']);

  const subTitleOptions = useMemo(
    () => [{ label: 'Goals', value: 'goals' }, ...data.offensiveData, ...data.defenseData, ...data.otherData],
    [data],
  );

  return (
    <div className={styles.otherStats_container}>
      {Object.keys(data).map((item: any) => (
        <List key={item} header={translate(item)} className={styles.otherStats_list_style}>
          {data[item].map((val: any) => (
            <List.Item
              key={val.value}
              onClick={() => {
                showDataDetail({
                  title: type === 'Player' ? labelMaps.PlayerStats : labelMaps.TeamStats,
                  subTitleOptions: subTitleOptions,
                  list: allList || [],
                  sectionLeftText: type === 'Player' ? labelMaps.Players : labelMaps.Team,
                  currentProperty: val,
                  detailType: type,
                  // renderValue: renderValue(info, true, property),
                  // renderValue: { isTotal: true },
                });
              }}
            >
              <span style={{ paddingLeft: 12 }}>{translate(val.label)}</span>
            </List.Item>
          ))}
        </List>
      ))}
    </div>
  );
};

export default OtherStats;
