.container {
  background-color: #fff;
}

.competition {
  display: flex;
  align-items: center;
  height: 70px;
  padding: 12px 0;
  margin-bottom: 14px;
  border-bottom: 1px solid #E3E3E3;
  box-sizing: border-box;
  font-size: 28px;
  
  font-weight: 500;
  color: #343434;
  line-height: 30px;

  .competitionTitle {
    flex: 1;
    display: flex;
    align-items: center;
    border-right: 1px solid #E3E3E3;
    padding-left: 24px;

    .logo {
      width: 44px;
      height: 44px;
      margin-right: 16px;
    }
  }

  .season {
    width: 230px;
    padding-left: 24px;
    box-sizing: border-box;
  }
}

.baseDesc {
  background-color: #fff;
  padding-bottom: 20px;

  .baseRow {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 56px;
    padding: 0 24px;
    font-size: 20px;
    
    font-weight: 500;

    &.hl {
      .label, .value {
        color: #0f80da;
      }
    }

    .label {
      display: flex;
      align-items: center;
      color: #999999;

      .icon {
        width: 24px;
        height: 24px;
        margin-left: 8px;
      }
    }

    .value {
      color: #333333;
    }
  }
}
