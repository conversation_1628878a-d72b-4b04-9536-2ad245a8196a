import { inject, observer } from 'mobx-react';
import { Link } from 'umi';

import GlobalUtils, { GlobalConfig, PageTabs } from 'iscommon/const/globalConfig';
import { momentTimeZone } from 'iscommon/utils';
import { useParams } from 'umi';

import './MatchInfo.less';
import './SingleMatchLine.less';
import { translate, useTranslateKeysToMaps } from 'iscommon/i18n/utils';

import calendarDays from 'iscommon/assets/images/calendarDays.png'
import courtSport from 'iscommon/assets/images/courtSport.png'
import locationDot from 'iscommon/assets/images/locationDot.png'
import RightOutlined from '@ant-design/icons/lib/icons/RightOutlined';
import { useCrumbList } from 'iscommon/hooks/content';
import { useState, useMemo } from 'react';

const MatchInfo = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Match,
        Smallball: { SmallballMatch },
      },
    } = props;
    const { tabId } = useParams<{ categoryId: string; tabId: string }>();
    const footballHeaderInfo = Match?.matchHeaderInfo;
    const isSmallball = !footballHeaderInfo?.id;
    const matchHeaderInfo = footballHeaderInfo?.id ? footballHeaderInfo : SmallballMatch?.matchHeaderInfo;

    if (!matchHeaderInfo) return null;

    const { homeTeam, awayTeam, matchTime, competition, venue } = matchHeaderInfo;
    const startTime = momentTimeZone(matchTime, 'DD MMM YYYY HH:mm:ss');

    const matchName = useMemo(
      () => `${matchHeaderInfo?.homeTeam?.name} vs ${matchHeaderInfo?.awayTeam?.name}, ${tabId.toLowerCase()}`,
      [matchHeaderInfo?.awayTeam?.name, matchHeaderInfo?.homeTeam?.name, tabId],
    );
    const { crumbs } = useCrumbList(PageTabs.match, '', matchName, [], {
      competition: {
        id: competition?.id,
        name: competition?.name,
      },
    });

    const [isExpanded, setIsExpanded] = useState(false);
    const labelMaps = useTranslateKeysToMaps(['MatchInfo']);
    const shouldShowVideo = Match.shouldShowVideo;

    const toggleContent = () => {
      setIsExpanded(prev => !prev);
    };

    const htmlContent = useMemo(() => {
      if (!matchHeaderInfo) {
        return { __html: '' };
      }
      const competitionLink = `<a target="_blank" style="color:#0F80DA; font-weight:700" href="${crumbs?.[1]?.url || ''}">${competition.name}</a>`;
      const matchName = `<a target="_blank" style="color:#0F80DA; font-weight:700" href="${window.location.href}">
      ${homeTeam.name} vs ${awayTeam.name}</a>`;
      const startTime = momentTimeZone(matchTime, 'YYYY/MM/DD HH:mm:ss');
      const videoText = shouldShowVideo ? ' (and video online live stream)' : '';
      const competitionName = competition.name;

      return {
        __html: `
          <p style="font-size:12px;color: #fff;">
            <span style="font-size:13px;font-weight:700">About The Match</span>
            <br />
            ${matchName} live score${videoText} starts on ${startTime} UTC time in ${competitionName}.
            Here on ${matchName} livescore you can find all ${matchName} previous results sorted by their H2H matches.
            <br />
            <span style="font-size:13px;font-weight:700">Match Details:</span>
            <br />
            ${isSmallball ? '' : `Event: ${competitionLink}<br />`}
            Name: ${matchName}
            <br />
            Date: ${momentTimeZone(matchTime, 'YYYY/MM/DD')}
            <br />
            Time: ${momentTimeZone(matchTime, 'HH:mm:ss')}
            <br />
            Stadium: ${venue?.name || '--'}
            <br />
            <span style="font-size:13px;font-weight:700">More details:</span>
            <br />
            ${
              isSmallball
                ? ''
                : `<a target="_blank" style="color:#0F80DA; font-weight:700" href="${GlobalUtils.getPathname(PageTabs.team, homeTeam.id)}">
              ${homeTeam.name} fixtures
            </a>
            <br />
            <a target="_blank" style="color:#0F80DA; font-weight:700" href="${GlobalUtils.getPathname(PageTabs.team, awayTeam.id)}">
              ${awayTeam.name} fixtures
            </a>
            <br />`
            }
            IGScore ${GlobalConfig.pathname} livescore is available as iPhone app, Android app on Google Play.
            You can find us in all stores on different languages as "IG Score". Install IG Score app on and follow
            ${matchName} live on your mobile!
          </p>`,
      };
    }, [crumbs, shouldShowVideo, matchHeaderInfo]);

    return (
      <div className='overview-match-info-container'>
        <div className='container-title'>{labelMaps.MatchInfo}</div>
        <div className='container-body'>
          <div className='item-container'>
            <div className='item-icon-text'>
              <img className='item-icon' src={calendarDays} alt='calendar'/>
              <span className='item-text'>{translate('Date & Time')}</span>
            </div>
            <span className='item-value'>{startTime}</span>
          </div>
          <div className='item-container'>
            <div className='item-icon-text'>
              <img className='item-icon' src={courtSport} alt='stadium'/>
              <span className='item-text'>{translate('Stadium')}</span>
            </div>
            <span className='item-value'>{venue?.name || '-'}</span>
          </div>
          <div className='item-container'>
            <div className='item-icon-text'>
              <img className='item-icon' src={locationDot} alt='location'/>
              <span className='item-text'>{translate('Location')}</span>
            </div>
            <span className='item-value'>{venue?.city || '-'}</span>
          </div>
          <div className='item-container toggle' onClick={toggleContent}>
            <div className='item-icon-text'>
              <RightOutlined className={isExpanded ? 'rotated-icon' : 'default-icon'} />
              <span className='item-text'>Click to show/hide more content</span>
            </div>
          </div>
          {isExpanded && <div className="about-match-content" dangerouslySetInnerHTML={htmlContent} />}
        </div>
      </div>
      // <div className="singleMatchLine">
      //   <div className="sectionTitle">Info</div>
      //   <div className="plugins" style={{ minHeight: 0, marginBottom: 0 }}>
      //     <div className="cardContent">
      //       <ul className="listBox">
      //         <li>
      //           <span className="name">Name:</span>
      //           <span itemProp="name" className="text">
      //             {matchNameLink}
      //           </span>
      //         </li>
      //         <li>
      //           <span className="name">Date:</span>
      //           <span itemProp="startDate" className="text">
      //             {momentTimeZone(matchTime, 'YYYY/MM/DD')}
      //           </span>
      //         </li>
      //         <li>
      //           <span className="name">Time:</span>
      //           <span className="text">{momentTimeZone(matchTime, 'HH:mm:ss')} UTC</span>
      //         </li>
      //         <li itemProp="location" itemType="https://schema.org/Place">
      //           <span className="name">Stadium:</span>
      //           <span itemProp="name address" className="text">
      //             {venue?.name || '--'}
      //           </span>
      //         </li>
      //       </ul>
      //       <div itemProp="description" className="description">
      //         <p>
      //           {matchNameLink} live score {videoText} starts on {startTime} UTC time in {competitionName}. Here on
      //           {matchNameLink} livescore you can find all {matchNameLink} previous results sorted by their H2H matches.
      //           <div style={{ margin: '10px 0 6px', fontSize: 13, fontWeight: 700 }}>More details:</div>
      //           {isSmallball ? (
      //             ''
      //           ) : (
      //             <>
      //               <Link
      //                 target="_blank"
      //                 style={{ color: '#0F80DA' }}
      //                 to={GlobalUtils.getPathname(PageTabs.team, homeTeam.id)}
      //                 rel="noreferrer"
      //               >
      //                 {homeTeam.name} Schedule
      //               </Link>
      //               <br />
      //               <Link
      //                 target="_blank"
      //                 style={{ color: '#0F80DA' }}
      //                 to={GlobalUtils.getPathname(PageTabs.team, awayTeam.id)}
      //                 rel="noreferrer"
      //               >
      //                 {awayTeam.name} Schedule
      //               </Link>
      //               <br />
      //               <br />
      //             </>
      //           )}
      //           IGScore {GlobalConfig.pathname} livescore is available as iPhone app, Android app on Google Play. You can find us
      //           in all stores on different languages as &quot;IGScore&quot;. Install IGScore app on and follow
      //           {matchNameLink} live on your mobile!
      //         </p>
      //       </div>
      //     </div>
      //   </div>
      // </div>
    );
  }),
);

export default MatchInfo;
