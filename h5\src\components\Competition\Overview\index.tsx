import { inject, observer } from 'mobx-react';
import React, { useCallback, useEffect, useState } from 'react';

import { getTopPlayers } from 'iscommon/api/competition';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { translate, useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { CompetitionsTabH5 } from 'iscommon/mobx/modules/competition';

import CommonTopPlayer from '@/components/Common/CommonTopPlayer';

import ScheduleCard from '../Schedule/ScheduleCard';
import Standings from '../Standings/Standings';
import Lineup from './Lineup';
import SectionTitle from './SectionTitle';
import ScheduleGame from '../Schedule/ScheduleGame';
import OverviewInfo from './OverviewInfo';
import TopPlayers from './TopPlayers';

interface Props {
  store?: any;
}

const OverView: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: { Competitions },
    } = props;

    const {
      competitionId,
      currentSeason: { id: seasonId },
    } = Competitions;

    const labelMaps = useTranslateKeysToMaps(['Standings']);
    const homeTopScorersText = translate('homeTopScorers');
    const [playerList, setPlayerList] = useState([]);

    const goToPlayer = useCallback(() => {
      GlobalUtils.goToPage(PageTabs.competition, competitionId, {
        target: 'history',
        customTab: CompetitionsTabH5.playerStats,
      });
    }, [competitionId]);

    const goStanding = useCallback(() => {
      GlobalUtils.goToPage(PageTabs.competition, competitionId, {
        target: 'history',
        customTab: CompetitionsTabH5.standings,
      });
    }, [competitionId]);

    useEffect(() => {
      if (competitionId && seasonId) {
        getTopPlayers({
          competitionId,
          seasonId,
          orderBy: 0,
        }).then(({ topScorers }: any) => {
          setPlayerList(topScorers?.slice(0, 3) ?? []);
        });
      }
    }, [competitionId, seasonId]);

    return (
      <div className='competition-overview-container'>
        <ScheduleGame />
        <Standings />
        <TopPlayers />
        <Lineup direction="vertical" width="100%" />
        <OverviewInfo />
      </div>
      // <div style={{ background: '#fff' }}>
      //   <div className="section-bg">
      //     <ScheduleCard />
      //     <SectionTitle leftTitle={labelMaps.Standings} rightTitle="More" onClick={goStanding} />
      //     <Standings len={8} />
      //     {playerList.length > 0 && (
      //       <div>
      //         <SectionTitle leftTitle={homeTopScorersText} rightTitle="More" onClick={goToPlayer} />
      //         <CommonTopPlayer hasMoreButton={false} type="goals" list={playerList} />
      //       </div>
      //     )}
      //     <div style={{ width: '100%' }}>
      //       <Lineup direction="vertical" width="100%" />
      //     </div>
      //   </div>
      // </div>
    );
  }),
);

export default OverView;
