import { makeAutoObservable } from "mobx";
import moment from "moment";
import { translate } from "iscommon/i18n/utils";
// import { getFavorites } from "../../api/home";

// function WebHome() {
//   this.currentGameTab = HomeGameTab.All;
//   makeAutoObservable(this)
// }

// WebHome.prototype.switchHomeGameTab = function(nextTab) {
//   this.currentGameTab = nextTab
// }

class TransferStore {
  // currentRanksTab = "All";
  constructor() {
    // 0-Live， 1-ALL, 2-Finished, 3-Scheduled
    this.currentYear = "2022";
    this.currentTeam = {};

    makeAutoObservable(this);
    // getFavorites().then((ids) => (this.favoriteMatches = ids));
  }

  selectCurrentYear(nextValue) {
    this.currentYear = nextValue;
  }

  selectCurrentTeam(nextValue) {
    this.currentTeam = nextValue;
  }
}

export default TransferStore;
