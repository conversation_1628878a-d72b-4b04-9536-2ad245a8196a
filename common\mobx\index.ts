// @ts-nocheck
import IceHockey from './ice-hockey';
import WebConfig from "./modules/config";
import WebHome from "./modules/webHome";
import StandingStore from "./modules/competition/standings";
import StatsStore from "./modules/competition/stats";
import TransferStore from "./modules/competition/transfer";
import Competitions from "./modules/competition";
import Team from "./modules/team";
import Match from "./modules/match";
import Player from "./modules/player";

import Basketball from "./basketball";
import Smallball from "./smallball";

export default {
  WebConfig: new WebConfig(),
  StandingStore: new StandingStore(),
  StatsStore: new StatsStore(),
  TransferStore: new TransferStore(),
  WebHome: new WebHome(),
  Competitions: new Competitions(),
  Team: new Team(),
  Match: new Match(),
  Player: new Player(),
  Basketball: { ...Basketball },
  IceHockey: { ...IceHockey },
  Smallball: { ...Smallball }
};
