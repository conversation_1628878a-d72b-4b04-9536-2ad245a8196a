import './index.less'

import {
  BadmintonInLiveStatusEnum,
  BadmintonMatchStatusCodeToText,
  BadmintonStatusCodeEnum,
} from 'iscommon/const/badminton/constant';
import { getBadmintonMatchTimeText, getBadmintonScoreList } from 'iscommon/utils/dataUtils';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { inject, observer } from 'mobx-react';
import React, { useMemo } from 'react';

import { FallbackImage } from 'iscommon/const/icon';
import { Link } from 'umi';
import SmallballMatchHeader from '@/components/SmallGameCommon/Match/MatchHeader';

interface Props {
  store?: any;
}

interface StatusTextProps {
  matchHeaderInfo: any;
  serverTime: number;
}

const StatusText = React.memo<StatusTextProps>(({ matchHeaderInfo, serverTime }) => {
  const { matchStatus, remainSeconds } = matchHeaderInfo;
  const { statusText, isIng } = useMemo(
    () => getBadmintonMatchTimeText({ serverTime, matchStatus, remainSeconds }),
    [serverTime, matchStatus, remainSeconds ]
  );
  const displayText = statusText === 'FT' ? 'Full Time' : statusText === 'HT' ? 'Half Time' : statusText;

  return (
    <div className={`pk-time ${isIng ? 'in-progress' : ''}`}>
      {displayText}
    </div>
  );
});

const MatchHeader: React.FC<Props> = inject('store')(
  observer((props) => {
    const { store: { Smallball: { SmallballMatch: Match }, }, } = props;
    const { serverTime, matchHeaderInfo } = Match;
    const {       
      matchTime,
      matchStatus,
      uniqueTournament,
      competition,
      homeTeam,
      awayTeam,
      statusId,
      scores, 
      servingSide 
    } = matchHeaderInfo || {};
    const list: any[] = getBadmintonScoreList(matchStatus, scores, servingSide, false);
    const inProgress = BadmintonInLiveStatusEnum.includes(matchStatus);
    // const latestScore = list[list.length - 1];
    // const text = (BadmintonMatchStatusCodeToText as any)[matchStatus];
    // const res = {
    //   statusText: text,
    //   isIng,
    // };
    // return res;

    return (
      <div className='common-match-container'>
        <Link className='home-team-container' to={GlobalUtils.getPathname(PageTabs.team, homeTeam?.id)}>
          <img className='team-logo' src={homeTeam?.logo || FallbackImage} alt={homeTeam?.name} loading="lazy"/>
          <span className='team-name'>{homeTeam?.name}</span>
        </Link>

        <div className='score-container'>
          {matchStatus === BadmintonStatusCodeEnum.NotStarted ? (
            <>
              <span className='not-started'>VS</span>
              <span className='status-text'>Not Started</span>
            </>
          ) : (
            <>
              <div className='match-score'>
                <span className={`score-text ${inProgress ? 'in-progress' : ''}`}>{scores.ft?.[0] ?? 0}</span>
                <span className={`score-text ${inProgress ? 'in-progress' : ''}`}>-</span>
                <span className={`score-text ${inProgress ? 'in-progress' : ''}`}>{scores.ft?.[1] ?? 0}</span>
              </div>
              <StatusText matchHeaderInfo={matchHeaderInfo} serverTime={serverTime} />
            </>
          )}
        </div>

        <Link className='away-team-container' to={GlobalUtils.getPathname(PageTabs.team, awayTeam?.id)}>
          <img className='team-logo' src={awayTeam?.logo || FallbackImage} alt={awayTeam?.name} loading="lazy"/>
          <span className='team-name'>{awayTeam?.name}</span>
        </Link>
      </div>

    // <SmallballMatchHeader
    //   checkIsIng={(matchStatus) => BadmintonInLiveStatusEnum.includes(matchStatus)}
    //   getHomeScore={({ scores }) => scores?.ft?.[0] || 0}
    //   getAwayScore={({ scores }) => scores?.ft?.[1] || 0}
    //   notStartedCode={BadmintonStatusCodeEnum.NotStarted}
    //   getHomeBasketBallMatchTimeText={getHomeBasketBallMatchTimeText}
    //   // ballIconCode="#icontabletennis_ball"
    // />
    );
  }),
);


export default MatchHeader;
