import MatchResultIcon from '@/components/Common/MatchResultIcon';
import SectionTitle from '@/components/Competition/Overview/SectionTitle';
import { getMatchHistoryList } from 'iscommon/api/match';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { translate } from 'iscommon/i18n/utils';
import { MatchTabH5 } from 'iscommon/mobx/modules/match';
import { momentTimeZone } from 'iscommon/utils';
import { calculateScore } from 'iscommon/utils/dataUtils';
import { inject, observer } from 'mobx-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { history } from 'umi';
import './SingleMatchLine.less';

const HeaderAs = ({ count, stats, name }: any) => {
  const lastText = translate('Last');
  const loseText = translate('Lose');
  const winText = translate('Win');
  const drawText = translate('Draw');
  const perGoalText = `<span class="myBlue">${stats.perGoal}</span>`;
  const perLostText = `<span class="myBlue">${stats.perLost}</span>`;
  const perHtmlString = translate('Pergame', { perGoalText, perLostText });
  return (
    <div className="h2hDesc">
      {lastText} {count}, {name} {winText}
      <span className="w">{stats.win}</span>, {drawText}
      <span className="d">{stats.draw}</span>, {loseText}
      <span className="l">{stats.lose}</span>,<span dangerouslySetInnerHTML={{ __html: perHtmlString }}></span>
    </div>
  );
};

const H2H = inject('store')(
  observer((props: any) => {
    const h2hText = translate('H2H');
    const [list, setList] = useState<any[]>([]);
    const {
      store: {
        Match: {
          matchHeaderInfo: {
            homeTeam: { id: homeTeamId, name },
            awayTeam: { id: awayTeamId },
          },
          matchId,
        },
      },
    } = props;

    const click = useCallback(() => {
      history.replace(GlobalUtils.getPathname(PageTabs.match, matchId, MatchTabH5.h2h));
    }, [matchId]);

    const stats = useMemo(() => {
      const stat = list.reduce(
        (st, { homeScores, awayScores }) => {
          const hScore = calculateScore(homeScores);
          const awayScore = calculateScore(awayScores);
          if (hScore > awayScore) {
            st.win += 1;
          } else if (hScore === awayScore) {
            st.draw += 1;
          } else {
            st.lose += 1;
          }
          st.lostGoals += awayScore;
          st.winGoals += hScore;
          return st;
        },
        { win: 0, draw: 0, lose: 0, lostGoals: 0, winGoals: 0 },
      );
      const perLost = ((stat.lostGoals || 0) / (list.length || 1)).toFixed(1);
      const perGoal = ((stat.winGoals || 0) / (list.length || 1)).toFixed(1);
      return { ...stat, perLost, perGoal };
    }, [list]);

    useEffect(() => {
      if (homeTeamId && awayTeamId) {
        getMatchHistoryList({
          homeTeamId,
          awayTeamId,
          size: 5,
        }).then((res: any) => {
          if (Array.isArray(res?.matches)) {
            setList(res.matches);
          }
        });
      }
    }, [homeTeamId, awayTeamId]);

    if (list.length === 0) return null;

    console.log('list12345', list)

    return (
      <div className="singleMatchLine">
        <SectionTitle leftTitle={h2hText} rightTitle="More" onClick={click} />
        <div className="plugins" style={{ minHeight: 0 }}>
          <HeaderAs stats={stats} name={name} count={list.length} />
          {list.map((item, index) => {
            return (
              <div className="h2hItem" key={item.competition?.id + index}>
                <div className="inner">
                  <div className="time h2hTime">
                    <div className="text-overflow">{momentTimeZone(item.matchTime, 'YYYY/MM/DD')}</div>
                    <div className="text-overflow">{item.competition?.name}</div>
                  </div>
                  <div className="team">
                    <div className="teamSection text-overflow">
                      <img className="teamIcon" src={item.homeTeam.logo} alt="" />
                      {item.homeTeam.name}
                    </div>
                    <div className="teamSection text-overflow">
                      <img className="teamIcon" src={item.awayTeam.logo} alt="" />
                      {item.awayTeam.name}
                    </div>
                  </div>
                </div>
                <div className="inner fright">
                  <div className="team">
                    <div className="teamSection">{calculateScore(item.homeScores)}</div>
                    <div className="teamSection">{calculateScore(item.awayScores)}</div>
                  </div>
                  <div className="result">
                    <MatchResultIcon code={calculateScore(item.homeScores) - calculateScore(item.awayScores)} />
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  }),
);

export default H2H;
