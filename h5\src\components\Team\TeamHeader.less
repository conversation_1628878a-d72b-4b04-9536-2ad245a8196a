// .container {
//   display: flex;
//   flex-direction: column;
//   align-items: center;
// }

// .logo {
//   width: 108px;
//   height: 108px;
//   margin-bottom: 12px;
// }

// .name {
//   height: 28px;
//   font-size: 28px;
//   color: #ffffff;
//   line-height: 28px;
//   margin-bottom: 26px;
// }

// .info {
//   display: flex;
//   align-items: center;
//   justify-content: center;

//   .countryLogo {
//     width: 24px;
//     height: 24px;
//     margin-right: 5px;
//     border-radius: 50%;
//     overflow: hidden;
//   }

//   .country {
//     height: 24px;
//     font-size: 24px;
//     color: #ffffff;
//     line-height: 24px;
//   }

//   .worth {
//     height: 24px;
//     font-size: 24px;
//     line-height: 24px;
//     color: #fff;
//     margin-left: 5px;
//   }
// }

.common-team-header-container {
  background: #1e1e1e;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 80px 32px;
  width: 100%;

  .team-container {
    display: flex;
    flex-direction: row;
    align-items: center;

    .team-icon {
      height: 96px;
      width: 96px;
      border-radius: 50%;
      object-fit: contain;
      vertical-align: middle;
      margin-right: 10px;
    }

    .team-info {
      display: flex;
      flex-direction: column;

      .team-name-container {
        display: flex;
        align-items: center;

        .team-name {
          font-size: 28px;
          font-weight: bold;
          color: #fff;
        }
      }

      .team-detail {
        display: flex;
        align-items: center;

        .country-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin-right: 10px;
          overflow: hidden;
          background-size: cover;
        }

        .country-name {
          font-size: 24px;
          color: #fff;
        }
      }
    }
  }

  .value-container {
    display: flex;
    flex-direction: column;
    background: #2c2c2c;
    border-radius: 24px;

    .value-container-title {
      border-radius: 24px 24px 0px 0px;
      background: #121212;
      color: #fff;
      padding: 12px 24px;
      font-weight: 600;
    }

    .value-container-body {
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      padding: 12px 24px;
      font-size: 24px;
      font-weight: bold;
    }
  }
}
