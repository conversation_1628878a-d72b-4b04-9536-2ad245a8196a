import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { getBasketballStatsValue, getProgress } from 'iscommon/utils/dataUtils';
import { inject, observer } from 'mobx-react';

import './SingleMatchLine.less';

const SvgCircle = ({ list = [] }) => {
  const [type, home, away] = list;
  let size = 3140 * 0.5;
  if (home + away != 0) {
    size = 3140 * (home / (home + away));
  }
  return (
    <svg viewBox="0 0 1070 1070">
      <path
        d="M 535 535 m 0, -500 a 500, 500 0 1, 0 0, 1000 a 500, 500 0 1, 0 0, -1000"
        className="van-circle__hover"
        style={{ fill: 'none', stroke: '#FFBA5A', strokeWidth: '70px' }}
      ></path>
      <path
        d="M 535 535 m 0, -500 a 500, 500 0 1, 0 0, 1000 a 500, 500 0 1, 0 0, -1000"
        stroke="#2196F3"
        className="van-circle__layer"
        style={{ stroke: 'rgb(33, 150, 243)', strokeWidth: '71px', strokeDasharray: `${size}px, 3140px` }}
      ></path>
    </svg>
  );
};

const StatisComp = ({ statisList, teamStats }: { statisList: number[] }) => {
  const labelMaps = useTranslateKeysToMaps(['Rebounds', 'Turnovers']);

  const labelText = ['2 Points', '3 Points', 'Free throws'];

  const renderCircle = (item, index) => {
    const valList = getBasketballStatsValue(statisList, item);
    return (
      <div className="goalsTypeItem" key={index}>
        <div className="typeName">{labelText[index]}</div>
        <div className="goalsCount">
          <span className="color-blue">{valList[1]}</span>
          <div className="mycircle">
            <SvgCircle list={valList} />
          </div>
          <span className="color-yellow">{valList[2]}</span>
        </div>
      </div>
    );
  };

  const home = teamStats[0] || {};
  const away = teamStats[1] || {};

  const renderPer = (key) => {
    const h = home[key] || 0;
    const w = away[key] || 0;
    return (
      <div className="flex-2 countLine">
        <span>{h}</span>
        <div className="percentLine" style={{ marginLeft: 5 }}>
          <div
            className="percent"
            style={{
              width: `${getProgress(h, w)}%`,
              background: 'rgb(64, 158, 255)',
              right: 0,
            }}
          />
        </div>
        <div className="percentLine" style={{ marginLeft: 5 }}>
          <div
            className="percent"
            style={{
              width: `${getProgress(w, h)}%`,
              background: 'rgb(255, 174, 15)',
              left: 0,
            }}
          />
        </div>
        <span>{w}</span>
      </div>
    );
  };

  return (
    <div className="matchGoals">
      <div className="goalsType">
        {[2, 1, 3].map((item, index) => {
          return renderCircle(item, index);
        })}
      </div>

      <div className="onTarget">
        <div className="name">{labelMaps.Rebounds}</div>
        <div className="targetItem">
          <div className="targetCount">
            <div className="w115 spread">
              <div className="foulStyle">Foul</div>
            </div>
            {renderPer('rebounds')}
            <div className="w115 spread">
              <div className="foulStyle">Foul</div>
            </div>
          </div>
        </div>
      </div>
      <div className="onTarget">
        <div className="name">{labelMaps.Turnovers}</div>
        <div className="targetItem">
          <div className="targetCount">
            <div className="w115 spread">
              <div className="foulStyle color-red" style={{ background: 'transparent' }}>
                {getBasketballStatsValue(statisList, 5)[1]}
              </div>
            </div>
            {renderPer('turnovers')}
            <div className="w115 spread">
              <div className="foulStyle color-red" style={{ background: 'transparent' }}>
                {getBasketballStatsValue(statisList, 5)[2]}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const SingleMatchLine = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Basketball: { Match },
      },
    } = props;
    const { statsList, teamStats } = Match;

    return (
      <div className="singleMatchLine">{statsList.length > 0 && <StatisComp statisList={statsList} teamStats={teamStats} />}</div>
    );
  }),
);

export default SingleMatchLine;
