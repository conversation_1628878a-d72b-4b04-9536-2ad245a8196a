// .gameCategory {
//   width: 100%;
//   height: 40px;
//   background: transparent;
//   border-radius: 4px;
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
//   font-size: 13px;
//   box-sizing: border-box;
//   padding-left: 16px;
//   color: #333;
//   cursor: pointer;

//   div {
//     display: flex;
//     height: 100%;
//     align-items: center;
//   }

  // .squareLogo {
  //   width: 18px;
  //   height: 12px;
  //   background-size: contain;
  //   margin: 0 3px;

  //   &.teamLogo {
  //     width: 24px;
  //     height: 24px;
  //   }
  // }

  // .countryName {
  //   color: #999999;
  // }

  // .score {
  //   width: 56px;
  //   justify-content: center;
  //   color: #999;

  //   .hot {
  //     width: 14px;
  //     height: 14px;
  //     margin-right: 2px;
  //   }
  // }

  // &.listItem {
  //   height: 64px;
  //   background: #121212;
  //   border-bottom: 1px solid #eee;

  //   &:hover {
  //     background-color: transparent;

  //     .oddList {
  //       background: transparent;
  //     }
  //   }
  // }

  // .time {
  //   width: 64px;

  //   &.ing {
  //     color: #c1272d;
  //   }
  // }

  // .match-status {
  //   width: 80px;
  //   color: fff;
  
  //   &.live {
  //     color: #c1272d;
  
  //     .match-status-text {
  //       animation: Twinkle 1s infinite;
  //     }
  //   }
  
  //   &.neutral {
  //     color: #fff;
  //   }
  // }

  // .vsCountry {
  //   overflow: hidden;
  //   flex-direction: column;
  //   align-items: flex-start;
  //   width: 165px;
  //   margin-right: 45px;
  //   .teamName {
  //     text-align: left;
  //     overflow: hidden;
  //     width: 130px;
  //     &.right {
  //       text-align: right;
  //     }
  //   }
  //   .vsCountryItem {
  //     flex: 1;
  //     overflow: hidden;
  //     margin-left: 10px;

  //     &.left {
  //       justify-content: flex-end;
  //     }

  //     &.right {
  //       justify-content: flex-start;
  //       margin-right: 10px;
  //       margin-left: 0;
  //     }

  //     .card {
  //       text-indent: -0.5px;
  //       text-align: center;
  //       border-radius: 2px;
  //       line-height: 12px;
  //       font-size: 12px;
  //       width: 10px;
  //       color: #fff;
  //       margin: 0 1px;

  //       &.red {
  //         background-color: #c1272d;
  //       }

  //       &.yellow {
  //         background-color: #ffa830
  //       }
  //     }
  //   }
  // }

  // .matchRight{
  //   flex: 1;
  //   padding-left: 30px;justify-content: flex-end;
  // }

  // .vsscore {
  //   display: flex;
  //   align-items: center;
  //   margin-right: 4px;
  //   justify-content: flex-end;
  //   color: #999;

  //   .vshc {
  //     width: 100px;
  //   }

  //   .iconWrap {
  //     display: flex;
  //     justify-content: center;
  //     align-items: center;
  //     width: 52px;
  //   }

  //   .icon-donghuazhibo {
  //     color: #ffa830;
  //   }

  //   .icon-corner {
  //     margin-left: 3px;
  //   }

  //   .icon-shipinzhibo {
  //     color: #c72a1d
  //   }

  //   .vsscoreht {
  //     display: flex;
  //     align-items: center;
  //     justify-content: flex-start;

  //     &.halfBox {
  //       width: 50px;
  //     }

  //     &.cornerBox {
  //       width: 60px;
  //     }
  //   }
  // }
// }

.list-item {
  width: 100%;
  height: 80px;
  background: #121212;
  border-radius: 16px;
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  box-sizing: border-box;
  padding: 0 16px;
  margin-bottom: 4px;
  transition: all 0.3s;
  cursor: pointer;

  div {
    display: flex;
    height: 100%;
    align-items: center;
  }

  &:hover {
    transform: translateX(5px);
  }

  .time {
    color: #fff;
    display: inline-flex;
    width: 64px;
  }

  .match-status {
    width: 80px;
    color: #fff;
  
    &.live {
      color: #c1272d;
  
      .match-status-text {
        animation: Twinkle 1s infinite;
      }
    }
  
    &.neutral {
      color: #fff;
    }
  }

  .team-section {
    display: flex;
    flex-direction: column;
    width: 165px;
    margin-right: 45px;

    .team-row {
      display: flex;
      align-items: center;
      margin-left: 10px;

      .team-logo {
        width: 20px;
        height: 20px;
        object-fit: cover;
        border-radius: 50%;
        margin: 0 6px 0 3px;
      }

      .team-name {
        width: 130px;
        color: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: left;
      }
    }
  }

  .score-summary {
    flex: 1;
    justify-content: center;
    font-weight: 500;
    color: #fff;
    justify-content: flex-start;
    min-width: 190px;
    overflow-x: hidden;
    display: flex;

    .score-item{
      display: flex;
      flex-direction: column;
      justify-content: center;
      color: #fff;

      >div{
        width: 34px;
        text-align: center;
      }

      .score-total {
        font-weight: bold !important;
        min-width: 40px;
        text-align: center;
        margin: 0 12px 0 4px;
      }
    }
  }

  .match-footer {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .live-icon-wrap {
      display: flex;
      align-items: center;
      margin-right: 8px;
      color: #fff;
    }

    .odd-list {
      width: 266px;
      background-color: transparent; 
    
      &.odd-title {
        background: transparent; 
      }
    
      .odd-tab {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 100%;
    
        .odd-half {
          width: 100%;
        }
      }
    
      .odd-item {
        justify-content: center;
        color: #fff; 
        background-color: transparent; 
    
        &.tw {
          width: 52px;
        }
    
        &.sp {
          width: 100px;
        }
    
        &.tp {
          width: 114px;
        }
    
        &.handicap {
          flex: auto;
          width: 100%;
          color: #f60; /* Maintain the orange color for the handicap */
          display: flex;
          flex-direction: row;
          background-color: transparent; /* Remove background */
        }
      }
    
      &.value {
        .tw, .sp, .tp {
          border-left: none; /* Remove border */
        }
      }
    }
  }
}

.game-category-basketball {
  width: 100%;
  height: 80px;
  background: #1e1e1e;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  box-sizing: border-box;
  padding: 0px 0px 0px 16px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    transform: translateX(5px);
  }

  .game-category-left-aligned {
    display: flex;
    flex-direction: row;
    align-items: center;

    .team-logo {
      display: flex;
      width: 20px;
      height: 20px;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      margin: 0px 6px 0px 3px;
    }

    .team-name {
      color: #fff;
    }
  }

  .game-category-right-aligned {
    display: flex;
    flex-direction: row;
    align-items: center;

    .score {
      width: 56px;
      justify-content: center;
      color: #fff;
  
      .hot {
        width: 14px;
        height: 14px;
        margin-right: 2px;
      }
    }

    .odds-title {
      display: flex;
      justify-content: center;
      color: #fff;
  
      .tw {
        display: flex;
        justify-content: center;
        width: 52px;
      }

      .sp {
        display: flex;
        justify-content: center;
        width: 100px;
      }

      .tp {
        display: flex;
        justify-content: center;
        width: 114px;
      }
    }
  }
}
