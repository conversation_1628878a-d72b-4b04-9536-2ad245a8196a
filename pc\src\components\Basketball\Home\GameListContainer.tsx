import './GameListContainer.less'

import { filterCompetitionsWithFavorite, momentTimeZone, simpleCloneObj, sortByMatchTime } from 'iscommon/utils';
import { inject, observer } from 'mobx-react';
import { useCallback, useEffect, useRef, useState } from 'react';

import FavoriteMatches from './FavoriteMatches';
import GameCategory from './GameCategory';
import { getCompetitionList } from 'iscommon/api/basketball/home';
import { GlobalConfig } from 'iscommon/const/globalConfig';
import { HomeGameTab } from 'iscommon/const/constant';
import HomeLoading from './HomeLoading';
import Loading from '@/components/Loading';
import UpComing from './UpComing';
import { useMatchTimer } from 'iscommon/hooks/apiData';

const GameListContainer = inject('store')(
  observer((props: any) => {
    const { WebConfig } = props.store;
    const { WebHome } = props.store.Basketball;
    const { currentGameTab, isSortByTime, date, homeCompetitions, favoriteMatches, favoriteCompetitions, serverTime } = WebHome;
    const competitionsRef = useRef([]);
    const isSortByTimeRef = useRef(false);
    const [loading, setLoading] = useState(true);

    const refTimer = useRef<NodeJS.Timeout | null>(null);

    const setFormatCompetitionList = useCallback(() => {
      const list = simpleCloneObj(competitionsRef.current);
      if (list.length > 0) {
        const fList = filterCompetitionsWithFavorite(list, favoriteMatches, favoriteCompetitions);
        if (isSortByTimeRef.current) {
          // order by time
          WebHome.setHomeCompetitions(fList.concat(sortByMatchTime(list)));
        } else {
          WebHome.setHomeCompetitions(fList.concat(list));
        }
      }
    }, [favoriteCompetitions, favoriteMatches, WebHome]);

    const getListData = useCallback(async () => {
      try {
        const apiParams = {
          dateFilter: momentTimeZone(date, 'YYYY-MM-DDTHH:mm:SS.SSS') + GlobalConfig.timezone,
          listType: currentGameTab,
        };
        console.log('🏀 Basketball GameListContainer: Fetching with params:', apiParams);

        const response = await getCompetitionList(apiParams);
        console.log('🏀 Basketball GameListContainer: Full API response:', response);

        const { competitions, total } = response;
        console.log('🏀 Basketball GameListContainer: competitions received:', {
          count: competitions.length,
          total,
          competitions: competitions.map((comp: any) => ({
            id: comp.competitionId,
            name: comp.competitionName,
            matchCount: comp.matches?.length || 0,
            matches: comp.matches?.map((m: any) => ({ id: m.matchId, homeTeam: m.homeTeam?.name, awayTeam: m.awayTeam?.name })) || []
          }))
        });
        requestAnimationFrame(() => {
          competitionsRef.current = JSON.parse(JSON.stringify(competitions));
          setFormatCompetitionList();
        });
        if (currentGameTab === HomeGameTab.Live) {
          // interval loop getData
          WebConfig.setOnTypeLiveCount(total);
          // refTimer.current = setTimeout(getListData, 20000);
        }
        setLoading(false);
      } catch (e) {
        console.log(e);
      }
    }, [currentGameTab, date, setFormatCompetitionList, WebConfig]);

    useEffect(() => {
      if (refTimer.current) {
        clearTimeout(refTimer.current);
      }
      setLoading(true);
      WebHome.setHomeCompetitions([]);
      if (currentGameTab !== 4) {
        getListData();
      } else {
        WebHome.setHomeCompetitions([]);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [currentGameTab, date]);

    useEffect(() => {
      isSortByTimeRef.current = isSortByTime;
      setFormatCompetitionList();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isSortByTime, favoriteCompetitions, favoriteMatches]);

    // update server time every 60s
    useMatchTimer(serverTime, WebHome, getListData);

    return currentGameTab !== HomeGameTab.Favourite ? (
      <>
        <Loading
          loading={loading}
          isEmpty={currentGameTab !== HomeGameTab.Favourite && !homeCompetitions.length}
          emptyText="There are no matches at this moment"
          renderLoading={<HomeLoading />}
        >
          {/* live list */}
          {homeCompetitions.map((item: any, index: number) => {
            return <GameCategory key={item.competitionId + index} competition={item} />;
          })}
        </Loading>
        {/* upcoming list */}
        {!loading && currentGameTab === HomeGameTab.Live && <UpComing />}
      </>
    ) : (
      <FavoriteMatches />
    );
  }),
);

export default GameListContainer;
