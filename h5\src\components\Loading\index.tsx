import { DotLoading } from 'antd-mobile';
import React from 'react';
import CommonEmpty from '../Common/CommonEmpty';

import styles from './index.less';

interface Props {
  loading?: boolean;
  isEmpty?: boolean;
}

// @ts-ignore
const Loading: React.FC<Props> = (props) => {
  const { loading = false, isEmpty = false, children } = props;

  if (loading) {
    return (
      <div className={styles.loading}>
        <DotLoading />
      </div>
    );
  }

  if (isEmpty) {
    return (
      <div className={styles.loading}>
        <CommonEmpty />
      </div>
    );
  }

  return children || null;
};

export default Loading;
