import './index.less'

import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { inject, observer } from 'mobx-react';
import { Link, useParams } from 'umi';
import React, { useMemo } from 'react';

import { FallbackImage } from 'iscommon/const/icon';
import { getArrayFromString } from 'iscommon/utils';
import { getMatchDetailPageTime } from 'iscommon/utils/dataUtils';
import { StatusCodeEnum } from 'iscommon/const/constant';

interface Props {
  store?: any;
}

interface StatusTextProps {
  matchHeaderInfo: any;
  serverTime: number;
}

const StatusText = React.memo<StatusTextProps>(({ matchHeaderInfo, serverTime }) => {
  const { isIng, statusText, isAni } = useMemo(
    () => getMatchDetailPageTime(matchHeaderInfo, serverTime),
    [matchHeaderInfo, serverTime],
  );
  return (
    <div className={`${isAni && 'twinkleScore'} ${isIng ? 'match-progress' : ''}`}>
      <span>{statusText}</span>
    </div>
  );
});

const MatchHeader: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: { Match },
    } = props;
    const { serverTime, matchHeaderInfo } = Match;
    const { homeTeam, awayTeam, statusId, homeScores: hsocres = [], awayScores: ascores = [] } = matchHeaderInfo || {};

    const { categoryId: matchId } = useParams<{ categoryId: string }>();

    const homeScores = useMemo(() => getArrayFromString(hsocres), [hsocres]);
    const awayScores = useMemo(() => getArrayFromString(ascores), [ascores]);

    const getFormattedScore = (scores: number[], isHome: boolean): React.ReactNode => {
      if (!Array.isArray(scores)) return null;
    
      const baseScore = scores[5] !== 0 ? scores[5] : scores[0];
      const penaltyScore = scores[6] !== 0 ? scores[6] : null;
    
      if (penaltyScore !== null) {
        return isHome ? (
          <>
            <span style={{ color: '#9e9e9e', fontSize: '16px' }}>({penaltyScore})</span> {baseScore}
          </>
        ) : (
          <>
            {baseScore} <span style={{ color: '#9e9e9e', fontSize: '16px' }}>({penaltyScore})</span>
          </>
        );
      }
    
      return <>{baseScore}</>;
    };

    return (
      <div className='common-match-container'>
        <Link className='home-team-container' to={GlobalUtils.getPathname(PageTabs.team, homeTeam?.id)}>
          <img className='team-logo' src={homeTeam?.logo || FallbackImage} alt={homeTeam?.name}/>
          <span className='team-name'>{homeTeam?.name}</span>
        </Link>
        {statusId ? (
          <div className='match-score-container'>
            {statusId === StatusCodeEnum.NotStarted ? (
              <>
                <span className='match-not-start'>VS</span>
                <span className='match-time'>Not Started</span>
              </>
            ):(
              <>
                <div className='score-container'>
                  <span className='score-text'>{getFormattedScore(homeScores, true)} - {getFormattedScore(awayScores, false)}</span>
                </div>
                <StatusText matchHeaderInfo={matchHeaderInfo} serverTime={serverTime} />
              </>
            )}
            {statusId > StatusCodeEnum.FirstHalf && (
              <div className='score-time-text'>
                (HT {homeScores[1] || 0}:{awayScores[1] || 0})
              </div>
            )}
          </div>
        ) : null}
        <Link className='away-team-container' to={GlobalUtils.getPathname(PageTabs.team, awayTeam?.id)}>
          <img className='team-logo' src={awayTeam?.logo || FallbackImage} alt={awayTeam?.name}/>
          <span className='team-name'>{awayTeam?.name}</span>
        </Link>
      </div>
      // <CommonMatchHeader inProgress={!InProgressStatusEnum.includes(statusId)} matchId={matchId} background="#152b5c">
      //   <div className={styles.content}>
      //     <div className={styles.team}>
      //       <Image width={42} height={42} style={{ borderRadius: '50%' }} src={homeTeam?.logo} fit="contain" />
      //       <div className={styles.teamName}>{homeTeam?.name}</div>
      //     </div>
      //     {statusId ? (
      //       <div className={styles.pk}>
      //         {statusId === StatusCodeEnum.NotStarted ? (
      //           <>
      //             <div className={styles.vs}>VS</div>
      //             <div style={styles.vsText}>not started</div>
      //           </>
      //         ) : null}
      //         {statusId === StatusCodeEnum.End && <div className={styles.resultType}>FT</div>}
      //         {statusId !== StatusCodeEnum.NotStarted && (
      //           <div className={styles.score}>
      //             <div style={{ marginRight: 8 }}>{calculateScore(homeScores)}</div>-
      //             <div style={{ marginLeft: 8 }}>{calculateScore(awayScores)}</div>
      //           </div>
      //         )}
      //         {InProgressStatusEnum.includes(statusId) && (
      //           <StatusText matchHeaderInfo={matchHeaderInfo} serverTime={serverTime} />
      //         )}
      //         {statusId > StatusCodeEnum.FirstHalf && (
      //           <div className={styles.result}>
      //             (HT {homeScores[1] || 0}:{awayScores[1] || 0})
      //           </div>
      //         )}
      //       </div>
      //     ) : null}
      //     <div className={classNames(styles.team, styles.right)}>
      //       <Image width={42} height={42} style={{ borderRadius: '50%' }} src={awayTeam?.logo} fit="contain" />
      //       <div className={styles.teamName}>{awayTeam?.name}</div>
      //     </div>
      //   </div>
      // </CommonMatchHeader>
    );
  }),
);

export default MatchHeader;
