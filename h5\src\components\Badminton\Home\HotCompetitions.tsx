import './HotCompetitions.less';

import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { useEffect, useState } from 'react';

import { CategoryIcon } from 'iscommon/const/icon';
import { getCompetitionHots } from 'iscommon/api/competition-hot';
import { Link } from 'umi';
import Loading from '@/components/Loading';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

interface Hot {
  id: string;
  name: string;
  logo: string;
  path: string;
  active: boolean;
}

const HotCompetitions = () => {
  const labelMaps = useTranslateKeysToMaps(['Popular']);

  const [hots, setHots] = useState<Hot[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    setLoading(true);
    getCompetitionHots().then((res) => {
      setHots(res);
      setLoading(false);
    });
  }, []);

  return (
    <div className='common-hot-competition-container'>
      <div className='container-title'>{labelMaps.Popular}</div>
      <div className='container-body'>
        <Loading loading={loading} isEmpty={!hots.length}>
          {hots.map((hot) => {
            console.log('pagetabs', PageTabs.competition, hot.id)
            return (
              <Link to={GlobalUtils.getPathname(PageTabs.competition, hot.id)} className='hot-competition-item' key={hot.id}>
                <img className="hot-competition-logo" src={(CategoryIcon as any)[hot.logo] || hot.logo} alt={hot.name} loading="lazy"/>
                <span className="hot-competition-name">{hot.name}</span>
              </Link>
            );
          })}
        </Loading>
      </div>
    </div>
  );
};

export default HotCompetitions;
