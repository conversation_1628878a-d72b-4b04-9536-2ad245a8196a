// .container_box {
//   display: flex;
//   flex-direction: column;
// }

// .radio_container {
//   background: rgb(241, 241, 241);
//   display: flex;
//   flex-direction: row;
//   align-items: center;
//   padding: 12px 0;
// }

// .color_999 {
//   color: #999;
// }

// .radio_btn {
//   padding: 8px 20px;
//   font-size: 24px;
//   color: #999;
//   display: inline-block;
//   border-radius: 4px;
//   overflow: hidden;
//   white-space: nowrap;
//   text-overflow: ellipsis;
//   margin-left: 24px;
// }

// // .radio_btn:not(:first-child) {
// //   margin-left: 24px;
// // }

// .active {
//   background: #2196f3;
//   color: #fff;
// }

// .table_title_team {
//   height: 60px;
//   display: flex;
//   flex-direction: row;
//   align-items: center;
// }

// .table_title_team_name {
//   color: #999;
//   padding-left: 20px;
// }

// .table_title_team_img {
//   width: 38px;
//   height: 38px;
//   object-fit: fill;
//   overflow: hidden;
//   border-radius: 50%;
//   margin-left: 24px;
// }

.competition-standing-tab-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;

  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .container-body {
    background: #1e1e1e;
    padding: 10px;

    .custom-standing-table {
      width: 100%;
      display: flex;
      flex-direction: column;
      border-collapse: collapse;
    
      .header-row, .table-row {
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid #2c2c2c;
        text-align: left;
        color: #fff;
      }

      .header-row {
        background-color: #1E1E1E;
        font-weight: bold;
      }

      .table-row {
        background-color: #121212;
      }
    
      .promotion-cell {
        position: relative;
        display: flex;
        align-items: center; 

        .promotion-indicator {
          width: 8px; 
          height: 100%;
          margin-right: 5px;
          border-radius: 2px; 
          background-color: transparent;
        }
      }

      .header-cell, .table-cell {
        flex: 0.5;
        text-align: center;
        padding: 10px;
        font-size: 24px;
        align-content: center;
      }

      .header-cell:first-child, 
      .table-cell:first-child {
        flex: 0.5;
      }

      .header-cell:nth-child(2), 
      .table-cell:nth-child(2) {
        flex: 3;  
      }

      .team-cell {
        display: flex;
        align-items: center;
        flex: 3;
        text-align: left;

        .team-logo {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin: 5px 10px;
          overflow: hidden;
          background-size: cover;
        }
  
        .team-name {
          font-size: 24px;
          color: #fff;
        }
      }
    }

    .standing-promotion-container {
      margin-top: 20px;

      .promotion-container {
        display: flex;
        align-items: center;
        color: #fff; 
        text-align: center;

        .promotion-badge {
          display: inline-block;
          margin-right: 5px;
          border-radius: 50%;
        }
      }
    }
  }
}