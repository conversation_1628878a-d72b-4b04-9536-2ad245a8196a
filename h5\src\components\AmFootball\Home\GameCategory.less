.homeGameCategory {
  width: 100%;
  height: 54px;
  background: #f1f1f1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 24px;
  box-sizing: border-box;
  padding: 0 12px;
  color: #666;
  cursor: pointer;

  > div {
    display: flex;
    height: 100%;
    align-items: center;
  }

  .cnameContainer {
    flex: 3;
    padding-right: 16px;
    overflow: hidden;
    box-sizing: border-box;

    .countryName {
      margin-right: 10px;
      white-space: nowrap;
      font-family: "Urbanist-Regular", sans-serif;
      font-weight: 400;
      color: #999;
    }

    .competitionName {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      cursor: pointer;
      font-family: Roboto-Medium, Roboto;
      font-weight: 500;
      color: #999;
    }
  }

  .countryLogo {
    display: flex;
    width: 28px;
    height: 28px;
    margin-right: 10px;
    border-radius: 50%;
    overflow: hidden;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }

  .countContainer {
    flex: 1;
    justify-content: space-between;

    .count {
      width: 100px;
    }

    .touchArea {
      padding: 20px;
    }
  }

  .countryName {
    color: #999999;
  }
}

.homeGameList {
  border-top: 2px solid #e3e3e3;
  border-bottom: 2px solid #e3e3e3;
  background-color: #fff;

  .listItem {
    display: flex;
    height: 121px;
    border-top: 2px solid #e3e3e3;

    .time {
      line-height: 39px;
      font-family: "Urbanist-Regular", sans-serif;

      &:first-child {
        margin-bottom: 6px;
      }
    }

    div {
      align-items: center;
      justify-content: center;
    }

    .listLeft {
      width: 118px;
      display: flex;
      flex-direction: column;
      color: #999999;
      font-size: 18px;
    }

    .spt {
      width: 50px;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: center;
      font-size: 24px;
      color: #87C31D;
    }
    .listRight {
      width: 105px;
      display: flex;
      flex-direction: column;
      font-weight: bolder;
      justify-content: space-around;
      box-sizing: border-box;
      font-size: 24px;
      color: #999999;
      &.rowFlex{
        flex-direction: row;
        justify-content: center;
        align-items: center;
        
      }
      .listRightSection {
        flex: 1;
        flex-direction: column;
        font-weight: bolder;
        justify-content: space-around;
        box-sizing: border-box;
        display: flex;
        height: 92px;
        &:first-child {
          border-right: 1px solid #e3e3e3;
        }
      }
      .odd {
        height: 30px;
      }
    }

    .listContent {
      flex: 1;
      border-left: 1px solid #e3e3e3;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 0 14px;
      overflow: hidden;

      .vsCountry {
        display: flex;
        flex-direction: column;
        justify-content: space-around;
        align-items: flex-start;
        flex: 1;
        overflow: hidden;

        .vsCountryItem {
          display: flex;
          overflow: hidden;
          width: 100%;
          justify-content: flex-start;
          height: 40%;

          .teamName {
            font-size: 26px;
            font-weight: 500;
            color: #333333;
            margin-right: 6px;

            .wangqiu{
              width: 24px;
              height: 24px;
            }
          }

          .squareLogo {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 32px;
            height: 32px;
            margin-right: 14px;
            border-radius: 50%;
            overflow: hidden;
            > img {
              max-width: 32px;
              max-height: 32px;
            }
          }

          .card {
            padding: 0 5px;
            border-radius: 2px;
            margin-left: 3px;
            line-height: 34px;
            color: #fff;

            &.red {
              background-color: #c1272d;
            }

            &.yellow {
              background-color: #ffa830;
            }
          }
        }
      }
    }
  }

  :first-child {
    border-top: none;
  }

  .ing {
    color: #c1272d;
  }
}
