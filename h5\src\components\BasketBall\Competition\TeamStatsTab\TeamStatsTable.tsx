import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { useMemo, useState } from "react";
import { LeftOutline, RightOutline } from 'antd-mobile-icons';
import './TeamStatsTable.less'
import { Button } from "antd-mobile";
import { Link } from "umi";
import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";

const Pagination = ({ pageInfo, onPageChange }: any) => {
  const { pageNum, pageSize, total } = pageInfo;
  const totalPages = Math.ceil(total / pageSize);

  const handlePrev = () => {
    if (pageNum > 1) {
      onPageChange(pageNum - 1);
    }
  };

  const handleNext = () => {
    if (pageNum < totalPages) {
      onPageChange(pageNum + 1);
    }
  };

  return (
    <div className="pagination">
      <Button onClick={handlePrev} disabled={pageNum === 1} size="mini" shape="rounded">
        <LeftOutline />
      </Button>
      <span>{`Page ${pageNum} of ${totalPages}`}</span>
      <Button onClick={handleNext} disabled={pageNum === totalPages} size="mini" shape="rounded">
        <RightOutline />
      </Button>
    </div>
  );
};

const TeamStatsTable = ({ data }: { data: any[] }) => {

  const labelMaps = useTranslateKeysToMaps(['Team']);
  const commonTitleMaps = useMemo(() => [
    { label: 'PTS', value: 'points' },
    { label: 'FGM', value: 'fieldGoalsScored' },
    { label: 'FGA', value: 'fieldGoalsTotal' },
    { label: '3PM', value: 'threePointersScored' },
    { label: '3PA', value: 'threePointersTotal' },
    { label: 'FTM', value: 'freeThrowsScored' },
    { label: 'FTA', value: 'freeThrowsTotal' },
    { label: 'OREB', value: 'offensiveRebounds' },
    { label: 'DREB', value: 'defensiveRebounds' },
    { label: 'REB', value: 'rebounds' },
    { label: 'AST', value: 'assists' },
    { label: 'STL', value: 'steals' },
    { label: 'BLK', value: 'blocks' },
    { label: 'TOV', value: 'turnovers' },
  ], []);

  const [pageInfo, setPageInfo] = useState<any>({
    pageNum: 1,
    pageSize: 15,
    total: data.length,
  });

  const onPageChange = (newPageNum: number) => {
    setPageInfo((prevPageInfo: any) => ({
      ...prevPageInfo,
      pageNum: newPageNum,
    }));
  };

  const paginatedData = useMemo(() => {
    const startIndex = (pageInfo.pageNum - 1) * pageInfo.pageSize;
    const endIndex = startIndex + pageInfo.pageSize;
    return data.slice(startIndex, endIndex);
  }, [data, pageInfo]);

  return (
    <div className="basketball-team-stats-table-container">
      <div className="custom-stats-table">
        <div className="table-header">
          <div className="header-cell">#</div>
          <div className="header-cell team-cell">{labelMaps.Team}</div>
          {commonTitleMaps.map((stat) => (
            <div className="header-cell" key={stat.value}>{stat.label}</div>
          ))}
        </div>

        <div className="table-body">
          {paginatedData.map((team, index) => (
            <div className="table-row" key={index}>
              <div className="table-cell">{(pageInfo.pageNum - 1) * pageInfo.pageSize + index + 1}</div>

              <Link className="table-cell team-cell" to={GlobalUtils.getPathname(PageTabs.team, team.team.id)}>
                <img src={team.team.logo} alt={team.team.name} className="team-icon" />
                <span className="team-name">{team.team.name}</span>
              </Link>

              {commonTitleMaps.map((stat) => (
                <div className="table-cell" key={stat.value}>
                  {team[stat.value] ? team[stat.value] : '0'}
                </div>
              ))}
            </div>
          ))}
        </div>
        <Pagination pageInfo={pageInfo} onPageChange={onPageChange} />
      </div>
    </div>
  )
}

export default TeamStatsTable;