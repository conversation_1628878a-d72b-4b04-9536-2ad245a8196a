// @ts-nocheck
import _ from 'lodash';

import store from 'iscommon/mobx';

const mixinCompetitionOdds = (matchRecentOdds, oldOdds = {}, oldStatus = {}) => {
  // console.log(matchRecentOdds, oldOdds, 'matchRecentOdds')
  const cloneList = _.cloneDeep(oldOdds);
  const allStatusMap = _.cloneDeep(oldStatus)
  for(let matchId in matchRecentOdds) {
    const homeValidOdds = matchRecentOdds[matchId] ? matchRecentOdds[matchId].filter(item => item.companyId == 2) : []
    if(cloneList[matchId] && Array.isArray(homeValidOdds)) {
      if(homeValidOdds.length > 0) {
        const {matchStatus} = homeValidOdds[0]
        allStatusMap[matchId] = matchStatus
        for(let h of homeValidOdds) {
          const { oddsType } = h;
          for (let i = 0; i < cloneList[matchId].length; i++) {
            const item = cloneList[matchId][i];
            if (oddsType === item.oddsType) {
              cloneList[matchId][i] = {
                ...h,
                lastOddsData: item.oddsData
              };
            }
          }
        }
      }
    } else {
      cloneList[matchId] = homeValidOdds;
    }
  }
  return {cloneList, allStatusMap};
}

const syncHomeOdds = (matchRecentOdds) => {
  const {  Smallball: {SmallballCommon}  } = store;
  const { homeCompetitionOdds, latestMatchStatus } = SmallballCommon;
  const {cloneList, allStatusMap} = mixinCompetitionOdds(matchRecentOdds, homeCompetitionOdds, latestMatchStatus);
  SmallballCommon.setHomeOdds(cloneList);
  SmallballCommon.setLatestMatchStatus({allStatusMap});
};

export const homeDataControllerType = {
  syncHomeOdds: 'syncHomeOdds',
};

const smallBallhomeDataController = {
  [homeDataControllerType.syncHomeOdds]: syncHomeOdds,
};

export default smallBallhomeDataController;
