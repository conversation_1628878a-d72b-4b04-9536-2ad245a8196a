// .positionWrap {
//   display: flex;
//   padding: 30px 24px;
//   background-color: #fff;
//   box-sizing: border-box;

//   .positionLeft {
//     flex: 1;
//     display: flex;
//     flex-direction: column;

//     .positionItem {
//       flex: 1;
//     }

//     .positionItemTitle {
//       margin-bottom: 12px;
//       width: 100%;
//       height: 24px;
//       font-size: 24px;
      
//       font-weight: 500;
//       color: #999999;
//       line-height: 25px;
//     }

//     .positionItemDesc {
//       margin-bottom: 8px;
//       width: 100%;
//       font-size: 24px;
      
//       font-weight: 500;
//       color: #333333;
//       line-height: 25px;
//     }
//   }

//   .positionRight {
//     padding-right: 36px;

  //   .field {
      // position: relative;
      // display: flex;
      // flex-direction: column;
      // background-position: center center;
      // background-repeat: no-repeat;
      // background-size: 100% 100%;
      // background-image: url('../../../../assets/images/footballbg.png');
      // width: 200px;
      // height: 322px;
  //   }

  //   .lineupCourt {
      // position: absolute;
      // display: flex;
      // justify-content: center;
      // align-items: center;
      // width: 40px;
      // height: 40px;
      // line-height: 40px;
      // text-align: center;
      // border-radius: 50%;
      // background-color: #fff;
      // color: #de1e30;

  //     &:after {
        // position: absolute;
        // content: "";
        // left: 0;
        // top: 0;
        // right: 0;
        // bottom: 0;
        // border: 1px solid #ff7086;
        // border-radius: 50%;
        // z-index: 1;
  //     }
  //   }
  // }
// }

.player-position-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 5px;
  background: #121212;
  border-radius: 24px;
  padding: 10px;

  .player-stats-container {
    display: flex;
    flex-direction: column;

    .position-item {
      display: flex;
      flex-direction: column;
      font-size: 22px;
      font-weight: bold;
      padding: 10px;

      .position-value {
        color: #fff;
        font-size: 20px;
      }
    }
  }

  .match-field {
    position: relative;
    display: flex;
    flex-direction: column;
    background-position: center center;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-image: url('../../../../assets/images/footballbg.png');
    width: 200px;
    height: 322px;
  
    .lineup-court {
      position: absolute;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 40px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      border-radius: 50%;
      background-color: #fff;
      color: #de1e30;
  
      &:after {
        position: absolute;
        content: "";
        left: 0;
        top: 0;
        right: 0;
        bottom: 0;
        border: 1px solid #ff7086;
        border-radius: 50%;
        z-index: 1;
      }
    }
  }
}
