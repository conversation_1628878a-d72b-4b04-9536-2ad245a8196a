# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: DEV CI

on:
  push:
    branches: [ "staging" ]

jobs:
  build:

    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16.14.0]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
    - uses: actions/checkout@v3

    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'yarn'
  
    - name: Build/Common
      run: yarn

    - name: Build/H5
      run: |
        ls
        echo "install common packages"
        yarn
        echo "cd h5"
        cd h5/
        echo "install h5 packages"
        yarn
        echo "build"
        yarn build
        echo "upload"
        yarn run upload
        ls
      shell: bash

    - name: Build/PC
      run: |
        ls
        echo "install common packages"
        yarn
        echo "cd pc"
        cd pc/
        echo "install pc packages"
        yarn
        echo "build"
        yarn build
        echo "upload"
        yarn run upload
        ls
      shell: bash
  
    - name: deploy PC File to server
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.SERVER_IP }}
        username: ${{ secrets.SSH_USERNAME }}
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        timeout: 100s
        source: './pc/dist/*'
        target: '/home/<USER>/ssr/pc/dist'
        strip_components: 2

    - name: deploy PC Node File to server
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.SERVER_IP }}
        username: ${{ secrets.SSH_USERNAME }}
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        timeout: 100s
        source: './node-pc/*'
        target: '/home/<USER>/ssr/ssr-pc'
        strip_components: 1
  
    - name: deploy H5 File to server
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.SERVER_IP }}
        username: ${{ secrets.SSH_USERNAME }}
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        timeout: 100s
        source: './h5/dist/*'
        target: '/home/<USER>/ssr/h5/dist'
        strip_components: 2

    - name: deploy H5 Node File to server
      uses: appleboy/scp-action@master
      with:
        host: ${{ secrets.SERVER_IP }}
        username: ${{ secrets.SSH_USERNAME }}
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        timeout: 100s
        source: './node-h5/*'
        target: '/home/<USER>/ssr/ssr-h5'
        strip_components: 1
