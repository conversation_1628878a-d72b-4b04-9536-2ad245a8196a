import { PlayerLink } from '@/components/Common/Football/Link';
import Loading from '@/components/Loading';
import { Avatar, Badge, List } from 'antd-mobile';
import { getFootBallTeam } from 'iscommon/api/football-team';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { inject, observer } from 'mobx-react';
import { useEffect, useMemo, useState } from 'react';
import styles from './index.less';
import './index.less'
import { FallbackCategoryImage, FallbackPlayerImage } from 'iscommon/const/icon';
import { Link } from 'umi';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';

const Squad = inject('store')(
  observer((props: any) => {
    const {
      store: { Team },
    } = props;
    const { teamId } = Team;
    const [dataSource, setDataSource] = useState<any>({});
    const [loading, setLoading] = useState<boolean>(false);

    useEffect(() => {
      if (teamId) {
        setLoading(true);
        getFootBallTeam({
          teamId,
        }).then((data) => {
          setDataSource(data);
          setLoading(false);
        });
      }
    }, [teamId]);

    const labelMaps = useTranslateKeysToMaps(['Coach', 'Midfielders', 'Forwards', 'Defender', 'Goalkeeper', 'Player Value',]);
    const positionList = useMemo(
      () => [
        { key: 'F', label: labelMaps.Forwards },
        { key: 'M', label: labelMaps.Midfielders },
        { key: 'D', label: labelMaps.Defender },
        { key: 'G', label: labelMaps.Goalkeeper },
      ],
      [labelMaps],
    );

    const formatBirthday = (birthday: any) => {
      return new Date(birthday * 1000).toLocaleDateString('en-GB', {
        day: '2-digit',
        month: 'short',
        year: 'numeric'
      });
    };

    const formatMarketValue = (value: string, currency: string): string => {
      const stringValue = value != null ? value.toString().trim() : '';
      if (stringValue === '0' || isNaN(parseFloat(stringValue))) {
        return '-';
      }

      const numericValue = parseFloat(value);
      if (isNaN(numericValue)) {
        return `Invalid value ${currency}`;
      }
  
      const valueInMillions = numericValue / 1_000_000;
      return `${currency} ${valueInMillions.toFixed(2)} M`;
    };

    console.log('datasource123', dataSource)

    return (
      <div className='team-squad-container'>
        <Loading loading={loading} isEmpty={!dataSource?.coach && !dataSource?.squad}>
          {dataSource?.coach && (
            <>
              <div className='container-title'>{labelMaps.Coach}</div>
              <div className='container-body'>
                <div className="player-card">
                  <div className="player-info">
                    <div className='player-image-container'>
                      <img className="player-image" src={dataSource?.coach.logo || FallbackPlayerImage} alt={dataSource?.coach?.name}/>
                    </div>
                    <div className="player-details">
                      <span className="player-name">{dataSource?.coach?.name}</span>
                      <div className='player-country'>
                        <img className='player-country-image' src={dataSource?.coach?.country?.logo || FallbackCategoryImage}/>
                        <span className='player-country-text'>{dataSource?.coach?.country?.name}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}

          {positionList.map((position) => {
            const players = dataSource?.squad?.[position.key] || [];
            return (
              <>
                <div className='container-title' key={position.key}>{position.label}</div>
                <div className='container-body'>
                  {players.map((item: any) => (
                    <Link to={GlobalUtils.getPathname(PageTabs.player, item.id)} className="player-card" key={item.id}>
                      <div className="player-info">
                        <div className='player-image-container'>
                          <img className="player-image" src={item.logo || FallbackPlayerImage} />
                          <Badge className='player-badge' content={item.shirtNumber} />
                        </div>
                        <div className="player-details">
                          <span className="player-name">{item.name}</span>
                          <div className='player-country'>
                            <span className='player-country-text'>{item.nationality}</span>
                          </div>
                        </div>
                      </div>
                      <div className='player-extra-info'>
                        <div className='player-footer'>
                          <span className='player-footer-value'>{item.age} years</span>
                          <span className='player-footer-label'>{formatBirthday(item.birthday)}</span>
                        </div>
                        <div className='player-footer'>
                          <span className='player-footer-value'>{formatMarketValue(item.marketValue, item.marketValueCurrency)}</span>
                          <span className='player-footer-label'>{labelMaps['Player Value']}</span>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </>
            )
          })}
        </Loading>
      </div>
      // <div className={styles.squad_container}>
      //   <Loading loading={loading} isEmpty={!dataSource?.coach && !dataSource?.squad}>
      //     <List header={<span className={styles.list_header}>{labelMaps.Coach}</span>}>
      //       <List.Item className={styles.list_item}>
      //         <div className={styles.squad_list_item}>
      //           <div className={styles.shirtNumber_column}>C</div>
      //           <Avatar src={dataSource.coach?.logo} className={styles.people_avatar} />
      //           <div className={styles.people_column}>
      //             <span className={styles.people_name}>{dataSource.coach?.name}</span>
      //             <span className={styles.people_countryName}>{dataSource.coach?.country?.name}</span>
      //           </div>
      //         </div>
      //       </List.Item>
      //     </List>

      //     {!!dataSource?.squad &&
      //       positionList.map(({ key, label }) => {
      //         const item = dataSource.squad[key];
      //         return (
      //           <List key={label} header={<span className={styles.list_header}>{label}</span>}>
      //             {item.map((val: any) => (
      //               <List.Item key={val.name} className={styles.list_item}>
      //                 <div className={styles.squad_list_item}>
      //                   <div className={styles.shirtNumber_column}>{val.shirtNumber}</div>
      //                   <PlayerLink className={styles.flex1} playId={val.id}>
      //                     <Avatar src={val.logo} className={styles.people_avatar} />
      //                     <div className={styles.people_column}>
      //                       <span className={styles.people_name}>{val.name}</span>
      //                       <span className={styles.people_countryName}>{val.nationality}</span>
      //                     </div>
      //                   </PlayerLink>
      //                   <div className={styles.markerValue_column}>
      //                     {val.marketValue ? `${(val.marketValue / 1000000).toFixed(1)}M${val.marketValueCurrency}` : '-'}
      //                   </div>
      //                 </div>
      //               </List.Item>
      //             ))}
      //           </List>
      //         );
      //       })}
      //   </Loading>
      // </div>
    );
  }),
);

export default Squad;
