// @ts-nocheck
import igsRequest from 'iscommon/request/instance';
// import 'iscommon/mock/home-mock';
import { HomeGameTab } from 'iscommon/const/constant';
import { getArrayFromString, getTodayDateFilter } from 'iscommon/utils';
import smallBallhomeDataController from 'iscommon/mqtt/smallball/homeDataController';
import { EsportsInLiveStatusEnum, EsportsTypeList } from '../../const/esports/constant';

let controller = null;

// 04:00 ===> 0400
export const formatMatchTimeToNumber = (timeString = '') => {
  return Number(timeString.replace(/[\s|:]/g, ''));
};

const filterValidMatch = (matches) => {
  const validMatch = [];
  const eachMatchIds = [];
  let minTime = 0;
  const currentTime = Math.floor(Date.now() / 1000);
  for (let j = 0; j < matches.length; j++) {
    const matchItem = matches[j];
    const { statusId, matchTime, id, scores } = matchItem;
    const matchStatus = statusId
    const matchId = id
    let isValid = true;
    // 筛选出正在进行中的比赛
    if (EsportsInLiveStatusEnum.includes(matchStatus)) {
      matchItem.inProgress = true;
      matchItem.period = true;
      matchItem.matchActiveTime = matchTime;
      // If match is older than last 3 hours
      if (matchTime < (currentTime - 3 * 60 * 60)) {
        isValid = false;
      }
    } else if(!matchTime || statusId === 0) {
      // 非live的，如果没有matchTime，过滤掉
      isValid = false;
    }
    if (isValid) {
      minTime = minTime === 0 ? matchTime : Math.min(matchTime, minTime);
      validMatch.push({
        ...matchItem,
        matchStatus,
        matchId,
        scores: getArrayFromString(scores)
      });
      eachMatchIds.push(matchId);
    }
  }

  return { validMatch, minTime, eachMatchIds };
};

const filterCompetitions = (result) => {
  try {
    let total = 0;
    let matchIds = [];
    const { matchGroups } = result;
    let competitions = []
    for (let i = 0; i < matchGroups.length; i++) {
      const {
        uniqueTournament: {
          name,
          id,
          category,
          logo,
          esportsType,
        },
        matches
      } = matchGroups[i]

      const country = EsportsTypeList.find(item=>item.id === esportsType)

      let comp = {
        additionalCompetitionName:'',
        competitionId: id,
        competitionName: name,
        country: country,
        onlineCount: null
      }
      if (!matches || !matches.length) {
        comp['matches'] = [];
        continue;
      }
      const { validMatch, minTime, eachMatchIds } = filterValidMatch(matches);
      comp['matches'] = validMatch;
      total += validMatch.length;
      comp['minMatchTime'] = minTime;
      matchIds = matchIds.concat(eachMatchIds);

      competitions.push(comp)
    }
    return { competitions, total, matchIds };
  } catch (e) {
    return { competitions: [], total: 0, matchIds: [] };
  }
};

export const getCompetitionList = async ({ dateFilter = '', listType = 0 } = {}) => {
  if (controller) {
    controller.abort();
  }
  controller = new AbortController();
  const signal = controller.signal;
  return igsRequest
    .post(
      'competition/list',
      {
        listType, //列表类型，0-Live， 1-ALL, 2-Finished, 3-Scheduled
        dateFilter: listType !== 0 ? dateFilter.replace('UTC', '') : getTodayDateFilter(), // "2022-08-27"
        skipOdds: true,
      },
      {
        signal,
      },
    )
    .then((result) => {
      const { competitions, total, matchIds } = filterCompetitions(result);

      getMatchOddsAndSave({ matchIds });
      return { competitions, total };
    })
    .finally(() => {
      // controller = null
    });
};

export const getUpComingList = async () => {
  return igsRequest
    .post('competition/list', {
      listType: HomeGameTab.Scheduled,
      skipOdds: true,
      dateFilter: getTodayDateFilter(),
    })
    .then((result) => {
      const { competitions, total, matchIds } = filterCompetitions(result);
      getMatchOddsAndSave({ matchIds });
      return { competitions, total };
    });
};

export const getMatchOddsAndSave = async ({ matchIds = [] }) => {
  if (matchIds.length > 0) {
    igsRequest.post('match/odds/last', { matchIds }).then(({ matchRecentOdds }) => {
      smallBallhomeDataController.syncHomeOdds(matchRecentOdds);
    });
  }
};

export const getTournamentList = () => {
  return igsRequest.post("tournament/list").then((result) => {
    try {
      return result;
    } catch (e) {
      return {};
    }
  });
};
