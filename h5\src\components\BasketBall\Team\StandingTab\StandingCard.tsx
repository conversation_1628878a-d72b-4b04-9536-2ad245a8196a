import Loading from "@/components/Loading";
import { Collapse } from "antd-mobile";
import { getCompetitionStandings } from "iscommon/api/basketball/basketball-competition";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { inject, observer } from "mobx-react";
import { useState, useEffect } from "react";
import './StandingCard.less'
import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";
import { FallbackImage } from "iscommon/const/icon";
import _ from "lodash";
import { Link } from "umi";

const StandingsCard = inject('store')(
  observer((props: any) => {
    const {
      store: { 
        Basketball: {
          Team: { teamHeaderInfo } 
        }
      },
    } = props;

    const competitionId = teamHeaderInfo?.competitions?.[0]?.id
    const seasonId = teamHeaderInfo?.currentSeason?.id;

    const labelMaps = useTranslateKeysToMaps(['All', 'Home', 'Away', 'Standings', 'Team']);

    const [sourceList, setSourceList] = useState<any[]>([]);
    const [type] = useState<0 | 1 | 2>(0);

    const [loading, setLoading] = useState<boolean>(true);

    useEffect(() => {
      if (competitionId && seasonId) {
        getCompetitionStandings({ seasonId, competitionId, scope: 5 })
          .then(({ tables }: any) => {
            setSourceList(tables);
          })
          .finally(() => {
            setLoading(false);
          });
      } else if (competitionId && !seasonId) {
        setLoading(false);
      }
    }, [type, seasonId, competitionId]);

    const titleMap = [
      { label: 'W', value: 'won', },
      { label: 'L', value: 'lost', },
      { label: 'Win%', value: ({ won_rate }) => (won_rate * 100).toFixed(1), },
      { label: 'STR', value: 'streaks', },
      { label: 'GB', value: ({game_back}) => game_back === '-' ? '0.0' : game_back, },
    ];

    console.log('source list', sourceList)
    return (
      <div className='basketball-team-standing-container'>
        <div className='container-title'>{labelMaps.Standings}</div>
        <div className='container-body'>
          <Loading loading={loading} isEmpty={!sourceList?.length}/>
          <Collapse className='custom-collapse'>
            {sourceList.map((source, index) => (
              <Collapse.Panel
                key={index}
                title={
                  <div className="panel-header">
                    <span className='panel-title'>{source.name}</span>
                  </div>
                }
              >
                <div className='custom-common-standing-table'>
                  <div className='header-row'>
                    <div className="header-cell">#</div>
                    <div className="header-cell team-cell">{labelMaps.Team}</div>
                    {titleMap.map((item, index) => {
                      return (
                        <div className='header-cell' key={index}>{item.label}</div>
                      )
                    })}
                  </div>
                  {source.standings.map((item: any, index: any) => {
                    return (
                      <Link className="table-row" key={item?.team?.id} to={GlobalUtils.getPathname(PageTabs.team, item?.team?.id)}>
                        <div className="table-cell">{item?.position}</div>
                        <div className="table-cell team-cell">
                          <img className="team-logo" src={item?.team?.logo || FallbackImage} alt={item?.team?.name} loading="lazy"/>
                          <span className='team-name'>{item?.team?.name}</span>
                        </div>
                        {titleMap.map((title, index) => {
                          return (
                            <div className='table-cell' key={index}>
                              {_.isString(title.value) ? item[title.value] : title.value(item)}
                            </div>
                          );
                        })}
                      </Link>
                    )
                  })}
                </div>
              </Collapse.Panel>
            ))}
          </Collapse> 
        </div>
      </div>
    );
  }),
);

export default StandingsCard;