import { Image } from 'antd-mobile';
import classNames from 'classnames';
import { inject, observer } from 'mobx-react';
import React, { useMemo } from 'react';

import { StatusCodeEnum } from 'iscommon/const/constant';
import { getArrayFromString } from 'iscommon/utils';
import { calculateScore, getBasketBallScoreList, getBasketBallTotalScore, getHomeBasketBallMatchTimeText } from 'iscommon/utils/dataUtils';

import { BasketBallInLiveStatusEnum, BasketBallStatusCodeEnum } from 'iscommon/const/basketball/constant';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { FallbackImage } from 'iscommon/const/icon';
import { Link } from 'umi';
import './index.less'

interface Props {
  store?: any;
}

interface StatusTextProps {
  matchHeaderInfo: any;
  serverTime: number;
}

const StatusText = React.memo<StatusTextProps>(({ matchHeaderInfo, serverTime }) => {
  const { matchStatus, remainSeconds } = matchHeaderInfo;
  const { statusText, isIng } = useMemo(
    () => getHomeBasketBallMatchTimeText({ serverTime, matchStatus, remainSeconds }),
    [serverTime, matchStatus, remainSeconds ]
  );
  const displayText = statusText === 'FT' ? 'Full Time' : statusText === 'HT' ? 'Half Time' : statusText;

  return (
    <div className={`pk-time ${isIng ? 'in-progress' : ''}`}>
      {displayText}
    </div>
  );
});

const MatchHeader: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: {
        Basketball: { Match },
      },
    } = props;
    const { serverTime, matchHeaderInfo } = Match;
    const { homeTeam, awayTeam, matchStatus, homeScores = [], awayScores = [] } = matchHeaderInfo || {};
    const list: any[] = getBasketBallScoreList(matchStatus, homeScores, awayScores);

    const inProgress = BasketBallInLiveStatusEnum.includes(matchStatus);

    return (
      <div className='common-match-container'>
        <Link className='home-team-container' to={GlobalUtils.getPathname(PageTabs.team, homeTeam?.id)}>
          <img className='team-logo' src={homeTeam?.logo || FallbackImage} alt={homeTeam?.name} loading="lazy"/>
          <span className='team-name'>{homeTeam?.name}</span>
        </Link>

        <div className='score-container'>
          {matchStatus === BasketBallStatusCodeEnum.NotStarted ? (
            <>
              <span className='not-started'>VS</span>
              <span className='status-text'>Not Started</span>
            </>
          ) : (
            <>
              <div className='match-score'>
                <span className={`score-text ${inProgress ? 'in-progress' : ''}`}>{list[0]?.h || 0}</span>
                <span className={`score-text ${inProgress ? 'in-progress' : ''}`}>-</span>
                <span className={`score-text ${inProgress ? 'in-progress' : ''}`}>{list[0]?.w || 0}</span>
              </div>
              <StatusText matchHeaderInfo={matchHeaderInfo} serverTime={serverTime} />
            </>
          )}
        </div>

        <Link className='away-team-container' to={GlobalUtils.getPathname(PageTabs.team, awayTeam?.id)}>
          <img className='team-logo' src={awayTeam?.logo || FallbackImage} alt={awayTeam?.name} loading="lazy"/>
          <span className='team-name'>{awayTeam?.name}</span>
        </Link>
      </div>
    );
  }),
);

export default MatchHeader;
