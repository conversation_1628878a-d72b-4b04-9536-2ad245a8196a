.container {
  width: 100%;
  height: 100%;
  padding-top: 30px;
  box-sizing: border-box;
}

.content {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin-bottom: 50px;
}

.team {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-left: 24px;
  padding-right: 0;
  overflow: hidden;
  position: relative;

  .ballIcon {
    position: absolute;
    // transform: scale(0.7);
    font-size: 14px;
    width: 28px;
    height: 28px;
    top: 30px;
  }
  .ballIconLeft{
    left: 20px;
  }
  .ballIconRight{
    right: 20px;
  }
  &.right {
    padding-left: 0;
    padding-right: 24px;
  }

  .teamName {
    width: 100%;
    text-align: center;
    margin-top: 20px;
    height: 56px;
    font-size: 28px;
    color: #ffffff;
    line-height: 28px;
  }
}

.pk {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 240px;
  color: #fff;
  font-size: 24px;

  .vs {
    font-size: 56px;

    font-weight: 400;
    color: #ffffff;
    line-height: 1;
    margin-bottom: 10px;
  }

  .progress {
    display: flex;
    justify-content: center;
    font-size: 24px;
    color: #ff4747;

    .pkTime {
      font-size: 28px !important;
      margin-right: 4px;
    }

    .timeSymbol {
      animation: Twinkle 1s infinite;
    }
  }
  .halfScore {
    color: rgba(255, 255, 255, 0.4);
  }
}

.score {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px 0;
  font-size: 56px;

  font-weight: 400;
  color: #ffffff;
  line-height: 1;
}

.result {
  font-size: 24px;
  color: rgba(255, 255, 255, 0.4);
}

.btnWrap {
  display: flex;
  justify-content: center;
  width: 100%;

  .btn {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 46px;
    padding: 0 20px;
    border-radius: 24px;
    border: 1px solid #ffffff;
    font-size: 24px;

    font-weight: 300;
    color: #ffffff;
    line-height: 24px;

    .img {
      display: inline-block;
      height: 28px;
      margin-right: 10px;
      color: rgb(255, 168, 48);
      font-size: 26px;
    }
  }
}

.live {
  width: 100%;
  position: relative;
  background-color: #152b5c;
  .inner{
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9;
    width: 100%;
  }
  .back{
    position: fixed;
    top: 0;
    width: 100px;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
  }
}

.liveIframe {
  width: 100%;
  height: 100%;
  border-width: initial;
  border-style: none;
  border-color: initial;
  border-image: initial;
}

@keyframes Twinkle {
  0% {
    opacity: 0;
  }

  25% {
    opacity: 1;
  }

  50% {
    opacity: 0;
  }

  75% {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}
