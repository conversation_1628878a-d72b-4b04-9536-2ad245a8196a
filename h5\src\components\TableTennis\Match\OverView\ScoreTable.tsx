import { FallbackPlayerImage } from 'iscommon/const/icon';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { getTableTennisScoreList } from 'iscommon/utils/dataUtils';
import { inject, observer } from 'mobx-react';
import styles from './ScoreTable.less';

const ScoreTable = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Smallball: {
          SmallballMatch: { matchHeaderInfo },
        },
      },
    } = props;
    const labelMaps = useTranslateKeysToMaps(['Total']);
    const { scores, matchStatus, servingSide, homeTeam, awayTeam } = matchHeaderInfo;
    const list = getTableTennisScoreList(matchStatus, scores, servingSide, true);
    const scoreList = list.slice(2, list.length).concat([list[1]]);

    const renderScore = (type = 1) => {
      const isHome = type === 1;
      const teamInfo = isHome ? homeTeam : awayTeam;
      const isServingSide = servingSide == type;
      return (
        <div className={styles.tableContent}>
          <div className={styles.teamNameBox}>
            <img src={teamInfo.logo || FallbackPlayerImage} className={styles.teamImg} />
            <span className={styles.teamName}>{teamInfo?.name}</span>
            {isServingSide && (
              <svg aria-hidden="true" className={`icon fs-12 wangqiu svgPostop ${styles.wangqiu}`}>
                <use xlinkHref="#icontabletennis_ball"></use>
              </svg>
            )}
          </div>
          {scoreList.map((item: any, index) => {
            return (
              <div
                key={index}
                className={`${styles.flex1} ${item.isRed && 'color-ff4747'} ${
                  item.compareStatus !== type && !item.isRed && styles.insideLoserColor
                }`}
              >
                {isHome ? item.h : item.w}
                <span className={styles.hxScore}>{isHome ? item.hx : item.wx}</span>
              </div>
            );
          })}
        </div>
      );
    };

    return (
      <div className={styles.scoreTableContainer}>
        <div className={styles.tableTitle}>
          <div className={styles.teamNameBox} />
          {scoreList.slice(0, scoreList.length - 1).map((i, index) => {
            return (
              <div key={index} className={styles.flex1}>
                {index + 1}
              </div>
            );
          })}
          <div className={`${styles.flex1}`}>S</div>
        </div>
        {renderScore(1)}
        {renderScore(2)}
      </div>
    );
  }),
);

export default ScoreTable;
