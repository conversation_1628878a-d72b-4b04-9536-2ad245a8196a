import { <PERSON><PERSON>, <PERSON><PERSON>, Card } from 'antd';

// Examples of how to use the Recent Activity system
import React from 'react';
import { useRecentActivity } from '../hooks/useRecentActivity';

const RecentActivityExamples: React.FC = () => {
  const { 
    trackTeamClick, 
    trackMatchClick, 
    trackCompetitionClick, 
    trackLeagueClick,
    trackCustomActivity 
  } = useRecentActivity();

  // Example 1: Track team click
  const handleTeamClick = () => {
    trackTeamClick({
      id: 'team_123',
      name: 'Manchester United',
      logo: 'https://example.com/man-utd-logo.png',
      competitionName: 'Premier League'
    });
  };

  // Example 2: Track match click
  const handleMatchClick = () => {
    trackMatchClick({
      id: 'match_456',
      homeTeam: { 
        name: 'Manchester United', 
        logo: 'https://example.com/man-utd-logo.png' 
      },
      awayTeam: { 
        name: 'Liverpool', 
        logo: 'https://example.com/liverpool-logo.png' 
      },
      competitionName: 'Premier League',
      matchTime: 1640995200
    });
  };

  // Example 3: Track competition click
  const handleCompetitionClick = () => {
    trackCompetitionClick({
      id: 'comp_789',
      name: 'Premier League',
      logo: 'https://example.com/premier-league-logo.png'
    });
  };

  // Example 4: Track league click
  const handleLeagueClick = () => {
    trackLeagueClick({
      id: 'league_101',
      name: 'English Football',
      logo: 'https://example.com/england-logo.png'
    });
  };

  // Example 5: Track custom activity
  const handleCustomClick = () => {
    trackCustomActivity({
      id: 'custom_202',
      type: 'player',
      name: 'Cristiano Ronaldo',
      logo: 'https://example.com/ronaldo.png',
      sport: 'football',
      metadata: {
        position: 'Forward',
        team: 'Manchester United'
      }
    });
  };

  return (
    <div style={{ padding: '20px' }}>
      <h2>Recent Activity Tracking Examples</h2>
      
      <Card title="Team Click Tracking" style={{ marginBottom: '16px' }}>
        <Button onClick={handleTeamClick}>
          <Avatar src="https://example.com/man-utd-logo.png" size="small" />
          Click Manchester United
        </Button>
      </Card>

      <Card title="Match Click Tracking" style={{ marginBottom: '16px' }}>
        <Button onClick={handleMatchClick}>
          🏆 Click Man Utd vs Liverpool Match
        </Button>
      </Card>

      <Card title="Competition Click Tracking" style={{ marginBottom: '16px' }}>
        <Button onClick={handleCompetitionClick}>
          🏅 Click Premier League
        </Button>
      </Card>

      <Card title="League Click Tracking" style={{ marginBottom: '16px' }}>
        <Button onClick={handleLeagueClick}>
          🌍 Click English Football
        </Button>
      </Card>

      <Card title="Custom Activity Tracking" style={{ marginBottom: '16px' }}>
        <Button onClick={handleCustomClick}>
          👤 Click Cristiano Ronaldo (Player)
        </Button>
      </Card>

      <Card title="Integration in Components">
        <h4>How to integrate in your components:</h4>
        <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px' }}>
{`// 1. Import the hook
import { useRecentActivity } from '../hooks/useRecentActivity';

// 2. Use in component
const MyComponent = () => {
  const { trackTeamClick } = useRecentActivity();
  
  const handleClick = (team) => {
    trackTeamClick({
      id: team.id,
      name: team.name,
      logo: team.logo,
      competitionName: team.competition
    });
    
    // Your existing click logic here
    navigateToTeam(team.id);
  };
  
  return (
    <div onClick={() => handleClick(team)}>
      {team.name}
    </div>
  );
};`}
        </pre>
      </Card>
    </div>
  );
};

export default RecentActivityExamples;
