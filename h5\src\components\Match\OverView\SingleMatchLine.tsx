import { inject, observer } from 'mobx-react';
import React from 'react';

import { translate, useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { getProgress, getStaticsItemData } from 'iscommon/utils/dataUtils';
import MatchTimeLine from './MatchTimeLine';
// import ScoreRatings from './ScoreRatings';

import './SingleMatchLine.less';
import { FallbackImage } from 'iscommon/const/icon';

import assist from 'iscommon/assets/images/football/assist.png'
import goal from 'iscommon/assets/images/football/goal.png'
import ownGoal from 'iscommon/assets/images/football/ownGoal.png'
import penaltyMissed from 'iscommon/assets/images/football/penaltyMissed.png'
import penalty from 'iscommon/assets/images/football/penalty.png'
import red from 'iscommon/assets/images/football/red.png'
import yellow from 'iscommon/assets/images/football/yellow.png'
import secYellow from 'iscommon/assets/images/football/secYellow.png'
import substitution from 'iscommon/assets/images/football/substitution.png'

interface StatsItem {
  type: string;
  home: number;
  away: number;
}

const Footer = React.memo(
  () => {
    return (
      <div className='match-timeline-footer'>
        <div className='incident-container'>
          <img className='incident-icon' src={goal}/>
          <span className='incident-name'>{translate('Goals')}</span>
        </div>
        <div className='incident-container'>
          <img className='incident-icon' src={ownGoal}/>
          <span className='incident-name'>{translate('OwnGoal')}</span>
        </div>
        <div className='incident-container'>
          <img className='incident-icon' src={penalty}/>
          <span className='incident-name'>{translate('Penalty')}</span>
        </div>
        <div className='incident-container'>
          <img className='incident-icon' src={penaltyMissed}/>
          <span className='incident-name'>{translate('PenaltyMissed')}</span>
        </div>
        <div className='incident-container'>
          <img className='incident-icon' src={assist}/>
          <span className='incident-name'>{translate('Assists')}</span>
        </div>
        <div className='incident-container'>
          <img className='incident-icon' src={yellow}/>
          <span className='incident-name'>{translate('YellowCard')}</span>
        </div>
        <div className='incident-container'>
          <img className='incident-icon' src={red}/>
          <span className='incident-name'>{translate('RedCard')}</span>
        </div>
        <div className='incident-container'>
          <img className='incident-icon' src={secYellow}/>
          <span className='incident-name'>{translate('SecondYellow')}</span>
        </div>
        <div className='incident-container'>
          <img className='incident-icon' src={substitution}/>
          <span className='incident-name'>{translate('Substitution')}</span>
        </div>
      </div>
    );
  },
  () => true,
);

// const SvgCircle = React.memo(({ statsList = [], type }) => {
//   const { home, away } = getStaticsItemData(statsList, type);
//   let size = 3140 * 0.5;
//   if (home + away != 0) {
//     size = 3140 * (home / (home + away));
//   }
//   return (
//     <svg viewBox="0 0 1070 1070">
//       <path
//         d="M 535 535 m 0, -500 a 500, 500 0 1, 0 0, 1000 a 500, 500 0 1, 0 0, -1000"
//         className="van-circle__hover"
//         style={{ fill: 'none', stroke: '#FFBA5A', strokeWidth: '70px' }}
//       ></path>
//       <path
//         d="M 535 535 m 0, -500 a 500, 500 0 1, 0 0, 1000 a 500, 500 0 1, 0 0, -1000"
//         stroke="#2196F3"
//         className="van-circle__layer"
//         style={{ stroke: 'rgb(33, 150, 243)', strokeWidth: '71px', strokeDasharray: `${size}px, 3140px` }}
//       ></path>
//     </svg>
//   );
// });

// const Stats = React.memo<{ statsList: StatsItem[] }>(({ statsList }) => {
//   return (
//     <div className="matchGoals">
//       <div className="goalsType">
//         <div className="goalsTypeItem">
//           <div className="typeName">{translate('attacks')}</div>
//           <div className="goalsCount">
//             <span className="color-blue">{getStaticsItemData(statsList, 23).home}</span>
//             <div className="mycircle">
//               <SvgCircle statsList={statsList} type={23} />
//             </div>
//             <span className="color-yellow">{getStaticsItemData(statsList, 23).away}</span>
//           </div>
//         </div>
//         <div className="goalsTypeItem">
//           <div className="typeName">{translate('dangerousAttack')}</div>
//           <div className="goalsCount">
//             <span className="color-blue">{getStaticsItemData(statsList, 24).home}</span>
//             <div className="mycircle">
//               <SvgCircle statsList={statsList} type={24} />
//             </div>
//             <span className="color-yellow">{getStaticsItemData(statsList, 24).away}</span>
//           </div>
//         </div>
//         <div className="goalsTypeItem">
//           <div className="typeName">{translate('ballPossession')}</div>
//           <div className="goalsCount">
//             <span className="color-blue">{getStaticsItemData(statsList, 25).home}</span>
//             <div className="mycircle">
//               <SvgCircle statsList={statsList} type={25} />
//             </div>
//             <span className="color-yellow">{getStaticsItemData(statsList, 25).away}</span>
//           </div>
//         </div>
//       </div>

//       <div className="onTarget">
//         <div className="name">{translate('onTarget')}</div>
//         <div className="targetItem">
//           <div className="targetCount">
//             <div className="flex-1 spread">
//               <img src={require('@/assets/images/corner.png')} className="van-image" alt="" />
//               <i className="icon iconfont iconredcard1" style={{ color: '#E43434' }}></i>
//               <i className="icon iconfont iconyellowcard1" style={{ color: '#FEBD33' }}></i>
//             </div>
//             <div className="flex-2 countLine">
//               <span>{getStaticsItemData(statsList, 21).home}</span>
//               <div className="percentLine">
//                 <div
//                   className="percent"
//                   style={{
//                     width: `${getProgress(getStaticsItemData(statsList, 21).home, getStaticsItemData(statsList, 21).away)}%`,
//                   }}
//                 ></div>
//               </div>
//               <span>{getStaticsItemData(statsList, 21).away}</span>
//             </div>
//             <div className="flex-1 spread">
//               <i className="icon iconfont iconyellowcard1" style={{ color: '#FEBD33' }}></i>
//               <i className="icon iconfont iconredcard1" style={{ color: '#E43434' }}></i>
//               <img src={require('@/assets/images/corner.png')} className="van-image" alt="" />
//             </div>
//           </div>
//         </div>
//       </div>

//       <div className="onTarget">
//         <div className="name">{translate('offTarget')}</div>
//         <div className="targetItem">
//           <div className="targetCount">
//             <div className="flex-1 spread">
//               <span className="iconfont fs-10">{getStaticsItemData(statsList, 2).home}</span>
//               <span className="iconfont">{getStaticsItemData(statsList, 4).home}</span>
//               <span className="iconfont">{getStaticsItemData(statsList, 3).home}</span>
//             </div>
//             <div className="flex-2 countLine">
//               <span>{getStaticsItemData(statsList, 22).home}</span>
//               <div className="percentLine">
//                 <div
//                   className="percent"
//                   style={{
//                     width: `${getProgress(getStaticsItemData(statsList, 22).home, getStaticsItemData(statsList, 22).away)}%`,
//                   }}
//                 ></div>
//               </div>
//               <span>{getStaticsItemData(statsList, 22).away}</span>
//             </div>
//             <div className="flex-1 spread">
//               <span className="iconfont">{getStaticsItemData(statsList, 3).away}</span>
//               <span className="iconfont">{getStaticsItemData(statsList, 4).away}</span>
//               <span className="iconfont">{getStaticsItemData(statsList, 2).away}</span>
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// });

const SingleMatchLine = inject('store')(
  observer((props: any) => {
    const {
      store: { Match },
    } = props;
    const {
      matchHeaderInfo,
      statsList,
    } = Match;

    const labelMaps = useTranslateKeysToMaps(['Overview']);
    
    console.log('statslist123', JSON.stringify(statsList))

    return (
      // <div className="singleMatchLine">
      //   <div className="head">
      //     <span>{homeTeamName}</span>
      //     <span>{awayTeamName}</span>
      //   </div>
      //   {statsList.length > 0 && <Stats statsList={statsList} />}
      //   <MatchTimeLine />
      // </div>
      <div className='overview-timeline-container'>
        <div className='container-title'>{labelMaps.Overview}</div>
        <div className='container-body'>
          <div className='team-container'>
            <div className='home-team-container'>
              <img className='team-logo' src={matchHeaderInfo.homeTeam?.logo || FallbackImage} alt={matchHeaderInfo.homeTeam?.name}/>
              <span className='team-name'>{matchHeaderInfo.homeTeam?.name}</span>
            </div>
            <div className='away-team-container'>
              <span className='team-name'>{matchHeaderInfo.awayTeam?.name}</span>
              <img className='team-logo' src={matchHeaderInfo.awayTeam?.logo || FallbackImage} alt={matchHeaderInfo.awayTeam?.name}/>
            </div>
          </div>
          <MatchTimeLine />
        </div>
        <Footer />
      </div>
    );
  }),
);

export default SingleMatchLine;
