.round{
  color: #666666;
  font-size: 24px;
  display: flex;
  align-items: center;
  .roundText{
    margin-right: 10px;
  }
}
.vertical_container {
  background: url('../../../assets/images/lineup.png') no-repeat;
  background-size: 100% 100%;
  position: relative;
  overflow: hidden;
  height: 750px;
}

.horizontal_container {
  background: url('../../../assets/images/lineup.png') no-repeat;
  background-size: 100% 100%;
  position: relative;
  overflow: hidden;
  height: 735px;
}

// .football_field_img {
//   position: absolute;
//   left: 0;
//   top: 0;
//   right: 0;
//   bottom: 0;
// }

.player_box {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: absolute;
  height: 100px;
  transform: translateX(-50px) translateY(50px);
  overflow: hidden;
  justify-content: space-between;
  width: 100px;
}
.player_img_box {
  width: 64px;
  position: relative;
  margin-top: 6px;
}

.player_img {
  width: 64px;
  height: 64px;
  border-radius: 32px;
  background: #d8d8d8;
}

.player_name {
  font-weight: 500;
  color: #ffffff;
}

.team_img {
  width: 24px;
  height: 24px;
  position: absolute;
  top: 23px;
  left: -14px;
  background-size: 24px 24px;
  background-position: 0 0;
  background-repeat: no-repeat;
  background-color: #fff;
  border-radius: 12px;
}

.rating_box {
  width: 34px;
  height: 24px;
  background: #5cb400;
  border-radius: 4px;
  position: absolute;
  top: -6px;
  right: -12px;
  text-align: center;
  line-height: 24px;
  font-size: 20px;
  
  font-weight: 500;
  color: #ffffff;
}

.color9above {
  background-color: rgb(61, 132, 37);
}

.color8above {
  background-color: rgb(114, 178, 52);
}

.color7above {
  background-color: rgb(195, 204, 25);
}

.competition-overview-lineup-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;

    .lineup-header-btn {
      display: block;
      background: transparent;
      color: #fff;
      border-radius: 32px;
      width: 250px;

      .btn-content {
        display: flex; 
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .btn-value {
          white-space: nowrap;   
          overflow: hidden;      
          text-overflow: ellipsis;
          flex-grow: 1;
          margin-right: 8px;
        }

        .down-icon {
          flex-shrink: 0;  
          margin-left: auto;
        }
      }
    }
  }
  
  .container-body {
    background: #1e1e1e;

    &.vertical-container {
      background: url('../../../assets/images/lineup.png') no-repeat;
      background-size: 100% 100%;
      position: relative;
      overflow: hidden;
      height: 750px;
    }
  
    &.horizontal-container {
      background: url('../../../assets/images/lineup.png') no-repeat;
      background-size: 100% 100%;
      position: relative;
      overflow: hidden;
      height: 735px;
    }

    .player-box {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: absolute;
      width: 100px;
      height: 100px;
      transform: translateX(-50px) translateY(50px);
      overflow: hidden;
      justify-content: space-between;

      .player-image-box {
        width: 64px;
        position: relative;
        margin-top: 6px;

        .player-icon {
          width: 64px;
          height: 64px;
          border-radius: 32px;
          // background: #d8d8d8;
        }

        .team-icon {
          width: 24px;
          height: 24px;
          position: absolute;
          top: 23px;
          left: -14px;
          background-size: 24px 24px;
          background-position: 0 0;
          background-repeat: no-repeat;
          background-color: #fff;
          border-radius: 12px;
          z-index: 1;
        }

        .rating-box {
          width: 34px;
          height: 24px;
          // background: #5cb400;
          border-radius: 12px;
          position: absolute;
          top: -6px;
          right: -12px;
          text-align: center;
          line-height: 24px;
          font-size: 20px;
          z-index: 2;
        }
      }

      .player-name {
        font-size: 20px;
        color: #fff;
      }
    }
  }
}