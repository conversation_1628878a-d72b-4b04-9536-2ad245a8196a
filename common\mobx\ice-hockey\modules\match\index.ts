// @ts-nocheck
import { makeAutoObservable } from 'mobx';
import { GlobalConfig } from 'iscommon/const/globalConfig';

export const MatchTab = {
  overview: 'Overview',
  odds: 'Odds',
  lineup: 'Lineup',
  live: 'MatchLive',
  standings: 'Standings',
  h2h: 'H2H',
};

export const MatchTabH5 = {
  overview: 'Overview',
  chat: 'Chat',
  lineup: 'Lineup',
  odds: 'Odds',
  live: 'MatchLive',
  h2h: 'H2H',
  standings: 'Standings',
};

export default class Match {
  constructor() {
    this.matchId = '';
    this.serverTime = GlobalConfig.serverTime;
    this.matchHeaderInfo = {
      matchId: -1,
      competition: {
        id: '',
        name: '',
        logo: '',
        countryId: '',
        categoryId: '',
        type: 0,
      },
      homeTeam: {
        id: '',
        name: '',
        logo: '',
        shortName: '',
        marketValueCurrency: '',
        competitionId: '',
      },
      awayTeam: {
        id: '',
        name: '',
        logo: '',
        shortName: '',
        marketValueCurrency: '',
        competitionId: '',
      },
      venue: {
        name: '',
        city: '',
        capacity: 0,
      },
      matchTime: 0,
      aggScore: '',
      matchStatus: -1,
      homeScores: '',
      awayScores: '',
    };

    this.timeLineList = []
    this.statsList = []
    this.matchOdds = []

    makeAutoObservable(this);
  }

  changeMatchId(id = '') {
    this.matchId = id;
  }

  changeServerTime(time) {
    this.serverTime = time;
  }

  changeMatchHeaderInfo(data = {}) {
    this.matchHeaderInfo = data;
  }
  
  setTimeLineList(list = []) {
    this.timeLineList = list
  }

  setStatsList(list = []) {
    this.statsList = list.sort((a, b) => a.type - b.type)
  }

  setMatchOdds(matchOdds = []) {
    this.matchOdds = matchOdds
  }
}
