import { getStatsPlayer } from "iscommon/api/competition";
import { inject, observer } from "mobx-react";
import { useState, useEffect, useMemo } from "react";
import './TeamTopPlayer.less'
import { Link } from "umi";
import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { <PERSON><PERSON>, <PERSON>er } from "antd-mobile";
import { DownOutline } from 'antd-mobile-icons'
import { FallbackPlayerImage } from "iscommon/const/icon";
import Loading from "@/components/Loading";

const TeamTopPlayer = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Team: {
          teamHeaderInfo,
          teamHeaderInfo: { competition, currentSeason, mvp },
          teamId,
        },
      },
    } = props;

    const labelMaps = useTranslateKeysToMaps([
      'topPlayers',
      'Matches',
      'Goals',
      'Assists',
      'YellowCards',
      'RedCards',
      'Shots',
      'ShotsOnTarget',
      'Clearances',
      'Tackles',
      'keyPasses',
      'Crosses',
      'CrossesAccuracy',
      'Fouls',
      'WasFouled',
      'Penalty',
      'MinutesPlayed',
      'Dribble',
      'DribbleSucc',
      'Interceptions',
      'Steals',
      'Passes',
      'LongBalls',
      'LongBallsAccuracy',
      'Duels',
      'DuelsWon',
      'Dispossessed',
      'Saves',
      'Punches',
      'RunsOut',
      'RunsOutSucc',
      'GoodHighClaim',
      'BlockedShots',
      'PassesAccuracy',
      'Forwards',
      'Midfielders',
      'Defender',
      'Goalkeeper',
    ]);

    const playerItems = useMemo(
      () => [
        { label: labelMaps.Goals, key: 'goals', value: 'goals' },
        { label: labelMaps.Assists, key: 'assists', value: 'assists' },
        { label: labelMaps.MinutesPlayed, key: 'minutesPlayed', value: 'minutesPlayed' },
        { label: labelMaps.RedCards, key: 'redCards', value: 'redCards' },
        { label: labelMaps.YellowCards, key: 'yellowCards', value: 'yellowCards' },
        { label: labelMaps.Shots, key: 'shots', value: 'shots' },
        { label: labelMaps.ShotsOnTarget, key: 'shotOnTargets', value: 'shotOnTargets' },
        { label: labelMaps.Dribble, key: 'dribble', value: 'dribble' },
        { label: labelMaps.DribbleSucc, key: 'dribbleSucc', value: 'dribbleSucc' },
        { label: labelMaps.Clearances, key: 'clearances', value: 'clearances' },
        { label: labelMaps.Interceptions, key: 'interceptions', value: 'interceptions' },
        { label: labelMaps.Steals, key: 'steals', value: 'steals' },
        { label: labelMaps.Passes, key: 'passes', value: 'passes' },
        { label: labelMaps.keyPasses, key: 'keyPasses', value: 'keyPasses' },
        { label: labelMaps.Crosses, key: 'crosses', value: 'crosses' },
        { label: labelMaps.CrossesAccuracy, key: 'crossesAccuracy', value: 'crossesAccuracy' },
        { label: labelMaps.LongBalls, key: 'longBalls', value: 'longBalls' },
        { label: labelMaps.LongBallsAccuracy, key: 'longBallsAccuracy', value: 'longBallsAccuracy' },
        { label: labelMaps.Duels, key: 'duels', value: 'duels' },
        { label: labelMaps.DuelsWon, key: 'duelsWon', value: 'duelsWon' },
        { label: labelMaps.Fouls, key: 'fouls', value: 'fouls' },
        { label: labelMaps.Dispossessed, key: 'dispossessed', value: 'dispossessed' },
        { label: labelMaps.WasFouled, key: 'wasFouled', value: 'wasFouled' },
        { label: labelMaps.Saves, key: 'saves', value: 'saves' },
        { label: labelMaps.Punches, key: 'punches', value: 'punches' },
        { label: labelMaps.RunsOut, key: 'runsOut', value: 'runsOut' },
        { label: labelMaps.RunsOutSucc, key: 'runsOutSucc', value: 'runsOutSucc' },
        { label: labelMaps.GoodHighClaim, key: 'goodHighClaim', value: 'goodHighClaim' },
        { label: labelMaps.BlockedShots, key: 'blockedShots', value: 'blockedShots' },
        { label: labelMaps.PassesAccuracy, key: 'passesAccuracy', value: 'passesAccuracy' },
      ],
      [labelMaps],
    );
    
    const positionList = useMemo(
      () => [
        { key: 'F', label: labelMaps.Forwards },
        { key: 'M', label: labelMaps.Midfielders },
        { key: 'D', label: labelMaps.Defender },
        { key: 'G', label: labelMaps.Goalkeeper },
      ],
      [labelMaps],
    );

    const [statsPlayer, setStatsPlayer] = useState<any[]>([]);
    const [statVisible, setStatVisible] = useState(false);
    const [selectedPlayerStat, setSelectedPlayerStat] = useState(playerItems.length > 0 ? playerItems[0].value : '');
    const [loading, setLoading] = useState<boolean>(true);

    useEffect(() => {
      if (teamId && currentSeason.id && competition.id) {
        getStatsPlayer({
          teamId,
          competitionId: competition.id,
          seasonId: currentSeason.id,
          property: 'goals',
        }).then(({ playerStatistics }: any) => {
          setLoading(false)
          setStatsPlayer(playerStatistics);
        });
      }
    }, [competition.id, currentSeason.id, teamId]);

    const onPlayerConfirm = (selected: any[]) => {
      const selectedStat = playerItems.find(item => item.value === selected[0]);
      if (selectedStat) {
        setSelectedPlayerStat(selectedStat.value); 
      }
      setStatVisible(false);
    };

    // const playerPosition = player.position;

    // const positionLabel = useMemo(() => {
    //   const normalizedPosition = playerPosition?.trim().toUpperCase();
    //   const position = positionList.find(pos => pos.key === normalizedPosition);
    //   return position ? position.label : 'Unknown Position';
    // }, [playerPosition, positionList]);

    console.log('statplayer', statsPlayer)

    return (
      <div className="team-top-player-container">
        <div className="container-title">
          {labelMaps.topPlayers}
          <>
            <Button className='team-stat-header-btn' size="small" onClick={() => setStatVisible(true)}>
              <span className='team-stat-content'>
                <span className='team-stat-value'>{playerItems.find(item => item.value === selectedPlayerStat)?.label || ''}</span>
                <DownOutline className='team-stat-icon'/>
              </span>
            </Button>
            <Picker
              columns={[playerItems.map(item => ({ label: item.label, value: item.value }))]}
              visible={statVisible}
              onClose={() => setStatVisible(false)}
              onConfirm={onPlayerConfirm}
            />
          </>
        </div>
        <div className="container-body">
          <div className='player-stat-table'>
            <div className="header-row">
              <div className="header-cell">#</div>
              <div className="header-cell">Players</div>
              <div className="header-cell">{playerItems.find(item => item.value === selectedPlayerStat)?.label || ''}</div>
            </div>
            {statsPlayer.length !== 0 ? (
              [...statsPlayer]
              .sort((a, b) => (b[selectedPlayerStat] ?? 0) - (a[selectedPlayerStat] ?? 0))
              .map((item: any, index) => {
                return (
                  <Link className="table-row" key={item?.player?.id} to={GlobalUtils.getPathname(PageTabs.player, item?.player?.id)}>
                    <div className="table-cell">
                      <span>{index + 1}</span>
                    </div>
                    <div className="table-cell player-cell">
                      <img className="player-icon" src={item?.player?.logo || FallbackPlayerImage} alt={item?.player?.name} />
                      <div className="player-info">
                        <span className="player-name">{item?.player?.name}</span>
                        <span className="player-position">{positionList.find(pos => pos.key === item?.player?.position?.toUpperCase())?.label || 'Unknown Position'}</span>
                      </div>
                    </div>
                    <div className="table-cell">{item[selectedPlayerStat]}</div>
                  </Link>
                )
              })
            ) : (
              <Loading loading={loading}/> 
            )}
          </div>
        </div>
      </div>
    );
  }),
);

export default TeamTopPlayer;