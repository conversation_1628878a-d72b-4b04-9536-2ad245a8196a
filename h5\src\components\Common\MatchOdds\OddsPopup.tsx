import clockIcon from '@/assets/images/clock.png';
import { Popup } from 'antd-mobile';
import { H5OddGameIcon, oddsCompanyTextMaps } from 'iscommon/const/constant';
import { GlobalConfig } from 'iscommon/const/globalConfig';
import { translate } from 'iscommon/i18n/utils';
import moment from 'moment';
import styles from './OddsPopup.less';
import './OddsPopup.less'
const OddsPopup: React.FC = ({ visible, data, headerInfo, closePopup }) => {
  const { label: headerLabel, oddsValueIndexList } = headerInfo;
  return (
    <Popup
      visible={visible}
      bodyClassName='odds-popup-container'
      // bodyStyle={{
      //   borderTopLeftRadius: '16px',
      //   borderTopRightRadius: '16px',
      //   height: '80%',
      //   display: 'flex',
      //   flexDirection: 'column',
      // }}
    >
      <div className='popup-container-header'>
        <span>{oddsCompanyTextMaps[data.companyId]}</span>
        <i className='icon iconfont icondelete' onClick={closePopup}/>
      </div>
      <div className='popup-container-body'>
        <div className='container-body-header'>
          <div className='header-container'>
            <div className='iconfont icon-time header-icon'/>
          </div>
          <div className='header-container'>
            <img src={H5OddGameIcon[GlobalConfig.pathname]} className='header-icon'/>
          </div>
          {headerLabel.map((item: any) => (
            <div className='header-container' key={item}>
              {item}
            </div>
          ))}
          <div className='header-container-time'>{translate('Time')}</div>
        </div>
        <div className='container-body-list'>
          {data.odds?.map((item: any, oddsIndex: any) => (
            <div className='body-list-cell' key={`odd_${oddsIndex}`}>
              <div className='cell-content'>
                {item.timeOfMatch || '-'}
              </div>
              <div className='cell-content'>
                {item.ratio}
              </div>
              {oddsValueIndexList.map((val: any, index: any) => {
                let color = '#fff';
                
                // Check if the next item exists before comparing
                if (data.odds[oddsIndex + 1]) {
                  if (item.oddsData[index] > data.odds[oddsIndex + 1].oddsData[index]) {
                    color = '#14AE5C'; 
                  } else if (item.oddsData[index] < data.odds[oddsIndex + 1].oddsData[index]) {
                    color = '#FF3131'; 
                  }
                }
                
                return (
                  <div className='cell-content' key={val}>
                    <span style={{ color: color }}>{Number(item.oddsData[index])?.toFixed(2)}</span>
                  </div>
                )
              })}
              <div className='cell-content-time'>
                {moment(item.changeTime * 1000).format('mm:ss')}
              </div>
            </div>
          ))}
        </div>
      </div>
      {/* <div className={styles.header_box}>
        <span className={styles.popup_title}>{oddsCompanyTextMaps[data.companyId]}</span>
        <i className="icon iconfont icondelete" style={{ fontSize: 20, color: 'rgb(153, 153, 153)' }} onClick={closePopup}></i>
      </div>
      <div className={styles.content_container}>
        <div className={styles.content_header_box} style={{ boxShadow: '0px 5px 0.11rem 0 rgb(0 0 0 / 20%)' }}>
          <div className={styles.common_item}>
            <img src={clockIcon} width={12} height={12} />
          </div>
          <div className={styles.common_item}>
            <img src={H5OddGameIcon[GlobalConfig.pathname]} width={12} height={12} />
          </div>
          {headerLabel.map((item: any) => (
            <div className={styles.common_item} key={item}>
              {item}
            </div>
          ))}
          <div className={styles.time_header_item}>{translate('Time')}</div>
        </div>
        <div className={styles.content_list_box}>
          {data.odds?.map((item: any, oddsIndex: any) => (
            <div className={styles.content_item_box} key={`odd_${oddsIndex}`}>
              <div className={styles.content_item} style={{ color: 'color: #ff1f3c' }}>
                {item.timeOfMatch || '-'}
              </div>
              <div className={styles.content_item} style={{ color: '#999' }}>
                {item.ratio || '-'}
              </div>
              {oddsValueIndexList.map((validIndex: any, index: any) => (
                <div
                  className={styles.content_item}
                  style={{
                    color: data.odds[oddsIndex + 1]
                      ? item.oddsData[validIndex] > data.odds[oddsIndex + 1].oddsData[validIndex]
                        ? '#5db400'
                        : item.oddsData[validIndex] < data.odds[oddsIndex + 1].oddsData[validIndex]
                        ? '#ff1f3c'
                        : 'black'
                      : 'black',
                  }}
                  key={index}
                >
                  {Number(item.oddsData[validIndex])?.toFixed(2)}
                </div>
              ))}
              <div className={styles.time_header_item}>{moment(item.changeTime * 1000).format('mm:ss')}</div>
            </div>
          ))}
        </div>
      </div> */}
    </Popup>
  );
};

export default OddsPopup;
