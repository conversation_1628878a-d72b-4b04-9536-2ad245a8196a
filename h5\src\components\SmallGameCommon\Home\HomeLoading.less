@-webkit-keyframes van-skeleton-blink {
  50% {
      opacity: .6
  }
}

@keyframes van-skeleton-blink {
  50% {
      opacity: .6
  }
}
.matchListSke {
    width: 100%
}

.matchListSke .myback2 {
    background: #f2f2f2;
    width: 30%
}

.matchListSke .myback1 {
  width: 70%
}

.matchListSke .tab {
    height: 94px;
    background: #fff
}

.matchListSke .itemske {
    background: #fff;
    margin-top: 40px;
}

.matchListSke .time {
    width: 130px;
    border-right: 1px solid #e3e3e3
}

.matchListSke .time .time_content {
    width: 91px;
    height: 32px
}

.matchListSke .team {
    flex: 1;
    border-right: 1px solid #e3e3e3;
    position: relative
}

.matchListSke .team .img {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    margin: 0 10px
}

.matchListSke .team .teamName {
    width: 200px;
    height: 30px;
    &.myback2 {
      width: 100px
    }
    &.myback1{
      width: 300px;
    }
}

.matchListSke .team .myicon {
    width: 20px;
    height: 20px;
    border-radius: 2px;
    right: 20px;
    top: 38px;
    position: absolute
}

.matchListSke .score {
    width: 140px
}

.matchListSke .score .score_content {
    width: .43rem;
    height: .32rem
}

.matchListSke .backAnimation .myback1,.matchListSke .backAnimation .myback2 {
    -webkit-animation: van-skeleton-blink 1.2s ease-in-out infinite;
    animation: van-skeleton-blink 1.2s ease-in-out infinite
}