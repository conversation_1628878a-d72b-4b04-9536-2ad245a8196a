import { getStatsPlayer } from 'iscommon/api/competition';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { inject, observer } from 'mobx-react';
import { useCallback, useEffect, useState } from 'react';

import Loading from '@/components/Loading';

import OtherStats from './OtherStats';
import PlayerStats from './PlayerStats';

import styles from './index.less';

const otherStatsList = {
  offensiveData: [
    {
      label: 'Assists',
      value: 'assists',
    },
    {
      label: 'scoringFrequencyFiveGoals',
      value: 'scoringFrequencyFiveGoals',
    },
    {
      label: 'totalShorts',
      value: 'shots',
    },
    {
      label: 'keyPassesPerGame',
      value: 'passesPerGame',
    },
    {
      label: 'keyPasses',
      value: 'passes',
    },
    {
      label: 'accuracyCrosses',
      value: 'crossesAccuracy',
    },
    {
      label: 'accuracyLongBalls',
      value: 'longBallsAccuracy',
    },
  ],
  defenseData: [
    {
      label: 'Interceptions',
      value: 'interceptions',
    },
    {
      label: 'Clearances',
      value: 'clearances',
    },
    {
      label: 'blockShots',
      value: 'blockedShots',
    },
    {
      label: 'Saves',
      value: 'saves',
    },
  ],
  otherData: [
    {
      label: 'MinutesPlayed',
      value: 'minutesPlayed',
    },
    {
      label: 'YellowCards',
      value: 'yellowCards',
    },
    {
      label: 'RedCards',
      value: 'redCards',
    },
    {
      label: 'Fouls',
      value: 'fouls',
    },
    {
      label: 'WasFouled',
      value: 'wasFouled',
    },
  ],
};

const PlayerStatsTab = inject('store')(
  observer((props: any) => {
    const { competitions, teamId, showTeamInfo = true } = props;
    const {
      competitionId,
      currentSeason: { id: seasonId },
    } = competitions;
    const [allList, setAllList] = useState<any[]>([]);
    const [sortInfo, setSortInfo] = useState<any>({});
    const [loading, setLoading] = useState<boolean>(false);

    const compare = useCallback((property: any) => {
      return function (obj1: any, obj2: any) {
        return obj2[property] - obj1[property];
      };
    }, []);

    useEffect(() => {
      if (competitionId && seasonId) {
        setLoading(true);
        getStatsPlayer({
          competitionId,
          seasonId,
          teamId,
        }).then(({ playerStatistics }: any) => {
          if (Array.isArray(playerStatistics)) {
            const list = playerStatistics.map((item: any) => {
              const passesPerGame = (item.passes / item.court).toFixed(1);
              return {
                ...item,
                passesPerGame,
              };
            });
            setAllList(list);
          }
          setLoading(false);
        });
      }
    }, [competitionId, seasonId, teamId]);

    useEffect(() => {
      const sortLTypeList = [
        ...otherStatsList.defenseData,
        ...otherStatsList.offensiveData,
        ...otherStatsList.otherData,
        { label: 'Goals', value: 'goals' },
      ];
      let sortInfo: any = {};
      sortLTypeList.forEach((item: any) => {
        sortInfo[item.value] = [...allList.sort(compare(item.value))];
      });
      setSortInfo(sortInfo);
    }, [allList, compare]);

    const labelMaps = useTranslateKeysToMaps(['Goals', 'Assists', 'YellowCards', 'RedCards', 'Saves']);

    return (
      <Loading loading={loading} isEmpty={!allList?.length}>
        <div className={styles.stats_container}>
          <div className={styles.mt16}>
            <div className={styles.stats_type_title}>{labelMaps.Goals}</div>
            <PlayerStats showTeamInfo={showTeamInfo} type="goals" list={sortInfo['goals'] || []} />
          </div>
          <div className={styles.mt16}>
            <div className={styles.stats_type_title}>{labelMaps.Assists}</div>
            <PlayerStats showTeamInfo={showTeamInfo} type="assists" list={sortInfo['assists'] || []} />
          </div>
          <div className={styles.mt16}>
            <div className={styles.stats_type_title}>{labelMaps.YellowCards}</div>
            <PlayerStats showTeamInfo={showTeamInfo} type="yellowCards" list={sortInfo['yellowCards'] || []} />
          </div>
          <div className={styles.mt16}>
            <div className={styles.stats_type_title}>{labelMaps.RedCards}</div>
            <PlayerStats showTeamInfo={showTeamInfo} type="redCards" list={sortInfo['redCards'] || []} />
          </div>
          <div className={styles.mt16}>
            <div className={styles.stats_type_title}>{labelMaps.Saves}</div>
            <PlayerStats showTeamInfo={showTeamInfo} type="saves" list={sortInfo['saves'] || []} />
          </div>
          <OtherStats showTeamInfo={showTeamInfo} data={otherStatsList} allList={allList} />
        </div>
      </Loading>
    );
  }),
);

export default PlayerStatsTab;
