import React from 'react';

import CommonTopPlayer from '@/components/Common/CommonTopPlayer';

const PlayerStats = (props: any) => {
  const { hasMoreButton = true, type = 'goals', list = [], showTeamInfo = true } = props;
  if (list.length === 0) return null;
  return <CommonTopPlayer showTeamInfo={showTeamInfo} hasMoreButton={hasMoreButton} type={type} list={list} />;
};

export default React.memo(PlayerStats);
