import { But<PERSON> } from "antd-mobile";
import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { useMemo, useState } from "react";
import { Link } from "umi";
import { LeftOutline, RightOutline } from 'antd-mobile-icons';
import './PlayerStatsTable.less'

const Pagination = ({ pageInfo, onPageChange }: any) => {
  const { pageNum, pageSize, total } = pageInfo;
  const totalPages = Math.ceil(total / pageSize);

  const handlePrev = () => {
    if (pageNum > 1) {
      onPageChange(pageNum - 1);
    }
  };

  const handleNext = () => {
    if (pageNum < totalPages) {
      onPageChange(pageNum + 1);
    }
  };

  return (
    <div className="pagination">
      <Button onClick={handlePrev} disabled={pageNum === 1} size="mini" shape="rounded">
        <LeftOutline />
      </Button>
      <span>{`Page ${pageNum} of ${totalPages}`}</span>
      <Button onClick={handleNext} disabled={pageNum === totalPages} size="mini" shape="rounded">
        <RightOutline />
      </Button>
    </div>
  );
};

const PlayerStatsTable = ({ data }: { data: any[] }) => {

  const labelMaps = useTranslateKeysToMaps(['Player']);
  const commonTitleMaps = useMemo(() => [
    { label: 'PTS', value: 'points' },
    { label: 'FGM', value: 'fieldGoalsScored' },
    { label: 'FGA', value: 'fieldGoalsTotal' },
    { label: '3PM', value: 'threePointsScored' },
    { label: '3PA', value: 'threePointsTotal' },
    { label: 'FTM', value: 'freeThrowsScored' },
    { label: 'FTA', value: 'freeThrowsTotal' },
    { label: 'OREB', value: 'offensiveRebounds' },
    { label: 'DREB', value: 'defensiveRebounds' },
    { label: 'REB', value: 'rebounds' },
    { label: 'AST', value: 'assists' },
    { label: 'STL', value: 'steals' },
    { label: 'BLK', value: 'blocks' },
    { label: 'TOV', value: 'turnovers' },
  ], []);

  const [pageInfo, setPageInfo] = useState<any>({
    pageNum: 1,
    pageSize: 15,
    total: data.length,
  });

  const onPageChange = (newPageNum: number) => {
    setPageInfo((prevPageInfo: any) => ({
      ...prevPageInfo,
      pageNum: newPageNum,
    }));
  };

  const paginatedData = useMemo(() => {
    const startIndex = (pageInfo.pageNum - 1) * pageInfo.pageSize;
    const endIndex = startIndex + pageInfo.pageSize;
    return data.slice(startIndex, endIndex);
  }, [data, pageInfo]);

  return (
    <div className="basketball-player-stats-table-container">
      <div className="custom-stats-table">
        <div className="table-header">
          <div className="header-cell">#</div>
          <div className="header-cell player-cell">{labelMaps.Player}</div>
          {commonTitleMaps.map((stat) => (
            <div className="header-cell" key={stat.value}>{stat.label}</div>
          ))}
        </div>

        <div className="table-body">
          {paginatedData.map((player, index) => (
            <div className="table-row" key={index}>
              <div className="table-cell">{(pageInfo.pageNum - 1) * pageInfo.pageSize + index + 1}</div>

              <Link className="table-cell player-cell" to={GlobalUtils.getPathname(PageTabs.player, player.player.id)}>
                <img src={player.player.logo} alt={player.player.name} className="player-icon" />
                <div className="player-info">
                  <span className="player-name">{player.player.name}</span>
                  <div className="player-team-detail">
                    <img src={player.team.logo} alt={player.team.name} className="team-icon"/>
                    <span className="team-name">{player.team.name}</span>
                  </div>
                </div>
              </Link>

              {commonTitleMaps.map((stat) => (
                <div className="table-cell" key={stat.value}>
                  {player[stat.value] ? player[stat.value] : '0'}
                </div>
              ))}
            </div>
          ))}
        </div>
        <Pagination pageInfo={pageInfo} onPageChange={onPageChange} />
      </div>
    </div>
  )
}

export default PlayerStatsTable;