import { useCallback, useEffect, useRef, useState } from 'react';

import { getCompetitionByCountryOrCategory } from 'iscommon/api/smallball-common/home';
import GlobalUtils, { GlobalConfig, PageTabs } from 'iscommon/const/globalConfig';
import { CategoryIcon } from 'iscommon/const/icon';
import { translate } from 'iscommon/i18n/utils';

import Loading from '@/components/Loading';

import { getTournamentList } from 'iscommon/api/esports/home';
import { EsportsTypeList, esportsTypeMap } from 'iscommon/const/esports/constant';
import './AllCompetitions.less';

interface Item {
  id: string;
  name: string;
  logo: string;
  path: string;
  active: boolean;
  mouseActive: boolean;
  isCategory?: boolean;
  competitions: {
    id: string;
    name: string;
    logo: string;
    active: boolean;
    path: string;
  }[];
}

const AllCompetitions = () => {
  const [list, setList] = useState<Item[]>([]);
  const [data, setData] = useState({});
  const selectedId = useRef('');
  const [loading, setLoading] = useState<boolean>(false);

  const fetchCountriesAndCategories = async () => {
    setLoading(true);
    // const data: any = (await getCountriesAndCategories()) as unknown as {
    //   countries: Item[];
    //   categories: Item[];
    // };
    // const countries = data.countries || [];
    // const categories = data.categories
    //   ? data.categories.map((item: any) => {
    //       return {
    //         ...item,
    //         isCategory: true,
    //       };
    //     })
    //   : [];
    // const mergeList = [...categories, ...countries];
    const data: object = await getTournamentList();
    const list = EsportsTypeList;
    setList(list);
    setData(data);
    setLoading(false);
  };

  const fetchCompetitions = useCallback(async (params: any) => {
    return await getCompetitionByCountryOrCategory(params, GlobalConfig.pathname);
  }, []);

  const showInnerList = async (id: string) => {
    let newFilterList: any[];
    // 点击相同的li
    if (selectedId.current === id) {
      newFilterList = list.map((item) => {
        return {
          ...item,
          active: false,
        };
      });
      selectedId.current = '';
      return setList(newFilterList);
    }
    selectedId.current = id;

    const selectedItem = list.filter((item) => item.id === id);
    if (selectedItem[0].competitions?.length) {
      newFilterList = list.map((item) => {
        return {
          ...item,
          active: item.id === id,
        };
      });
    } else {
      const competitions = data[esportsTypeMap[id]];
      newFilterList = list.map((item) => {
        return {
          ...item,
          active: item.id === id,
          competitions: item.id === id ? competitions : item.competitions,
        };
      });
    }
    setList(newFilterList);
  };

  useEffect(() => {
    fetchCountriesAndCategories();
  }, []);

  const goCompetition = useCallback((item) => {
    if (item.curStageId) {
      GlobalUtils.goToPage(PageTabs.competition, item.id);
    }
  }, []);

  return (
    <>
      <p className="title">{translate('h5_Leagues')}</p>
      <Loading loading={loading} isEmpty={list.length === 0}>
        {list.map((item, i) => {
          return (
            <div key={item.id + i}>
              <div className="item" onClick={() => showInnerList(item.id)}>
                <div className="left">
                  <img className="logo" src={(CategoryIcon as any)[item.logo] || item.logo} alt="" />
                  <span className="text">{item.name}</span>
                </div>
              </div>
              {item.active && (
                <ul className="inner-list">
                  {item.competitions &&
                    item.competitions.map((competition, index) => {
                      return (
                        <div className="target-blank" key={competition.id + index}>
                          <li className="inner-item">
                            <img className="inner-logo" src={competition.logo} alt={competition.name} loading="lazy"/>
                            <span className="inner-name">{competition.name}</span>
                          </li>
                        </div>
                      );
                    })}
                </ul>
              )}
            </div>
          );
        })}
      </Loading>
    </>
  );
};

export default AllCompetitions;
