// @ts-nocheck
import i18n from 'iscommon/i18n';
import { useTranslation } from 'react-i18next';
import { langStoreKey } from '../const/globalConfig';
import { saveStore } from '../store';

export const getValue = (t, key = '', options = {}) => {
  const value = t(key, options);
  return !value || value === key ? t(key, { ...options, lng: 'en' }) : value;
}

/**
 * This is the function that you want to use to translate your text
 * @param key The key of the text you want to translate
 * @param options
 */
export const translate = (key = '', options = {}) => {
  const { t } = useTranslation();

  return getValue(t, key, options);
};

/**
 * This is the function that you want to use to translate your text
 * @param lng The language you want to change to
 */
export const changeLanguage = (lng = 'en') => {
  saveStore(langStoreKey, lng);
  return i18n.changeLanguage(lng);
};

/**
 * This is the function that you want to use to get the current language
 * @param arr The array of languages that you want to get
 * @param attr The attribute of the language that you want to get
 * @returns {any}
 */
export const useTranslateKeysToMaps = (arr, attr = '') => {
  const { t } = useTranslation();
  return arr.reduce((result, item) => {
    if (item) {
      if (typeof item === 'object' && attr) {
        result[item[attr]] = getValue(t, item[attr]);
      } else {
        result[item] = getValue(t, item);
      }
    }
    return result;
  }, {});
};

/**
 * This is the function that you want to use to get the current language
 * @param arr {string[]} The array of languages that you want to get
 * @param data The data of the language that you want to get
 * @param autoKey
 */
export const translateKeysToList = (arr, data, autoKey = true) => {
  const { t } = useTranslation();
  return arr.map((item) => {
    const label = typeof item === 'string' ? item : item.label;
    const key = typeof item === 'string' ? item : item.key;
    const dataKey = autoKey ? label.replace(/^\S/, (s) => s.toLowerCase()) : label;
    return {
      label: t(label),
      value: data ? data[key || dataKey] : null,
    };
  });
};
