import { inject, observer } from 'mobx-react';
import './index.less'

interface Props {
  vlive: number;
  mlive: number;
}

const MatchLiveIcon = inject('store')(
  observer((props: Props & any) => {
    const {
      store: { WebConfig },
      vlive,
      mlive,
    } = props;
    const { showVideo } = WebConfig;
    return (
      <div className="match-live-container">
        {showVideo && vlive === 1 && (
          <span className="iconfont iconhuaban1" style={{ color: '#c72a1d' }}></span>
        )}
        {mlive === 1 && (
          <span className="iconfont iconhuaban" style={{ color: 'rgb(255, 168, 48)' }}></span>
        )}
      </div>
    );
  }),
);

export default MatchLiveIcon;
