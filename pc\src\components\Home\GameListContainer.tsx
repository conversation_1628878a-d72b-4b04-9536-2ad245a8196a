import { filterCompetitionsWithFavorite, simpleCloneObj, sortByMatchTime } from 'iscommon/utils';
import { inject, observer } from 'mobx-react';
import React, { memo, useCallback, useEffect, useRef, useState } from 'react';

import AdBanner from '../Common/AdBanner';
import GameCategory from './GameCategory';
import { getCompetitionList } from 'iscommon/api/home';
import { GlobalConfig } from 'iscommon/const/globalConfig';
import { HomeGameTab } from 'iscommon/const/constant';
import HomeLoading from './HomeLoading';
import { isEqual } from 'lodash';
import LazyLoad from 'react-lazyload';
import Loading from '@/components/Loading';
import moment from 'moment';
import UpComing from './UpComing';
import { useMatchTimer } from 'iscommon/hooks/apiData';

const FavoriteMatches = React.lazy(() => import('./FavoriteMatches'));

const MaxCount = 6;
let MaxMatchItem = [3, 6];
const getMaxtItem = (list: any) => {
  try {
    if (list.length === 0) return [1, 2];
    let maxDisplayCount = 0,
      count = 0;
    for (let i = 0; i < list.length; i++) {
      const cItem = list[i];
      if (count < MaxCount) {
        count += cItem.matches.length;
        maxDisplayCount++;
      } else {
        break;
      }
    }
    return [Math.floor(maxDisplayCount / 2), maxDisplayCount];
  } catch (e) {
    return [3, 6];
  }
};

const RenderHomeCompetitions = memo(
  ({ list, scroll }: any) => {
    useEffect(() => {
      if (scroll) {
        window.scrollTo(0, 1);
        window.scrollTo(0, 0);
      }
    }, [scroll]);

    return (
      <div>
        {list.map((item: any, index: number) => {
          return <GameCategory key={item.competitionId + index} competition={item} />;
        })}
      </div>
    );
  },
  (p, n) => isEqual(p, n),
);

const GameListContainer = inject('store')(
  observer((props: any) => {
    const { WebConfig } = props.store;
    const { WebHome } = props.store;
    const { currentGameTab, isSortByTime, date, homeCompetitions, favoriteMatches, favoriteCompetitions, serverTime } = WebHome;
    const competitionsRef = useRef([]);
    const isSortByTimeRef = useRef(false);
    const [loading, setLoading] = useState(true);

    const refTimer = useRef<NodeJS.Timeout | null>(null);

    const setFormatCompetitionList = useCallback(() => {
      const list = simpleCloneObj(competitionsRef.current);
      if (list.length > 0) {
        const fList = filterCompetitionsWithFavorite(list, favoriteMatches, favoriteCompetitions);
        if (isSortByTimeRef.current) {
          // order by time
          WebHome.setHomeCompetitions(fList.concat(sortByMatchTime(list)));
        } else {
          WebHome.setHomeCompetitions(fList.concat(list));
        }
      }
    }, [favoriteCompetitions, favoriteMatches, WebHome]);

    const getListData = useCallback(async () => {
      try {
        const parsedDateWithOffset = moment(date).format('YYYY-MM-DDTHH:mm:SS.SSS') + GlobalConfig.timezone.replace('UTC', '');
        const { competitions, total } = await getCompetitionList({
          dateFilter: parsedDateWithOffset,
          listType: currentGameTab,
        });
        requestAnimationFrame(() => {
          competitionsRef.current = JSON.parse(JSON.stringify(competitions));
          setFormatCompetitionList();
        });
        if (currentGameTab === HomeGameTab.Live) {
          // interval loop getData
          WebConfig.setOnTypeLiveCount(total);
          // refTimer.current = setTimeout(getListData, 20000);
        }
        setLoading(false);
      } catch (e) {
        console.log(e);
      }
    }, [currentGameTab, date, setFormatCompetitionList, WebConfig]);

    useEffect(() => {
      if (refTimer.current) {
        clearTimeout(refTimer.current);
      }
      setLoading(true);
      WebHome.setHomeCompetitions([]);
      if (currentGameTab !== 4) {
        getListData();
      } else {
        WebHome.setHomeCompetitions([]);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [currentGameTab, date]);

    useEffect(() => {
      isSortByTimeRef.current = isSortByTime;
      setFormatCompetitionList();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [isSortByTime, favoriteCompetitions, favoriteMatches]);

    // update server time every 60s
    useMatchTimer(serverTime, WebHome, getListData);

    const homeCompetitionsLength = homeCompetitions.length;

    useEffect(() => {
      if (homeCompetitionsLength === 0 && !loading) {
        window.scrollTo(0, 1);
        window.scrollTo(0, 0);
      }
    }, [loading, homeCompetitionsLength]);

    MaxMatchItem = getMaxtItem(homeCompetitions);

    return currentGameTab !== HomeGameTab.Favourite ? (
      <>
        <Loading
          loading={loading}
          isEmpty={currentGameTab !== HomeGameTab.Favourite && !homeCompetitionsLength}
          emptyText="There are no matches at this moment"
          renderLoading={<HomeLoading />}
        >
          {/* live list */}
          {/* {homeCompetitions.map((item: any, index: number) => {
            return <GameCategory key={item.competitionId + index} competition={item} />;
          })} */}
          {/* <RenderHomeCompetitions list={homeCompetitions.slice(0, Count)} />
          {homeCompetitionsLength > Count
            ? homeCompetitions.slice(Count, homeCompetitionsLength).map((item: any, index: number) => {
                return (
                  <LazyLoad key={item.competitionId + index} height={400} offset={10}>
                    <GameCategory competition={item} />
                  </LazyLoad>
                );
              })
            : null} */}
          <RenderHomeCompetitions list={homeCompetitions.slice(0, MaxMatchItem[0])} scroll />
          {currentGameTab !== 5 && homeCompetitionsLength > MaxMatchItem[0] ? (
            <LazyLoad height={800} offset={10}>
              <RenderHomeCompetitions list={homeCompetitions.slice(MaxMatchItem[0], MaxMatchItem[1])} scroll />
            </LazyLoad>
          ) : null}
          {currentGameTab !== 5 && homeCompetitionsLength > MaxMatchItem[1] ? (
            <LazyLoad height={800} offset={10}>
              <RenderHomeCompetitions list={homeCompetitions.slice(MaxMatchItem[1], homeCompetitionsLength)} />
            </LazyLoad>
          ) : null}
        </Loading>
        {/* upcoming list */}
        {!loading && currentGameTab === HomeGameTab.Live && (
          <LazyLoad height={200} offset={10}>
            <UpComing />
          </LazyLoad>
        )}
      </>
    ) : (
      <FavoriteMatches />
    );
  }),
);

export default GameListContainer;
