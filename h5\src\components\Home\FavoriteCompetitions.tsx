import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";
import { CategoryIcon, FallbackCategoryImage } from "iscommon/const/icon";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { inject, observer } from "mobx-react";
import { useMemo } from "react";
import { Link } from "umi";
import './FavoriteCompetitions.less'

interface Props {
  store?: any;
}

const FavoriteCompetitions: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: { WebHome },
    } = props;
    const { favoriteCompetitions } = WebHome;
    const labelMaps = useTranslateKeysToMaps(['Favorites']);

    const fList = useMemo(() => [...(favoriteCompetitions || [])], [favoriteCompetitions]);

    return (
      <div className='common-favorite-competition-container'>
        <div className='container-title'>{labelMaps.Favorites}</div>
        <div className='container-body'>
          {fList.length === 0 ? (
              <div style={{color: '#fff'}}>No data...</div>
            ) : (
              fList.map((a, index) => {
                const id = a.competitionId;
                const name = a.competitionName;
                const logo = (CategoryIcon as any)[a.logo] || a.logo || (CategoryIcon as any);
                return (
                  <Link to={GlobalUtils.getPathname(PageTabs.competition, id)} className="favorite-competition-item" key={id + index}>
                    <img className="favorite-competition-logo" src={logo || FallbackCategoryImage} alt={name} loading="lazy"/>
                    <span className='favorite-competition-name'>{name}</span>
                  </Link>
                );
              })
            )}
        </div>
      </div>
    )
  }),
);


export default FavoriteCompetitions;