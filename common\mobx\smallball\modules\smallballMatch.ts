// @ts-nocheck
import { GlobalConfig } from 'iscommon/const/globalConfig';
import { makeAutoObservable } from 'mobx';

export const MatchTab = {
  overview: 'Overview',
  odds: 'Odds',
  lineup: 'Lineup',
  live: 'MatchLive',
  standings: 'Standings',
  h2h: 'H2H',
  boxScore: 'BoxScore',
};

export const MatchTabH5 = {
  overview: 'Overview',
  chat: 'Chat',
  boxScore: 'BoxScore',
  lineup: 'Lineup',
  odds: 'Odds',
  live: 'MatchLive',
  h2h: 'H2H',
  standings: 'Standings',
};

const defaultMatchHeaderInfo = {
  matchId: -1,
  competition: {
    id: '',
    name: '',
    logo: '',
    countryId: '',
    categoryId: '',
    type: 0,
  },
  homeTeam: {
    abbr: "",
    id: "",
    logo: "",
    name: "",
    shortName: ""
  },
  homeTeamId: "",
  awayTeam: {
    abbr: "",
    id: "",
    logo: "",
    name: "",
    shortName: ""
  },
  awayTeamId: "",
  venue: {
    name: '',
    city: '',
    capacity: 0,
  },
  mlive: 0,
  matchTime: 0,
  scores: [],
  extraScores: [],
  seasonId: '',
  matchStatus: -1,
  venue: {
    capacity:0,
    city: "Maia",
    country: null,
    countryId: null,
    id: "kjw2r0jf1k9mz84",
    name: "Court 4",
  },
  servingSide: 0,
  venueId: '',
  weather: null
};

export default class Match {
  constructor() {
    this.matchId = '';
    this.serverTime = GlobalConfig.serverTime;
    this.matchHeaderInfo = Object.assign({}, defaultMatchHeaderInfo);

    this.matchOdds = []

    makeAutoObservable(this);
  }

  changeMatchId(id = '') {
    this.matchId = id;
  }

  changeServerTime(time) {
    this.serverTime = time;
  }

  changeMatchHeaderInfo(data = {}) {
    this.matchHeaderInfo = Object.assign({}, defaultMatchHeaderInfo, data);
  }

  setMatchOdds(matchOdds = []) {
    this.matchOdds = matchOdds
  }

}
