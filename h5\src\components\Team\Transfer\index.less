// .container_box {
//   width: 100%;
//   display: flex;
//   flex-direction: column;
// }

// .select_data_container {
//   width: 100%;
//   height: 72px;
//   display: flex;
//   align-items: center;
//   background-color: #fff;
//   margin-top: 16px;
//   padding: 0 13px;
// }

// .select_team_style {
//   display: flex;
//   flex-direction: row;
//   justify-content: space-between;
//   flex: 1;
//   align-items: center;
// }

// .team_box {
//   display: flex;
//   flex-direction: row;
// }

// .select_year_style {
//   display: flex;
//   flex-direction: row;
//   justify-content: space-between;
//   align-items: center;
//   padding-left: 0.32rem;
//   // margin-left: 0.32rem;
//   position: relative;
//   width: 100%;
// }

// .select_year_style:nth-child(2):before {
//   border-left: 1px solid #e3e3e3;
//   margin-top: -0.26rem;
//   position: absolute;
//   height: 0.53rem;
//   content: '';
//   left: 1px;
//   top: 50%;
// }

// .transfer_type_style {
//   padding: 15px 20px;
//   display: flex;
//   flex-direction: row;
//   align-items: center;
// }

.team-transfer-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;

    .transfer-header-btn {
      display: block;
      background: transparent;
      color: #fff;
      border-radius: 32px;
      width: 200px;

      .transfer-content {
        display: flex; 
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .transfer-text {
          white-space: nowrap;   
          overflow: hidden;      
          text-overflow: ellipsis;
          flex-grow: 1;
          margin-right: 8px;
        }

        .transfer-icon {
          flex-shrink: 0;  
          margin-left: auto;
        }
      }
    }
  }

  .secondary-title {
    display: flex;
    flex-direction: row;
    padding: 20px;
    background: #1e1e1e;
    font-weight: 700;

    .secondary-title-btn {
      background-color: #121212;
      color: #fff;
      border: none;
      margin-right: 16px;
      padding: 4px 12px;
      cursor: pointer;
      display: flex;
      align-items: center;
      border-radius: 16px;
      font-size: 24px;
    
      &.active {
        background-color: #fff;
        color: #000;
      }
    }
  }

  .container-body {
    background: #1e1e1e;
  }
}

.adm-picker {
  .adm-picker-header {
    background: #2c2c2c;

    .adm-picker-header-button {
      color: #fff;
    }
  }

  .adm-picker-body {
    .adm-picker-view {
      background: #1e1e1e;
      color: #fff;

      .adm-picker-view-mask-top,
      .adm-picker-view-mask-bottom {
        background: #1e1e1e;
      }
    }
  }
}