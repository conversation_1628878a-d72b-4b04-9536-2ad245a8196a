// .scoreTableContainer {
//   width: 100%;
//   // height: 90px;
//   position: relative;
//   margin: 13px 0;
// }

// .tableTitle {
//   width: 100%;
//   height: 50px;
//   display: flex;
//   font-size: 24px;
  
//   font-weight: 400;
//   color: #999;
//   background-color: #fff;
//   border-bottom: 1px solid #eee;

//   > div {
//     line-height: 50px;
//     text-align: center;
//   }
// }

// .firstBoxContent {
//   width: 120px;
// }

// .flex1 {
//   flex: 1;
// }

// .tableContent {
//   width: 100%;
//   height: 50px;
//   background: #fff;
//   display: flex;
//   font-size: 24px;
//   font-family: Roboto-Regular, Roboto;
//   font-weight: 400;
//   color: #333;

//   > div {
//     line-height: 50px;
//     text-align: center;
//   }
// }

// .teamNameBox {
//   width: 120px;
//   overflow: hidden;
//   text-overflow: ellipsis;
//   white-space: nowrap;
//   cursor: pointer;
//   display: flex;
//   align-items: center;
//   box-sizing: border-box;
//   padding-left: 32px;
//   position: relative;
// }

// .teamTip {
//   width: 14px;
//   height: 30px;
//   position: absolute;
//   left: 0;
//   top: 9.729px;
//   border-radius: 13px;
//   left: -6.624px;
// }

// .teamImg {
//   width: 34px;
//   height: 34px;
//   margin-right: 8px;
//   object-fit: contain;
// }

// .teamName {
//   display: inline-block;
//   max-width: 130px;
//   white-space: nowrap;
//   overflow: hidden;
//   text-overflow: ellipsis;
// }

// .insideLoserColor {
//   color: #999;
// }

.basketball-overview-score-table-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .container-body {
    background: #1e1e1e;

    .custom-score-table-header, .custom-score-table-body {
      display: flex;
      align-items: center;
    }
  
    .custom-score-table-header {
      font-weight: 600;
      margin-bottom: 10px;
      color: #fff;

      .header-cell {
        text-align: center;
        padding: 8px;
      }

      .team-cell {
        width: 250px;
      }

      .total-cell {
        width: 20%;
      }

      .header-cell:not(.team-cell):not(.total-cell) {
        flex: 1;
        width: 10%;
      }
    }

    .custom-score-table-body {
      .score-cell {
        text-align: center;
        padding: 8px;
        color: #fff;
        font-weight: bold;

        .color-c1272d {
          color: #c1272d;
          font-weight: bold;
        }
      
        .color-white {
          color: #fff;
        }
      
        .color-dim {
          color: #c0c5c9;
        }
      }

      .team-cell {
        display: flex;
        align-items: center;
        width: 250px;

        .team-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin: 5px 10px;
          overflow: hidden;
          background-size: cover;
        }

        .team-name {
          color: #fff;
          font-size: 18px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }

      .total-cell {
        width: 20%;
      }

      .score-cell:not(.team-cell):not(.total-cell) {
        flex: 1;
        width: 10%;
      }
    }
  }
}
