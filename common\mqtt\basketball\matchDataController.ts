// @ts-nocheck
import _ from 'lodash';

import store from 'iscommon/mobx';
import { getArrayFromString } from 'iscommon/utils';
import { formatTimeLineData } from '../../utils/dataUtils';

const syncmatchScore = (data) => {
  // console.log('score', data)
  const { Match } = store.Basketball;
  const {matchId} = Match
  const matchHeaderInfo = _.cloneDeep(Match.matchHeaderInfo);
  if (!matchId) {
    return false;
  }

  const { id, score = [] } = data;
  if (score.length > 0 && matchId == id) {
    const matchStatus = score[1];
    const homeScores = getArrayFromString(score[3]);
    const awayScores = getArrayFromString(score[4]);
    matchHeaderInfo.matchStatus = matchStatus;
    matchHeaderInfo.homeScores = homeScores;
    matchHeaderInfo.awayScores = awayScores;
    matchHeaderInfo.remainSeconds = Math.max(score[2], 0);
    
    Match.changeMatchHeaderInfo(matchHeaderInfo);
  }
};

// noinspection JSUnusedLocalSymbols
const syncIncidents = (data) => {
  // console.log('incidents', data)
  // incidents = [
  //   {type: 9, position: 1, time: 15},
  //   {type: 1, position: 1, time: 25},
  //   {type: 3, position: 1, time: 30}
  // ]
  // const { Match } = store;
  // const {matchId, matchHeaderInfo: {homeScores, awayScores}} = Match
  // if (!matchId) {
  //   return false;
  // }
  // const { id, incidents } = data;
  // if (matchId === id && incidents.length > 0 && homeScores.length > 0 && awayScores.length > 0) {
  //   const list = formatTimeLineData({
  //     awayScores,
  //     homeScores,
  //     incidents: incidents.map(item => {
  //       return {
  //         ...item,
  //         homeScore: item.home_score,
  //         awayScore: item.away_score
  //       }
  //     }),
  //   });
  //   Match.setTimeLineList(list);
  // }
};

// the stats data is same as the scores data
// noinspection JSUnusedLocalSymbols
const syncStats = (data) => {
  // console.log('stats', data)
  // stats = [
  //   {type: 3, match: 2, away: 2},
  //   {type: 23, match: 74, away: 68},
  //   {type: 2, match: 3, away: 5}
  // ]
  // const { Match } = store;
  // const {matchId} = Match
  // if (!matchId) {
  //   return false;
  // }
  // const { id, stats } = data;
  // if (matchId === id && stats.length > 0) {
  //   Match.setStatsList(stats);
  // }
};

const mixinCompetitionOdds = (matchRecentOdds, oldOdds = [], matchId) => {
  let cloneList = _.cloneDeep(oldOdds);
  if(!cloneList || cloneList.length === 0) {
    return []
  }
  // latest odd
  const validOddList = matchRecentOdds[matchId] // []
  if(validOddList && validOddList.length > 0) {
      // const newOddsMaps = validOddList.reduce((acc, cur) => {
      //   const { companyId } = cur;
      //   acc[companyId] = cur;
      //   return acc;
      // }, {});
      // for (let i = 0; i < cloneList.length; i++) {
      //   const item = cloneList[i];
      //   if (newOddsMaps[item.companyId]) {
      //     cloneList[i] = {
      //       ...newOddsMaps[item.oddsType],
      //       lastOddsData: item.oddsData
      //     };
      //   }
      // }
      // 循环添加
      for(let v of validOddList) {
        const {companyId, oddsType} = v
        for (let i = 0; i < cloneList.length; i++) {
            const item = cloneList[i];
            if (companyId === item.companyId) {
              for(let j = 0; j < item.companyOdds.length; j++) {
                if(oddsType === item.companyOdds[j].oddsType) {
                  item.companyOdds[j].odds.unshift(v)
                }
              }
            }
          }
      }
  }
  return cloneList;
}

const syncMatchOdds = (matchRecentOdds) => {
  const { Match } = store.Basketball;
  const { matchOdds, matchId } = Match;
  const cloneList = mixinCompetitionOdds(matchRecentOdds, matchOdds, matchId);
  Match.setMatchOdds(cloneList);
};

export const matchDataControllerType = {
  score: 'score',
  stats: 'stats',
  incidents: 'incidents',
  syncMatchOdds: 'syncMatchOdds',
};

const matchDataController = {
  [matchDataControllerType.score]: syncmatchScore,
  [matchDataControllerType.stats]: syncStats,
  [matchDataControllerType.incidents]: syncIncidents,
  [matchDataControllerType.syncMatchOdds]: syncMatchOdds,
};

export default matchDataController;
