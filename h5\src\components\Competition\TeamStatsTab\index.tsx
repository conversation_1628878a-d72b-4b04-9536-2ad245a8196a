import { inject, observer } from 'mobx-react';
import { useEffect, useMemo, useState } from 'react';

import { getStatsTeam } from 'iscommon/api/competition';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

import { showDataDetail } from '@/utils/dataDetail';

import OtherStats from '../PlayerStatsTab/OtherStats';
import TeamStats from './TeamStats';

import styles from './index.less';

const index = inject('store')(
  observer((props: any) => {
    const {
      store: { Competitions },
    } = props;
    const {
      competitionId,
      currentSeason: { id: seasonId },
    } = Competitions;

    const [allList, setAllList] = useState([]);

    const labelMaps = useTranslateKeysToMaps([
      'Team',
      'TeamStats',
      'Goals',
      'ViewAll',
      'ballPossession',
      'shotsPerGame',
      'penaltiesWon',
      'cornersPerGame',
      'keyPassesPerGame',
      'accurateLongBallsPerGame',
      'accurateCrossesPerGame',
      'goalsConceded',
      'tacklesPerGame',
      'interceptionsPerGame',
      'clearancesPerGame',
      'blockedShotsPerGame',
      'YellowCards',
      'RedCards',
      'turnoversPerGame',
      'foulsPerGame',
    ]);

    const otherStatsList = useMemo(
      () => ({
        offensiveData: [
          {
            label: labelMaps.ballPossession,
            value: 'ballPossession',
          },
          {
            label: labelMaps.shotsPerGame,
            value: 'shotsPerGame',
          },
          {
            label: labelMaps.penaltiesWon,
            value: 'penalty',
          },
          {
            label: labelMaps.cornersPerGame,
            value: 'cornerKicksPerGame',
          },
          {
            label: labelMaps.keyPassesPerGame,
            value: 'keyPassesPerGame',
          },
          {
            label: labelMaps.accurateLongBallsPerGame,
            value: 'longBallsAccuracyPerGame',
          },
          {
            label: labelMaps.accurateCrossesPerGame,
            value: 'crossesAccuracyPerGame',
          },
        ],
        defenseData: [
          {
            label: labelMaps.goalsConceded,
            value: 'goalsAgainst',
          },
          {
            label: labelMaps.tacklesPerGame,
            value: 'tacklesPerGame',
          },
          {
            label: labelMaps.interceptionsPerGame,
            value: 'interceptionsPerGame',
          },
          {
            label: labelMaps.clearancesPerGame,
            value: 'clearancesPerGame',
          },
          {
            label: labelMaps.blockedShotsPerGame,
            value: 'blockedShotsPerGame',
          },
        ],
        otherData: [
          {
            label: labelMaps.YellowCards,
            value: 'yellowCards',
          },
          {
            label: labelMaps.RedCards,
            value: 'redCards',
          },
          // {
          //   label: labelMaps.turnoversPerGame,
          //   value: 'turnoversPerGame',
          // },
          {
            label: labelMaps.foulsPerGame,
            value: 'foulsPerGame',
          },
        ],
      }),
      [
        labelMaps.RedCards,
        labelMaps.YellowCards,
        labelMaps.accurateCrossesPerGame,
        labelMaps.accurateLongBallsPerGame,
        labelMaps.ballPossession,
        labelMaps.blockedShotsPerGame,
        labelMaps.clearancesPerGame,
        labelMaps.cornersPerGame,
        labelMaps.foulsPerGame,
        labelMaps.goalsConceded,
        labelMaps.interceptionsPerGame,
        labelMaps.keyPassesPerGame,
        labelMaps.penaltiesWon,
        labelMaps.shotsPerGame,
        labelMaps.tacklesPerGame,
        // labelMaps.turnoversPerGame,
      ],
    );

    useEffect(() => {
      if (competitionId && seasonId) {
        getStatsTeam({
          competitionId,
          seasonId,
        }).then((res: any) => {
          if (res?.teamStatistics) {
            const list = res?.teamStatistics.map((item: any) => {
              const shotsPerGame = (item.shots / item.matches).toFixed(1);
              const cornerKicksPerGame = (item.cornerKicks / item.matches).toFixed(1);
              const keyPassesPerGame = (item.keyPasses / item.matches).toFixed(1);
              const longBallsAccuracyPerGame = (item.longBallsAccuracy / item.matches).toFixed(1);
              const crossesAccuracyPerGame = (item.crossesAccuracy / item.matches).toFixed(1);
              const tacklesPerGame = (item.tackles / item.matches).toFixed(1);
              const interceptionsPerGame = (item.interceptions / item.matches).toFixed(1);
              const clearancesPerGame = (item.clearances / item.matches).toFixed(1);
              const blockedShotsPerGame = (item.blockedShots / item.matches).toFixed(1);
              const foulsPerGame = (item.fouls / item.matches).toFixed(1);
              return {
                ...item,
                shotsPerGame,
                cornerKicksPerGame,
                keyPassesPerGame,
                longBallsAccuracyPerGame,
                crossesAccuracyPerGame,
                tacklesPerGame,
                interceptionsPerGame,
                clearancesPerGame,
                blockedShotsPerGame,
                foulsPerGame,
              };
            });
            setAllList(list);
          }
        });
      }
    }, [competitionId, seasonId]);

    return (
      <div className={styles.stats_container}>
        <div className={styles.stats_type_title}>{labelMaps.Goals}</div>
        <TeamStats type="goals" />
        <div className={styles.stats_type_action_btn}>
          <div
            className={styles.action_btn}
            onClick={() => {
              showDataDetail({
                title: labelMaps.TeamStats,
                subTitleOptions: [
                  { label: labelMaps.Goals, value: 'goals' },
                  ...otherStatsList.offensiveData,
                  ...otherStatsList.defenseData,
                  ...otherStatsList.otherData,
                ],
                list: allList || [],
                sectionLeftText: labelMaps.Team,
                currentProperty: { label: labelMaps.Goals, value: 'goals' },
                detailType: 'Team',
                // renderValue: renderValue(info, true, property),
                // renderValue: { isTotal: true },
              });
            }}
          >
            {labelMaps.ViewAll}
            {'>'}
          </div>
        </div>
        <OtherStats data={otherStatsList} type="Team" allList={allList} />
      </div>
    );
  }),
);

export default index;
