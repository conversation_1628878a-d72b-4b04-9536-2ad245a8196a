import SmallballMatchHeader from '@/components/SmallGameCommon/Match/MatchHeader';
import {
  BaseballInLiveStatusEnum,
  BaseballMatchStatusCodeToText,
  BaseballStatusCodeEnum,
} from 'iscommon/const/baseball/constant';
import { getBaseballScoreList } from 'iscommon/utils/dataUtils';

const MatchHeader = () => {
  const getHomeBasketBallMatchTimeText = (matchHeaderInfo) => {
    const { matchStatus, scores, servingSide } = matchHeaderInfo;
    const list: any[] = getBaseballScoreList(matchStatus, scores, servingSide);
    const isIng = BaseballInLiveStatusEnum.includes(matchStatus);
    const latestScore = list[list.length - 1];
    const text = (BaseballMatchStatusCodeToText as any)[matchStatus];
    const res = {
      statusText: text,
      isIng,
    };
    return res;
  };

  return (
    <SmallballMatchHeader
      checkIsIng={(matchStatus) => BaseballInLiveStatusEnum.includes(matchStatus)}
      getHomeScore={({ scores }) => scores?.ft?.[0] || 0}
      getAwayScore={({ scores }) => scores?.ft?.[1] || 0}
      notStartedCode={BaseballStatusCodeEnum.NotStarted}
      getHomeBasketBallMatchTimeText={getHomeBasketBallMatchTimeText}
      ballIconCode="#iconbaseball_ball_1"
    />
  );
};

export default MatchHeader;
