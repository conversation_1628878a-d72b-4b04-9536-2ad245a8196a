import SmallballMatchHeader from '@/components/SmallGameCommon/Match/MatchHeader';
import {
  AmFootballInLiveStatusEnum,
  AmFootballMatchStatusCodeToText,
  AmFootballStatusCodeEnum,
} from 'iscommon/const/amfootball/constant';
import { getAmFootballScoreList } from 'iscommon/utils/dataUtils';

const MatchHeader = () => {
  const getHomeBasketBallMatchTimeText = (matchHeaderInfo) => {
    const { matchStatus, scores, servingSide } = matchHeaderInfo;
    const list: any[] = getAmFootballScoreList(matchStatus, scores, servingSide, true, AmFootballInLiveStatusEnum);
    const isIng = AmFootballInLiveStatusEnum.includes(matchStatus);
    const latestScore = list[list.length - 1];
    const text = (AmFootballMatchStatusCodeToText as any)[matchStatus];
    const res = {
      statusText: text,
      isIng,
    };
    return res;
  };

  return (
    <SmallballMatchHeader
      checkIsIng={(matchStatus) => AmFootballInLiveStatusEnum.includes(matchStatus)}
      getHomeScore={({ scores }) => scores?.ft?.[0] || 0}
      getAwayScore={({ scores }) => scores?.ft?.[1] || 0}
      notStartedCode={AmFootballStatusCodeEnum.NotStarted}
      getHomeBasketBallMatchTimeText={getHomeBasketBallMatchTimeText}
    />
  );
};

export default MatchHeader;
