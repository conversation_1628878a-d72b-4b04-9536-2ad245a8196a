import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

import './WorldRank.less';

const WorldRank = () => {
  const labelMaps = useTranslateKeysToMaps(['FIFAWorldRanking']);

  return (
    <div className="world-rank">
      <div className="left">
        <img className="logo" alt="logo" loading="lazy"/>
        <span className="text">{labelMaps.FIFAWorldRanking}</span>
      </div>
      <i className="icon iconfont iconjiantou fs-12"></i>
    </div>
  );
};

export default WorldRank;
