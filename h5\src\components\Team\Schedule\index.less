// .teamSchedule {
//   .nextButton {
//     width: 100%;
//     height: 54px;
//     background: #ffffff;
//     display: flex;
//     justify-content: space-between;
//     padding: 0 20px;
//     align-items: center;
//     font-size: 24px;
//     font-weight: 500;
//     color: #0d80d9;
//     div {
//       display: flex;
//       align-items: center;
//     }
//     .icon {
//       width: 40px;
//       height: 40px;
//       margin-right: 12px;
//     }
//     .count {
//       color: #999;
//     }
//   }
// }

.team-schedule-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;

    .team-month-btn {
      display: block;
      background: transparent;
      color: #fff;
      border-radius: 32px;

      .btn-content {
        display: flex; 
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .btn-value {
          white-space: nowrap;   
          overflow: hidden;      
          text-overflow: ellipsis;
          flex-grow: 1;
          margin-right: 8px;
        }

        .down-icon {
          flex-shrink: 0;  
          margin-left: auto;
        }
      }
    }
  }

  .container-body {
    background: #1e1e1e;
    padding: 10px;
  
    .custom-collapse {
      .adm-list-default, 
      .adm-list-body,
      .adm-list-item,
      .adm-list-item-content {
        background: transparent;
        border: none;
      }
  
      .adm-list {
        --border-inner: none;
      }
  
      .panel-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
  
        .competition-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48px;
          height: 48px;
          border-radius: 50%;
          margin-right: 10px;
          overflow: hidden;
          background-size: cover;
        }

        .competition-name {
          font-size: 22px;
          font-weight: bold;
          color: #fff;
          text-overflow: ellipsis;
        }
      }

      .match-list-container {
        width: 100%;
        height: fit-content;
        background: #121212;
        border-radius: 24px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;
        padding: 0 16px;
        cursor: pointer;
        margin-bottom: 16px;
    
        .match-time-container {
          display: flex;
          flex-direction: row;
          align-items: center;
          width: 20%;
          padding: 0px 10px;
    
          .match-time {
            font-size: 24px;
            font-weight: bold;
            color: #fff;
          }
        }
    
        .match-team-container {
          display: flex;
          flex-direction: column;
          width: 70%;
    
          .match-team-info {
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 10px 0px;
    
            .team-logo {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 40px;
              height: 40px;
              border-radius: 50%;
              margin-right: 10px;
              overflow: hidden;
              background-size: cover;
            }
    
            .team-name {
              font-size: 24px;
              font-weight: bold;
              color: #fff;
              text-overflow: ellipsis;
            }
          }
        }

        .match-score-container {
          display: flex;
          flex-direction: column;
          margin-right: 10px;
          color: #fff;
    
          .match-score {
            font-size: 24px;
            font-weight: bold;
            color: #fff;
            padding: 10px 0px;
          }
        }
      }
    }
  }
}


