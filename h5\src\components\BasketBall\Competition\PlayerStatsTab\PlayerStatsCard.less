.basketball-player-stats-card-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;

  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .container-body {
    background: #121212;
    padding: 10px;

    .picker-container {
      display: flex;
      align-items: center;
      margin: 10px;
      justify-content: space-between;

      .custom-btn {
        display: block;
        background: transparent;
        color: #fff;
        border-radius: 32px;

        &.stage {
          width: 300px;
        }

        &.game {
          width: 200px;
        }

        .btn-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: 100%;

          .btn-value {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            flex-grow: 1;
            margin-right: 8px;
          }

          .down-icon {
            flex-shrink: 0;
            margin-left: auto;
          }
        }
      }
    }
  }
}

.adm-picker {
  .adm-picker-header {
    background: #2c2c2c;

    .adm-picker-header-button {
      color: #fff;
    }
  }

  .adm-picker-body {
    .adm-picker-view {
      background: #1e1e1e;
      color: #fff;

      .adm-picker-view-mask-top,
      .adm-picker-view-mask-bottom {
        background: #1e1e1e;
      }
    }
  }
}