import SectionTitle from '@/components/Competition/Overview/SectionTitle';
import { Avatar } from 'antd-mobile';
import { getTransfer } from 'iscommon/api/competition';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { translate, useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { compare } from 'iscommon/utils';
import { useEffect, useState } from 'react';
import styles from './TeamLastTransfer.less';

interface Props {
  teamId: string;
  teamHeaderInfo: any;
}

const TeamLastTransfer = (props: Props) => {
  const { teamId, teamHeaderInfo } = props;
  const {
    competition: { id: competitionId },
    currentSeason: { id: seasonId, year },
  } = teamHeaderInfo;
  const LatestTransfersText = translate('LatestTransfers');

  const [transferIn, setTransferIn] = useState([]);
  const [transferOut, setTransferOut] = useState([]);
  const labelMaps = useTranslateKeysToMaps(['TransferIn', 'TransferOut']);

  useEffect(() => {
    if (competitionId && seasonId && teamId)
      getTransfer({ competitionId, seasonId, teamId, year }).then(({ teamTransfers }: any) => {
        if (teamTransfers?.length) {
          setTransferIn(teamTransfers[0].transferIn.sort(compare('transferTime')) || []);
          setTransferOut(teamTransfers[0].transferOut.sort(compare('transferTime')) || []);
        }
      });
  }, [competitionId, seasonId, teamId, year]);

  if (transferIn.length === 0 && transferOut.length === 0) return null;

  return (
    <>
      <SectionTitle leftTitle={LatestTransfersText} />
      <div className={styles.lastTransfer}>
        <div>
          <span>
            {labelMaps.TransferIn} {transferIn.length}
          </span>
          {transferIn.slice(0, 3).map((item: any, index) => (
            <div className={styles.player} key={index} onClick={() => GlobalUtils.goToPage(PageTabs.player, item.player.id)}>
              <Avatar src={item.player ? item.player.logo : ''} className={styles.avatar_style} />
              <span className={styles.player_name_style}>{item.player?.name}</span>
            </div>
          ))}
        </div>
        <div className={styles.leftBorder}>
          <span>
            {labelMaps.TransferOut} {transferOut.length}
          </span>
          {transferOut.slice(0, 3).map((item: any, index) => (
            <div className={styles.player} key={index} onClick={() => GlobalUtils.goToPage(PageTabs.player, item.player.id)}>
              <Avatar src={item.player ? item.player.logo : ''} className={styles.avatar_style} />
              <span className={styles.player_name_style}>{item.player?.name}</span>
            </div>
          ))}
        </div>
      </div>
    </>
  );
};

export default TeamLastTransfer;
