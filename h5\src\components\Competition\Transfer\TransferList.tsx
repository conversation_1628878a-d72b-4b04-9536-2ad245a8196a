import { Avatar, List } from 'antd-mobile';
import moment from 'moment';
import { useMemo } from 'react';

import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

import styles from './TransferList.less';
import './TransferList.less'
import { FallbackImage, FallbackPlayerImage } from 'iscommon/const/icon';

interface Props {
  data: any[];
  type: string;
  comeType?: 'Competition' | 'Team';
}

const TransferList: React.FC<Props> = (props: any) => {
  // comeType: Competition Team 赛事根据type决定取fromTeam还是toTeam 球队根据getType决定取fromTeam还是toTeam
  const { data = [], type = 'Arrivals', comeType = '' } = props;
  const labelMaps = useTranslateKeysToMaps([
    'Loan',
    'EndOfLoan',
    'Transfer',
    'Retirement',
    'Draft',
    'Released',
    'Signed',
    'Unknown',
  ]);
  const transferTypeMaps: any = useMemo(
    () => ({
      1: labelMaps['Loan'],
      2: labelMaps['EndOfLoan'],
      3: labelMaps['Transfer'],
      4: labelMaps['Retirement'],
      5: labelMaps['Draft'],
      6: labelMaps['Released'],
      7: labelMaps['Signed'],
      8: labelMaps['Unknown'],
    }),
    [labelMaps],
  );

  return (
    <List className='competition-transfer-list-container'>
      {data.map((item: any, index: number) =>
        item.fromTeam && item.toTeam ? (
          <List.Item 
            className='transfer-list-container' 
            key={index} 
            onClick={() => GlobalUtils.goToPage(PageTabs.player, item.player.id)}
            arrow={false}
            prefix={
              <img 
                className='player-logo' 
                src={item?.player?.logo || FallbackPlayerImage} 
                alt={item?.player?.name} 
              />
            }>
              <div className='transfer-player-detail'>
                <div className='transfer-player-info'>
                  <span className='player-name'>{item?.player?.name}</span>
                  <span className='transfer-date'>{moment(item.transferTime * 1000).format('YYYY-MM-DD')}</span>
                </div>
                <div className='transfer-player-team'>
                  <div className='transfer-team-container'>
                    <img
                      className='transfer-team-logo'
                      src={
                        type === 'Arrivals'
                        ? (item?.toTeam?.logo || FallbackImage)
                        : comeType === 'Competition'
                        ? (item?.toTeam?.logo || FallbackImage) 
                        : (item?.fromTeam?.logo || FallbackImage) 
                      }
                      alt={
                        type === 'Arrivals' 
                        ? item?.toTeam?.name
                        : comeType === 'Competition'
                        ? item?.toTeam?.name
                        : item?.fromTeam?.name
                      }
                    />
                    <span className='transfer-team-name'>
                      {type === 'Arrivals' 
                      ? item?.toTeam?.name
                      : comeType === 'Competition'
                      ? item?.toTeam?.name  
                      : item?.fromTeam?.name
                      }
                    </span>
                  </div>
                  <span className='transfer-type'>{item.transferFee ? `€ ${(item.transferFee / 1000000).toFixed(1)}M` : transferTypeMaps[item.transferType]}</span>
                </div>
              </div>
          </List.Item>
        ) : null 
      )}
    </List>
    // <List>
    //   {data.map((item: any, index: number) =>
    //     item.fromTeam ? (
    //       <List.Item
    //         key={index}
    //         className={styles.list_item_box}
    //         onClick={() => GlobalUtils.goToPage(PageTabs.player, item.player.id)}
    //       >
    //         <div className={styles.list_item_style}>
    //           <div className={styles.list_item_left}>
    //             <Avatar className={styles.list_item_avatar} src={item.player?.logo} />
    //             <div className={styles.left_item_name_box}>
    //               <span className={styles.left_item_name}>{item.player?.name}</span>
    //               <span className={styles.left_item_date}>{moment(item.transferTime * 1000).format('YYYY-MM-DD')}</span>
    //             </div>
    //           </div>
    //           <div className={styles.list_item_right}>
    //             <div className={styles.list_item_right_transferTeam}>
    //               {comeType === 'Competition'
    //                 ? type === 'Arrivals'
    //                   ? item.fromTeam?.name
    //                   : item.toTeam?.name
    //                 : item.getType === 'fromTeam'
    //                 ? item.fromTeam?.name
    //                 : item.toTeam?.name}
    //               <i className="iconfont icondianji"></i>
    //             </div>
    //             <span className={styles.list_item_right_transferType}>
    //               {item.transferFee ? `€ ${(item.transferFee / 1000000).toFixed(1)}M` : transferTypeMaps[item.transferType]}
    //             </span>
    //           </div>
    //         </div>
    //       </List.Item>
    //     ) : null,
    //   )}
    // </List>
  );
};

export default TransferList;
