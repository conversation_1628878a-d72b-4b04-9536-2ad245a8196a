<!-- <script class="aioseop-schema" type="application/ld+json">
  {
    "@context": "https://schema.org",
    "@graph": [
      {
        "@type": "Organization",
        "@id": "https://www.igscore.net/#organization",
        "url": "https://www.igscore.net/",
        "name": "Live Stream Ucl, Epl Live Stream, Hasil Liga Italia",
        "sameAs": []
      },
      {
        "@type": "WebSite",
        "@id": "https://www.igscore.net/#website",
        "url": "https://www.igscore.net/",
        "name": "Jadwal Liga Italia, Klasemen Liga Italia, Europa League Live Stream",
        "publisher": {
          "@id": "https://www.igscore.net/#organization"
        }
      },
      {
        "@type": "WebPage",
        "@id": "https://www.igscore.net/#webpage",
        "url": "https://www.igscore.net/",
        "inLanguage": "ENH",
        "name": "Hasil Liga Jerman, Jadwal Liga Jerman, Klasemen Liga Jerman",
        "isPartOf": {
          "@id": "https://www.igscore.net/#website"
        },
        "breadcrumb": {
          "@id": "https://www.igscore.net/#breadcrumblist"
        },
        "description": "Igscore- Hasil Liga Inggris, Jadwal Liga Inggris, Klasemen Liga Inggris, Hasil Liga Spanyol, Jadwal Liga Spanyol, Klasemen Liga Spanyol, Hasil Liga 1, Hasil Liga Champions",
        "about": {
          "@id": "https://www.igscore.net/#organization"
        }
      },
      {
        "@type": "BreadcrumbList",
        "@id": "https://www.igscore.net/#breadcrumblist",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "item": {
              "@type": "WebPage",
              "@id": "https://www.igscore.net/",
              "url": "https://www.igscore.net/",
              "name": "Igscore- Hasil Liga 1, Jadwal Liga 1, Klasemen Liga 1, Hasil Liga Champions, Jadwal Liga Champions"
            }
          }
        ]
      }
    ]
  }
</script> -->
<script src="https://accounts.google.com/gsi/client" async></script>
<!-- <script>
  function addJS(src, id) {
    var js,
        fjs = document.getElementsByTagName('script')[0];
      if (document.getElementById(id)) {
        return;
      }
      js = document.createElement('script');
      js.id = id;
      js.src = src;
      js.async = true;
      fjs.parentNode.insertBefore(js, fjs);
  }
</script> 
<script>
  window.fbAsyncInit = function () {
      FB.init({
        appId: "***************",
        cookie: true,
        xfbml: true,
        version: "v16.0",
      });
      FB.AppEvents.logPageView();
    };
  (function (d, s, id) {
    var js,
      fjs = d.getElementsByTagName(s)[0];
    if (d.getElementById(id)) {
      return;
    }
    js = d.createElement(s);
    js.id = id;
    js.src = "https://connect.facebook.net/en_US/sdk.js";
    js.async = true;
    fjs.parentNode.insertBefore(js, fjs);
  })(document, "script", "facebook-jssdk");
</script> -->
<script>
  if (process.env.APP_ENV === 'production') {
    window.__consoleLog__ = window.console.log;
    window.console.log = function () {};
  }
</script>