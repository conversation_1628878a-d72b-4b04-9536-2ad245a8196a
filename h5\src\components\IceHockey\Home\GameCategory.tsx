import { inject, observer } from 'mobx-react';
import React, { useMemo } from 'react';

import { getIceHockeyScoreList } from 'iscommon/utils/dataUtils';

import SmallballCommonGameCategory from '@/components/SmallGameCommon/Home/GameCategory';
import {
  IckHockeyInLiveStatusEnum,
  IckHockeyMatchStatusCodeToText,
  IckHockeyStatusCodeEnum,
} from 'iscommon/const/iceHockey/constant';
import _ from 'lodash';
import './GameCategory.less';

interface PureMatchStatusTextProps {
  serverTime: number;
  matchStatus: number;
  remainSeconds: number;
}

const MatchStatusText = React.memo<PureMatchStatusTextProps>((props) => {
  const { matchStatus } = props;
  const { statusText, isIng } = useMemo(() => {
    return {
      statusText: (IckHockeyMatchStatusCodeToText as any)[matchStatus],
      isIng: IckHockeyInLiveStatusEnum.includes(matchStatus),
    };
  }, [matchStatus]);

  return <span className={`time fs-10-center ${isIng ? 'ing' : ''}`}>{statusText}</span>;
});

interface ScoreItem {
  h: number;
  w: number;
  isRed: boolean;
  compareStatus: number; // 1 home > away 2 home < away 0 =
}

const ScoreItem = ({ item, isTotal = false }: { item: ScoreItem; isTotal: boolean }) => {
  return (
    <div className={`listRightSection ${item.isRed && 'color-c1272d'}`}>
      <div className={`${item.isRed && 'ing'} ${item.compareStatus === 1 && !item.isRed && 'color-333'}`}>{item.h}</div>
      <div className={`${item.isRed && 'ing'} ${item.compareStatus === 2 && !item.isRed && 'color-333'}`}>{item.w}</div>
    </div>
  );
};

const ScoreText = (props: any) => {
  const { item } = props;
  const { matchStatus, scores } = item;
  const list: any = getIceHockeyScoreList(matchStatus, scores);
  if (matchStatus === IckHockeyStatusCodeEnum.NotStarted || _.isEmpty(scores)) {
    return <div className="listRight">-</div>;
  }
  // show ft
  return <div className="listRight rowFlex">{list.ft && <ScoreItem item={list.ft} isTotal />}</div>;
};

const ScoreServingPt = ({ item }: any) => {
  const { scores } = item;
  if (scores.pt && scores.pt.length > 1) {
    return (
      <div className="spt">
        <span>{scores.pt[0] || 0}</span>
        <span className="spt">{scores.pt[1] || 0}</span>
      </div>
    );
  }
  return <div className="spt"></div>;
};

const ScoreOrOdds = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Smallball: { SmallballCommon },
      },
      item,
    } = props;
    const { matchId, matchStatus } = item;
    const { homeCompetitionOdds } = SmallballCommon;
    const matchOdds = homeCompetitionOdds[matchId] || [];
    if (matchStatus === IckHockeyStatusCodeEnum.NotStarted) {
      const currentOdd = matchOdds?.filter((item: any) => item.oddsType === 'eu')[0]?.oddsData;
      // future games show odds
      /*
      if (currentOdd) {
        return (
          <div className="listRight">
            {[0, 2].map((key) => (
              <span key={key} className="odd">
                {Number(currentOdd[key]).toFixed(2)}
              </span>
            ))}
          </div>
        );
      }
      */
      return (
        <div className="listRight">
          <span className="odd">-</span>
        </div>
      );
    }
    return <ScoreText item={item} />;
  }),
);

const TeamNameExra = ({ isHome = false, item }: any) => {
  const { servingSide } = item;
  if ((isHome && servingSide == 1) || (!isHome && servingSide == 2)) {
    return (
      <svg aria-hidden="true" className="icon fs-12 wangqiu svgPostop">
        <use xlinkHref="#iconiconwangqiu"></use>
      </svg>
    );
  }
  return null;
};

const GameCategory = ({ competition }: any) => {
  return (
    <SmallballCommonGameCategory
      competition={competition}
      renderMatchStatusText={(item) => <MatchStatusText {...item} />}
      renderScoreOrOdds={(item) => {
        return (
          <>
            <ScoreServingPt item={item} />
            <ScoreOrOdds item={item} />
          </>
        );
      }}
      renderTeamNameExra={(isHome, item) => <TeamNameExra isHome={isHome} item={item} />}
    />
  );
};

export default GameCategory;
