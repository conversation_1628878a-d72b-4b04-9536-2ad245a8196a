import { getCompetitionList } from "iscommon/api/basketball/home";
import { getHomeBasketBallMatchTimeText } from 'iscommon/utils/dataUtils';
import PluginHomeShared from './pluginHomeShared';

const PluginBasketball: React.FC = () => {
  // Basketball-specific data fetching logic
  const fetchMatches = async () => {
    try {
      // Fetch live basketball matches (listType: 0)
      const { competitions: liveCompetitions } = await getCompetitionList({ listType: 0 });
      console.log('🏀 Basketball Plugin: live competitions', liveCompetitions);

      // Extract live competition IDs for sharing with PopularCompetitions
      const liveCompIds = new Set(liveCompetitions.map((comp: any) => comp.competitionId?.toString()).filter(Boolean)) as Set<string>;

      const liveMatches = liveCompetitions.flatMap((comp: any) =>
        (comp.matches || []).map((match: any) => ({
          ...match,
          competitionName: comp.competitionName,
          type: 'live',
        }))
      );

      // Prepare dateFilter (today in ISO string with +08:00)
      const today = new Date();
      const dateFilter = new Date(today.getTime() - today.getTimezoneOffset() * 60000)
        .toISOString()
        .replace('Z', '+08:00');

      // Fetch upcoming basketball matches (listType: 2 with dateFilter)
      const { competitions: upcomingCompetitions } = await getCompetitionList({ listType: 2, dateFilter });
      const upcomingMatches = upcomingCompetitions.flatMap((comp: any) =>
        (comp.matches || []).map((match: any) => ({
          ...match,
          competitionName: comp.competitionName,
          type: 'upcoming',
        }))
      );

      const allMatches = [...liveMatches, ...upcomingMatches];

      // Extract unique competition names for dropdown
      const competitions = Array.from(new Set(allMatches.map(match => match.competitionName)));

      return {
        allMatches,
        competitions,
        liveCompetitionIds: liveCompIds
      };
    } catch (error) {
      console.error('Error fetching basketball matches:', error);
      return {
        allMatches: [],
        competitions: [],
        liveCompetitionIds: new Set<string>()
      };
    }
  };

  // Basketball-specific live timer logic
  const getLiveTimer = (match: any) => {
    const serverTime = Date.now() / 1000; // Current time in seconds

    // Basketball uses remainSeconds and getHomeBasketBallMatchTimeText
    const { matchStatus, remainSeconds } = match;
    const { statusText } = getHomeBasketBallMatchTimeText({
      matchStatus,
      remainSeconds,
      serverTime,
      currentGameTab: 1,
    });
    return statusText;
  };

  // Extract and format handicap values and odds data from match odds (Basketball version)
  const getHandicapAndOdds = (match: any) => {
    try {
      // Get the asia odds from matchOdds
      const asiaOdds = match.matchOdds?.find((odds: any) => odds.oddsType === 'asia');
      if (!asiaOdds || !asiaOdds.handicap || !asiaOdds.oddsData) {
        return { 
          homeHandicap: null, 
          awayHandicap: null,
          homeOdds: null,
          awayOdds: null
        };
      }

      const handicap = asiaOdds.handicap; // e.g., "0/0.5" or "0"

      // Handle different handicap formats
      let hdpValue;
      
      if (handicap.includes('/')) {
        // Format: "0/0.5", "0/1.5", etc.
        const parts = handicap.split('/');
        if (parts.length !== 2) {
          return { 
            homeHandicap: null, 
            awayHandicap: null,
            homeOdds: null,
            awayOdds: null
          };
        }
        hdpValue = parseFloat(parts[1]); // 0.5, 1.5, etc.
      } else {
        // Format: "0", "1", "-0.5", etc. (direct handicap value)
        hdpValue = parseFloat(handicap);
      }

      // Format handicap values - display even when hdpValue is 0
      const homeHandicap = hdpValue === 0 ? '0' : (hdpValue > 0 ? `+${hdpValue}` : `${hdpValue}`);
      const awayHandicap = hdpValue === 0 ? '0' : (hdpValue > 0 ? `-${hdpValue}` : `+${Math.abs(hdpValue)}`);

      // Extract odds data[0] and data[2]
      const homeOdds = asiaOdds.oddsData[0] != null ? parseFloat(Number(asiaOdds.oddsData[0]).toFixed(2)) : null;
      const awayOdds = asiaOdds.oddsData[2] != null ? parseFloat(Number(asiaOdds.oddsData[2]).toFixed(2)) : null;
      
      return { homeHandicap, awayHandicap, homeOdds, awayOdds };
    } catch (error) {
      console.error('❌ Error processing basketball handicap and odds:', error);
      return { 
        homeHandicap: null, 
        awayHandicap: null,
        homeOdds: null,
        awayOdds: null
      };
    }
  };

  return (
    <PluginHomeShared 
      fetchMatches={fetchMatches}
      getLiveTimer={getLiveTimer}
      getHandicapAndOdds={getHandicapAndOdds}
      sportName="basketball"
    />
  );
};

export default PluginBasketball;
