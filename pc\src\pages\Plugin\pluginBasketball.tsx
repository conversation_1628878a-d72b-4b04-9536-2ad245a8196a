import './pluginHome.less';
import './pluginShared.less';

import { Button, Carousel, Popover, Select } from 'antd';
import { useCallback, useEffect, useState } from "react";

import { getCompetitionList } from "iscommon/api/basketball/home";
import { getHomeBasketBallMatchTimeText } from 'iscommon/utils/dataUtils';
import { GlobalConfig } from 'iscommon/const/globalConfig';
import moment from 'moment';
import { PlayCircleOutlined } from '@ant-design/icons';
import PopularCompetitions from './pluginPopular';

const PluginBasketball: React.FC = () => {
  const [allMatches, setAllMatches] = useState<any[]>([]);
  const [filteredMatches, setFilteredMatches] = useState<any[]>([]);
  const [competitionList, setCompetitionList] = useState<string[]>([]);
  const [matchType, setMatchType] = useState<'live' | 'upcoming'>('live');
  const [selectedCompetition, setSelectedCompetition] = useState<string>('all');
  const [liveCompetitionIds, setLiveCompetitionIds] = useState<Set<string>>(new Set());

  // Get search params for plugin mode detection
  const searchParams = new URLSearchParams(window.location.search);

  useEffect(() => {
    // Apply plugin-specific styling
    const pluginContainer = document.querySelector('.plugin-container') as HTMLElement;
    if (pluginContainer) {
      pluginContainer.style.width = '1200px';
      pluginContainer.style.margin = '0 auto';
    }
  }, [searchParams]);
  
  const fetchMatches  = useCallback(async () => {
    try {
      // Fetch live basketball matches (listType: 0)
      const { competitions: liveCompetitions } = await getCompetitionList({ listType: 0 });
      console.log('🏀 Basketball Plugin: live competitions', liveCompetitions)

      // Extract live competition IDs for sharing with PopularCompetitions
      const liveCompIds = new Set(liveCompetitions.map((comp: any) => comp.competitionId?.toString()).filter(Boolean)) as Set<string>;
      setLiveCompetitionIds(liveCompIds);

      const liveMatches = liveCompetitions.flatMap((comp: any) =>
        (comp.matches || []).map((match: any) => ({
          ...match,
          competitionName: comp.competitionName,
          type: 'live',
        }))
      );
  
      // Prepare dateFilter (today in ISO string with +08:00)
      const today = new Date();
      const dateFilter = new Date(today.getTime() - today.getTimezoneOffset() * 60000)
        .toISOString()
        .replace('Z', '+08:00');
  
      // Fetch upcoming basketball matches (listType: 2 with dateFilter)
      const { competitions: upcomingCompetitions } = await getCompetitionList({ listType: 2, dateFilter });
      const upcomingMatches = upcomingCompetitions.flatMap((comp: any) =>
        (comp.matches || []).map((match: any) => ({
          ...match,
          competitionName: comp.competitionName,
          type: 'upcoming',
        }))
      );
  
      const allMatches = [...liveMatches, ...upcomingMatches];
      setAllMatches(allMatches);
      setFilteredMatches(allMatches);
  
      // Extract unique competition names for dropdown
      const competitions = Array.from(new Set(allMatches.map(match => match.competitionName)));
      setCompetitionList(competitions);
    } catch (error) {
      console.error('Error fetching basketball matches:', error);
    }
  }, []);

  useEffect(() => {
    fetchMatches();
    const interval = setInterval(fetchMatches, 1000); // Update every 1 second for real-time odds
    return () => clearInterval(interval);
  }, [fetchMatches]);

  // Extract and format handicap values and odds data from match odds
  const getHandicapAndOdds = (match: any) => {
    try {
      // Get the asia odds from matchOdds
      const asiaOdds = match.matchOdds?.find((odds: any) => odds.oddsType === 'asia');
      if (!asiaOdds || !asiaOdds.handicap || !asiaOdds.oddsData) {
        return { 
          homeHandicap: null, 
          awayHandicap: null,
          homeOdds: null,
          awayOdds: null
        };
      }

      const handicap = asiaOdds.handicap; // e.g., "0/0.5" or "0"

      // Handle different handicap formats
      let hdpValue;
      
      if (handicap.includes('/')) {
        // Format: "0/0.5", "0/1.5", etc.
        const parts = handicap.split('/');
        if (parts.length !== 2) {
          return { 
            homeHandicap: null, 
            awayHandicap: null,
            homeOdds: null,
            awayOdds: null
          };
        }
        hdpValue = parseFloat(parts[1]); // 0.5, 1.5, etc.
      } else {
        // Format: "0", "1", "-0.5", etc. (direct handicap value)
        hdpValue = parseFloat(handicap);
      }

      // Format handicap values - display even when hdpValue is 0
      const homeHandicap = hdpValue === 0 ? '0' : (hdpValue > 0 ? `+${hdpValue}` : `${hdpValue}`);
      const awayHandicap = hdpValue === 0 ? '0' : (hdpValue > 0 ? `-${hdpValue}` : `+${Math.abs(hdpValue)}`);

      // Extract odds data[0] and data[2]
      const homeOdds = asiaOdds.oddsData[0] != null ? parseFloat(Number(asiaOdds.oddsData[0]).toFixed(2)) : null;
      const awayOdds = asiaOdds.oddsData[2] != null ? parseFloat(Number(asiaOdds.oddsData[2]).toFixed(2)) : null;
      
      return { homeHandicap, awayHandicap, homeOdds, awayOdds };
    } catch (error) {
      console.error('❌ Error processing basketball handicap and odds:', error);
      return { 
        homeHandicap: null, 
        awayHandicap: null,
        homeOdds: null,
        awayOdds: null
      };
    }
  };

  // Get proper live timer for basketball matches
  const getLiveTimer = (match: any) => {
    const serverTime = Date.now() / 1000; // Current time in seconds

    // Basketball uses remainSeconds and getHomeBasketBallMatchTimeText
    const { matchStatus, remainSeconds } = match;
    const { statusText } = getHomeBasketBallMatchTimeText({
      matchStatus,
      remainSeconds,
      serverTime,
      currentGameTab: 1,
    });
    return statusText;
  };

  // Calculate match counts for button labels
  const getLiveMatchCount = () => {
    return allMatches.filter(m => m.type === 'live').length;
  };

  // Handle watch live click
  const handleWatchLive = async (match: any) => {
    const matchId = match.matchId || match.id
    const pathName = GlobalConfig.pathname;

    const url = `${location.origin}/${pathName}-match/${matchId}/MatchLive?isplugin=true&pluginName=${pathName}LivePlugin&lang=en`;
  
    window.open(url, '_blank');
  };

  function chunkIntoPairs(array: any) {
    const result = [];
    for (let i = 0; i < array.length; i += 2) {
      result.push(array.slice(i, i + 2));
    }
    return result;
  }

  // Filter matches based on type and competition
  useEffect(() => {
    let filtered = allMatches.filter(match => match.type === matchType);
    
    if (selectedCompetition !== 'all') {
      filtered = filtered.filter(match => match.competitionName === selectedCompetition);
    }
    
    setFilteredMatches(filtered);
  }, [allMatches, matchType, selectedCompetition]);

  const matchPairs = chunkIntoPairs(filteredMatches);

  return (
    <div className="plugin-container">
      <div className="plugin-header">
        <div className="filter-bar">
          <div className="filter-left">
            <Button
              className={matchType === 'live' ? 'active' : ''}
              style={{
                backgroundColor: matchType === 'live' ? 'red' : '#2c2c2c',
                borderColor: matchType === 'live' ? 'red' : '#2c2c2c',
                color: '#fff'
              }}
              onClick={() => setMatchType('live')}
            >
              Live{getLiveMatchCount() > 0 ? ` (${getLiveMatchCount()})` : ''}
            </Button>
            <Button
              className={matchType === 'upcoming' ? 'active' : ''}
              style={{
                backgroundColor: matchType === 'upcoming' ? 'red' : '#2c2c2c',
                borderColor: matchType === 'upcoming' ? 'red' : '#2c2c2c',
                color: '#fff'
              }}
              onClick={() => setMatchType('upcoming')}
            >
              Upcoming
            </Button>
          </div>
          <div className="filter-right">
            <Select
              value={selectedCompetition}
              onChange={setSelectedCompetition}
              style={{ width: 200 }}
              className="competition-select"
            >
              <Select.Option value="all">All Competitions</Select.Option>
              {competitionList.map(comp => (
                <Select.Option key={comp} value={comp}>{comp}</Select.Option>
              ))}
            </Select>
          </div>
        </div>
        
        <PopularCompetitions 
          liveCompetitionIds={liveCompetitionIds}
          onCompetitionSelect={setSelectedCompetition}
          selectedCompetition={selectedCompetition}
        />
      </div>

      <div className="plugin-content">
        {matchPairs.length === 0 ? (
          <div className="no-matches">No matches available</div>
        ) : (
          <Carousel 
            dots={true} 
            infinite={false}
            slidesToShow={1}
            slidesToScroll={1}
          >
            {matchPairs.map((pair, pairIndex) => (
              <div key={pairIndex} className="carousel-slide">
                <div className="match-container">
                  {pair.map((match: any, matchIndex: number) => (
                    <div key={match.matchId || matchIndex} className="match-item">
                      <div className="match-header">
                        <span className="competition-name">{match.competitionName}</span>
                        <Popover content={match.vlive === 1 ? 'Watch Live' : 'Watch Animation'} placement="top">
                          <Button
                            type="text"
                            icon={<PlayCircleOutlined />}
                            className="action-btn"
                            onClick={() => handleWatchLive(match)}
                          />
                        </Popover>
                      </div>
                      <div className="match-body">
                        <div className="match-info">
                          <div className="match-time">
                            {match.type === 'live' ? getLiveTimer(match) : moment.unix(match.matchTime).format('HH:mm')}
                          </div>
                        </div>
                        <div className="teams-section">
                          <div className="team-row">
                            <img 
                              src={match.homeTeam?.logo} 
                              alt={match.homeTeam?.name}
                              className="team-icon"
                            />
                            <span className="team-name">{match.homeTeam?.name}</span>
                          </div>
                          <div className="team-row">
                            <img 
                              src={match.awayTeam?.logo} 
                              alt={match.awayTeam?.name}
                              className="team-icon"
                            />
                            <span className="team-name">{match.awayTeam?.name}</span>
                          </div>
                        </div>
                        <div className='match-score-section'>
                          <span className="match-score">{match.calculatedHomeScore}</span> 
                          <span className='match-score'>{match.calculatedAwayScore}</span>
                        </div>
                        {(() => {
                          const { homeHandicap, awayHandicap, homeOdds, awayOdds } = getHandicapAndOdds(match);
                          // Display betting section if we have handicap data (including '0') or odds data
                          if (homeHandicap !== null && awayHandicap !== null) {
                            return (
                              <div className='betting-section'>
                                <div className='handicap-section'>
                                  <span className="handicap-value home-handicap">{homeHandicap}</span>
                                  <span className="handicap-value away-handicap">{awayHandicap}</span>
                                </div>
                                {homeOdds !== null && awayOdds !== null && (
                                  <div className='odds-section'>
                                    <span className="odds-value home-odds">{homeOdds}</span>
                                    <span className="odds-value away-odds">{awayOdds}</span>
                                  </div>
                                )}
                              </div>
                            );
                          }
                          return null;
                        })()}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </Carousel>
        )}
      </div>
    </div>
  )
}

export default PluginBasketball;
