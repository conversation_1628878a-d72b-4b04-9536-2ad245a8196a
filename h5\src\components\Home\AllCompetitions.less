// .title {
//   padding: 16px 20px;
//   font-size: 24px;
  
//   font-weight: 500;
//   color: #999999;
// }
// .item {
//   background: #fefffe;
//   padding: 22px 20px;
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
//   .left {
//     .logo {
//       width: 28px;
//       height: 28px;
//       border-radius: 14px;
//     }
//     .text {
//       font-weight: 500;
//       color: #333;
//       font-size: 24px;
//       margin-left: 12px;
//     }
//   }
//   .iconfont {
//     color: #d0d0d0;
//   }
//   border-bottom: 1px solid #e3e3e3;
// }
// p {
//   margin-bottom: 0;
// }

// .inner-list {
  // padding-left: 60px !important;
  // background-color: #fff !important;
  // .inner-item {
  //   padding: 22px 0;
  //   display: flex;
  //   align-items: center;
  //   border-bottom: 1px solid #eee;
  //   .inner-logo {
  //     width: 28px;
  //     height: 28px;
  //     border-radius: 14px;
  //   }
  //   .inner-name {
  //     margin-left: 12px;
  //     color: #333;
  //     font-weight: 400;
  //     font-size: 24px;
  //   }
  // }
// }

.common-all-competition-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: #1E1E1E;
  border-radius: 24px;
  margin: 16px 0px;

  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 20px 10px 20px;
    align-items: center;
    color: #fff;
  }

  .container-body {
    padding: 24px;

    .all-competition-item {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 12px 0px;

      .all-competition-logo {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 10px;
        overflow: hidden;
        background-size: cover;
      }

      .all-competition-name {
        font-size: 24px;
        font-weight: 600;
        color: #fff;
      }
    }

    .competition-item-list {
      padding-left: 40px;

      .competition-item-inner-list {
        padding: 12px 0;
        display: flex;
        align-items: center;
        border-bottom: 1px solid #eee;
        border-width: 80%;

        .inner-list-logo {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin-right: 10px;
          overflow: hidden;
          background-size: cover;
        }
        
        .inner-list-name {
          font-size: 24px;
          color: #fff;
        }
      }
    }
  }
}
