
import { TechnicalStatistics } from 'iscommon/const/constant';
import { translate } from 'iscommon/i18n/utils';
import { inject, observer } from 'mobx-react';
import React, { useCallback } from 'react';
// import LiveTitle, { LiveTeamTitle } from './LiveTitle';
// import styles from './MatchStatistics.less';
import './MatchStatistics.less'
import { FallbackImage } from 'iscommon/const/icon';

interface StatsItem {
  type: string;
  home: number;
  away: number;
}

const StatsItem = React.memo(({ item }: { item: StatsItem }) => {
  const renderProgress = useCallback((fleft = true, progress = 50) => {
    return (
      <div className="progress">
        <div className={`current-progress ${fleft ? 'fleft' : 'fright'}`} style={{ width: `${progress}%` }} />
      </div>
    );
  }, []);

  return (
    <div className="stat-item-container">
      <div className="stat-item">
        <span className='stat-name'>{item.home}</span>
        <span className='stat-name'>{translate((TechnicalStatistics as any)[item.type].in18Name)}</span>
        <span className='stat-name'>{item.away}</span>
      </div>
      <div className="progress-container">
        {renderProgress(false, item.home !== 0 ? (item.home * 100) / (item.home + item.away) : 0)}
        {renderProgress(true, item.away !== 0 ? (item.away * 100) / (item.home + item.away) : 0)}
      </div>
    </div>
  );
});

const MatchStatistics = inject('store')(
  observer((props: any) => {
    const {
      store: { Match },
    } = props;
    const { statsList, matchHeaderInfo } = Match;

    if (statsList.length === 0) return null;

    return (
      <div className='overview-match-statistics-container'>
        <div className='container-title'>{translate('Statistics')}</div>
        <div className='container-body'>
          <div className='team-container'>
            <div className='home-team-container'>
              <img className='team-logo' src={matchHeaderInfo.homeTeam?.logo || FallbackImage} alt={matchHeaderInfo.homeTeam?.name}/>
              <span className='team-name'>{matchHeaderInfo.homeTeam?.name}</span>
            </div>
            <div className='away-team-container'>
              <span className='team-name'>{matchHeaderInfo.awayTeam?.name}</span>
              <img className='team-logo' src={matchHeaderInfo.awayTeam?.logo || FallbackImage} alt={matchHeaderInfo.awayTeam?.name}/>
            </div>
          </div>
          {statsList.map((item: any) => (
            <StatsItem item={item} key={item.type} />
          ))}
        </div>
      </div>
      // <div id="matchStatPluginCard" className={styles.rightContent}>
      //   {/* <LiveTitle title={statText} />
      //   <LiveTeamTitle /> */}
        // {statsList.map((item: any) => (
        //   <StatsItem item={item} key={item.type} />
        // ))}
      // </div>
    );
  }),
);

export default MatchStatistics;