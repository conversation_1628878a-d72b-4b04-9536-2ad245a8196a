{"All": "ყველა", "Live": "ლაივი", "LiveH5": "ლაივი", "MatchLive": "ლაივი", "TimeSort": "დროის მიხედვით დალაგება", "SortByTime": "დროის მიხედვით დალაგება", "AllGames": "თამაშებები", "Leagues": "ლიგები", "h5_Leagues": "ლიგები", "Today": "დღეს", "Cancel": "გაუქმება", "Popular": "პოპულარული", "Settings": "გარემოცვა", "Language": "ენა", "Overview": "მიმოხილვა", "LiveOverview": "მიმოხილვა", "Standings": "ხანგძლიობა", "Stats": "სტატისტიკა", "Transfer": "ტრანსფერი", "Champions": "ჩემპიონები", "TeamChampions": "ჩემპიონები", "teamChampions": "ჩემპიონები", "Football": "ფერხბურთი", "Basketball": "კალათბურთი", "Baseball": "ბეისბოლი", "Icehockey": "ჰოკეი", "Tennis": "ჩოგნურთი", "Volleyball": "ფრენბურთი", "Esports": "ესპორტი", "Handball": "HANDBALL", "Cricket": "კრიკეტი", "WaterPolo": "Წყალბურთი", "TableTennis": "მაგიდის ჩოგბურთი", "Snooker": "SNOOKER", "Badminton": "ბადმინტონი", "BusinessCooperation": "Business Cooperation", "TermsOfService": "მომსახურების პირორები", "PrivacyPolicy": "კონფიდენციალურობის პოლიტიკა", "Players": "მოთამაშეები", "ForeignPlayers": "უცხო ქვეყნის მოთამაშეები", "NumberOfTeams": "გუნდების რაოდენობა", "YellowCards": "ყვითელი ბარათები", "RedCards": "წითელი ბარათები", "Capacity": "ტევადობა", "City": "ქალაქი", "Info": "ინფორმაცია", "Matches": "მატჩები", "Team": "გუნდი", "Teams": "გუნდები", "Goals": "გოლები", "Assists": "წარმატებული გადაწოდება", "assists": "წარმატებული გადაწოდება", "Home": "საკუთარ მოედანზე, სახლში", "Away": "გასვლითი", "topScorers": "საუკეთესო მომბარდირები", "TopScorers": "საუკეთესო მომბარდირები", "homeTopScorers": "საუკეთესო მომბარდირები", "season": "სეზონი", "Season": "სეზონი", "ShotsOnTarget": "დარტყმები სამიზნეზე", "Clearances": "ბურთით წინ გაჭრა", "Tackles": "სხვა მოთამაშის დაგდება", "keyPasses": "შედეგიანი მოქმედება", "KeyPasses": "შედეგიანი მოქმედება", "Fouls": "წესის დარღვევა", "totalFouls": "წესის დარღვევა", "WasFouled": "წესის დარღვია", "Penalty": "ჯარიმა", "MinutesPlayed": "ნათამაშები წუთები", "BasketballMinutesPlayed": "ნათამაშები წუთები", "Interceptions": "გადაწოდების ჩაჭრა", "Steals": "ბურთის ჩაჭრა", "steals": "ბურთის ჩაჭრა", "Passes": "Გამსვლელი", "Saves": "ბურთის აღება", "BlockedShots": "დარტყმის ბლოკირება", "Signed": "ავტოგრაფიანი", "league": "", "offensiveData": "თავდასხმის მონაცემები", "defenseData": "თავდაცვის მონაცემები", "otherData": "სხვა მონაცემები", "ballPossession": "ბურთის ფლობა", "shotsPerGame": "დარტმები თამაშისას", "ShotsPerGame": "დარტმები თამაშისას", "keyPassesPerGame": "შედეგიანი გადაცემები თამაშისას", "accurateLongBallsPerGame": "ზუსტი გრძელი ბურთები თამაშისას", "accurateCrossesPerGame": "ზუსტი გასვლები თამაშისას", "tacklesPerGame": "სხვა მოთამაშის დაგდება თამაშის დროს", "TacklesPerGame": "სხვა მოთამაშის დაგდება თამაშის დროს", "interceptionsPerGame": "ბურთის წართმევა თამაშისას", "InterceptionsPerGame": "ბურთის წართმევა თამაშისას", "clearancesPerGame": "ბურთით წინ გაჭრები თამაშისას", "ClearancesPerGame": "ბურთით წინ გაჭრები თამაშისას", "blockedShotsPerGame": "დარტყმის ბლოკირება თამაშისას", "turnoversPerGame": "ბურთის დაკარგვა თამაშისას", "foulsPerGame": "წესის დარღვევები თამაშისას", "scoringFrequencyFiveGoals": "", "Coach": "მწვრთნელი", "Goalkeeper": "მეკარე", "Stadium": "სტადიონი", "Login": "შესვლა", "Corner": "კუთხე", "ShotsOffTarget": "დარტყმები სამიზნიდან", "H2H": "H2H", "Date": "თარიღი", "OwnGoal": "გოლის გატანა საკუტარ კარში", "PenaltyMissed": "პანალტის აცდენა", "SecondYellow": "მეორე ყვითელი ბარათი", "Odds": "ანგარიში", "attacks": "შეტევები", "Started": "დაწყებული", "Chat": "ჩატი", "Strengths": "უპირატესობა", "Weaknesses": "სისუსტე", "Group": "ჯგუფი", "Birthday": "დაბადების დღე", "Club": "კლუბი", "MainPosition": "ძირითადი პოზიცია", "PassesAccuracy": "", "Preseason": "", "RegularSeason": "", "PointsPerGame": "ქულები თამაშში", "Glossary": "ტერმინები", "h5Glossary": "ტერმინები", "Career": "კარიერა", "Bench": "სათადარიგო მოთამაშეთა სკამი", "ReboundsPerGame": "ბურთის კალათში ჩაგდება თამაშისას", "AssistsPerGame": "წარმატებული გადაწოდება თამაშისას", "OddsFormat": "ალბათობების ფორმატი", "Squad": "შემადგენლობა", "TotalMarketValue": "მთლიანი საბაზრო ღირებულება", "Rounds": "რაუნდი, ეტაპი", "LowerDivision": "ქვედა დონე", "TeamStats": "გუნდის სტატისტიკა", "GoalsPk": "გოლები(PK)", "Crosses": "კროსი", "CrossesAccuracy": "Გაივლის ბურთი წარმატებით", "Dribble": "Საგანგებო", "DribbleSucc": "Საგანგებო წარმატება", "LongBalls": "გრძელი ბურთები", "LongBallsAccuracy": "Ხანგრძლივი მიღება წარმატების მაჩვენებელი", "Duels": "წესების დარღვევა", "DuelsWon": "Სუფთა ფურცელი", "Dispossessed": "ბურთის აღება", "Punches": "Მეკარე თავდასხმის წარმატება", "RunsOut": "კარიდან გამოსვ;ა", "RunsOutSucc": "დაბლოკილი დარტყმები", "GoodHighClaim": "Გავლის სიზუსტით", "Loan": "დაქირავება", "EndOfLoan": "დაქირავების დასრულება", "Unknown": "უცნობი", "AverageAge": "საშუალო ასაკი", "cornersPerGame": "კუთხური ჩაწოდება თამაშისას", "goalsConceded": "გასული გოლები", "Defender": "მცველი", "Discipline": "დისციპლინა", "Pass": "", "FB_Login": "Facebook-ით გაგრძელება", "Google_Login": "Google-ით გაგრძელება", "Substitutes": "სათადარიგო მოთამაშეები", "PenaltyKick": "", "ShareYourViews": "თქვენი შეხედულებების გაზიარება", "Nodata": "მონაცემები არ არის", "Foot": "ფეხი", "dangerousAttack": "", "venue": "შეჯიბრების ჩატარების ადგილი", "playerStatistics": "მოთამაშის სტატისტიკა", "TotalPlayed": "სულ ნათამაშები", "MinutesPerGame": "წუთები თამაშისას", "GoalsFrequency": "", "GoalsPerGame": "Საველე მიზნების", "Arrivals": "ჩამოსვლები", "Departures": "გამგზავრებები", "LeftFoot": "მარცხენა", "RightFoot": "მარჯვენა", "LatestTransfers": "უკანასკნელი ტრანსფერები", "DraftInfo": "ინფორმაცია დრაფტზე", "OK": "კარგი", "AsianHandicap": "", "TotalGoals": "", "AmFootballTotalGames": "", "TotalCorners": "", "OverBall": "მეტი", "Over": "მეტი", "h5Over": "მეტი", "UnderBall": "Under", "Under": "Under", "h5Under": "Under", "OtherLeagues": "სხვა ლიგები [A-Z]", "GoalPopup": "", "FullStandings": "სრული რეიტინგი", "teamWeek": "კვირის საუკეთესო გუნდი", "weekTop": "კვირის საუკეთესო გუნდი", "TeamOfTheWeek": "კვირის საუკეთესო გუნდი", "round": "რაუნდი, ეტაპი", "Released": "ერთი ერთზე გასული", "Retirement": "", "Draft": "მოზიდვა", "TransferIn": "", "TransferOut": "", "MarketValue": "საბაზრო ღირებულება", "Salary": "ხელფასი", "Next": "შემდეგი", "Position": "პოზიცია", "CTR": "ბოლო ტრანსფერის რეკორდი", "FoulBall": "", "FreeKick": "", "GoalKick": "", "Midfield": "", "HalftimeScore": "", "InjuryTime": "", "OvertimeIsOver": "", "PenaltyKickEnded": "", "Last": "ბოლო", "Win": "მოგებები", "Draw": "ფრეები", "Lose": "წაგებები", "Lineup": "", "Substitution": "მოთამაშის შეცვლა", "Offsides": "თამაშგარე მგომარეობა", "Playoffs": "", "Qualifier": "", "GroupStage": "", "Rebounds": "ბურთის კალათში ჩაგდება", "rebounds": "ბურთის კალათში ჩაგდება", "OffensiveRebounds": "შეტევის შერჩევა", "offensiveRebounds": "შეტევის შერჩევა", "DefensiveRebounds": "დაცვის შერჩევა", "defensiveRebounds": "დაცვის შერჩევა", "Turnovers": "ბურთის დაკარგვა", "turnovers": "ბურთის დაკარგვა", "Blocks": "ბლოკირება", "blocks": "ბლოკირება", "BoxScore": "შედეგების შემაჯამებელი ცხრილი", "Foul": "წესების დარღვევა ერთი მოთამაშის მიერ", "FreeThrows": "საჯარიმო სროლა", "freeThrowsScored": "საჯარიმო სროლა", "fieldGoalsScored": "", "fieldGoalsTotal": "", "threePointersScored": "", "threePointersTotal": "", "freeThrowsTotal": "", "Finished": "Დასრულდა", "Scheduled": "Კალენდარი", "Favourite": "", "OddsMarkets": "Odds markets", "Search": "", "Timezone": "", "SoccerGoalReminderSettings": "", "RemindersOnlyForRacesToWatch": "", "GoalSound": "", "LiveScore": "", "Schedule": "Გრაფიკი", "Rugby": "", "FooterContentFootball": "IGScore Football LiveScore გთავაზობთ შეუდარებელ საფეხბურთო ცოცხალ ანგარიშებს და ფეხბურთის შედეგებს 2600– ზე მეტი საფეხბურთო ლიგადან, თასებიდან და ტურნირებიდან. მიიღეთ ცოცხალი ანგარიშები, ტაიმის ნახევარ განაკვეთზე და სრული დროით ჩატარებული ფეხბურთის შედეგები, გოლის გამტანები და ასისტენტები, ბარათები, ჩანაცვლებითი მატჩები, მატჩების სტატისტიკა და ცოცხალი ნაკადი პრემიერ ლიგაში, ლა ლიგა, სერია A, ბუნდესლიგა, ლიგა 1, ერედივიზია, რუსეთის პრემიერ ლიგა, ბრაზილეირაო, MLS სუპერ ლიგა და ჩემპიონატი igscore.net– ზე. IGScore გთავაზობთ ფეხბურთის ყველა გულშემატკივარს ცოცხალ ანგარიშებს, ფეხბურთის პირდაპირ ეთერში, ფეხბურთის ქულებს, ლიგის ცხრილებსა და ლიგებს, თასებსა და ტურნირებს და არა მხოლოდ ყველაზე პოპულარულ საფეხბურთო ლიგებს, როგორიცაა ინგლისის პრემიერ ლიგა, ესპანეთის ლა ლიგა, იტალია სერია A, გერმანია ბუნდესლიგა, საფრანგეთის ლიგა 1, მაგრამ ასევე მთელი მსოფლიოს საფეხბურთო ქვეყნების დიდი სპექტრიდან, მათ შორის ჩრდილოეთიდან და სამხრეთ ამერიკიდან, აზიიდან და აფრიკიდან. ჩვენი საფეხბურთო ცოცხალი ანგარიშის ქულების განახლება ხდება რეალურ დროში რეალურ დროში, რათა შეგახსენოთ საფეხბურთო მატჩის ყველა ცოცხალი ანგარიშის განახლება, რომელიც დღეს ხდება, ფეხბურთის ლაივქორის შედეგების ყველა დასრულებული საფეხბურთო მატჩის ყველა საფეხბურთო და საფეხბურთო ლიგისთვის. მატჩის გვერდზე, ჩვენი ფეხბურთის ქულები საშუალებას გაძლევთ ნახოთ წარსულის თამაშის შედეგები ყველა ადრე ჩატარებული სეზონისთვის ყველა საფეხბურთო შეჯიბრისთვის. მიიღეთ ყველა თქვენი ფეხბურთის პირდაპირი შედეგი igscore.net– ზე!", "FooterContentBasketball": "IGScore Basketball LiveScore გთავაზობთ NBA ლიგის ცოცხალ ქულებს, შედეგებს, ცხრილებს, სტატისტიკას, მატჩებს, პოზიციონირებას და წინა შედეგებს კვარტალში, პირველი ტაიმის ან საბოლოო შედეგის მიხედვით. IGScore გთავაზობთ ქულებს მომსახურებას მსოფლიოს 200-ზე მეტი კალათბურთის კონკურსისგან (მაგალითად, NCAA, ABA League, ბალტიის ლიგა, ევროლიგა, ეროვნული საკალათბურთო ლიგა). აქ ნახავთ არა მხოლოდ ცოცხალ ქულებს, მეოთხედის შედეგებს, საბოლოო შედეგებსა და შემადგენლობაში, არამედ 2- და 3 – ქულიანი მცდელობების რაოდენობას, უფასო დარტყმებს, სროლის პროცენტს, მოხსნას, ბრუნვას, იპარავენ, პირად შეცდომას, მატჩის ისტორიას და მოთამაშეთა სტატისტიკას. .IGScore– ის კალათბურთის ცოცხალ პლანზე ნახავთ კალათბურთს ინტერნეტით, უბრალოდ დააჭირეთ მას, და მოგაწვდით ტოპ – ლიგების მატჩების ონლაინ გაშუქებას. მატჩების გვერდი ასევე იქნება ცხრილი კალათბურთის სტატისტიკის მიხედვით გუნდების უახლესი თამაშების შესახებ. ჩვენი კალათბურთის ქულათა სიები განახლებულია რეალურ დროში, რათა გააგრძელოთ ინფორმაცია კალათბურთის ყველა შედეგის შესახებ, რომელიც დღეს ხდება და საშუალებას გაძლევთ ნახოთ წარსული თამაშის შედეგები ყველა ადრე ჩატარებული მატჩისთვის ყველა კალათბურთის შეჯიბრებებისთვის. მიიღეთ ყველა თქვენი NBA– ის ცოცხალი შედეგი igscore.net– ზე! მიჰყევით NBA– ს საარსებო წყაროს, NBA– ს თამაშებს, NBA– ს პოზიციებს და გუნდურ გვერდებს!", "FooterContentAmFootball": "IGScore american football live score გთავაზობთ ყველა შედეგს და ცოცხალ ქულას მსოფლიოში ყველაზე დიდი და ყველაზე პოპულარული ამერიკული საფეხბურთო ლიგისგან - NFL და როდესაც რეგულარული NFL სეზონი დასრულდა, დაიცვას ცოცხალი ქულები NFL Playoffs და Superbowl. გარდა ამისა, NFL ჩვენ ასევე მოგაწვდით livescores, შედეგები, პოზიციები და გრაფიკები NCAA კოლეჯის ამერიკული ფეხბურთი და კანადის CFL.", "FooterContentBaseball": "IGScore baseball live score გთავაზობთ ცოცხალ ქულებს, შედეგებს და რეიტინგებს მსოფლიოში ყველაზე პოპულარული ბეისბოლის ლიგიდან - USSSA Baseball, NCAA Baseball, MLB Baseball, MLB Allstar თამაში. ჩვენ ასევე ვაძლევთ ცოცხალ ანგარიშს იაპონიის პროფესიონალური ლიგის, მექსიკის ლიგის, გერმანიის 1. ბუნდესლიგის, NCAA ასევე ბეისბოლის საერთაშორისო ტურნირის მსოფლიო ბეისბოლის კლასიკაში. თქვენ ასევე შეგიძლიათ ნებისმიერ დროს ნახოთ ბეისბოლის ლიგის ცხრილები, გასული თამაშები შედეგების მიხედვით და ბეისბოლის მომავალი მატჩების განრიგი IGScore baseball live score - ზე.", "FooterContentIcehockey": "IGScore ice hockey live score გთავაზობთ რეალურ დროში ყინულის ჰოკეის შედეგების შედეგებს ყინულის ჰოკეის ლიგებისთვის, თასებისა და ტურნირებისთვის. IGScore ice hockey live score მოგაწვდით ჰოკეის ლაივსქორს, ცხრილებს, სტატისტიკას, თამაშებს, შედეგებს და ქულებს NHL, SHL, KHL და ჩვენ ასევე გთავაზობთ ფინეთის ჰოკეის ეროვნულ ლიგებს, შვედეთის ჰოკეის ლიგებს, სლოვაკეთის ჰოკეის ლიგებს, ჩეხეთის ჰოკეის ლიგებს, ლიგის ცხრილებს, გოლის გამტანებს, მესამედ და ყინულის ჰოკეის საბოლოო შედეგები პირდაპირ ეთერში. ყინულის ჰოკეის რეგულარული სეზონის დასრულების შემდეგ ჩვენ შემოგთავაზებთ ჰოკეის ცოცხალ ანგარიშებს, ცხრილებს და შედეგებს ყინულის ჰოკეის საუკეთესო მოვლენების შესახებ- IIHF მსოფლიო ჩემპიონატის სტენლის თასი და ასევე ჰოკეის ქულები ზამთრის ოლიმპიური ტურნირიდან. IGScore ice hockey live score - ზე ასევე შეგიძლიათ იპოვოთ უფასო ჰოკეის პირდაპირი ნაკადი NHL, SHL და სხვებისთვის.", "FooterContentTennis": "IGScore tennis live score გთავაზობთ ცოცხალ ანგარიშებს, შედეგებს, ATP რეიტინგს და WTA- ს რეიტინგს, თამაშებს და სტატისტიკას ჩოგბურთის ყველა უდიდესი ტურნირისგან, როგორიცაა დევისი და ფედ თასი, ფრანგული ღია ჩოგბურთი, ან ყველა დიდი სლემის ტურნირი - ავსტრალიის ღია ჩოგბურთი, აშშ ღია ჩოგბურთი, როლანდი გაროსი და უიმბლდონი ორივე ქალი და მამაკაცი მარტოხელა და ორადგილიანი. ასევე ნებისმიერი ჩოგბურთელისთვის შეგიძლიათ დეტალურად ნახოთ მისი ჩატარებული მატჩები ინდივიდუალურად და მათი შედეგები სეტის მიხედვით და რომელ ტურნირზე ჩატარდა ეს მატჩი. IGScore tennis live score მოგაწვდით შედეგებს, სტატისტიკას, ცოცხალ ანგარიშებს და პირდაპირ ნაკადს ორ მოთამაშეს შორის, რომლებიც თამაშობენ მატჩს.", "FooterContentVolleyball": "IGScore volleyball live score გთავაზობთ გაშუქებას ფრენბურთის ყველა მნიშვნელოვანი ქალისა და მამაკაცის ეროვნული ლიგებიდან, მათ შორის იტალიის სერია A1 და იტალიის სერია A1 ქალები, რუსეთის სუპერლიგა, პოლონეთის პლიუსლიგა, თურქეთი 1. ლიგა და მრავალი სხვა. ფრენბურთის ეროვნული ლიგების გარდა, ჩვენ ასევე მოგაწვდით ლაიქორქის ინფორმაციას ფრენბურთის ძირითადი საერთაშორისო ტურნირებიდან, როგორიცაა FIVB მსოფლიო ჩემპიონატი და ევროპის ჩემპიონატი, ასევე ფრენბურთის ლაივსქორის შედეგები ოლიმპიურ თამაშებზე. ასევე შეგიძლიათ შეამოწმოთ ძველი ფრენბურთის გუნდის ძველი შედეგები, ნახოთ ფრენბურთის მომავალი განრიგი და შეამოწმოთ ლიგის ფრენბურთის ცხრილები IGScore volleyball live score - ზე.", "FooterContentEsports": "Esports Live ქულა მომსახურების IGScore ცოცხალი ქულა გთავაზობთ eSports Live ქულა, გრაფიკები, შედეგები და მაგიდები. დაიცავით თქვენი საყვარელი გუნდები აქ ცხოვრობენ! Esports Live ანგარიში igscore.net ცოცხალი ანგარიში ავტომატურად განახლდება და თქვენ არ უნდა განახლების ხელით. დაამატოთ თამაშები გსურთ დაიცვას \"ჩემი თამაშები\" შემდეგ თქვენი მატჩების Livescores, შედეგები და სტატისტიკა კიდევ უფრო მარტივია.", "FooterContentHandball": "IGScore handball live score მოგაწვდით ხელბურთის ცოცხალ ანგარიშებს და ცოცხალ შედეგებს ყველაზე პოპულარული ხელბურთის ლიგებიდან, როგორიცაა გერმანული ბუნდესლიგა, ესპანეთი ლიგა ასობალი, დანიის მამაკაცთა Handboldligaen და France D1. ჩვენ ასევე გთავაზობთ ლაივსქორს, შედეგებს, სტატისტიკას, ცხრილებს, ცხრილებს და თამაშებს ისეთი მნიშვნელოვანი თასებიდან, როგორიცაა ევროპის ხელბურთის ჩემპიონთა ლიგა, SEHA ლიგა და EHF ხელბურთის თასი. IGScore handball live score - ზე შეგიძლიათ ნახოთ ცოცხალი ანგარიშები და უფასო პირდაპირი ტრანსლაცია ხელბურთის საერთაშორისო გუნდების ტურნირებისთვის, როგორიცაა ევროპის ჩემპიონატი და მსოფლიო ჩემპიონატი, როგორც ქალებისთვის, ასევე მამაკაცებისთვის. ნებისმიერ დროს შეგიძლიათ შეამოწმოთ ხელბურთის შედეგები და თქვენი გუნდის მიერ ჩატარებული ბოლო 10 თამაშების სტატისტიკა და ასევე თავით ანგარიშით იმ გუნდებს შორის, რომლებიც თამაშობენ სტატისტიკით.", "FooterContentCricket": "IGScore cricket live score გაძლევს საშუალებას მიყვე რეალურ დროში კრიკეტის შედეგებს, კრიკეტის რეიტინგს და კრიკეტს. ეს ყველაფერი ხელმისაწვდომია ყველაზე პოპულარული ლიგებისთვის და თასებისთვის: ინდოეთის პრემიერ ლიგა, ჩემპიონთა ლიგა Twenty20, დიდი ბაშ ლიგა, კარიბის ზღვის პრემიერ ლიგა, Friends Life T20 და ICC კრიკეტის მსოფლიო ჩემპიონატი. კრიკეტის ყველა ქულა IGScore– ზე ავტომატურად განახლდება და არ არის საჭირო მისი ხელით განახლება. ამ ყველაფერთან ერთად, არსებობს შესაძლებლობა უყუროთ კრიკეტის პირდაპირ სტრიმინგს და შეამოწმოთ უახლესი შანსები მსოფლიოს ყველაზე საინტერესო კრიკეტის მატჩების საბოლოო შედეგისთვის.", "FooterContentWaterPolo": "IGScore water polo live score გთავაზობთ წყალბურთის ცოცხალ ანგარიშებს და შედეგებს იტალიის სერია A1- დან, უნგრეთის OB1- დან, ჩემპიონებისა და ადრიატიკის ლიგის კლუბების დონეზე, ხოლო საერთაშორისო დონეზე, IGScore water polo live score გთავაზობთ მთავარ ტურნირებს, როგორიცაა წყალბურთის მსოფლიო ჩემპიონატი და წყალბურთის ევროპის ჩემპიონატი რა ჩვენ მოგაწვდით მიზნის მიხედვით ლაივსქორს და უფასო პირდაპირ სტრიმინგს.", "FooterContentTableTennis": "IGScore table tennis live score მოგაწვდით ლაიქ ბორებს, მაგიდას, შედეგებს, მაგიდის ჩოგბურთის რეიტინგს, მაგიდის ჩოგბურთის ყველა უდიდესი ტურნირის თამაშებს და სტატისტიკას, როგორიცაა რუსული მაგიდის ჩოგბურთი, მაგიდის ჩოგბურთის ოლიმპიადა. ასევე ნებისმიერი მაგიდის ჩოგბურთელისთვის შეგიძლიათ დეტალურად ნახოთ მისი ჩატარებული მატჩები ინდივიდუალურად და მათი შედეგები სეტის მიხედვით და რომელ ტურნირზე ჩატარდა ეს მატჩი. IGScore table tennis live score გაწვდით პირდაპირ შედეგებს, სტატისტიკას, ცოცხალ ანგარიშებს და პირდაპირ ნაკადს ორ მოთამაშეს შორის, რომლებიც თამაშობენ მატჩს.", "FooterContentSnooker": "IGScore snooker live score გაძლევთ შესაძლებლობას თვალყური ადევნოთ სნუკერის ყველა ტურნირის ცოცხალ ანგარიშს, შედეგებს და ცხრილებს. ჩვენ ასევე გთავაზობთ რეიტინგებს დიდი ბრიტანეთიდან და მსოფლიო ჩემპიონატიდან, ასევე სნუკერის ლაივსქორს, სნუკერის თამაშებს და სნუკერის საბოლოო შედეგებს საერთაშორისო ტურნირებიდან, როგორიცაა მსოფლიო სნუკერის ტური. ნებისმიერ დროს შეგიძლიათ ნახოთ სნუკერის ტურნირების განრიგი, დაწყებული სნუკერის ტურნირების შედეგები და ბოლო 10 თამაში თითოეული მოთამაშისთვის. გარდა ამისა, თქვენ შეგიძლიათ შეამოწმოთ მოთამაშეებს შორის გასული მატჩები. IGScore snooker live score –ზე შეგიძლიათ იხილოთ მატჩების სია, რომლებიც დაფარულია სნუკერის უფასო პირდაპირი სტრიმინგით.", "FooterContentBadminton": "IGScore badminton live score გთავაზობთ ბადმინტონის ცოცხალ შედეგებს, ცხრილებს, თამაშებს და სტატისტიკას საერთაშორისო ტურნირებიდან, როგორიცაა მსოფლიო ჩემპიონატი, BWF სუპერ სერია და ოლიმპიური თამაშების ბადმინტონის შედეგები. ასევე შეგიძლიათ შეამოწმოთ ბადმინტონის მიერ ჩატარებული თამაშების შედეგები, ნახოთ ბადმინტონის თამაშის განრიგი და ინდივიდუალურად შეამოწმოთ ბადმინტონის შედეგები მოთამაშეებისგან IGScore badminton live score.", "ContactUs": "Დაგვიკავშირდით", "UpdateLineups": "Update Lineups", "FreeLiveScoresWidget": "Free Livescores Widget", "PlayerSalary": "", "DivisionLevel": "Ღონისძიება დონე", "Foreigners": "Უცხოელი მოთამაშე", "LeagueInfo": "Ტურნირები", "TeamInfo": "Team ინფორმაცია", "Venues": "", "MostValuablePlayer": "", "UpperDivision": "ზედა დონე", "NewcomersFromUpperDivision": "", "NewcomersFromLowerDivision": "", "TitleHolder": "ტიტულის მფლობელები", "MostTitle": "Up გაიმარჯვებს ნომერი", "Pts": "ქულები გუნდის მიხედვით", "FullStats": "", "Relegation": "", "Result": "შედეგი", "Score": "ქულა", "PlayerStats": "", "fixtures": "", "topPlayers": "Ძირითადი მოთამაშე", "Shots": "Სროლა", "SelectTeam": "", "SelectBasketBallTeam": "", "SelectAll": "ყველას მონიშვნა", "SquadSize": "Პუნქტების მოთამაშე", "ViewAll": "ყველას ნახვა", "penaltiesWon": "საჯარიმო დარტყმის მოგება", "accuracyCrosses": "", "totalShorts": "", "accuracyLongBalls": "", "blockShots": "", "MatchesToday": "მატჩები დღეს", "Strikers": "Წინ", "Midfielders": "Ნახევარმცველი", "Year": "", "Loans": "", "TopValuablePlayers": "", "total": "", "Total": "", "Results": "", "Prev": "", "Apps": "Appearances", "ShotsPg": "Საშუალოდ shot", "Possession": "", "TotalAndAve": "", "Suspended": "დაშავებული ან გაძევებული", "injuredOrSuspended": "დაშავებული ან გაძევებული", "Since": "Ტრავმის დროს", "Overall": "Overall", "Age": "ასაკი", "LastMatchFormations": "Ბოლო თამაში შემადგენლობა", "Formation": "", "GoalDistribution": "Მიზანი განაწილება", "Favorite": "", "FoundedIn": "Დაარსდა", "LocalPlayers": "ადგილობრივი მოთამაშეები", "ShowNext": "მომდევნო მატჩის ჩვენება", "HideNext": "მომდევნო მატჩის დამალვა", "FIFAWorldRanking": "ფიფას სმოფლიო რეიტინგი", "Register": "", "OP": "1X2", "Handicap": "", "h5Handicap": "", "TennisHandicap": "", "OU": "ზემოთ/ქვემოთ", "CardUpgradeConfirmed": "ბარათის განახლება დადასტურებულია", "VAR": "", "LatestMatches": "ბოლო მატჩები", "ScheduledMatches": "", "Only": "", "LeagueFilter": "", "WinProb": "", "ScoreHalf": "", "Animation": "", "FigureLegends": "", "YellowCard": "ყვითელი ბარათი", "RedCard": "წითელი ბარათი", "Chatroom": "სასაუბრო სივრცე", "Send": "მოედნიდან გაძევება", "Logout": "", "CurrentLeague": "", "ShirtNumber": "", "ContractUntil": "მიმართეთ მდე", "PlayerInfo": "PLAYER INFO", "Height": "სიმაღლე", "Weight": "წონა", "PlayerValue": "Სოციალური სტატუსი", "View": "", "Time": "დრო", "onTarget": "", "offTarget": "", "Played": "", "Rating": "", "Attacking": "შეტევა", "Creativity": "კრეატიულობა", "Defending": "დაცვა", "Tactical": "ტაქტიკური", "Technical": "ტექნიკური", "Other": "", "Cards": "Ჯარიმები ბარათები", "AccuratePerGame": "Ზუსტი მიღება", "AccLongBalls": "Ასოცირებული გრძელი პასის", "AccCrosses": "Ზუსტი ბიოგრაფია", "SuccDribbles": "Საგანგებო წარმატება", "TotalDuelsWon": "წესების დარღვევა", "YellowRedCards": "", "AiScoreRatings": "", "Pergame": "", "Player": "", "Upcoming": "", "FreeKicks": "", "Corners": "კუთხე", "DaysUntil": "დღეები ... -მდე", "In": "თამაშის დროს", "Out": "აუტი", "NoStrengths": "არანაირი მნიშვნელოვანი უპირატესობა", "NoWeaknesses": "არანაირი მნიშვნელოვანი სისუსტე", "UpcomingMatches": "", "Pos": "", "Ht(cm)": "", "wt(kg)": "", "Exp": "გამოცდილების რაოდენობა", "College": "", "Salary/Year": "", "Manager": "", "TotalPlayers": "Პუნქტების მოთამაშე", "Form": "", "Points": "ქულები", "GamesPlayed": "", "WinPercentage": "", "FieldGoalsMade": "", "FieldGoalsAttempted": "", "FieldGoalPercentage": "", "threePointFieldGoalsMade": "", "threePointFieldGoalsAttempted": "", "threePointFieldGoalsPercentage": "", "FieldGoaldsMade": "", "FreeThrowsAttempted": "", "FreeThrowPercentage": "", "BlockedFieldGoalAttempts": "", "PersonalFouls": "", "PersonalFoulsDrawn": "", "Efficiency": "", "PlusMinus": "", "Atlantic": "", "Central": "", "Southeast": "", "Northwest": "", "Pacific": "", "Southwest": "", "EasternConference": "", "WesternConference": "", "ToWin": "", "Spread": "Spread", "AmFootballHand": "Spread", "TotalPoints": "", "TotalPoint": "", "TableTennisTotalGames": "", "Wins": "მოგება", "Losses": "წაგებები", "WinningPercentage": "", "GamesBack": "", "HomeRecord": "", "AwayRecord": "", "DivisionRecord": "", "ConferenceRecord": "", "OpponentPointsPerGame": "", "AveragePointDifferential": "", "CurrentStreak": "", "RecordLast5Games": "", "Match": "მატჩი", "OnTheCourt": "მოთამაშე", "Starters": "სასტარტო შემადგენლობის მოთამაშეები", "FieldGoals": "ნებისმიერი დარტყმა,  საჯარიმო დარტყმების გარდა", "Timeout": "", "OpeningOdds": "", "PreMatchOdds": "", "PointPerGame": "", "PointsAllowed": "", "StartingLineups": "სასტარტო შემადგენლობა", "HandicapGames": "", "TennisHand": "", "TotalGames": "", "TennisTotalGames": "", "Defensive": "", "Offensive": "", "Points(PerGame)": "", "Rebounds(PerGame)": "", "Other(PerGame)": "Სხვა (საშუალოდ)", "Attists": "", "FirstServe": "", "FirstServePoints": "", "BreakPoints": "", "Aces": "ეისები", "DoubleFaults": "ორმაგი შეცდომა", "Winner": "Winner", "HandicapSets": "", "TableTennisHand": "", "MoneyLine": "", "TableTennisHandicapGames": "", "HandicapRuns": "", "BaseballHand": "", "TotalRuns": "", "BaseballTotalGames": "", "DIFF": "", "BasketPergame": "", "HandicapPoints": "", "HandicapFrames": "", "TotalFrames": "", "SeeAll": ""}