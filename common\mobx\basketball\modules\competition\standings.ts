import { makeAutoObservable } from "mobx";
import moment from "moment";
// import { getFavorites } from "../../api/home";

// function WebHome() {
//   this.currentGameTab = HomeGameTab.All;
//   makeAutoObservable(this)
// }

// WebHome.prototype.switchHomeGameTab = function(nextTab) {
//   this.currentGameTab = nextTab
// }

class StandingsStore {
  // currentRanksTab = "All";
  constructor() {
    // 0-Live， 1-ALL, 2-Finished, 3-Scheduled
    this.currentRanksTab = "All";
    this.currentKeyType = "Goals";
    this.mobileCurrentRadio = "All";
    this.standingsList = [];
    makeAutoObservable(this);
    // getFavorites().then((ids) => (this.favoriteMatches = ids));
  }

  updateStandingsList(nextList) {
    this.standingsList = nextList;
  }

  switchMobileCurrentRadio(nextTab) {
    this.mobileCurrentRadio = nextTab;
  }

  switchRanksTab(nextTab) {
    this.currentRanksTab = nextTab;
  }

  switchKeyType(nextTab) {
    this.currentKeyType = nextTab;
  }
}

export default StandingsStore;
