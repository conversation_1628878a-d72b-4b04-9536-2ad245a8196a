// @height: 40px;
// @width: @height * 5.33;

// .header {
//   display: flex;
//   align-items: center;
//   justify-content: space-between;

//   width: 100%;
//   height: 88px;
//   background: #0f80d9;
//   font-size: 32px;
//   font-weight: 500;
//   font-family: Roboto-Medium, Roboto;
//   color: #ffffff;
//   line-height: 88px;
//   text-align: center;
//   position: fixed;
//   z-index: 999;
//   padding: 0 20px;

//   .left {
//     display: flex;
//     height: max-content;
//     width: 33.3%;
//     overflow: hidden;

//     .logo {
//       position: relative;
//       width: @width;
//       height: @height;
//     }
//   }

//   .title {
//     flex: 1;
//     justify-content: center;
//     align-items: center;
//     padding-left: 150px;
//     box-sizing: border-box;
//   }

//   .backArea {
//     height: 88px;
//     color: #fff;
//     text-align: center;
//     line-height: 88px;
//     font-weight: normal;
//     font-size: 28px;
//     width: 68px;
//     white-space: nowrap;
//     .iconback{
//       color: #fff;
//     }
//   }

//   .center-right {
//     width: 68px;
//   }

//   .center {
//     display: flex;
//     align-items: center;

//     .myfont {
//       display: inline-block;
//       color: #fff;
//       font-size: 32px;
//       transform: scale(0.4);
//       position: relative;
//     }
//   }

//   .center-title {
//     align-items: center;
//     justify-content: center;
//     height: 88px;
//     line-height: initial;

//     .subTitle {
//       font-weight: normal;
//       font-size: 24px;
//     }
//   }

//   .right {
//     display: flex;
//     align-items: center;
//     justify-content: flex-end;
//     width: 33.3%;

//     span.appdownload {
//       width: 40px;

//       a {
//         display: flex;
//         align-items: center;
//         width: 40px;
//         margin-left: -5px;

//         .icon {
//           width: 2em;
//           height: 2em;
//           vertical-align: -0.125em;
//         }
//       }
//     }

//     .myIcon {
//       width: 40px;
//       text-align: center;
//       color: #fff;
//     }
//   }
// }

// .language-text {
//   overflow: hidden;
//   white-space: nowrap;
//   text-overflow: ellipsis;
// }

// .lngPopup {
//   position: relative;
//   width: 100%;
//   height: 100%;
//   padding-top: 88px;
//   box-sizing: border-box;
//   overflow: auto;

//   .lngHeader {
//     position: absolute;
//     left: 0;
//     top: 0;
//     z-index: 2;
//     display: flex;
//     justify-content: center;
//     align-content: center;
//     width: 100%;
//     height: 88px;
//     background: #0f80da;
//     font-size: 30px;
//     line-height: 88px;
//     color: #ffffff;
//     font-weight: bold;

//     .lngBack {
//       position: absolute;
//       left: 0;
//       top: 0;
//       display: flex;
//       align-items: center;
//       justify-content: center;
//       width: 88px;
//       height: 88px;
//       text-align: center;
//       color: #fff;
//     }
//   }

//   .lngList {
//     display: flex;
//     flex-direction: column;
//     width: 100%;

//     .lngItem {
//       width: 100%;
//       height: 96px;
//       padding: 0 32px;
//       box-sizing: border-box;
//       display: flex;
//       align-items: center;
//       justify-content: space-between;
//       background: #fff;
//       border-bottom: 2px solid rgba(255, 241, 241, 0.9451);
//       font-size: 28px;
//     }
//   }
// }

// .contentBox {
//   padding: 12px;

//   .w100 {
//     width: 100%;

//     .ball {
//       margin-bottom: 16px;
//       margin-top: 0;
//       background: #fff;
//       display: flex;
//       justify-content: space-between;
//       align-items: center;
//       width: 100%;
//       height: 88px;
//       border-radius: 4px;
//       padding: 0 26px;
//       box-sizing: border-box;

//       .flex {
//         display: flex;
//       }

//       .align-center {
//         align-items: center;
//       }

//       .h100 {
//         height: 100%;
//       }

//       .ballType {
//         margin-right: 30px;
//         color: #0f80da;
//         font-size: 48px;
//       }

//       .ballType.active {
//         color: #fff;
//       }

//       .type_text {
//         font-size: 24px;
//         font-weight: 500;
//         color: #666;
//       }

//       .type_text.active {
//         color: #fff;
//       }

//       .countStyle_normal {
//         font-size: 24px;
//       }

//       .countStyle_normal.active {
//         background: hsla(0, 0%, 100%, 0.29);
//         border-radius: 20px;
//         padding: 0 20px;
//       }
//     }

//     .ball.active {
//       background: #0f80d9;
//       color: #fff !important;
//       i{
//         color: #fff;
//       }
//       .type_text{
//         color: #fff;
//       }
//     }

//     // .selected-ball-count {
//     //   background: #ffffff;
//     //   border-radius: 20px;
//     //   opacity: 0.3;
//     //   padding: 8px 20px;
//     // }
//     .ball:last-child {
//       margin-bottom: 0;
//     }
//   }
// }

.common-header-container {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  background-color: #1e1e1e;
  padding: 10px 15px;

  .header-left-aligned {
    display: flex;
    align-items: center;

    .header-logo {
      height: 40px;
      width: auto;
    }
  }

  // .header-right-aligned {
  //   display: flex;
  //   align-items: center;
  //   width: 15%;
  //   justify-content: space-between;

  //   .header-search-btn,
  //   .header-setting-btn,
  //   .header-league-btn {
  //     display: flex;
  //     height: 40px;
  //     width: fit-content;
  //     margin-left: 5px;
  //     cursor: pointer;
      
  //     .icons {
  //       font-size: 40px;
  //       color: #fff;
  //     }
  //   }
  // }
}

.header-navbar-swiper {
  padding: 10px 8px;
  background-color: #121212;

  .adm-swiper-horizontal .adm-swiper-indicator {
    display: none;
  }

  .adm-swiper-slide, 
  .adm-swiper-slide-placeholder {
    width: fit-content;
  }

  .header-sport-btn {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 50px;
    width: fit-content;
    padding: 5px 10px;
    text-align: center;
    background: transparent;
    border: none;
    border-radius: 24px;
    margin-left: 10px;

    .adm-space {
      --gap: 4px;
    }

    .sport-icon {
      font-size: 24px;
      color: #fff;
      transition: color 0.3s;
    }

    .sport-text {
      font-size: 28px;
      font-weight: 600;
      color: #fff;
      transition: color 0.3s;
    }

    &.active {
      background-color: #fff;
      border-radius: 32px;

      .sport-icon,
      .sport-text {
        color: #121212;
      }
    }

    .header-live-count-badge {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background: #F79421;
      color: #fff;
      height: 28px;
      min-width: 28px;
      max-width: max-content;
      
      .adm-badge-content {
        font-size: 21px;
        padding: 0px;
      }
    }

    .adm-space-vertical {
      flex-direction: row;

      .adm-space-item {
        margin: 0px 6px;
      }
    }
  }

  .custom-header-swiper {
    .adm-swiper-track-inner {
      transition: transform 0.3s ease;
      will-change: transform;
    }

    .adm-swiper-slide {
      width: auto;
      flex-shrink: 0;
    }
  }
}
