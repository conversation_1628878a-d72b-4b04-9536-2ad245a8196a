// @ts-nocheck
import igsRequest from 'iscommon/request/instance';
import { genValidObj } from 'iscommon/utils';

export const getCompetitionList = ({ competitionId, seasonId, stageId, groupNum, roundNum } = {}) => {
  return igsRequest
    .post(
      'competition/detail/matches',
      genValidObj({
        competitionId,
        seasonId,
        stageId,
        groupNum, //int
        roundNum, //int
      }),
    )
    .then((result) => {
      try {
        return result;
      } catch (e) {
        return [];
      }
    });
};

export const getCompetitionFilter = ({ competitionId, seasonId } = {}) => {
  return igsRequest
    .post('competition/detail/selector', {
      competitionId,
      seasonId,
    })
    .then((result) => {
      try {
        const { stages } = result;
        for (let v of stages) {
          const { groupCount, roundCount } = v;

          let groupList = [];
          for (let i = 0; i < groupCount; i++) {
            groupList.push({
              text: String.fromCharCode(64 + i + 1),
              id: i + 1,
            });
          }
          if (groupList.length > 0) {
            groupList.unshift({ text: 'All', id: 0 });
          }
          v['groupList'] = groupList;

          let roundList = [];
          for (let i = 0; i < roundCount; i++) {
            roundList.push({ text: i + 1, id: i + 1 });
          }
          v['groupList'] = groupList;
          v['roundList'] = roundList;
        }
        return result;
      } catch (e) {
        return [];
      }
    });
};

// standings
// "competitionId": null,
// "seasonId": null,
// "type": 0 // int 0-ALL, 1-Home, 2-Away
export const getStandings = ({ competitionId, seasonId, type, needMatchHistory = false } = {}) => {
  return igsRequest
    .post('competition/detail/standings', {
      competitionId,
      seasonId,
      type,
      needMatchHistory,
    })
    .then((result) => {
      return result;
    });
};

export const getStatsTeam = ({ 
  competitionId = '', 
  seasonId = '', 
  property = '',
  pageNum = 0,
  pageSize = 10,
} = {}) => {
  return igsRequest
    .post('competition/detail/statistics/team', {
      competitionId,
      seasonId,
      property,
    })
    .then((result) => {
      return result;
    });
};

export const getStatsPlayer = ({
  competitionId = '',
  seasonId = '',
  property = '',
  pageNum = 0,
  pageSize = 10,
  teamId = '',
} = {}) => {
  return igsRequest
    .post(
      'competition/detail/statistics/player',
      genValidObj({
        competitionId,
        seasonId,
        property,
        pageNum,
        pageSize,
        teamId,
      }),
    )
    .then((result) => {
      return result;
    });
};

export const getNextMatch = ({ competitionId, seasonId }) => {
  // competition/detail/nextMatch
  return igsRequest
    .post('competition/detail/nextMatch', {
      competitionId,
      seasonId,
    })
    .then((result) => {
      return result;
    });
};

//  orderBy int 0-score, 1-assists
export const getTopPlayers = ({ competitionId = '', seasonId = '', orderBy = 0, pageNum = 0, pageSize = 10 }) => {
  return igsRequest
    .post('competition/detail/topScorers', {
      competitionId,
      seasonId,
      orderBy,
      pageNum,
      pageSize,
    })
    .then((result) => {
      return result;
    });
};

export const getTransfer = ({ competitionId = '', seasonId = '', teamId = '', year = '' } = {}) => {
  return igsRequest
    .post('competition/detail/transfers', {
      competitionId,
      seasonId,
      teamId,
      year: year.split('-')[0],
    })
    .then((result) => {
      return result;
    });
};

export const getTopValuablePlayers = ({ competitionId = '', teamId = '' } = {}) => {
  return igsRequest
    .post(
      'competition/detail/topValuablePlayers',
      genValidObj({
        competitionId,
        teamId,
      }),
    )
    .then((result) => {
      return result;
    });
};

export const getCompetitionHeader = ({ competitionId, seasonId, includeMvp = true } = {}) =>
  igsRequest.post(
    'competition/detail/header',
    genValidObj({
      competitionId,
      seasonId,
      includeMvp,
    }),
  );

export const getMostMarketValuePlayer = ({ competitionId, seasonId } = {}) =>
  igsRequest.post(
    'competition/detail/mvpPlayer',
    genValidObj({
      competitionId,
      seasonId,
    }),
  );

export const getCompetitionChampions = ({ competitionId } = {}) => {
  return igsRequest.post('competition/detail/winners', { competitionId }).then((result) => {
    return result;
  });
};

export const getBestLineupDetails = ({ competitionId, seasonId } = {}) => {
  return igsRequest
    .post('competition/detail/bestLineupDetails', {
      competitionId,
      seasonId,
    })
    .then((result) => {
      return result;
    });
};
