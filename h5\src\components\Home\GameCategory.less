// .homeGameCategory {
//   width: 100%;
//   height: 62px !important;
//   background: #f1f1f1;
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
//   font-size: 28px;
//   box-sizing: border-box;
//   padding: 0 12px;
//   color: #666;
//   cursor: pointer;

//   > div {
//     display: flex;
//     height: 100%;
//     align-items: center;
//   }

//   .cnameContainer {
//     flex: 3;
//     padding-right: 16px;
//     overflow: hidden;
//     box-sizing: border-box;

//     .countryName {
//       margin-right: 10px;
//       white-space: nowrap;
//       font-family: Roboto-Regular, Roboto, serif;
//       font-weight: 400;
//       color: #999;
//     }

//     .competitionName {
//       flex: 1;
//       overflow: hidden;
//       text-overflow: ellipsis;
//       white-space: nowrap;
//       cursor: pointer;
//       font-family: Roboto-Medium, Roboto, serif;
//       font-weight: 500;
//       color: #999;
//     }
//   }

//   .countryLogo {
//     display: flex;
//     width: 28px;
//     height: 28px;
//     margin-right: 10px;
//     border-radius: 50%;
//     overflow: hidden;
//     background-size: cover;
//     background-position: center;
//     background-repeat: no-repeat;
//   }

//   .countContainer {
//     flex: 1;
//     justify-content: space-between;

//     .count {
//       width: 100px;
//     }

//     .touchArea {
//       padding: 20px;
//     }
//   }

//   .countryName {
//     color: #999999;
//   }
// }

// .homeGameList {
//   border-top: 2px solid #e3e3e3;
//   border-bottom: 2px solid #e3e3e3;
//   background-color: #fff;

//   .listItem {
//     display: flex;
//     height: 100px;
//     border-top: 2px solid #e3e3e3;

//     .time {
//       line-height: 39px;
//       font-family: Roboto-Regular, Roboto, serif;

//       &:first-child {
//         margin-bottom: 6px;
//       }
//     }

//     div {
//       align-items: center;
//       justify-content: center;
//     }

//     .listLeft {
//       width: 118px;
//       display: flex;
//       flex-direction: column;
//       color: #999999;
//       font-size: 18px;
//     }

//     .listRight {
//       width: 105px;
//       display: flex;
//       flex-direction: column;
//       font-weight: bolder;
//       justify-content: space-around;
//       box-sizing: border-box;
//       font-size: 24px;
//       color: #999999;

//       .odd {
//         height: 30px;
//       }
//     }

//     .listContent {
//       flex: 1;
//       border-left: 1px solid #e3e3e3;
//       border-right: 1px solid #e3e3e3;
//       display: flex;
//       flex-direction: row;
//       justify-content: space-around;
//       align-items: center;
//       padding: 0 14px;
//       overflow: hidden;

//       .vsCountry {
//         display: flex;
//         flex-direction: column;
//         justify-content: center;
//         align-items: flex-start;
//         flex: 1;
//         overflow: hidden;
//         height: 100%;

//         .vsCountryItem {
//           display: flex;
//           overflow: hidden;
//           width: 100%;
//           justify-content: flex-start;
//           height: 40%;

//           .teamName {
//             font-size: 26px;
//             font-weight: 500;
//             color: #333333;
//             margin-right: 6px;
//           }

//           .squareLogo {
//             display: flex;
//             justify-content: center;
//             align-items: center;
//             width: 32px;
//             height: 32px;
//             margin-right: 14px;
//             border-radius: 50%;
//             overflow: hidden;
//             > img {
//               max-width: 32px;
//               max-height: 32px;
//             }
//           }

          // .card {
          //   padding: 0 5px;
          //   border-radius: 2px;
          //   margin-left: 3px;
          //   line-height: 34px;
          //   color: #fff;

          //   &.red {
          //     background-color: #c1272d;
          //   }

          //   &.yellow {
          //     background-color: #ffa830;
          //   }
          // }
//         }
//       }
//     }
//   }

//   :first-child {
//     border-top: none;
//   }

//   .ing {
//     color: #c1272d;
//   }
// }

.home-game-category-container {
  display: flex;
  flex-direction: column;
  width: 100%;

  .game-category {
    display: flex;
    flex-direction: row;
    width: 100%;
    padding: 12px;
    align-items: center;
    justify-content: space-between;

    .game-category-left-aligned {
      display: flex;
      align-items: center;
  
      .category-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        margin-right: 10px;
        overflow: hidden;
        background-size: cover;
      }
  
      .competition-container {
        display: flex;
        flex: 1;
        flex-direction: column;
  
        .competition-name {
          color: #fff;
          font-size: 22px;
        }
  
        .country-name {
          color: #C0C5C9;
          font-size: 22px;
        }
      }
    }
  
    .game-category-right-aligned {
      .count-container {
        display: flex;
        flex-direction: row;
        align-items: center;

        .icon-size,
        .online-count {
          font-size: 28px;
          color: #fff;
        }
      }
    }
  }

  .match-list-container {
    width: 100%;
    height: fit-content;
    background: #121212;
    border-radius: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    padding: 0 16px;
    cursor: pointer;
    margin-bottom: 16px;
    transition: all 0.3s ease;

    .match-time-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 20%;
      padding: 0px 10px;

      .match-time {
        color: #fff;
      }

      .match-time-live {
        color: #FF3131;
      }

      .match-time,
      .match-time-live {
        font-size: 24px;
      }
    }

    .match-team-container {
      display: flex;
      flex-direction: column;
      width: 60%;

      .match-team-info {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 10px 0px;

        .team-logo {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin-right: 10px;
          overflow: hidden;
          background-size: cover;
        }

        .team-name {
          font-size: 24px;
          font-weight: bold;
          color: #fff;
          text-overflow: ellipsis;
        }

        .card {
          padding: 0 5px;
          border-radius: 2px;
          margin-left: 3px;
          line-height: 34px;
          color: #fff;

          &.red {
            background-color: #c1272d;
          }

          &.yellow {
            background-color: #ffa830;
          }
        }
      }
    }

    .match-odd-container {
      display: flex;
      flex-direction: column;
      width: 10%;

      .odd {
        font-size: 18px;
        color: #fff;
      }
    }

    .match-live-score-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      width: 20%;
      margin-left: 10px;

      .match-score-container {
        display: flex;
        flex-direction: column;
        width: 50px;
  
        .match-score {
          font-size: 20px;
          font-weight: bold;
          color: #fff;
          padding: 10px 0px;
        }
      }
    }
  }
}
