.competition-overview-info-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }
  
  .container-body {
    background: #1e1e1e;
    padding: 10px;

    .champion-container {
      display: flex;
      margin-bottom: 15px;
      border-bottom: 1px solid #fff;

      .team-item-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 15px;
        border-radius: 24px;
        background: #121212;
        color: #fff;
        font-size: 18px;
    
        .team-icon {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          margin: 5px 10px;
          overflow: hidden;
          object-fit: cover;
        }

        .team-transkey {
          color: #999999;
        }
      }
    }

    .desc-item-container {
      display: flex;
      flex-direction: column;
      padding-bottom: 15px;
      border-bottom: 1px solid #fff;

      .desc-title {
        font-size: 24px;
        font-weight: bold;
        color: #fff;
        margin: 10px 0px;
      }

      .desc-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #121212;
        border-radius: 24px;
        padding: 10px;
        margin: 5px;

        .desc-info {
          display: flex;
          align-items: center;

          .desc-icon {
            width: 58px;
            height: 58px;
            border-radius: 50%;
            margin: 5px 10px;
            overflow: hidden;
            object-fit: cover;
          }

          .desc-player {
            display: flex;
            flex-direction: column;

            .player-name {
              font-size: 22px;
              color: #fff;
            }

            .player-team {
              font-size: 20px;
              color: #fff;
            }
          }

          .desc-value {
            font-size: 22px;
            color: #fff;
          }
        }

        .player-value {
          font-size: 22px;
          font-weight: 700;
          color: #F79421;
        }
      }

      .info-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px;
        margin: 5px;
        border-bottom: 1px solid #fff;

        .info-value {
          font-size: 22px;
          color: #fff;
        }
      }
    }

    .desc-item-container:last-child {
      border-bottom: none;
    }
  }
}