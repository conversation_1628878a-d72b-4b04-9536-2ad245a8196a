import './TopPlayersCard.less'

import { Card, Image, Typography } from 'antd';
import { inject, observer } from 'mobx-react';

import { basketballPlayerIcon } from 'iscommon/const/basketball/constant';
import { FallbackImage } from 'iscommon/const/icon';
import { PlayerLink } from '@/components/Common/Link';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import WidgetBox from '@/components/Common/Widget';

// import { LiveTeamTitle } from './LiveTitle';
// import styles from './TopPlayersCard.less';




const {Text} = Typography;

const TopPlayers = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Basketball: { Match },
      },
    } = props;
    const labelMaps = useTranslateKeysToMaps(['topPlayers', 'Points', 'Rebounds', 'Assists']);

    const renderContrastBox = (keyType: any, leftCount: any, rightCount: any) => {
      const leftBarHeight = (leftCount || 0) + 5;
      const rightBarHeight = (rightCount || 0) + 5;
      return (
        <div className='contrast-box-container'>
          <Text className='item-count home-item'>{leftCount}</Text>
          {keyType}
          <Text className='item-count away-item'>{rightCount}</Text>
        </div>
        // <div className={styles.contrastBox}>
        //   <div className={styles.lineBox}>
        //     <div className={styles.lineLeft}>
        //       <span className={styles.lineText} style={{ bottom: leftBarHeight }}>
        //         {leftCount}
        //       </span>
        //       <span className={styles.bar} style={{ height: leftBarHeight, background: '#409eff' }} />
        //     </div>
        //     <div className={styles.lineRight}>
        //       <span className={styles.lineText} style={{ bottom: leftBarHeight }}>
        //         {rightCount}
        //       </span>
        //       <span className={styles.bar} style={{ height: rightBarHeight, background: '#ffba59' }} />
        //     </div>
        //     <span className={styles.lineBottom} />
        //     <div className={styles.keyTypeText}>{keyType}</div>
        //   </div>
        // </div>
      );
    };

    const renderPlayerItem = (renderInfo: any) => (
      <PlayerLink playId={renderInfo.player.id} className='player-item'>
        <Image src={renderInfo.player?.logo || basketballPlayerIcon} preview={false} className='player-icon'/>
        <Text className='player-name'>{renderInfo.player?.shortName}</Text>
      </PlayerLink>
    );

    const { keyPlayers = {} } = Match;
    const { homeTeam, awayTeam } = keyPlayers;

    const renderEach = (labelText: string, index: number) => {
      if (!homeTeam.items[index] || !awayTeam.items[index]) return null;
      return (
        <div className='player-container' key={index}>
          {renderPlayerItem(homeTeam.items[index])}
          {renderContrastBox(labelText, homeTeam.items[index].value, awayTeam.items[index].value)}
          {renderPlayerItem(awayTeam.items[index])}
        </div>
      );
    };

    if (!homeTeam || !awayTeam || homeTeam.items.length === 0 || awayTeam.items.length === 0) return null;
    // 最高得分、篮板、助攻

    return (
      // <Card
      //   className={styles.container}
      //   title={labelMaps.topPlayers}
      //   size="small"
      //   headStyle={{ color: '#0F80DA', borderBottom: 'none', height: 40 }}
      //   bordered={false}
      //   id="bsmTopPlayerPlugin"
      // >
      //   {/* <LiveTeamTitle /> */}
      //   <div className={styles.content}>
      //     {[labelMaps.Points, labelMaps.Rebounds, labelMaps.Assists].map((label, index) => renderEach(label, index))}
      //   </div>
      //   <WidgetBox withOuter pluginId="bsmTopPlayerPlugin" tab="MatchLive" title="Top players" />
      // </Card>
      <div className='basketball-top-players-card-container' id="bsmTopPlayerPlugin">
        <div className='container-title'>{labelMaps.topPlayers}</div>
        <div className='player-team-container'>
          <div className='team-container'>
            <Image src={homeTeam.team.logo || FallbackImage} className='team-icon' preview={false}/>
            <Text className='team-name'>{homeTeam.team.name}</Text>
          </div>
          <div className='team-container'>
            <Text className='team-name'>{awayTeam.team.name}</Text>
            <Image src={awayTeam.team.logo || FallbackImage} className='team-icon' preview={false}/>
          </div>
        </div>
        <div className='container-body'>
          {[labelMaps.Points, labelMaps.Rebounds, labelMaps.Assists].map((label, index) => renderEach(label, index))}
        </div>
        <div className='container-footer'>
          <WidgetBox pluginId="bsmTopPlayerPlugin" tab="MatchLive" title="Top players" titlePosition='right'/>
        </div>
      </div>
    );
  }),
);

export default TopPlayers;
