import { inject, observer } from 'mobx-react';
import React, { useMemo } from 'react';

import { getHandballScoreList } from 'iscommon/utils/dataUtils';

import SmallballCommonGameCategory from '@/components/SmallGameCommon/Home/GameCategory';
import {
  HandballInLiveStatusEnum,
  HandballMatchStatusCodeToText,
  HandballStatusCodeEnum,
} from 'iscommon/const/handball/constant';
import './GameCategory.less';

interface PureMatchStatusTextProps {
  serverTime: number;
  matchStatus: number;
  remainSeconds: number;
}

const MatchStatusText = React.memo<PureMatchStatusTextProps>((props) => {
  const { matchStatus } = props;
  const { statusText, isIng } = useMemo(() => {
    return {
      statusText: (HandballMatchStatusCodeToText as any)[matchStatus],
      isIng: HandballInLiveStatusEnum.includes(matchStatus),
    };
  }, [matchStatus]);

  return <span className={`time fs-10-center ${isIng ? 'ing' : ''}`}>{statusText}</span>;
});

interface ScoreItem {
  h: number;
  w: number;
  isRed: boolean;
  compareStatus: number; // 1 home > away 2 home < away 0 =
}

const ScoreItem = ({ item, isTotal = false }: { item: ScoreItem; isTotal: boolean }) => {
  return item.isGreen ? (
    <div className={`listRightSection`}>
      <div className={`${item.isRed && 'ing'}`} style={{ color: '#87c31d' }}>
        {item.h}
      </div>
      <div className={`${item.isRed && 'ing'}`} style={{ color: '#87c31d' }}>
        {item.w}
      </div>
    </div>
  ) : (
    <div className={`listRightSection ${item.isRed && 'color-c1272d'}`}>
      <div className={`${item.isRed && 'ing'} ${item.compareStatus === 1 && !item.isRed && 'color-333'}`}>{item.h}</div>
      <div className={`${item.isRed && 'ing'} ${item.compareStatus === 2 && !item.isRed && 'color-333'}`}>{item.w}</div>
    </div>
  );
};

const ScoreText = (props: any) => {
  const { item } = props;
  const { matchStatus, scores, servingSide } = item;
  const list: ScoreItem[] | any[] = getHandballScoreList(matchStatus, scores, servingSide);
  if (matchStatus === HandballStatusCodeEnum.NotStarted) {
    return <div className="listRight">-</div>;
  }
  // 如果比赛不在进行中，直接展示总比分
  if (!HandballInLiveStatusEnum.includes(matchStatus)) {
    const newList = [scores.ft ? { h: scores.ft[0], w: scores.ft[1] } : { h: 0, w: 0 }];
    return (
      <div className="listRight rowFlex">
        {newList.map((item, index) => {
          return <ScoreItem item={item} key={index} isTotal={index === 0} />;
        })}
      </div>
    );
  }
  // 如果比赛进行中，展示总比分（红色）
  const newList = [scores.ft ? { h: scores.ft[0], w: scores.ft[1], isRed: true } : { h: 0, w: 0, isRed: true }];

  return (
    <div className="listRight rowFlex">
      {newList.map((item, index) => {
        return <ScoreItem item={item} key={index} isTotal={index === 0} />;
      })}
    </div>
  );
};

const ScoreServingPt = ({ item }: any) => {
  const { scores } = item;
  if (scores.pt && scores.pt.length > 1) {
    return (
      <div className="spt">
        <span>{scores.pt[0] || 0}</span>
        <span className="spt">{scores.pt[1] || 0}</span>
      </div>
    );
  }
  return <div className="spt"></div>;
};

const ScoreOrOdds = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Smallball: { SmallballCommon },
      },
      item,
    } = props;
    const { matchId, matchStatus } = item;
    const { homeCompetitionOdds } = SmallballCommon;
    const matchOdds = homeCompetitionOdds[matchId] || [];
    if (matchStatus === HandballStatusCodeEnum.NotStarted) {
      const currentOdd = matchOdds?.filter((item: any) => item.oddsType === 'eu')[0]?.oddsData;
      // future games show odds
      /*
      if (currentOdd) {
        return (
          <div className="listRight">
            {[0, 2].map((key) => (
              <span key={key} className="odd">
                {Number(currentOdd[key]).toFixed(2)}
              </span>
            ))}
          </div>
        );
      }
      */
      return (
        <div className="listRight">
          <span className="odd">-</span>
        </div>
      );
    }
    return <ScoreText item={item} />;
  }),
);

const TeamNameExra = ({ isHome = false, item }: any) => {
  const { servingSide } = item;
  // if ((isHome && servingSide == 1) || (!isHome && servingSide == 2)) {
  //   return (
  //     <svg aria-hidden="true" className="icon fs-12 wangqiu svgPostop">
  //       <use xlinkHref="#icontabletennis_ball"></use>
  //     </svg>
  //   );
  // }
  return null;
};

const GameCategory = ({ competition }: any) => {
  return (
    <SmallballCommonGameCategory
      competition={competition}
      renderMatchStatusText={(item) => <MatchStatusText {...item} />}
      renderScoreOrOdds={(item) => {
        return (
          <>
            {/* <ScoreServingPt item={item} /> */}
            <ScoreOrOdds item={item} />
          </>
        );
      }}
      renderTeamNameExra={(isHome, item) => <TeamNameExra isHome={isHome} item={item} />}
    />
  );
};

export default GameCategory;
