import PlayerStatsTab from '@/components/Competition/PlayerStatsTab/PlayerStatsTab';
import Loading from '@/components/Loading';
import { Avatar, Picker } from 'antd-mobile';
import { getTeamCompetitions } from 'iscommon/api/football-team';
import { inject, observer } from 'mobx-react';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useParams } from 'umi';
import styles from './index.less';

interface Props {
  store?: any;
}

const PlayerStatisticsTab: React.FC<Props> = inject('store')(
  observer(() => {
    const { categoryId: teamId } = useParams<{ categoryId: string }>();

    const [data, setData] = useState<any[]>([]);
    const [competition, setCurrentCompetition] = useState<any>({});
    const [competitionPickerList, setCompetitionPickerList] = useState<any>([]);
    const [seasonPickerList, setSeasonPickerList] = useState<any[]>([]);

    const [competitionVisible, setCompetitionPickerVisible] = useState(false);
    const [seasonVisible, setSeasonPickerVisible] = useState(false);
    const [loading, setLoading] = useState<boolean>(false);

    const handleCompetition = useCallback(
      (value) => {
        // console.log('choose competition', value);
        if (value) {
          const competition = data.find((item) => item.id === value);
          const currentCompetition = {
            competitionId: value,
            currentSeason: { id: competition?.seasons[0]?.id },
          };

          // 赛季Picker数据
          const seasonPickerList = competition?.seasons?.map((item: any) => {
            return { label: item.year, value: item.id };
          });
          setSeasonPickerList(seasonPickerList);
          setCurrentCompetition(currentCompetition);
        }
      },
      [data],
    );

    const handleSeason = useCallback(
      (id) => {
        // console.log('choose seasonId', id);
        if (id) {
          const currentCompetition = {
            competitionId: competition.competitionId,
            currentSeason: { id: id },
          };
          setCurrentCompetition(currentCompetition);
        }
      },
      [competition.competitionId],
    );

    useEffect(() => {
      if (teamId) {
        setLoading(true);
        getTeamCompetitions({ teamId, validKey: 'hasPlayerStats' }).then(({ competitions }: any) => {
          // console.log('competitions', competitions);
          if (competitions?.length > 0) {
            // 第一个联赛的最新赛季
            const latestCompetition = competitions[0];
            const currentSeason = latestCompetition?.seasons?.find((i: any) => i.isCurrent === 1);
            const currentCompetition = {
              competitionId: latestCompetition?.id,
              currentSeason: { id: currentSeason?.id },
            };
            // 联赛Picker数据
            const competitionPickerList = competitions.map((item: any) => {
              return { label: item.name, value: item.id };
            });
            // 赛季Picker数据
            const seasonPickerList = competitions[0]?.seasons?.map((item: any) => {
              return { label: item.year, value: item.id };
            });

            setData(competitions);
            setCompetitionPickerList(competitionPickerList);
            setSeasonPickerList(seasonPickerList);
            setCurrentCompetition(currentCompetition);
            setLoading(false);
          }
        });
      }
    }, [teamId]);

    const year = () => {
      const item = seasonPickerList?.find((item) => item.value === competition?.currentSeason?.id);
      return item?.label || '';
    };
    const competitionInfo = useMemo(() => {
      const _competition = data?.find((item) => item.id === competition?.competitionId);
      return {
        logo: _competition?.logo || '',
        name: _competition?.name || '',
      };
    }, [data, competition]);

    return (
      <div className={styles.container}>
        <Loading loading={loading} isEmpty={!data.length}>
          <div className={styles.select_data_container}>
            <div className={styles.select_team_style} onClick={() => setCompetitionPickerVisible(true)}>
              <div className={styles.team_box}>
                {competitionInfo.logo ? (
                  <Avatar src={competitionInfo.logo} style={{ width: 20, height: 20 }} />
                ) : (
                  <span
                    style={{
                      width: 20,
                      height: 20,
                      backgroundColor: '#D8D8D8',
                      borderRadius: '50%',
                    }}
                  ></span>
                )}
                <span className={styles.competition_name}>{competitionInfo.name}</span>
              </div>
              <i className="iconfont iconxiala" style={{ fontSize: 24 }}></i>
            </div>
            <div className={styles.select_year_style} onClick={() => setSeasonPickerVisible(true)}>
              <span>{year()}</span>
              <i className="iconfont iconxiala" style={{ fontSize: 24 }}></i>
            </div>
          </div>
          <PlayerStatsTab showTeamInfo={false} competitions={competition} teamId={teamId} />
        </Loading>
        <Picker
          columns={[seasonPickerList]}
          visible={seasonVisible}
          value={[competition?.currentSeason?.id]}
          onClose={() => setSeasonPickerVisible(false)}
          onConfirm={(v) => handleSeason(v[0])}
        />
        <Picker
          columns={[competitionPickerList]}
          visible={competitionVisible}
          value={[competition.competitionId]}
          onClose={() => setCompetitionPickerVisible(false)}
          onConfirm={(v) => handleCompetition(v[0])}
        />
      </div>
    );
  }),
);

export default PlayerStatisticsTab;
