import IMG_188bet from 'iscommon/assets/images/football/188bet.png';
import IMG_1xbet from 'iscommon/assets/images/football/1xbet_c.png';
import IMG_bet365 from 'iscommon/assets/images/football/bet365.png';
import IMG_crownbet from 'iscommon/assets/images/football/crownbet.png';
import goal from '../assets/images/football/goal.png'
import ownGoal from '../assets/images/football/ownGoal.png'
import penalty from '../assets/images/football/penalty.png'
import penaltyMissed from '../assets/images/football/penaltyMissed.png'
import red from '../assets/images/football/red.png'
import secYellow from '../assets/images/football/secYellow.png'
import substitution from '../assets/images/football/substitution.png'
import yellow from '../assets/images/football/yellow.png'
import assist from '../assets/images/football/assist.png'

export const PlatformEnum = {
  web: 'web',
  mobile: 'mobile',
};

// homePage game tab
export const HomeGameTab = {
  All: 1,
  Live: 0,
  Finished: 3,
  Scheduled: 2,
  Favourite: 4,
  Leagues: 5,
};

export const OddsOptionsMap = [
  {
    key: '1x2',
    value: 0,
    type: 'eu',
  },
  {
    key: 'AsianHandicap',
    value: 1,
    type: 'asia',
  },
  {
    key: 'TotalGoals',
    value: 2,
    type: 'bs',
  },
  {
    key: 'TotalCorners',
    value: 3,
    type: 'cr',
  },
];

export const OddsOptionsTypeMap = ['eu', 'asia', 'bs', 'cr'];

/**
 * Azerbaijan
 * Bosnian
 * Indonesian
 * Serbian
 * German
 * Danish
 * English
 * Spanish
 * Estonia
 * French
 * Croatian
 * Italian
 * Latvian
 * Lithuanian
 * Hungarian
 * Norwegian
 * Dutch
 * Portuguese
 * Russian
 * Polish
 * Brazilian Portuguese
 * Romania
 * Swedish
 * Slovak
 * Slovene
 * In Finnish
 * Albanian
 * Turkish
 * Vietnamese
 * Czech
 * Greek
 * Bulgarian
 * Macedonian
 * Ukrainian
 * Arabic
 * Thailand
 * Myanmar
 * Georgian
 * Khmer
 * traditional Chinese
 * Japanese
 * Korean
 */
export const LanguageList = [
  { id: 'aze', index: 37, text: 'Azərbaycan dili', locale: 'az' }, // Azerbaijan 阿塞拜疆
  { id: 'bs', index: 30, text: 'Bosanski', locale: 'bs' }, // Bosnian 波斯尼亚语
  { id: 'index', index: 34, text: 'Bahasa Indonesia', locale: 'index' }, // Indonesian 印度尼西亚 !!!
  { id: 'sr', index: 31, text: 'Cрпски', locale: 'sr' }, // Serbian 塞尔维亚
  { id: 'de', index: 7, text: 'Deutsch', locale: 'de' }, // German 德语 !!!
  { id: 'da', index: 11, text: 'Dansk', locale: 'da' }, // Danish 丹麦语
  { id: 'en', index: 2, text: 'English', locale: 'en' }, // English !!!
  { id: 'es', index: 4, text: 'Español', locale: 'es' }, // Spanish 西班牙语 !!!
  { id: 'et', index: 22, text: 'Eesti', locale: 'et' }, // Estonia 爱沙尼亚
  { id: 'fr', index: 6, text: 'Français', locale: 'fr' }, // French 法语
  { id: 'hr', index: 25, text: 'Hrvatski', locale: 'hr' }, // Croatian 克罗地亚语
  { id: 'it', index: 8, text: 'Italiano', locale: 'it' }, // Italian 意大利语 !!!
  { id: 'lv', index: 27, text: 'Latviešu', locale: 'lv' }, // Latvian 拉脱维亚语
  { id: 'lt', index: 33, text: 'Lietuvių', locale: 'lt' }, // Lithuanian 立陶宛语
  { id: 'hu', index: 19, text: 'Magyar', locale: 'hu' }, // Hungarian 匈牙利语
  { id: 'nn', index: 13, text: 'Norsk', locale: 'nn' }, // Norwegian 挪威语
  { id: 'nl', index: 21, text: 'Nederlands', locale: 'nl' }, // Dutch 荷兰语
  { id: 'pt', index: 5, text: 'Português', locale: 'pt' }, // Portuguese 葡萄牙语 !!!
  { id: 'ru', index: 9, text: 'Pусский', locale: 'ru' }, // Russian 俄语 !!!
  { id: 'pl', index: 16, text: 'Polski', locale: 'pl' }, // Polish 波兰语
  { id: 'br', index: 43, text: 'Português do Brasil', locale: 'pt-br' }, // Brazilian Portuguese 巴西葡萄牙语 !!!
  { id: 'ro', index: 17, text: 'Română', locale: 'ro' }, // Romania 罗马尼亚语
  { id: 'sv', index: 12, text: 'Svenska', locale: 'sv' }, // Swedish 瑞典语
  { id: 'sk', index: 20, text: 'Slovenčina', locale: 'sk' }, // Slovak 斯洛伐克语
  { id: 'sl', index: 26, text: 'Slovenščina', locale: 'sl' }, // Slovene 斯洛文尼亚语
  { id: 'fi', index: 28, text: 'Suomeksi', locale: 'fi' }, // In Finnish 芬兰语
  { id: 'sqi', index: 39, text: 'Shqip', locale: 'sq' }, // Albanian 阿尔巴尼亚语
  { id: 'tr', index: 24, text: 'Türkçe', locale: 'tr' }, // Turkish 土耳其语 !!!
  { id: 'vi', index: 35, text: 'Tiếng Việt', locale: 'vi' }, // Vietnamese 越南语 !!!
  { id: 'cs', index: 18, text: 'Česky', locale: 'cs' }, // Czech 捷克语
  { id: 'el', index: 15, text: 'Ελληνικά', locale: 'el' }, // Greek 希腊语
  { id: 'bg', index: 14, text: 'Български', locale: 'bg' }, // Bulgarian 保加利亚语
  { id: 'mk', index: 32, text: 'Македонски', locale: 'mk' }, // Macedonian 马其顿语
  { id: 'ukr', index: 40, text: 'Українська', locale: 'uk' }, // Ukrainian 乌克兰语
  { id: 'aa', index: 36, text: 'العربية', locale: 'ar-sa' }, // Arabic 阿拉伯语
  { id: 'th', index: 29, text: 'ไทย', locale: 'th' }, // Thai 泰语 !!!
  { id: 'mm', index: 41, text: 'မြန်မာဘာသာ', locale: 'my' }, // Myanmar 缅甸语
  { id: 'ka', index: 38, text: 'ქართული', locale: 'ka' }, // Georgian 格鲁吉亚语
  { id: 'km', index: 45, text: 'ភាសាខ្មែរ', locale: 'km' }, // Khmer 高棉语
  { id: 'zht', index: 3, text: '中文繁體', locale: 'zh-tw' }, // Chinese Traditional 繁体中文
  { id: 'ja', index: 10, text: '日本語', locale: 'ja' }, // Japanese 日语
  { id: 'ko', index: 23, text: '한국어', locale: 'ko' }, // Korean 韩语
];

export const TimezoneList = [
  { id: 'UTC-12:00', text: 'UTC -12:00' },
  { id: 'UTC-11:00', text: 'UTC -11:00' },
  { id: 'UTC-10:00', text: 'UTC -10:00' },
  { id: 'UTC-09:30', text: 'UTC -09:30' },
  { id: 'UTC-09:00', text: 'UTC -09:00' },
  { id: 'UTC-08:00', text: 'UTC -08:00' },
  { id: 'UTC-07:00', text: 'UTC -07:00' },
  { id: 'UTC-06:00', text: 'UTC -06:00' },
  { id: 'UTC-05:00', text: 'UTC -05:00' },
  { id: 'UTC-04:00', text: 'UTC -04:00' },
  { id: 'UTC-03:30', text: 'UTC -03:30' },
  { id: 'UTC-03:00', text: 'UTC -03:00' },
  { id: 'UTC-02:00', text: 'UTC -02:00' },
  { id: 'UTC-01:00', text: 'UTC -01:00' },
  { id: 'UTC+00:00', text: 'UTC +00:00' },
  { id: 'UTC+01:00', text: 'UTC +01:00' },
  { id: 'UTC+02:00', text: 'UTC +02:00' },
  { id: 'UTC+03:00', text: 'UTC +03:00' },
  { id: 'UTC+03:30', text: 'UTC +03:30' },
  { id: 'UTC+04:00', text: 'UTC +04:00' },
  { id: 'UTC+04:30', text: 'UTC +04:30' },
  { id: 'UTC+05:00', text: 'UTC +05:00' },
  { id: 'UTC+05:30', text: 'UTC +05:30' },
  { id: 'UTC+05:45', text: 'UTC +05:45' },
  { id: 'UTC+06:00', text: 'UTC +06:00' },
  { id: 'UTC+06:30', text: 'UTC +06:30' },
  { id: 'UTC+07:00', text: 'UTC +07:00' },
  { id: 'UTC+08:00', text: 'UTC +08:00' },
  { id: 'UTC+08:45', text: 'UTC +08:45' },
  { id: 'UTC+09:00', text: 'UTC +09:00' },
  { id: 'UTC+09:30', text: 'UTC +09:30' },
  { id: 'UTC+10:00', text: 'UTC +10:00' },
  { id: 'UTC+10:30', text: 'UTC +10:30' },
  { id: 'UTC+11:00', text: 'UTC +11:00' },
  { id: 'UTC+12:00', text: 'UTC +12:00' },
  { id: 'UTC+12:45', text: 'UTC +12:45' },
  { id: 'UTC+13:00', text: 'UTC +13:00' },
  { id: 'UTC+14:00', text: 'UTC +14:00' },
];

export const OddFormatList = [
  { id: 'EU', text: 'EU (1.50)' },
  { id: 'HK', text: 'HK (0.50)' },
  { id: 'US', text: 'US (-200)' },
];

// ms
export const TimesUnits = {
  day: 24 * 60 * 60 * 1000,
  h: 60 * 60 * 1000,
  m: 60 * 1000,
  s: 1000,
};

// statusCode	description
// 0	Abnormal(suggest hiding)
// 1	Not started
// 2	First half
// 3	Half-time
// 4	Second half
// 5	Overtime
// 6	Overtime(deprecated)
// 7	Penalty Shoot-out
// 8	End
// 9	Delay
// 10	Interrupt
// 11	Cut in half
// 12	Cancel
// 13	To be determined
export const StatusCodeEnum = {
  Abnormal: 0,
  NotStarted: 1,
  FirstHalf: 2,
  HalfTime: 3,
  SecondHalf: 4,
  Overtime: 5,
  OvertimeDeprecated: 6,
  PenaltyShootout: 7,
  End: 8,
  Delay: 9,
  Interrupt: 10,
  CutInHalf: 11,
  Cancel: 12,
  ToBeDetermined: 13,
};

// has started status code enum
export const PeriodStatusEnum = [
  StatusCodeEnum.FirstHalf,
  StatusCodeEnum.HalfTime,
  StatusCodeEnum.SecondHalf,
  StatusCodeEnum.Overtime,
  StatusCodeEnum.PenaltyShootout,
  StatusCodeEnum.End,
  StatusCodeEnum.CutInHalf,
];

export const RedNumberStatusEnum = [
  StatusCodeEnum.FirstHalf,
  StatusCodeEnum.HalfTime,
  StatusCodeEnum.SecondHalf,
  StatusCodeEnum.Overtime,
  StatusCodeEnum.PenaltyShootout,
  StatusCodeEnum.CutInHalf,
];

export const InProgressStatusEnum = [
  StatusCodeEnum.FirstHalf,
  StatusCodeEnum.SecondHalf,
  StatusCodeEnum.Overtime,
  StatusCodeEnum.PenaltyShootout,
];

export const InLiveStatusEnum = [
  StatusCodeEnum.FirstHalf,
  StatusCodeEnum.HalfTime,
  StatusCodeEnum.SecondHalf,
  StatusCodeEnum.Overtime,
  StatusCodeEnum.OvertimeDeprecated,
  StatusCodeEnum.PenaltyShootout,
];

export const DefaultUserData = {
  type: '',
  token: '',
  name: '',
  avatar: '',
  isLogin: false,
};

export const oddsCompanyNameMaps = {
  '2': IMG_bet365,
  '4': IMG_1xbet,
  '3': IMG_crownbet,
  '21': IMG_188bet,
};

export const oddsCompanyTextMaps = {
  '2': 'bet365',
  '4': '1xbet',
  '3': 'CrownBet',
  '21': '188BET',
};

export const MatchStatusCodeToText = {
  0: '-',
  1: '-',
  2: '-', // 'First Half' matchMinutes should exist
  3: 'HT',
  4: '-', // 'Sencond Half' matchMinutes should exist
  5: '90+', //  'OverTime', if matchMinutes > 0 show matchMinutes
  6: '90+', // 'OverTime', if matchMinutes > 0 show matchMinutes
  7: 'Penalty',
  8: 'FT',
  9: 'Postponed',
  10: 'Interrupt',
  11: '-',
  12: 'Cancel',
  13: 'TBD',
};

// 这些code情况下，没有 playername 也要显示
export const DisplayTimeLineIconCode = [1, 3, 4, 8, 15, 16, 17, 30, 29];

export const iconMaps = {
  1: 'icon-goal', //进球
  3: 'icon-yellow-card', //黄牌,
  4: 'icon-red-card', //红牌
  8: 'icon-Penalty', // 点球
  9: 'icon-substitution', //换人
  15: 'icon-twoyellow-red', //两黄变红
  16: 'icon-PenaltySaved', //点球未进
  17: 'icon-own-goal', //乌龙球
  30: 'icon-PenaltySaved', //点球未进
  29: 'icon-Penalty', // 点球
  assist: 'icon-assist', // 助攻
};

// C:\Users\<USER>\Downloads\website\common\assets\images\football\goal.png
// common\assets\images\football\assist.png

// export type EventKey = '1' | '3' | '4' | '8' | '9' | '15' | '16' | '17' | '30' | '29' | 'assist';

export const iconProps = {
  1: goal, // Adjust path
  3: yellow, // Adjust path
  4: red, // Adjust path
  8: penalty, // Adjust path
  9: substitution, // Adjust path
  15: secYellow, // Adjust path
  16: penaltyMissed, // Adjust path
  17: ownGoal, // Adjust path
  30: penaltyMissed, // Adjust path
  29: penalty, // Adjust path
  assist: assist, // Adjust path
};

export const iconH5Maps = {
  1: 'icongoal', //进球
  3: 'icon-yellow-card', //黄牌,
  4: 'icon-red-card', //红牌
  8: 'iconPenalty', // 点球
  9: 'iconsubstitution', //换人
  15: 'icontwoyellow-red', //两黄变红
  16: 'iconPenaltySaved', //点球未进
  17: 'icon-own-goal', //乌龙球
  30: 'iconPenaltySaved', //点球未进
  29: 'iconPenalty', // 点球
  assist: 'iconassists', // 助攻
};

export const WeekNum = ['Mon', 'Tue', 'Wen', 'Thu', 'Fri', 'Sat', 'Sun'];

export const WeatherTextMap = {
  1: 'Partially cloudy',
  2: 'Cloudy',
  3: 'Partially cloudy/rain',
  4: 'Snow',
  5: 'Sunny',
  6: 'Overcast Rain/partial thunderstorm',
  7: 'overcast',
  8: 'mist',
  9: 'cloudy with rain',
  10: 'cloudy with rain',
  11: 'cloudy with rain/partial Thunderstorms',
  12: 'Clouds/rains and thunderstorms locally',
  13: 'Fog',
};

export const LineupRatingColor = ['#FF203C', '#FF6600', '#FFAE0F', '#C1CC01', '#5CB400', '#0E8604'];

// penalty,assists,redCards,yellowCards,shots,shotOnTargets
export const StatsKeyMaps = {
  ballPossession: {
    name: 'Possession',
  },
  goals: {
    name: 'Shots',
  },
  shotOnTargets: {
    name: 'ShotsOnTarget',
  },
  attacks: {
    name: 'attacks',
  },
  dangerousAttack: {
    name: 'dangerousAttack',
  },
  freeKicks: {
    name: 'FreeKicks',
  },
  corners: {
    name: 'Corners',
  },
  yellowCards: {
    name: 'yellowCards',
  },
  redCards: {
    name: 'redCards',
  },
};

export const TechnicalStatistics = {
  1: {
    key: 'Goal',
    in18Name: 'Goals',
  },
  2: {
    key: 'Corner',
    in18Name: 'Corner',
  },
  3: {
    key: 'Yellow card',
    in18Name: 'YellowCard',
  },
  4: {
    key: 'Red card',
    in18Name: 'RedCard',
  },
  5: {
    key: 'Foul ball',
    in18Name: 'FoulBall',
  },
  6: {
    key: 'Free kick',
    in18Name: 'FreeKick',
  },

  7: {
    key: 'Goal kick',
    in18Name: 'GoalKick',
  },
  8: {
    key: 'Penalty',
    in18Name: 'PenaltyKick',
  },
  9: {
    key: 'Substitution',
    in18Name: 'Substitution',
  },
  10: {
    key: 'Start',
    in18Name: 'Start',
  },
  11: {
    key: 'Midfield',
    in18Name: 'Midfield',
  },
  12: {
    key: 'End',
    in18Name: 'End',
  },
  13: {
    key: 'Halftime score',
    in18Name: 'HalftimeScore',
  },
  15: {
    key: 'Card upgrade confirmed',
    in18Name: 'CardUpgradeConfirmed',
  },
  16: {
    key: 'Penalty missed',
    in18Name: 'PenaltyMissed',
  },
  17: {
    key: 'Own goal',
    in18Name: 'OwnGoal',
  },
  19: {
    key: 'Injury time',
    in18Name: 'InjuryTime',
  },
  21: {
    key: 'Shots on target',
    in18Name: 'ShotsOnTarget',
  },
  22: {
    key: 'Shots off target',
    in18Name: 'ShotsOffTarget',
  },
  23: {
    key: 'Attacks',
    in18Name: 'attacks',
  },
  24: {
    key: 'Dangerous Attack',
    in18Name: 'dangerousAttack',
  },
  25: {
    key: 'Ball possession',
    in18Name: 'ballPossession',
  },
  26: {
    key: 'Overtime is over',
    in18Name: 'OvertimeIsOver',
  },
  27: {
    key: 'Penalty kick ended',
    in18Name: 'PenaltyKickEnded',
  },
  28: {
    key: 'VAR', // Video assistant referee)
    in18Name: 'VAR',
  },
  29: {
    key: 'Penalty', // Penalty Shoot-out
    in18Name: 'Penalty',
  },
  30: {
    key: 'Penalty missed', //(Penalty Shoot-out)
    in18Name: 'PenaltyMissed',
  },
};

export const DefaultUserIcon = require('../../common/assets/images/user_icon.png');

export const H5OddGameIcon = {
  basketball:
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAADVUlEQVQ4T3VVTWicVRQ955sm6cKFC8FkkoJCQQVFhApddBEhggUhEVPoQlDBzvs+UuKmYsRKGFSMuFBiJvPeNGLBSAO20IKLQgMKFgy4ibhoQcGKzUwCioIFE5h5R274ZhyGybeYnzf33nfuOefeIfo88/Pzh0ZHRydjjFMkj0satjCS25I2kiS5urW1da1cLjd709l7UKvVXpS0AOAogDsAlmKMf+YFHyQ5LmmC5B2Sc6VS6Up3jU5BQ1UsFt8FMAdgJUdzTtLxNE1/7E6qVCrDAwMD85JSAAv1ev2dNtpOwRDCB5LOSjqTZdmaFfDefwPggUaj8XS5XN7t7cZ7/yZJ62bBOffWfhf2krd5GcALzrmr7cTFxcWxoaGhn6x2O6H9m/f+SQDXjZac56k0Ta8xb/UWyfVSqZT1ogghzEj6hOQTzrnbuWB2Zsgu1+v1V4vFYhXAeL1ef4yGLsa41mq1jszMzGz3U917/z3Je5LWTAijQdK5NE0/s3jrZHBw8FcA0wwhfGEBzrmT3cVyJI9IOgHgJQD2/rckE+xD59wf3fEhhBsAtq3gzwDs9k0AhwCMkRyT9BBJ+35X0rqd5Rc/1a+LEMLbdjG99/+Q3JT0i1kFwG6M8Tf73Gq1Nts0VCqVo4VC4RaA8yTN0PdZYUmm/m2Sj0uaM4T/xhizLMsu9rs5t88kybMAJiQ1SW7kRRFjPEzyUQD3S7rXbnnVOVfu4zOzxorRkCTJiiSbmI/39vaOzM7O3u2O995/RPJ5K3jJZjVN02d6SH5Z0pJ5sNFo2CTsmlAjIyO/k1xyzr3fE//dvier1erpJEkuxRgfzrLMZtcm5HUA7wE4laapmbfzhBAuADjmnOuI0+Y3xnjKjH24WCya0tedc2e895MAVkmedM7d7KVheXl5olAo3Gg2myNtwbz3n5M8sW/sHNE0ya9MJVMRQJam6Wo/kXIAf5F8rVQqfWkASNq47o9tZznUarVqvj3WnXPPHqS4nYcQfgBwU9K3eTfG6f/LwYJywi+QfEXSRUnnsyzbOmAUqyRPm1UOXF/txBDCFIBP8y1tE2I8ti1iMzwO4DlbsADe6N5OnfXVi8LQDg8PTydJYgIdMx/mhrZJ2mi1Wld2dna+7vcX8B8k5bzlLmcEEgAAAABJRU5ErkJggg==',
  tennis:
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAMAAAC6V+0/AAAAtFBMVEUAAAB/f39/f3+VlZWPj4+cnJyXl5eZmZmVlZWXl5eYmJiVlZWcnJyZmZmampqXl5eWlpaXl5eYmJiYmJiWlpaZmZmampqYmJiYmJiXl5eYmJiWlpaXl5eYmJiZmZmYmJiampqYmJiYmJiampqZmZmYmJiYmJiZmZmZmZmampqYmJiZmZmZmZmYmJiampqZmZmYmJiZmZmZmZmZmZmZmZmYmJiZmZmYmJiYmJiZmZmYmJiZmZnL0JpFAAAAO3RSTlMABAgMEB8gIyQsLzA7PD9AREdIT1BQU1RXWGNkZ2h/g4OLl5ebn6Ojp6u3t7u/v8PHy8/X29/j5/P3+3wx08YAAADlSURBVBgZBcEHQtswAADAkzMwJQGySYdDIGC17JHYkv7/r94BYXb4zOUYbyoAZt99e19Wv9r+tAbYpduBizJm2OQ9sOumqNICrro9zNIUPEdwldeE71tgmWvQnIJZPwDC+0uAYX/j0ALU/UOANvq8+1EBpv3LGf4c5VLSvwVQv+e4Hq2KshovY34bAcvnVEpy/In6tZsAqov7D39bCA/dGCAebPshhNc3wCDNVacG1HkB7L6wyZcgPoFJvoZ9dwmbVGHSNSDsczNkXM4NdrkB2Jz6x9+LchfT1xyAahuPJX0c5gH8BzCcGc+B7IMfAAAAAElFTkSuQmCC',
  icehockey:
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAQAAAAngNWGAAABn0lEQVQoz3WSTShEURTH3+QjTGZFKDGlfJWNyMZi2FlYWM4OCxYKpSxs5Hvhq6a4//OGCPmYhSlFPlJTZkZqNshYEBKJjQbRNLznvpl582bGOJt7z7m/c+7/3HsEIYFRJUbhIS8ml/TCf0al2IOEb5zjBD9wTqUnxnoRwBcNU1bQG4FMB39QWyptKQdUEEnrxCc+4lCLgVwk0WBMfRvczKSgC2lqKAVOrqglBsvjInoEgZnoCuNq0A5JbI3TC3pdMQR3E3QTCnVDZmNxWC2/oSu0xxBulVAR+ZkjFrNk4565bElh8AibymInPxXFVMsgF54t+WGvApLVLIjFkGg6GhvXYx/vYrXqYxu3/ckC9UGaK9Qwaw47xhvVRTAzZLFJKbyFBw1j9XjAE6pUf7YEPmyEMnbpMQwZaZHLcFCeVp2/4OV8ZkhqB0lYxQB2EWAvrF3WqdhMLs7YIzNqPa5Bho920GaL+lNrGa65jPLoNnWg+O9jzXjHRXSTCVCqYYf8juXEQ8tRusM6ZtgpHzWv2PDvZAs66oAbHiygUWtIs18nhdTnJl2B9AAAAABJRU5ErkJggg==',
  volleyball:
    'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAQAAAAngNWGAAACDklEQVQYGW3BS0iUYRiG4cdDxESrFBKjhRDJ0CaC2kUmtKmNVGIlJJSLoMICN0k6uhGEmE6O3/3OaFboIjceQEjoKIUbt4IIiS6EhMQ8NFk6/9+XQmp4Xdpk+dQywjIZt8acdbtK7cTqbJmQaZpdPQ0uxSKhG3el2iqx14Ys4B1h8rj+aSu3STLUaJMNEXD7yW5CV6It4hEGLbAqbbA6QovJs69c1X/cB1ZTUUmWb8sW2B151ktXZ4HddD2M2bz9Yp5JPlrAgCRqCXjNgDwus8Iq07TbLStLneEiN9xjpgk6C8SITXGWn615ku1inD5laZu2YguoEcs09+TYFPfl2WkyViYpqzUvefhpYSxbHkvWIjKuXrJqt9BRKM/uskIf3wgJCVlkwF2wKeuXW6NB6snhE8OxXHm8caN2JXnEnvOe89bBChm6xJxLyXMHmKVVWbFsZtuq5bnP7oG8VBGBa5R126LWpU6y4F4mL7HSmif1RPhhZfLsHCEVcpWEVq51iaM241ZJyHPXWLI98uglHcuV5MaZjEfkxbJ5ZTPt++QxashzJQQ06S9XSoZBKR7hhc3bMXlcJ50qkhKHmGMiHtEGaixgmC9MuxPynh3kO/ek5Cnm3EIiqk1WxSoZe9hWLD3azxhvrYxeAiYSUW2XijJAYAFLpC3gNyFpmuIR7aSzgBprsX66XCMVsVxt8QfnazCFGemnigAAAABJRU5ErkJggg==',
};

export const InValidGames = ['cricket', 'esports'];

export const WithCompetitionGames = ['football', 'basketball'];
export const WithTeamsGames = ['football', 'basketball'];

export const themeColorMap = {
  t1: {
    name: 'Yellow',
    color: '#ffda03',
    tabcolor: '#ffcd05',
    endColor: '#fff'
  },
  t2: {
    name: 'Purple',
    color: '#7d12ff',
    tabcolor: '#e5cbf7',
    endColor: '#fff'
  },
  t3: {
    name: 'Soft Pink',
    color: '#ff8383',
    tabcolor: '#ff8383',
    endColor: '#fff'
  },
  t4: {
    name: 'Red',
    color: '#da0000',
    tabcolor: '#ff2c2c',
    endColor: '#fff'
  },
  t5: {
    name: 'Blue',
    color: '#6495ed',
    tabcolor: '#6395ec',
    endColor: '#fff'
  },
  t6: {
    name: 'Orange',
    color: '#ffb655',
    tabcolor: '#ffb655',
    endColor: '#fff'
  }
}
