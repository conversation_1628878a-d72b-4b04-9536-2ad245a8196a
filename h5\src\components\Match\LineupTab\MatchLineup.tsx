import { Avatar, Badge } from 'antd-mobile';
import { translate } from 'iscommon/i18n/utils';
import { changePlayerName, formatRateColor } from 'iscommon/utils/dataUtils';
import { inject, observer } from 'mobx-react';
import './MatchLineup.less'
import styles from './MatchLineup.less';
import { FallbackImage } from 'iscommon/const/icon';
import { iconProps } from 'iscommon/const/constant';

const getBadgeBackgroundColor = (count: number) => {
  if (count >= 8.0 && count <= 10.0) {
    return '#00ADC4';
  } else if (count >= 7.0 && count <= 7.9) {
    return '#1EC853';
  } else if (count >= 6.6 && count <= 6.9) {
    return '#FFAE0F';
  } else if (count >= 6.0 && count <= 6.5) {
    return '#F08022';
  } else if (count >= 0.1 && count <= 5.9) {
    return '#ED5454';
  }
};

const MatchLineup = inject('store')(
  observer((props: any) => {
    const { matchLineupInfo = {}, homeFirstRound = [], awayFirstRound = [], formation } = props;
    const { homeTeam, awayTeam } = matchLineupInfo;
    const Coach = translate('Coach');

    const iconH5Maps = {
      1: 'icongoal', //进球
      3: 'icon-yellow-card', //黄牌,
      4: 'icon-red-card', //红牌
      8: 'icon-Penalty', // 点球
      9: 'iconout1', //换人
      15: 'icon-twoyellow-red', //两黄变红
      16: 'iconPenaltySaved', //点球未进
      17: 'icon-own-goal', //乌龙球
    };

    const renderLineup = (renderList = [], lineupType = 'home') => {
      return renderList.map((item: any) => (
        <div
          className='player-box'
          style={
            lineupType === 'home'
              ? {
                  top: `${item.y}%`,
                  left: `${item.x}%`,
                  transform: 'translateX(-50%) translateY(-50%) scale(0.9)',
                }
              : {
                  bottom: `${item.y}%`,
                  right: `${item.x}%`,
                  transform: 'translateX(50%) translateY(50%) scale(0.9)',
                }
          }
          key={item.name}
        >
          {/* 1:进球 3:黄牌 4:红牌 9:换人 15:两黄变红 */}
          <div className='player-avatar-box'>
            <Avatar src={item.logo} className='player-image' />
            {/* {item.rating != '0.0' && (
              <span
                className={`${styles.rating_box} ${`${styles.color9above}`}`}
                style={{ background: formatRateColor(item.rating) }}
              >
                {item.rating}
              </span>
            )} */}

            {item.rating > 0 && (
              <Badge className='player-rating-text-size' content={parseFloat(item.rating || 0).toFixed(1)} style={{ background: getBadgeBackgroundColor(parseFloat(item.rating || 0)) }}/>
            )}

            {/* <div className={styles.leftTopStyle}>
              <div className={styles.even}>
                {item.incidents &&
                  Object.keys(item.incidents).map((val: any) =>
                    ['3', '4', '9', '15'].includes(val) ? (
                      <div className={styles.align_center} key={val + Math.random()}>
                        <svg className="svg-icon" style={{ width: 16, height: 16 }}>
                          <use xlinkHref={`#${iconH5Maps[val]}`} className="h16"></use>
                        </svg>
                        {val === '9' ? <span className={styles.event_time}>{`${item.incidents[val][0]?.minute}'`}</span> : null}
                      </div>
                    ) : null,
                  )}
              </div>
            </div> */}

            <div className='player-incident-container'>
                {item.incidents &&
                  Object.keys(item.incidents).map((val: any) => 
                    val !== '1' ? (
                      <div className='player-incident' key={val}>
                        <img src={iconProps[val]} className='incident-icon'/>
                        {val === '9' ? <span className='incident-time'>{`${item.incidents[val][0]?.minute}'`}</span> : null}
                      </div>
                    ) : null, 
                  )
                }
            </div>

            <div className='player-score-container'>
              {item.incidents && 
                Object.keys(item.incidents).map((val: any) =>
                  val === '1' ? (
                    <img src={iconProps[val]} className='incident-icon'/>
                  ) : null,
                )
              }
            </div>
            {/* <div className='player-time-score-container'>
              {item.incidents &&
                Object.keys(item.incidents).map((val: any) =>
                  val === '1' ? (
                    <svg className="svg-icon" style={{ width: 16, height: 16 }} key={val + Math.random()}>
                      <use xlinkHref={`#${iconH5Maps[val]}`} className="h16"></use>
                    </svg>
                  ) : null,
                )}
            </div> */}
          </div>
          <div className='player-info-container'>
            <span className='player-shirt-number'>{item.shirtNumber}</span>
            <span className='player-name'>{changePlayerName(item.name)}</span>
          </div>
          {/* <span className={styles.player_name}>{changePlayerName(item.name)}</span> */}
        </div>
      ));
    };

    const renderTeamInfo = (teamInfo = {}) => {
      console.log('teaminfo123', teamInfo)
      return (
        <></>
        // <div className={styles.teamInfo_box}>
        //   <div className={styles.teamInfo}>
        //     <Avatar src={teamInfo?.logo} className={styles.player_img} />
        //     <span>{teamInfo?.name}</span>
        //   </div>
        //   {/* <div className={styles.teamInfo}>{formationText}</div> */}
        // </div>
      );
    };

    const RenderGroundBox = (props: any) => {
      const { type = 'home' } = props;
      return (
        <div className='half-box' style={type === 'away' ? { borderBottom: 0 } : {}}>
          <div className={`${type !== 'home' && 'bg-line'}`}>
            <div className='square2'>
              <div className='square1'/>
            </div>
            <div className='circle-box'>
              <div className='circle'/>
            </div>
          </div>
          {props.children}
        </div>

        // <div className={styles.halfBox} style={type === 'away' ? { borderBottom: 0 } : {}}>
        //   <div className={`${type !== 'home' && styles.bgLine}`}>
        //     <div className={styles.square2}>
        //       <div className={styles.square1}></div>
        //     </div>
        //     <div className={styles.circleBox}>
        //       <div className={styles.circle}></div>
        //     </div>
        //   </div>
        //   {props.children}
        // </div>
      );
    };

    console.log('hometeam123', JSON.stringify(homeTeam))
    console.log('awayteam123', awayTeam)
    return (
      <div className='match-line-up-container'>
        <div className='container-title'></div>
        <div className='container-body'>
          <div className='team-container'>
            <div className='team-info'>
              <img className='team-logo' src={homeTeam?.logo || FallbackImage} alt={homeTeam?.name}/>
              <span className='team-name'>{homeTeam?.name}</span>
            </div>
            <span className='team-formation'>{formation.homeFormation}</span>
          </div>

          <div className='line-up-container'>
            <RenderGroundBox type="home">{renderLineup(homeFirstRound, 'home')}</RenderGroundBox>
            <RenderGroundBox type="away">{renderLineup(awayFirstRound, 'away')}</RenderGroundBox>
            <div className='mid-circle'>
              <div className='mid-inner'/>
            </div>
          </div>

          <div className='team-container'>
            <span className='team-formation'>{formation.awayFormation}</span>
            <div className='team-info'>
              <span className='team-name'>{awayTeam?.name}</span>
              <img className='team-logo' src={awayTeam?.logo || FallbackImage} alt={awayTeam?.name}/>
            </div>
          </div>
        </div>
      </div>
      // <div className={styles.backGreen}>
      //   {renderTeamInfo(homeTeam)}
      //   <div className={styles.groundBox}>
      //     <div className={`${styles.formationInfo} ${styles.t}`}>{formation.homeFormation}</div>

      //     <div className={`${styles.formationInfo} ${styles.f}`}>{formation.awayFormation}</div>
      //     <div className={styles.midCircle}>
      //       <div className={styles.midInner}></div>
      //     </div>
      //   </div>
      //   {renderTeamInfo(awayTeam)}
      // </div>
    );
  }),
);

export default MatchLineup;
