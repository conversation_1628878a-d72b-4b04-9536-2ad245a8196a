// .squad_container {
//   display: flex;
//   flex-direction: column;
//   width: 100%;
// }

// .squad_list_item {
//   display: flex;
//   flex-direction: row;
//   align-items: center;
//   width: 100%;
//   height: 100px;
// }

// .shirtNumber_column {
//   width: 68px;
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   font-size: 24px;
  
//   font-weight: 500;
//   color: #666666;
//   line-height: 24px;
// }

// .people_column {
//   flex: 1;
//   display: flex;
//   flex-direction: column;
//   justify-content: center;
//   height: 100%;
// }

// .people_avatar {
//   width: 58px;
//   height: 58px;
//   border: 2px solid #dbdbdb;
//   border-radius: 50%;
//   margin-right: 10px;
// }

// .people_countryName {
//   font-size: 24px;
  
//   font-weight: 500;
//   color: #999999;
//   line-height: 24px;
//   padding-top: 12px;
// }

// .people_name {
//   font-size: 24px;
  
//   font-weight: 500;
//   color: #333333;
//   line-height: 24px;
// }

// .markerValue_column {
//   font-size: 24px;
  
//   font-weight: 500;
//   color: #0f80da;
//   line-height: 24px;
//   text-align: right;
//   padding: 0 24px;
// }

// .list_item {
//   padding-left: 0;

//   :global {
//     .adm-list-item-content-main {
//       padding: 0;
//     }

//     .adm-list-item-content {
//       padding-right: 0 !important;
//     }

//     .adm-list-item {
//       padding-left: 0 !important;
//     }
//   }
// }

// .list_header {
//   width: 48px;
//   height: 24px;
//   font-size: 24px;
  
//   font-weight: 500;
//   color: #666666;
//   line-height: 24px;
// }

// .flex1 {
//   flex: 1;
//   width: 100%;
//   display: flex;
// }


.team-squad-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;

  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .container-body {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    background: #1e1e1e;
    padding: 10px;
    border-radius: 0px 0px 24px 24px;
    margin-bottom: 10px;

    .player-card {
      background-color: #272727;
      color: #fff;
      border-radius: 24px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      box-sizing: border-box;
      margin: 10px;
    
      .player-info {
        display: flex;
        align-items: center;
        justify-content: space-evenly;
        position: relative;

        .player-image-container {
          position: relative; 
          display: flex;
          justify-content: center;
          align-items: center;

          .player-image {
            height: 96px;
            width: 96px;
            border-radius: 50%;
            object-fit: contain;
            vertical-align: middle;
            margin: 10px;
          }

          .player-badge {
            position: absolute;
            bottom: 0;
            right: 0;
            background-color: #7892B7;
            border-radius: 50%;
            height: 30px;
            width: 30px;
            display: flex;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 14px;

            .adm-badge-content {
              padding: 0px;
            }
          }
        }
    
        .player-details {
          display: flex;
          flex-direction: column;
          margin-left: 10px;
          max-width: 180px;
  
          .player-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
            color: #fff;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          
          .player-country {
            display: flex;
            flex-direction: row;
            align-items: center;
  
            .player-country-text {
              font-size: 22px;
              color: #ccc;
            }
    
            .player-country-image {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 40px;
              height: 40px;
              border-radius: 50%;
              margin-right: 10px;
              overflow: hidden;
              background-size: cover;
            }
          }
        }
      }
    
      .player-extra-info {
        background-color: #121212; 
        width: 100%;
        height: 100%;
        color: #fff; 
        display: flex; 
        justify-content: space-between; 
        align-items: center;
        border-radius: 0px 0px 16px 16px;
    
        .player-footer {
          display: flex;
          flex-direction: column;
          padding: 5px 10px;
    
          .player-footer-label {
            font-size: 18px;
            color: #C0C5C9;
          }

          .player-footer-value {
            font-size: 22px;
            color: #fff;
          }
        }
      }
    }
  }
}
