import { ReactNode } from 'react';

import './SectionTitle.less';

interface Props {
  leftTitle?: string;
  rightTitle?: string;
  background?: string;
  onClick?: () => void;

  rightContent?(): ReactNode;
}

const SectionTitle = (props: Props) => {
  const { leftTitle, rightTitle, onClick, rightContent, background = '#F1F1F1' } = props;

  return (
    <div className="sectionTitle" style={{ background }}>
      {leftTitle ? <div className="leftTitle color-333">{leftTitle}</div> : ''}
      {rightContent ? (
        rightContent()
      ) : (
        <div onClick={onClick} className="right-area">
          {rightTitle ? <span className="right">{rightTitle}</span> : ''}
          {onClick ? <i className="iconfont iconjiantou rightjiantou"></i> : ''}
        </div>
      )}
    </div>
  );
};

export default SectionTitle;
