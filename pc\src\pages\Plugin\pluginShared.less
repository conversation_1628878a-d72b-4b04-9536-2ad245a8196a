// Shared styles for plugin components
.plugin-carousel-item {
  margin-bottom: 12px;
  border-radius: 16px;
  background: #2c2c2c;
  color: #fff;
  width: 250px !important;

  .container-title {
    font-weight: 600;
    padding: 6px;
    border-radius: 16px 16px 0px 0px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #63717a;

    .title-text {
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .action-btn {
      flex-shrink: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 28px;
      height: 28px;
      border-radius: 50%;
      border: 1px solid #fff;
      transition: all 0.3s ease;
      color: #fff;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.4);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }

      .anticon {
        font-size: 12px;
      }
    }
  }

  .container-body {
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 6px;
    border-radius: 0px 0px 16px 16px;
    color: #fff;
    height: 106px;

    &::before,
    &::after {
      content: '';
      position: absolute;
      height: 2px;
      width: 100%;
      background: linear-gradient(90deg, transparent, #03cfea, transparent);
      pointer-events: none;
    }

    &::before {
      top: 2px;
      left: 0;
      animation: neon-glow-left-to-right 5s linear infinite;
    }
  
    &::after {
      bottom: 2px;
      right: 0;
      animation: neon-glow-right-to-left 5s linear infinite;
    }

    .horizontal-content {
      display: flex;
      align-items: center;
      width: 100%;

      .match-time-section {
        width: 36px;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-right: 5px;

        .match-time {
          display: flex;
          flex-direction: column;
          text-align: center;
          font-size: 12px;
        }

        .match-status {
          font-size: 12px;
          font-weight: 600;
          color: red;
        }

        .live-timer {
          animation: twinkle 2.5s infinite;
        }
      }

      .team-section {
        display: flex;
        flex-direction: column;
        gap: 12px;
        justify-content: center;
        width: 180px;

        .home-team,
        .away-team {
          display: flex;
          align-items: center;
          gap: 6px;

          .team-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            object-fit: cover;
          }

          .team-name {
            font-size: 14px;
            color: #fff;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }

      .match-score-section {
        display: flex;
        flex-direction: column;
        gap: 12px;

        .match-score {
          text-align: center;
          font-weight: 500;
          font-size: 14px;
          color: #fff;
          width: 20px;
        }
      }
    }
    
    .betting-section {
      display: flex;
      gap: 8px;
      justify-content: space-evenly;
      margin: 4px 0px;

      .handicap-section,
      .odds-section {
        .pill {
          display: flex;
          align-items: center;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 20px;
          gap: 6px;
        }

        .label {
          font-size: 11px;
          font-weight: 500;
          color: #fff;
        }

        .handicap-value {
          text-align: center;
          font-weight: 500;
          font-size: 12px;
          color: #03cfea;
          width: 40px;
          padding: 2px 4px;
          border-radius: 4px;
          background: rgba(3, 207, 234, 0.1);
          border: 1px solid rgba(3, 207, 234, 0.3);

          &.home-handicap {
            color: #03cfea;
            border-top-left-radius: 20px;
            border-bottom-left-radius: 20px;
          }

          &.away-handicap {
            color: #ff6b6b;
            border-top-right-radius: 20px;
            border-bottom-right-radius: 20px;
            background: rgba(255, 107, 107, 0.1);
            border-color: rgba(255, 107, 107, 0.3);
          }
        }

        .odds-value {
          text-align: center;
          font-weight: 500;
          font-size: 12px;
          color: #03cfea;
          width: 40px;
          padding: 2px 4px;
          border-radius: 4px;
          background: rgba(3, 207, 234, 0.1);
          border: 1px solid rgba(3, 207, 234, 0.3);
          // text-align: center;
          // font-weight: 600;
          // font-size: 11px;
          // width: 35px;
          // padding: 2px 4px;
          // border-radius: 4px;
          // background: rgba(255, 215, 0, 0.1);
          // border: 1px solid rgba(255, 215, 0, 0.3);
          // color: #ffd700;

          &.home-odds {
            color: #03cfea;
            border-top-left-radius: 20px;
            border-bottom-left-radius: 20px;
            // color: #ffd700;
          }

          &.away-odds {
            color: #ff6b6b;
            border-top-right-radius: 20px;
            border-bottom-right-radius: 20px;
            background: rgba(255, 107, 107, 0.1);
            border-color: rgba(255, 107, 107, 0.3);
            // color: #ffa500;
            // background: rgba(255, 165, 0, 0.1);
            // border-color: rgba(255, 165, 0, 0.3);
          }
        }
      }
    }
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.3;
  }
}

@keyframes neon-glow-left-to-right {
  0% {
    opacity: 0;
    transform: scaleX(0.5) translateX(-50%);
  }
  50% {
    opacity: 1;
    transform: scaleX(1) translateX(0%);
  }
  100% {
    opacity: 0;
    transform: scaleX(0.5) translateX(50%);
  }
}

@keyframes neon-glow-right-to-left {
  0% {
    opacity: 0;
    transform: scaleX(0.5) translateX(50%);
  }
  50% {
    opacity: 1;
    transform: scaleX(1) translateX(0%);
  }
  100% {
    opacity: 0;
    transform: scaleX(0.5) translateX(-50%);
  }
}
