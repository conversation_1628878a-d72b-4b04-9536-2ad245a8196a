import './pluginVideo.less';
import './pluginShared.less'

import { GlobalConfig } from "iscommon/const/globalConfig";
import PluginBasketball from './pluginBasketball';
import PluginFootball from './pluginFootball';

/**
 * PluginVideo - Routes to the appropriate sport-specific plugin
 * based on GlobalConfig.pathname
 * 
 * This component acts as a router that determines which sport-specific
 * plugin to render based on the current sport context.
 */
const PluginVideo: React.FC = () => {
  // Determine which sport-specific plugin to render
  const sport = GlobalConfig.pathname; // 'football', 'basketball', etc.
  
  console.log('🎥 PluginVideo: Routing to sport-specific plugin:', sport);

  // Route to the appropriate sport-specific plugin
  switch(sport) {
    case 'basketball':
      return <PluginBasketball />;
    case 'football':
    default:
      return <PluginFootball />;
  }
};

export default PluginVideo;
