import './pluginVideo.less';
import './pluginShared.less'

import { Button, Carousel, Popover } from 'antd';
import { useCallback, useEffect, useState } from "react";
import { useHistory, useLocation } from "umi";

import { getCompetitionList } from "iscommon/api/home";
import { getHomeCompetitionMatchTime } from 'iscommon/utils/dataUtils';
import { GlobalConfig } from "iscommon/const/globalConfig";
import moment from "moment";
import { PlayCircleOutlined } from "@ant-design/icons";

const PluginVideo: React.FC = () => {
  const [liveMatches, setLiveMatches] = useState<any[]>([]);
  const history = useHistory();
  const location = useLocation();

  // Get proper live timer using the same logic as other components
  const getLiveTimer = (match: any) => {
    const { matchStatus, matchActiveTime, secondHalfKickOffTime } = match;
    const serverTime = Date.now() / 1000; // Current time in seconds

    const { statusText } = getHomeCompetitionMatchTime({
      serverTime,
      matchStatus,
      matchActiveTime,
      secondHalfKickOffTime,
      currentGameTab: 1,
    });

    return statusText;
  };

  const handleMatchClick = (match: any) => {
    const matchId = match.matchId || match.id;
    const searchParams = new URLSearchParams(location.search);
    const pluginParams = searchParams.toString();
    const basePath = `${GlobalConfig.pathname}-match`;

   history.replace(`${basePath}/${matchId}/MatchLive?${pluginParams}`);
  };

  const fetchLiveMatches = useCallback(async () => {
    try {
      const { competitions } = await getCompetitionList({ listType: 0 });
      const filtered = competitions
        .flatMap((comp: any) =>
          (comp.matches || [])
            .filter((match: any) => match.vlive === 1)
            .map((match: any) => ({ ...match, competitionName: comp.competitionName }))
        );
      setLiveMatches(filtered);
    } catch (e) {
      console.error('Failed to fetch live matches in LiveAndVideoCard:', e);
    }
  }, []);

  useEffect(() => {
    fetchLiveMatches(); // initial
    const interval = setInterval(fetchLiveMatches, 3000); // every 3s
    return () => clearInterval(interval);
  }, [fetchLiveMatches]);

  useEffect(() => {
    if (liveMatches.length > 0) {
      const firstMatch = liveMatches[0] as any;
      const currentMatchId = firstMatch.matchId || firstMatch.id;
      const urlParts = location.pathname.split('/');
      const currentUrlMatchId = urlParts[2]; // Assumes /football-match/:matchId/Overview

      if (currentMatchId !== currentUrlMatchId) {
        const searchParams = new URLSearchParams(location.search);
        const pluginParams = searchParams.toString();
        const basePath = '/football-match';

        history.replace(`${basePath}/${currentMatchId}/MatchLive?${pluginParams}`);
      }
    }
  }, [liveMatches, location.pathname]);

  return (
    <div className="plugin-video-container">
      <Carousel dots={false} swipeToSlide draggable infinite={false} variableWidth>
        {liveMatches.map((match: any) => {
          return (
            <div key={match.matchId} className="plugin-carousel-item">
              <div className="container-title">
                <span className='title-text'>{match.competitionName}</span>
                <Popover content={match.vlive === 1 ? 'Watch Live' : 'Watch Animation'} placement="top">
                  <Button
                    type="text"
                    icon={<PlayCircleOutlined />}
                    className="action-btn"
                    onClick={() => handleMatchClick(match)}
                  />
                </Popover>
              </div>
              <div className="container-body">
                <div className='horizontal-content'>
                  <div className='match-time-section'>
                    {match.matchStatus === 8 || match.matchStatus === 3 ? (
                      <div className="match-time-container">
                        <span className="match-status">{match.matchStatus === 8 ? 'FT' : 'HT'}</span>
                      </div>
                    ) : match.matchStatus === 1 ? (
                      <div className="match-time">
                        <span className="hour-time">{moment.unix(match.matchTime).format('HH:mm')}</span>
                        <span className="date-time">{moment.unix(match.matchTime).format('DD/MM')}</span>
                      </div>
                    ) : (
                      <div className="match-score-container">
                        <span className="match-status live-timer">{getLiveTimer(match)}</span>
                      </div>
                    )} 
                  </div>
                  <div className='team-section'>
                    <div className='home-team'>
                      <img className="team-icon" loading="lazy" src={match.homeTeam?.logo} alt={match.homeTeam.name}/>
                      <span className='team-name'>{match.homeTeam.name}</span>
                    </div>
                    <div className='away-team'>
                      <img className="team-icon" loading="lazy" src={match.awayTeam?.logo} alt={match.awayTeam.name}/>
                      <span className='team-name'>{match.awayTeam.name}</span>
                    </div>
                  </div>
                  <div className='match-score-section'>
                    <span className="match-score">{match.calculatedHomeScore}</span> 
                    <span className='match-score'>{match.calculatedAwayScore}</span>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </Carousel>
    </div>
  )
}
export default PluginVideo;