import './pluginVideo.less';
import './pluginShared.less'

import { Button, Carousel, Popover } from 'antd';
import { getHomeBasketBallMatchTimeText, getHomeCompetitionMatchTime } from 'iscommon/utils/dataUtils';
import { useCallback, useEffect, useState } from "react";
import { useHistory, useLocation } from "umi";

import { getCompetitionList } from "iscommon/api/home";
import { GlobalConfig } from "iscommon/const/globalConfig";
import moment from "moment";
import { PlayCircleOutlined } from "@ant-design/icons";

const PluginVideo: React.FC = () => {
  const [liveMatches, setLiveMatches] = useState<any[]>([]);
  const history = useHistory();
  const location = useLocation();

  // Get proper live timer using sport-specific logic
  const getLiveTimer = (match: any) => {
    const serverTime = Date.now() / 1000; // Current time in seconds

    // Use sport-specific logic based on GlobalConfig.pathname
    if (GlobalConfig.pathname === 'basketball') {
      // Basketball uses remainSeconds and getHomeBasketBallMatchTimeText
      const { matchStatus, remainSeconds } = match;
      const { statusText } = getHomeBasketBallMatchTimeText({
        matchStatus,
        remainSeconds,
        serverTime,
        currentGameTab: 1,
      });
      return statusText;
    } else {
      // Football and other sports use matchActiveTime and getHomeCompetitionMatchTime
      const { matchStatus, matchActiveTime, secondHalfKickOffTime } = match;
      const { statusText } = getHomeCompetitionMatchTime({
        serverTime,
        matchStatus,
        matchActiveTime,
        secondHalfKickOffTime,
        currentGameTab: 1,
      });
      return statusText;
    }
  };

  const handleMatchClick = (match: any) => {
    const matchId = match.matchId || match.id;
    const searchParams = new URLSearchParams(location.search);
    const pluginParams = searchParams.toString();

    // Build the complete new path from root, not relative to current path
    const newPath = `/${GlobalConfig.pathname}-match/${matchId}/MatchLive?${pluginParams}`;

    console.log('🔄 Navigating to:', newPath);
    history.replace(newPath);
  };

  const fetchLiveMatches = useCallback(async () => {
    try {
      const { competitions } = await getCompetitionList({ listType: 0 });
      const filtered = competitions
        .flatMap((comp: any) =>
          (comp.matches || [])
            .filter((match: any) => match.vlive === 1)
            .map((match: any) => ({ ...match, competitionName: comp.competitionName }))
        );
      setLiveMatches(filtered);
    } catch (e) {
      console.error('Failed to fetch live matches in LiveAndVideoCard:', e);
    }
  }, []);

  useEffect(() => {
    fetchLiveMatches(); // initial
    const interval = setInterval(fetchLiveMatches, 3000); // every 3s
    return () => clearInterval(interval);
  }, [fetchLiveMatches]);

  useEffect(() => {
    if (liveMatches.length > 0) {
      const urlParts = location.pathname.split('/');
      const currentUrlMatchId = urlParts[2]; // Assumes /football-match/:matchId/MatchLive

      // Check if current URL match ID exists in the live matches
      const currentMatchExists = liveMatches.some((match: any) => {
        const matchId = match.matchId || match.id;
        return matchId === currentUrlMatchId;
      });

      // Only auto-navigate if:
      // 1. No match ID in URL, OR
      // 2. Current match ID doesn't exist in live matches (match ended/not available)
      if (!currentUrlMatchId || !currentMatchExists) {
        const firstMatch = liveMatches[0] as any;
        const firstMatchId = firstMatch.matchId || firstMatch.id;
        const searchParams = new URLSearchParams(location.search);
        const pluginParams = searchParams.toString();

        // Build the complete new path from root
        const newPath = `/${GlobalConfig.pathname}-match/${firstMatchId}/MatchLive?${pluginParams}`;

        console.log('🔄 Auto-navigating to first available match:', newPath);
        console.log('📝 Reason:', !currentUrlMatchId ? 'No match ID in URL' : 'Current match not in live matches');
        history.replace(newPath);
      } else {
        console.log('✅ Current match exists in live matches, staying on current page');
      }
    }
  }, [liveMatches, location.pathname, history]);

  return (
    <div className="plugin-video-container">
      <Carousel dots={false} swipeToSlide draggable infinite={false} variableWidth>
        {liveMatches.map((match: any) => {
          return (
            <div key={match.matchId} className="plugin-carousel-item">
              <div className="container-title">
                <span className='title-text'>{match.competitionName}</span>
                <Popover content={match.vlive === 1 ? 'Watch Live' : 'Watch Animation'} placement="top">
                  <Button
                    type="text"
                    icon={<PlayCircleOutlined />}
                    className="action-btn"
                    onClick={() => handleMatchClick(match)}
                  />
                </Popover>
              </div>
              <div className="container-body">
                <div className='horizontal-content'>
                  <div className='match-time-section'>
                    {match.matchStatus === 8 || match.matchStatus === 3 ? (
                      <div className="match-time-container">
                        <span className="match-status">{match.matchStatus === 8 ? 'FT' : 'HT'}</span>
                      </div>
                    ) : match.matchStatus === 1 ? (
                      <div className="match-time">
                        <span className="hour-time">{moment.unix(match.matchTime).format('HH:mm')}</span>
                        <span className="date-time">{moment.unix(match.matchTime).format('DD/MM')}</span>
                      </div>
                    ) : (
                      <div className="match-score-container">
                        <span className="match-status live-timer">{getLiveTimer(match)}</span>
                      </div>
                    )} 
                  </div>
                  <div className='team-section'>
                    <div className='home-team'>
                      <img className="team-icon" loading="lazy" src={match.homeTeam?.logo} alt={match.homeTeam.name}/>
                      <span className='team-name'>{match.homeTeam.name}</span>
                    </div>
                    <div className='away-team'>
                      <img className="team-icon" loading="lazy" src={match.awayTeam?.logo} alt={match.awayTeam.name}/>
                      <span className='team-name'>{match.awayTeam.name}</span>
                    </div>
                  </div>
                  <div className='match-score-section'>
                    <span className="match-score">{match.calculatedHomeScore}</span> 
                    <span className='match-score'>{match.calculatedAwayScore}</span>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </Carousel>
    </div>
  )
}
export default PluginVideo;