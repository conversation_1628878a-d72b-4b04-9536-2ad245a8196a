import './pluginVideo.less';
import './pluginShared.less'

import { GlobalConfig } from "iscommon/const/globalConfig";
import PluginBasketball from './pluginBasketball';
import PluginFootball from './pluginFootball';

/**
 * PluginVideo - Routes to the appropriate sport-specific plugin
 * based on GlobalConfig.pathname
 * 
 * This component acts as a router that determines which sport-specific
 * plugin to render based on the current sport context.
 */
const PluginVideo: React.FC = () => {
  // Determine which sport-specific plugin to render
  const sport = GlobalConfig.pathname; // 'football', 'basketball', etc.
  
  console.log('🎥 PluginVideo: Routing to sport-specific plugin:', sport);

  // Route to the appropriate sport-specific plugin with carousel-only mode
  switch(sport) {
    case 'basketball':
      return <PluginBasketball displayMode="carousel-only" />;
    case 'football':
    default:
      return <PluginFootball displayMode="carousel-only" />;
  }
};

export default PluginVideo;
