import './pluginVideo.less';
import './pluginShared.less'

import { Button, Carousel, Popover } from 'antd';
import { getHomeBasketBallMatchTimeText, getHomeCompetitionMatchTime } from 'iscommon/utils/dataUtils';
import { useCallback, useEffect, useState } from "react";
import { useHistory, useLocation } from "umi";

import { getCompetitionList } from "iscommon/api/home";
import { GlobalConfig } from "iscommon/const/globalConfig";
import moment from "moment";
import { PlayCircleOutlined } from "@ant-design/icons";

const PluginVideo: React.FC = () => {
  const [liveMatches, setLiveMatches] = useState<any[]>([]);
  const history = useHistory();
  const location = useLocation();

  // Get proper live timer using sport-specific logic
  const getLiveTimer = (match: any) => {
    const serverTime = Date.now() / 1000; // Current time in seconds

    // Use sport-specific logic based on GlobalConfig.pathname
    if (GlobalConfig.pathname === 'basketball') {
      // Basketball uses remainSeconds and getHomeBasketBallMatchTimeText
      const { matchStatus, remainSeconds } = match;
      const { statusText } = getHomeBasketBallMatchTimeText({
        matchStatus,
        remainSeconds,
        serverTime,
        currentGameTab: 1,
      });
      return statusText;
    } else {
      // Football and other sports use matchActiveTime and getHomeCompetitionMatchTime
      const { matchStatus, matchActiveTime, secondHalfKickOffTime } = match;
      const { statusText } = getHomeCompetitionMatchTime({
        serverTime,
        matchStatus,
        matchActiveTime,
        secondHalfKickOffTime,
        currentGameTab: 1,
      });
      return statusText;
    }
  };

  // Extract and format handicap values and odds data from match odds
  const getHandicapAndOdds = (match: any) => {
    try {
      // Get the asia odds from matchOdds
      const asiaOdds = match.matchOdds?.find((odds: any) => odds.oddsType === 'asia');
      if (!asiaOdds || !asiaOdds.handicap || !asiaOdds.oddsData) {
        return {
          homeHandicap: null,
          awayHandicap: null,
          homeOdds: null,
          awayOdds: null
        };
      }

      const handicap = asiaOdds.handicap; // e.g., "0/0.5"
      const oddsData = asiaOdds.oddsData; // Array with odds values

      console.log('🎯 Processing handicap and odds:', { handicap, oddsData });

      // Split by "/" to get the handicap value
      const parts = handicap.split('/');
      if (parts.length !== 2) {
        return {
          homeHandicap: null,
          awayHandicap: null,
          homeOdds: null,
          awayOdds: null
        };
      }

      const hdpValue = parseFloat(parts[1]); // 0.5, 0, 1.5, etc.

      // Format handicap values - display even when hdpValue is 0
      const homeHandicap = hdpValue === 0 ? '0' : (hdpValue > 0 ? `+${hdpValue}` : `${hdpValue}`);
      const awayHandicap = hdpValue === 0 ? '0' : (hdpValue > 0 ? `-${hdpValue}` : `+${Math.abs(hdpValue)}`);

      // Extract odds data[0] and data[2]
      const homeOdds = oddsData[0] != null ? parseFloat(Number(oddsData[0]).toFixed(2)) : null;
      const awayOdds = oddsData[2] != null ? parseFloat(Number(oddsData[2]).toFixed(2)) : null;

      console.log('🎯 Formatted handicaps and odds:', {
        homeHandicap,
        awayHandicap,
        homeOdds,
        awayOdds
      });

      return { homeHandicap, awayHandicap, homeOdds, awayOdds };
    } catch (error) {
      console.error('❌ Error processing handicap and odds:', error);
      return {
        homeHandicap: null,
        awayHandicap: null,
        homeOdds: null,
        awayOdds: null
      };
    }
  };

  const handleMatchClick = (match: any) => {
    const matchId = match.matchId || match.id;
    const searchParams = new URLSearchParams(location.search);
    const pluginParams = searchParams.toString();

    // Build the complete new path from root, not relative to current path
    const newPath = `/${GlobalConfig.pathname}-match/${matchId}/MatchLive?${pluginParams}`;

    console.log('🔄 Navigating to:', newPath);
    history.replace(newPath);
  };

  const fetchLiveMatches = useCallback(async () => {
    try {
      const { competitions } = await getCompetitionList({ listType: 0 });
      const filtered = competitions
        .flatMap((comp: any) =>
          (comp.matches || [])
            .filter((match: any) => match.vlive === 1)
            .map((match: any) => ({ ...match, competitionName: comp.competitionName }))
        );
      setLiveMatches(filtered);
    } catch (e) {
      console.error('Failed to fetch live matches in LiveAndVideoCard:', e);
    }
  }, []);

  useEffect(() => {
    fetchLiveMatches(); // initial
    const interval = setInterval(fetchLiveMatches, 3000); // every 3s
    return () => clearInterval(interval);
  }, [fetchLiveMatches]);

  useEffect(() => {
    if (liveMatches.length > 0) {
      const urlParts = location.pathname.split('/');
      const currentUrlMatchId = urlParts[2]; // Assumes /football-match/:matchId/MatchLive

      // Check if current URL match ID exists in the live matches
      const currentMatchExists = liveMatches.some((match: any) => {
        const matchId = match.matchId || match.id;
        return matchId === currentUrlMatchId;
      });

      // Only auto-navigate if:
      // 1. No match ID in URL, OR
      // 2. Current match ID doesn't exist in live matches (match ended/not available)
      if (!currentUrlMatchId || !currentMatchExists) {
        const firstMatch = liveMatches[0] as any;
        const firstMatchId = firstMatch.matchId || firstMatch.id;
        const searchParams = new URLSearchParams(location.search);
        const pluginParams = searchParams.toString();

        // Build the complete new path from root
        const newPath = `/${GlobalConfig.pathname}-match/${firstMatchId}/MatchLive?${pluginParams}`;

        console.log('🔄 Auto-navigating to first available match:', newPath);
        console.log('📝 Reason:', !currentUrlMatchId ? 'No match ID in URL' : 'Current match not in live matches');
        history.replace(newPath);
      } else {
        console.log('✅ Current match exists in live matches, staying on current page');
      }
    }
  }, [liveMatches, location.pathname, history]);

  console.log('inside here123')
  return (
    <div className="plugin-video-container">
      <Carousel dots={false} swipeToSlide draggable infinite={false} variableWidth>
        {liveMatches.map((match: any) => {
          return (
            <div key={match.matchId} className="plugin-carousel-item">
              <div className="container-title">
                <span className='title-text'>{match.competitionName}</span>
                <Popover content={match.vlive === 1 ? 'Watch Live' : 'Watch Animation'} placement="top">
                  <Button
                    type="text"
                    icon={<PlayCircleOutlined />}
                    className="action-btn"
                    onClick={() => handleMatchClick(match)}
                  />
                </Popover>
              </div>
              <div className="container-body">
                <div className='horizontal-content'>
                  <div className='match-time-section'>
                    {(() => {
                      // Handle sport-specific status codes for live matches only
                      const isBasketball = GlobalConfig.pathname === 'basketball';
                      const isFootballHalfTime = !isBasketball && match.matchStatus === 3; // HT only for football
                      const isNotStarted = match.matchStatus === 1; // Not started is same for both sports

                      if (isFootballHalfTime) {
                        return (
                          <div className="match-time-container">
                            <span className="match-status">HT</span>
                          </div>
                        );
                      } else if (isNotStarted) {
                        return (
                          <div className="match-time">
                            <span className="hour-time">{moment.unix(match.matchTime).format('HH:mm')}</span>
                            <span className="date-time">{moment.unix(match.matchTime).format('DD/MM')}</span>
                          </div>
                        );
                      } else {
                        return (
                          <div className="match-score-container">
                            <span className="match-status live-timer">{getLiveTimer(match)}</span>
                          </div>
                        );
                      }
                    })()}
                  </div>
                  <div className='team-section'>
                    <div className='home-team'>
                      <img className="team-icon" loading="lazy" src={match.homeTeam?.logo} alt={match.homeTeam.name}/>
                      <span className='team-name'>{match.homeTeam.name}</span>
                    </div>
                    <div className='away-team'>
                      <img className="team-icon" loading="lazy" src={match.awayTeam?.logo} alt={match.awayTeam.name}/>
                      <span className='team-name'>{match.awayTeam.name}</span>
                    </div>
                  </div>
                  <div className='match-score-section'>
                    <span className="match-score">{match.calculatedHomeScore}</span>
                    <span className='match-score'>{match.calculatedAwayScore}</span>
                  </div>
                  {(() => {
                    const { homeHandicap, awayHandicap, homeOdds, awayOdds } = getHandicapAndOdds(match);
                    // Display betting section if we have handicap data (including '0') or odds data
                    if (homeHandicap !== null && awayHandicap !== null) {
                      return (
                        <div className='betting-section'>
                          <div className='handicap-section'>
                            <span className="handicap-value home-handicap">{homeHandicap}</span>
                            <span className="handicap-value away-handicap">{awayHandicap}</span>
                          </div>
                          {homeOdds !== null && awayOdds !== null && (
                            <div className='odds-section'>
                              <span className="odds-value home-odds">{homeOdds}</span>
                              <span className="odds-value away-odds">{awayOdds}</span>
                            </div>
                          )}
                        </div>
                      );
                    }
                    return null;
                  })()}
                </div>
              </div>
            </div>
          );
        })}
      </Carousel>
    </div>
  )
}
export default PluginVideo;