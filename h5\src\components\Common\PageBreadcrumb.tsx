import React, { useMemo } from 'react';
// import { Helmet } from 'react-helmet';
import { Link } from 'umi';

import './PageBreadcrumb.less';

interface Props {
  crumbs: {
    name: string;
    url?: string;
    meta?: {
      title?: string;
      description?: string;
    };
  }[];
  showCrumb?: boolean;
  style?: React.CSSProperties;
}

interface HelmetComponentProps {
  title: string;
  description: string;
}

// const HelmetComponent = React.memo<HelmetComponentProps>(({ title, description }) => {
//   return (
//     // @ts-ignore
//     <Helmet>
//       <title>{title}</title>
//       <meta name="description" content={description} />
//       <meta property="og:title" content={title} />
//       <meta property="og:description" content={description} />
//       <meta property="og:url" content={window.location.href} />
//       <link rel="canonical" href={window.location.href} />
//     </Helmet>
//   );
// });

const PageBreadcrumb = (props: Props) => {
  const { crumbs = [], showCrumb = true, style } = props;
  const list = useMemo(() => crumbs.filter((i) => i.name), [crumbs]);
  const title = useMemo(() => {
    if (crumbs[crumbs.length - 1]?.meta?.title) {
      return crumbs[crumbs.length - 1]?.meta?.title || '';
    }
    return list.map((item) => item.name).join(', ');
  }, [crumbs, list]);
  const description = useMemo(() => {
    if (crumbs[crumbs.length - 1]?.meta?.description) {
      return crumbs[crumbs.length - 1]?.meta?.description || '';
    }
    return crumbs?.[0]?.meta?.description || '';
  }, [crumbs]);

  return (
    <>
      {/* <HelmetComponent title={title} description={description} /> */}
      {showCrumb ? (
        <div className="crumbs" style={style}>
          {crumbs.map((item, index) => {
            const className = `crumbsItem ${item.url ? 'hl' : ''}`;
            return (
              <div key={item.name} className="crumbItemWrap text-overflow">
                {index > 0 && <div className="arrow">{'>'}</div>}
                {item.url ? (
                  <Link to={item.url || ''} className={className}>
                    {item.name}
                  </Link>
                ) : (
                  <div className={className}>{item.name}</div>
                )}
              </div>
            );
          })}
        </div>
      ) : null}
    </>
  );
};

export default PageBreadcrumb;
