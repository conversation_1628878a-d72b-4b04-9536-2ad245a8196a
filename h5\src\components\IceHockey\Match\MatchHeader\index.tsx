import SmallballMatchHeader from '@/components/SmallGameCommon/Match/MatchHeader';
import { IckHockeyInLiveStatusEnum, IckHockeyMatchStatusCodeToText } from 'iscommon/const/iceHockey/constant';
import { TennisStatusCodeEnum } from 'iscommon/const/tennis/constant';
import { getIceHockeyScoreList } from 'iscommon/utils/dataUtils';
import { useMemo } from 'react';

const MatchHeader = () => {
  const getHomeBasketBallMatchTimeText = (matchHeaderInfo) => {
    const { matchStatus } = matchHeaderInfo;
    const { statusText, isIng } = useMemo(() => {
      return {
        statusText: (IckHockeyMatchStatusCodeToText as any)[matchStatus],
        isIng: IckHockeyInLiveStatusEnum.includes(matchStatus),
      };
    }, [matchStatus]);
    return {statusText, isIng}
  };

  return (
    <SmallballMatchHeader
      checkIsIng={(matchStatus) => IckHockeyInLiveStatusEnum.includes(matchStatus)}
      getHomeScore={({ scores, matchStatus }) => {
        const scoreList = getIceHockeyScoreList(matchStatus, scores);
        return scoreList?.ft.h || 0
      }}
      getAwayScore={({ scores, matchStatus }) => {
        const scoreList = getIceHockeyScoreList(matchStatus, scores);
        return scoreList?.ft.w || 0
      }}
      notStartedCode={TennisStatusCodeEnum.NotStarted}
      getHomeBasketBallMatchTimeText={getHomeBasketBallMatchTimeText}
    />
  );
};

export default MatchHeader;
