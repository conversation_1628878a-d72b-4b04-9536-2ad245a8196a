import './HomeLoading.less';

export default function HomeLoading() {
  return (
    <div className="matchListSke">
      {[0, 1, 2].map((item) => (
        <div key={item} className="w100 itemske">
          {[0, 1, 2].map((s) => (
            <div key={s} className="w100 flex tab">
              <div className="time h100 flex align-center justify-center">
                <div className="time_content skeleton"></div>
              </div>
              <div className="team h100">
                <div className="flex align-center skeleton-rows" style={{ height: '50%' }}>
                  <div className="img skeleton"></div> 
                  <div className="teamName skeleton"></div>
                </div>
                <div className="flex align-center skeleton-rows" style={{ height: '50%' }}>
                  <div className="img skeleton"></div>
                  <div className="teamName skeleton"></div>
                </div>
                <div className="myicon skeleton"></div>
              </div>
              <div className="score h100 flex align-center justify-center skeleton-rows">
                <div className="score_content skeleton"></div>
              </div>
            </div>
          ))}
        </div>
      ))}
    </div>
  )
};