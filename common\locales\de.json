{"All": "Alle", "Live": "LIVE", "LiveH5": "LIVE", "MatchLive": "LIVE", "TimeSort": "Nach Zeit sortieren", "SortByTime": "Nach Zeit sortieren", "AllGames": "ALLE SPIELE", "Leagues": "LIGEN", "h5_Leagues": "LIGEN", "Today": "<PERSON><PERSON>", "Cancel": "Abbrechen", "Popular": "Beliebt", "Settings": "Einstellungen", "Language": "<PERSON><PERSON><PERSON>", "Overview": "Übersicht", "LiveOverview": "Übersicht", "Standings": "<PERSON><PERSON><PERSON>", "Stats": "Statistik", "Transfer": "Transfer", "Champions": "<PERSON><PERSON>", "TeamChampions": "<PERSON><PERSON>", "teamChampions": "<PERSON><PERSON>", "Football": "FUßBALL", "Basketball": "BASKETBALL", "Baseball": "Baseball", "Icehockey": "Hockey", "Tennis": "TENNIS", "Volleyball": "VOLLEYBALL", "Esports": "ESPORTS", "Handball": "HANDBALL", "Cricket": "CRICKET", "WaterPolo": "WASSER POLO", "TableTennis": "Tischtennis", "Snooker": "SNOOKER", "Badminton": "BADMINTON", "BusinessCooperation": "Unternehmenskooperation", "TermsOfService": "Nutzungsbestimmungen", "PrivacyPolicy": "Datenschutzerklärung", "Players": "SPIELER", "ForeignPlayers": "Ausländische Spieler", "NumberOfTeams": "Anzahl der Mannschaften", "YellowCards": "<PERSON><PERSON><PERSON>", "RedCards": "<PERSON><PERSON>", "Capacity": "Veranstaltungsort-Kapazität", "City": "Stadt", "Info": "Infos", "Matches": "SPIELE", "Team": "Team", "Teams": "Mannschaften", "Goals": "<PERSON><PERSON>", "Assists": "Vorlagen", "assists": "Vorlagen", "Home": "Heimspiele", "Away": "Auswärts", "topScorers": "Topscorer", "TopScorers": "Topscorer", "homeTopScorers": "Topscorer", "season": "<PERSON><PERSON>", "Season": "<PERSON><PERSON>", "ShotsOnTarget": "Schüsse aufs Tor", "Clearances": "Geklärt", "Tackles": "Tackles", "keyPasses": "Wichtige Pässe", "KeyPasses": "Wichtige Pässe", "Fouls": "Fouls", "totalFouls": "Fouls", "WasFouled": "Gefoult worden", "Penalty": "Strafe", "MinutesPlayed": "Gespielte Minuten", "BasketballMinutesPlayed": "Gespielte Minuten", "Interceptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Steals": "Steals", "steals": "Steals", "Passes": "Vorbeigehen", "Saves": "Paraden", "BlockedShots": "Geblockte Schüsse", "Signed": "Unterschrieben", "league": "", "offensiveData": "Angriff-Daten", "defenseData": "Verteidigung-Daten", "otherData": "<PERSON><PERSON>", "ballPossession": "<PERSON><PERSON><PERSON>", "shotsPerGame": "Schüsse pro Spiel", "ShotsPerGame": "Schüsse pro Spiel", "keyPassesPerGame": "Wichtige Pässe pro Spiel", "accurateLongBallsPerGame": "Genaue lange Bälle pro Spiel", "accurateCrossesPerGame": "Angekommene Flanken pro Spiel", "tacklesPerGame": "Tacklings pro Spiel", "TacklesPerGame": "Tacklings pro Spiel", "interceptionsPerGame": "<PERSON><PERSON><PERSON><PERSON>gene Bälle pro Spiel", "InterceptionsPerGame": "<PERSON><PERSON><PERSON><PERSON>gene Bälle pro Spiel", "clearancesPerGame": "Abgewehrte Bälle pro Spiel", "ClearancesPerGame": "Abgewehrte Bälle pro Spiel", "blockedShotsPerGame": "Geblockte Schüsse pro Spiel", "turnoversPerGame": "Turnover pro Spiel", "foulsPerGame": "Fouls pro Spiel", "scoringFrequencyFiveGoals": "", "Coach": "Trainer", "Goalkeeper": "<PERSON><PERSON>", "Stadium": "Stadion", "Login": "Einloggen", "Corner": "<PERSON><PERSON>", "ShotsOffTarget": "Schüsse neben das Tor", "H2H": "H2H", "Date": "Datum", "OwnGoal": "Eigentor", "PenaltyMissed": "<PERSON><PERSON><PERSON> verfehlt", "SecondYellow": "Zweite gelbe Karte", "Odds": "<PERSON>uo<PERSON>", "attacks": "Angriff", "Started": "Startelf", "Chat": "Cha<PERSON>", "Strengths": "<PERSON><PERSON><PERSON><PERSON>", "Weaknesses": "Schwächen", "Group": "Gruppe", "Birthday": "Geburtstag", "Club": "Club", "MainPosition": "Hauptposition", "PassesAccuracy": "", "Preseason": "", "RegularSeason": "", "PointsPerGame": "Punkte pro Spiel", "Glossary": "Glossar", "h5Glossary": "Glossar", "Career": "Karriere", "Bench": "<PERSON>f der Bank", "ReboundsPerGame": "Rebounds pro Spiel", "AssistsPerGame": "Vorlagen pro Spiel", "OddsFormat": "Quoten-Format", "Squad": "Aufstellungen", "TotalMarketValue": "Gesamter Marktwert", "Rounds": "<PERSON><PERSON><PERSON>", "LowerDivision": "Untere Liga", "TeamStats": "Team-Statistiken", "GoalsPk": "<PERSON><PERSON>(PK)", "Crosses": "<PERSON><PERSON><PERSON>", "CrossesAccuracy": "Pass den Ball erfolgreich", "Dribble": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "DribbleSucc": "Außerordentliche Erfolg", "LongBalls": "<PERSON>", "LongBallsAccuracy": "Lange Pässe Erfolgsquote", "Duels": "Fouls", "DuelsWon": "Sauberes Blatt", "Dispossessed": "Paraden", "Punches": "<PERSON><PERSON>", "RunsOut": "Herauslaufen", "RunsOutSucc": "Geblockte Schüsse", "GoodHighClaim": "Passing Genauigkeit", "Loan": "<PERSON><PERSON><PERSON>", "EndOfLoan": "Ende der Ausleihe", "Unknown": "unbekannt", "AverageAge": "Durchschnittliches Alter", "cornersPerGame": "Ecken pro Spiel", "goalsConceded": "Gegentore", "Defender": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Discipline": "<PERSON><PERSON><PERSON><PERSON>", "Pass": "", "FB_Login": "<PERSON><PERSON> mit Facebook", "Google_Login": "<PERSON><PERSON> mit Google", "Substitutes": "Ersatzspieler", "PenaltyKick": "", "ShareYourViews": "Teilen Sie Ihre Ansichten mit", "Nodata": "<PERSON><PERSON>", "Foot": "<PERSON><PERSON>", "dangerousAttack": "", "venue": "Stadion", "playerStatistics": "Spielerstatistik", "TotalPlayed": "<PERSON><PERSON><PERSON> gespielt", "MinutesPerGame": "Minuten pro Spiel", "GoalsFrequency": "", "GoalsPerGame": "<PERSON><PERSON>", "Arrivals": "Neuzugänge", "Departures": "<PERSON><PERSON>gä<PERSON>", "LeftFoot": "Links", "RightFoot": "<PERSON><PERSON><PERSON>", "LatestTransfers": "Letzte Transfers", "DraftInfo": "Info Draft NBA", "OK": "OK", "AsianHandicap": "", "TotalGoals": "", "AmFootballTotalGames": "", "TotalCorners": "", "OverBall": "<PERSON><PERSON> als", "Over": "<PERSON><PERSON> als", "h5Over": "<PERSON><PERSON> als", "UnderBall": "<PERSON><PERSON> als", "Under": "<PERSON><PERSON> als", "h5Under": "<PERSON><PERSON> als", "OtherLeagues": "Andere Ligen [A-Z]", "GoalPopup": "", "FullStandings": "Vollständige Rangliste", "teamWeek": "Team der Woche", "weekTop": "Team der Woche", "TeamOfTheWeek": "Team der Woche", "round": "<PERSON><PERSON>", "Released": "Entlassen", "Retirement": "", "Draft": "Draft", "TransferIn": "", "TransferOut": "", "MarketValue": "Marktwert", "Salary": "<PERSON><PERSON><PERSON><PERSON>", "Next": "Nächste", "Position": "Position", "CTR": "Aktueller Transfer-Datensatz.", "FoulBall": "", "FreeKick": "", "GoalKick": "", "Midfield": "", "HalftimeScore": "", "InjuryTime": "", "OvertimeIsOver": "", "PenaltyKickEnded": "", "Last": "Letz<PERSON>", "Win": "Siegesserie", "Draw": "Unentschieden", "Lose": "Niederlagenserie", "Lineup": "", "Substitution": "<PERSON><PERSON><PERSON>", "Offsides": "Abseits", "Playoffs": "", "Qualifier": "", "GroupStage": "", "Rebounds": "Rebounds", "rebounds": "Rebounds", "OffensiveRebounds": "Offensivrebounds", "offensiveRebounds": "Offensivrebounds", "DefensiveRebounds": "Defensivrebounds", "defensiveRebounds": "Defensivrebounds", "Turnovers": "Turnover", "turnovers": "Turnover", "Blocks": "Blocks", "blocks": "Blocks", "BoxScore": "Box score", "Foul": "F<PERSON>l", "FreeThrows": "Freiwürfe", "freeThrowsScored": "Freiwürfe", "fieldGoalsScored": "", "fieldGoalsTotal": "", "threePointersScored": "", "threePointersTotal": "", "freeThrowsTotal": "", "Finished": "<PERSON><PERSON>", "Scheduled": "Ausstattung", "Favourite": "", "OddsMarkets": "Odds Markets", "Search": "", "Timezone": "", "SoccerGoalReminderSettings": "", "RemindersOnlyForRacesToWatch": "", "GoalSound": "", "LiveScore": "", "Schedule": "Zeitplan", "Rugby": "", "FooterContentFootball": "IGScore Football LiveScore bietet Ihnen beispiellose Fußball-Live-Ergebnisse und Fußballergebnisse aus über 2600 Fußballligen, Pokalen und Turnieren. Erhalten Sie Live-Ergebnisse, Halbzeit- und Vollzeit-Fußballergebnisse, Torschützen und Assistenten, Karten, Auswechslungen, Spielstatistiken und Live-Streams aus der Premier League, der La Liga, der Serie A, der Bundesliga, der Ligue 1, der Eredivisie, der russischen Premier League, Brasileirão, MLS, Super Lig und Meisterschaft auf igscore.net. IGScore bietet allen Fußballfans Live-Ergebnisse, Fußball-LiveScore, Fußballergebnisse, Ranglisten und Spiele für Ligen, Pokale und Turniere und nicht nur aus den beliebtesten Fußballligen wie England Premier League, Spanien La Liga, Italien Serie A, Deutschland Bundesliga, Frankreich Ligue 1, aber auch aus einer Vielzahl von Fußballländern auf der ganzen Welt, darunter aus Nord- und Südamerika, Asien und Afrika. Unsere Fußball-LiveScore-Scorecards werden live in Echtzeit aktualisiert, um Sie über alle heute stattfindenden LiveScore-Updates für Fußballspiele sowie über Fußball-LiveScore-Ergebnisse für alle abgeschlossenen Fußballspiele für jede Fußball- und Fußballliga auf dem Laufenden zu halten. Auf der Spieleseite können Sie mit unseren Fußball-Scorecards die Ergebnisse früherer Spiele für alle zuvor gespielten Spiele für jeden Fußballwettbewerb anzeigen. Holen Sie sich alle Ihre Fußball-Live-Ergebnisse auf igscore.net!", "FooterContentBasketball": "IGScore Basketball LiveScore bietet Ihnen Live-Ergebnisse, Ergebnisse, Tabellen, Statistiken, Spiele, Tabellen und frühere Ergebnisse der NBA-Liga nach Quartalen, Halbzeit oder Endergebnis. IGScore bietet Punkteservice von mehr als 200 Basketballwettbewerben aus der ganzen Welt (wie NCAA, ABA League, Baltische Liga, Euroleague, nationale Basketballligen). Hier finden Sie nicht nur Live-Ergebnisse, Quartalsergebnisse, Endergebnisse und Aufstellungen, sondern auch die Anzahl der 2- und 3-Punkte-Versuche, Freiwürfe, <PERSON><PERSON><PERSON>ßp<PERSON>zentsätze, Re<PERSON>s, <PERSON><PERSON><PERSON><PERSON>, St<PERSON>s, per<PERSON><PERSON><PERSON><PERSON>, Spielverlauf und Spielerstatistiken .Auf IGScore Basketball LiveScore können Sie Basketball online ansehen, indem Sie einfach darauf klicken, und Sie erhalten die Online-Berichterstattung über die Spiele der Top-Ligen. Auf der Spielseite befindet sich auch die Tabelle mit allen Basketballstatistiken zu den neuesten Spielen der Mannschaften. Unsere Basketball-Scorecards werden in Echtzeit in Echtzeit aktualisiert, um Sie über alle heute stattfindenden Basketballergebnisse auf dem Laufenden zu halten und Ihnen zu ermöglichen, vergangene Spielergebnisse für alle zuvor gespielten Spiele für jeden Basketballwettbewerb anzuzeigen. Holen Sie sich alle Ihre NBA-Live-Ergebnisse auf igscore.net! Verfolgen Sie NBA-Livescores, NBA-Spiele, NBA-Tabellen und Teamseiten!", "FooterContentAmFootball": "IGScore american football live score gibt Ihnen alle Ergebnisse und Live-Scores aus der größten und beliebtesten amerikanischen Fußballliga der Welt - NFL, und wenn eine regelmäßige NFL-<PERSON>son abgeschlossen ist, folgen <PERSON>e den Live-Scores von NFL-Playoffs und Superbowl. Neben NFL informieren wir Sie auch mit den Livescores, <PERSON><PERSON><PERSON><PERSON><PERSON>, Gesandten und Zeitplänen des NCAA College American Footballs und Canadian CFL.", "FooterContentBaseball": "IGScore baseball live score bietet Ihnen die Live-Ergebnisse, Ergebnisse und Tabellen der beliebtesten Baseball-Liga der Welt - USSSA Baseball, NCAA Baseball, MLB Baseball, MLB Allstar Spiel. Wir bieten auch Live-Scores für die japanische Profiliga, die mexikanische Liga, die deutsche 1.Bundesliga, die NCAA sowie das internationale Baseballturnier World Baseball Classic. Sie können auch jederzeit den Stand der Baseball-Liga, vergangene Spiele mit Ergebnissen nach Innings und den Zeitplan für die bevorstehenden Baseball-Spiele auf IGScore baseball live score einsehen.", "FooterContentIcehockey": "IGScore ice hockey live score bietet Ihnen Echtzeit-Eishockey-Ergebnisse für Eishockey-Ligen, -Pokale und -Turniere. IGScore ice hockey live score bietet Ihnen Hockey-Liveergebnisse, Tabellen, Statistiken, Spielpläne, Ergebnisse und Spielstände aus NHL, SHL, KHL und wir bieten auch nationale finnische Hockeyligen, schwedische Hockeyligen, slowakische Hockeyligen, tschechische Hockeyligen, Ligatabellen, Torschützen, Drittel und letzte Eishockeyergebnisse live. Nach dem Ende der regulären Eishockey-Saison bieten wir Ihnen Eishockey-Liveergebnisse, Tabellen und Ergebnisse der Top-Eishockey-Events - IIHF World Championship Stanley Cup und auch Eishockey-Ergebnisse von Olympischen Winterturnieren. Auf IGScore ice hockey live score findest du außerdem einen kostenlosen Eishockey-Livestream für NHL, SHL und andere.", "FooterContentTennis": "IGScore tennis live score bietet <PERSON><PERSON><PERSON>cores, <PERSON><PERSON><PERSON><PERSON><PERSON>, ATP-Rankings und WTA-Rankings, Spielpläne und Statistiken aller größten Tennisturniere wie Davis und Fed Cup, French Open Tennis oder für alle Grand-Slam-Turniere - Australian Open Tennis, US Open Tennis, <PERSON> und Wimbledon sowohl Damen als auch Herren für Einzel und Doppel. Auch für jeden Tennisspieler können Sie im Detail seine gespielten Matches einzeln und die Ergebnisse davon nach <PERSON>tz sehen und in welchem ​​Turnier dieses Match gespielt wurde. IGScore tennis live score bietet Ihnen direkte Ergebnisse, Statistiken, Live-Ergebnisse und Live-Streams zwischen zwei Spielern, die ein Spiel spielen.", "FooterContentVolleyball": "IGScore volleyball live score bietet Ihnen Berichterstattung aus allen wichtigen nationalen Volleyballligen für Männer und Frauen, einschließlich der italienischen Serie A1 und der italienischen Serie A1 der Frauen, der russischen Superliga, der polnischen PlusLiga, der türkischen 1. Lig und vielen anderen. Neben den nationalen Volleyballligen bieten wir Ihnen auch Livescore-Informationen von großen internationalen Volleyballturnieren, wie der FIVB Welt- und Europameisterschaft, sowie Volleyball-Liveergebnisse von Olympischen Spielen. Sie können auch die alten Ergebnisse Ihres Lieblingsvolleyballteams überprüfen, zukünftige Volleyballpläne einsehen und die Volleyballwertung der Liga auf IGScore volleyball live score überprüfen.", "FooterContentEsports": "ESPORTS Live Scores Service bei IGScore Live Score bietet ESports Live-Scores, Zeitpl<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Ergebnisse und Tabellen an. Folgen Sie Ihren Lieblingsteams hier, hier leben! ESports Live Score on igscore.net Live Score wird automatisch aktualisiert und Sie müssen es nicht manuell aktualisieren. <PERSON><PERSON> dem Hinzufügen von S<PERSON>len, die <PERSON><PERSON> in \"My Games\" folgen möchten, sind die Ergebnisse und Statistiken nach Ihren Spielen, Ergebnisse und Statistiken noch einfacher.", "FooterContentHandball": "IGScore handball live score bietet Ihnen Handball-Liveergebnisse und Live-Ergebnisse aus den beliebtesten Handballligen wie der deutschen Bundesliga, der spanischen Liga Asobal, der dänischen Handboldligaen der Männer und der Frankreich D1. Wir bieten auch Livescore, Ergebnisse, Statistiken, Tabellen, Tabellen und Spielpläne aus wichtigen Pokalen wie der europäischen Handball Champions League, der SEHA Liga und dem EHF Handball Cup. Auf IGScore handball live score finden Sie Livescores und kostenlosen Livestream für Handballturniere internationaler Mannschaften wie Europa- und Weltmeisterschaft, sowohl für Damen als auch für Herren. Sie können jederzeit die Handballergebnisse und Statistiken der letzten 10 Spiele Ihres Teams einsehen und auch die Ergebnisse zwischen den Teams, die mit Statistiken spielen sollen, direkt vergleichen.", "FooterContentCricket": "IGScore cricket live score bietet Ihnen die Möglichkeit, Cricket-Ergebnisse, Cricket-Platzierungen und Cricket-Spiele in Echtzeit zu verfolgen. All das gibt es für die beliebtesten Ligen und Pokale: Indian Premier League, Champions League Twenty20, Big Bash League, Caribbean Premier League, Friends Life T20 und für den ICC Cricket World Cup. Alle Cricket-Ergebnisse auf IGScore werden automatisch aktualisiert und müssen nicht manuell aktualisiert werden. Mit all dem besteht die Möglichkeit, einen kostenlosen Cricket-Live-Stream zu sehen und die neuesten Quoten für das Endergebnis der interessantesten Cricket-Spiele der Welt zu überprüfen.", "FooterContentWaterPolo": "IGScore water polo live score bietet Ihnen die Wasserball-Liveergebnisse und -Ergebnisse der italienischen Serie A1, Ungarn OB1, der Champions und der Adria-Liga auf Vereinsebene, während auf internationaler Ebene IGScore water polo live score große Turniere wie die Wasserball-Weltmeisterschaft und die Wasserball-Europameisterschaft bietet . Wir bieten Ihnen Tor für Tor Livescore und kostenlosen Live-Stream.", "FooterContentTableTennis": "IGScore table tennis live score bietet <PERSON><PERSON><PERSON> Livescores, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Tischtennis-Ra<PERSON>list<PERSON>, Spielpläne und Statistiken aller größten Tischtennisturniere wie russisches Tischtennis, Tischtennis-Olympiade. Auch für jeden Tischtennisspieler können Sie im Detail seine gespielten Spiele einzeln und die Ergebnisse davon pro <PERSON>tz sehen und in welchem ​​Turnier dieses Spiel gespielt wurde. IGScore table tennis live score bietet Ihnen direkte Ergebnisse, Statistiken, Live-Ergebnisse und Live-Streams zwischen zwei Spielern, die ein Spiel spielen.", "FooterContentSnooker": "IGScore snooker live score bietet Ihnen die Möglichkeit, Live-<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>nisse und Tabellen aller Snooker-Turniere zu verfolgen. Wir bieten auch Livescores von UK und World Championships sowie Snooker Livescores, Snooker-Spiele und Snooker-Endergebnisse von internationalen Turnieren wie der World Snooker Tour. Sie können jederzeit den Zeitplan der anstehenden Snooker-Turniere, die Ergebnisse vergangener Snooker-Turniere und die letzten 10 Spiele für jeden Spieler einsehen. Darüber hinaus können Sie vergangene Kopf-an-Kopf-Spiele zwischen Spielern überprüfen. Auf IGScore snooker live score finden Sie die Liste der Spiele, die mit kostenlosem Snooker-Livestream abgedeckt werden.", "FooterContentBadminton": "IGScore badminton live score bietet Ihnen die Badminton-Live-<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Spielpaarungen und Statistiken von internationalen Turnieren wie Weltmeisterschaften, BWF Super Series und Badminton-<PERSON><PERSON><PERSON><PERSON><PERSON> von Olympischen Spielen. Sie können auch die Ergebnisse der Badminton-Spiele überprüfen, den Badminton-Spielplan einsehen und die Badminton-Ergebnisse der Spieler einzeln auf IGScore badminton live score überprüfen.", "ContactUs": "Kontaktiere uns", "UpdateLineups": "Update Lineups", "FreeLiveScoresWidget": "Free Livescores Widget", "PlayerSalary": "", "DivisionLevel": "Ereignisstufe", "Foreigners": "Die Zahl der ausländischen Spieler", "LeagueInfo": "Turniere", "TeamInfo": "Team Info", "Venues": "", "MostValuablePlayer": "", "UpperDivision": "Höhere Liga", "NewcomersFromUpperDivision": "", "NewcomersFromLowerDivision": "", "TitleHolder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MostTitle": "<PERSON><PERSON> zu gewinnen", "Pts": "PTS", "FullStats": "", "Relegation": "", "Result": "<PERSON><PERSON><PERSON><PERSON>", "Score": "<PERSON><PERSON><PERSON><PERSON>", "PlayerStats": "", "fixtures": "", "topPlayers": "Schlüsselfiguren", "Shots": "Schießen", "SelectTeam": "", "SelectBasketBallTeam": "", "SelectAll": "Alle auswählen", "SquadSize": "<PERSON><PERSON><PERSON> der Spieler", "ViewAll": "<PERSON>e ansehen", "penaltiesWon": "Zugesprochene Strafstöße", "accuracyCrosses": "", "totalShorts": "", "accuracyLongBalls": "", "blockShots": "", "MatchesToday": "<PERSON><PERSON>le heute", "Strikers": "<PERSON><PERSON> vorne", "Midfielders": "<PERSON><PERSON><PERSON><PERSON>", "Year": "", "Loans": "", "TopValuablePlayers": "", "total": "", "Total": "", "Results": "", "Prev": "", "Apps": "Einsätze", "ShotsPg": "<PERSON><PERSON><PERSON>", "Possession": "", "TotalAndAve": "", "Suspended": "Verletzt oder suspendiert", "injuredOrSuspended": "Verletzt oder suspendiert", "Since": "Verletzungs Zeit", "Overall": "Overall", "Age": "Alter", "LastMatchFormations": "Letztes Spiel Lineup", "Formation": "", "GoalDistribution": "Tor Verteilung", "Favorite": "", "FoundedIn": "<PERSON><PERSON> g<PERSON>ü<PERSON> in", "LocalPlayers": "Lokale SpielerInnen", "ShowNext": "Nächste Events anzeigen", "HideNext": "Nächste Events ausblenden", "FIFAWorldRanking": "FIFA-<PERSON><PERSON><PERSON><PERSON>", "Register": "", "OP": "1X2", "Handicap": "", "h5Handicap": "", "TennisHandicap": "", "OU": "O/U", "CardUpgradeConfirmed": "Karten-Upgrade bestätigt", "VAR": "", "LatestMatches": "Letzte Spiele", "ScheduledMatches": "", "Only": "", "LeagueFilter": "", "WinProb": "", "ScoreHalf": "", "Animation": "", "FigureLegends": "", "YellowCard": "<PERSON>el<PERSON>", "RedCard": "<PERSON><PERSON>", "Chatroom": "<PERSON><PERSON><PERSON>", "Send": "Senden", "Logout": "", "CurrentLeague": "", "ShirtNumber": "", "ContractUntil": "Vertrag bis", "PlayerInfo": "PLAYER INFO", "Height": "<PERSON><PERSON><PERSON>", "Weight": "Gewicht", "PlayerValue": "Sozialer Status", "View": "", "Time": "Zeit", "onTarget": "", "offTarget": "", "Played": "", "Rating": "", "Attacking": "Offensiv", "Creativity": "Kreativität", "Defending": "Verteidigung", "Tactical": "Taktisch", "Technical": "Technisch", "Other": "", "Cards": "-<PERSON><PERSON><PERSON><PERSON><PERSON>", "AccuratePerGame": "Genaue Pässe", "AccLongBalls": "Associate langer Pass", "AccCrosses": "Genaue Biografie", "SuccDribbles": "Außerordentliche Erfolg", "TotalDuelsWon": "Fouls", "YellowRedCards": "", "AiScoreRatings": "", "Pergame": "", "Player": "", "Upcoming": "", "FreeKicks": "", "Corners": "<PERSON><PERSON>", "DaysUntil": "Tage bis", "In": "Ein", "Out": "Aus", "NoStrengths": "<PERSON><PERSON> herausragenden Stärken", "NoWeaknesses": "<PERSON>ine herausragenden Schwächen", "UpcomingMatches": "", "Pos": "", "Ht(cm)": "", "wt(kg)": "", "Exp": "EXP", "College": "", "Salary/Year": "", "Manager": "", "TotalPlayers": "<PERSON><PERSON><PERSON> der Spieler", "Form": "", "Points": "Punkte", "GamesPlayed": "", "WinPercentage": "", "FieldGoalsMade": "", "FieldGoalsAttempted": "", "FieldGoalPercentage": "", "threePointFieldGoalsMade": "", "threePointFieldGoalsAttempted": "", "threePointFieldGoalsPercentage": "", "FieldGoaldsMade": "", "FreeThrowsAttempted": "", "FreeThrowPercentage": "", "BlockedFieldGoalAttempts": "", "PersonalFouls": "", "PersonalFoulsDrawn": "", "Efficiency": "", "PlusMinus": "", "Atlantic": "", "Central": "", "Southeast": "", "Northwest": "", "Pacific": "", "Southwest": "", "EasternConference": "", "WesternConference": "", "ToWin": "", "Spread": "Handicap", "AmFootballHand": "Handicap", "TotalPoints": "", "TotalPoint": "", "TableTennisTotalGames": "", "Wins": "Siegesserie", "Losses": "Niederlagenserie", "WinningPercentage": "", "GamesBack": "", "HomeRecord": "", "AwayRecord": "", "DivisionRecord": "", "ConferenceRecord": "", "OpponentPointsPerGame": "", "AveragePointDifferential": "", "CurrentStreak": "", "RecordLast5Games": "", "Match": "Spiel", "OnTheCourt": "<PERSON><PERSON> <PERSON>", "Starters": "Starter", "FieldGoals": "Field Goals", "Timeout": "", "OpeningOdds": "", "PreMatchOdds": "", "PointPerGame": "", "PointsAllowed": "", "StartingLineups": "Startaufstellungen", "HandicapGames": "", "TennisHand": "", "TotalGames": "", "TennisTotalGames": "", "Defensive": "", "Offensive": "", "Points(PerGame)": "", "Rebounds(PerGame)": "", "Other(PerGame)": "Andere (Mittelung)", "Attists": "", "FirstServe": "", "FirstServePoints": "", "BreakPoints": "", "Aces": "<PERSON><PERSON>", "DoubleFaults": "<PERSON><PERSON><PERSON><PERSON>", "Winner": "Winner", "HandicapSets": "", "TableTennisHand": "", "MoneyLine": "", "TableTennisHandicapGames": "", "HandicapRuns": "", "BaseballHand": "", "TotalRuns": "", "BaseballTotalGames": "", "DIFF": "", "BasketPergame": "", "HandicapPoints": "", "HandicapFrames": "", "TotalFrames": "", "SeeAll": ""}