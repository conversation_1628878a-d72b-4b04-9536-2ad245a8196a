
import { basketballPlayerIcon } from 'iscommon/const/basketball/constant';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { inject, observer } from 'mobx-react';

import './TopPlayersCard.less'
import { FallbackImage } from 'iscommon/const/icon';
import { Link } from 'umi';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';

const TopPlayersCard = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Basketball: { Match },
      },
    } = props;
    const labelMaps = useTranslateKeysToMaps(['topPlayers', 'Points', 'Rebounds', 'Assists']);

    const renderContrastBox = (keyType: any, leftCount: any, rightCount: any) => {
      return (
        <div className='contrast-box-container'>
          <span className='item-count home-item'>{leftCount}</span>
          {keyType}
          <span className='item-count away-item'>{rightCount}</span>
        </div>
      );
    };

    const renderPlayerItem = (renderInfo: any) => (
      <Link to={GlobalUtils.getPathname(PageTabs.player, renderInfo.player.id)} className='player-item'>
        <img src={renderInfo.player?.logo || basketballPlayerIcon} className='player-icon'/>
        <span className='player-name'>{renderInfo.player?.shortName}</span>
      </Link>
    );

    const { keyPlayers = {} } = Match;
    const { homeTeam, awayTeam } = keyPlayers;

    const renderEach = (labelspan: string, index: number) => {
      if (!homeTeam.items[index] || !awayTeam.items[index]) return null;
      return (
        <div className='player-container' key={index}>
          {renderPlayerItem(homeTeam.items[index])}
          {renderContrastBox(labelspan, homeTeam.items[index].value, awayTeam.items[index].value)}
          {renderPlayerItem(awayTeam.items[index])}
        </div>
      );
    };

    if (!homeTeam || !awayTeam || homeTeam.items.length === 0 || awayTeam.items.length === 0) return null;

    return (
      <div className='basketball-top-players-card-container' id="bsmTopPlayerPlugin">
        <div className='container-title'>{labelMaps.topPlayers}</div>
        <div className='player-team-container'>
          <div className='team-container'>
            <img src={homeTeam.team.logo || FallbackImage} className='team-icon'/>
            <span className='team-name'>{homeTeam.team.name}</span>
          </div>
          <div className='team-container'>
            <span className='team-name'>{awayTeam.team.name}</span>
            <img src={awayTeam.team.logo || FallbackImage} className='team-icon'/>
          </div>
        </div>
        <div className='container-body'>
          {[labelMaps.Points, labelMaps.Rebounds, labelMaps.Assists].map((label, index) => renderEach(label, index))}
        </div>
      </div>
    );
  }),
);

export default TopPlayersCard;