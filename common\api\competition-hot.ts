// @ts-nocheck
import igsRequest from "iscommon/request/instance";
let controller = null

// 热门赛事列表
export const getCompetitionHots = () => {
  return igsRequest.post("competition/hot").then((result) => {
    try {
      return result.competitions;
    } catch (e) {
      return [];
    }
  });
};

// 获取国家和大洲列表
export const getCountriesAndCategories = () => {
  return igsRequest.post("categoryAndCountry/list").then((result) => {
    try {
      return result;
    } catch (e) {
      return [];
    }
  });
};

// 根据国家或大洲获取赛事列表
export const getCompetitionByCountryOrCategory = (params) => {

  if(controller) {
    controller.abort()
  }
  controller = new AbortController();
  const signal = controller.signal;

  return igsRequest
    .post("competition/query", params,{
      signal
    })
    .then((result) => {
      try {
        return result.competitions;
      } catch (e) {
        return [];
      }
    });
};
