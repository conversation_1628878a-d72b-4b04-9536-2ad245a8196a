.team-stats-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;

  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;

    .team-stat-header-btn {
      display: block;
      background: transparent;
      color: #fff;
      border-radius: 32px;
      width: 300px;

      .team-stat-content {
        display: flex; 
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .team-stat-text {
          white-space: nowrap;   
          overflow: hidden;      
          text-overflow: ellipsis;
          flex-grow: 1;
          margin-right: 8px;
        }

        .team-stat-icon {
          flex-shrink: 0;  
          margin-left: auto;
        }
      }
    }
  }

  .secondary-title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 5px 20px;
    background: #1e1e1e;

    .secondary-title-text {
      color: #fff;
      padding: 4px 12px;
      cursor: pointer;
      font-size: 24px;
      font-weight: 700;
    }
  }

  .container-body {
    background: #121212;
    padding: 10px;

    .team-details {
      padding: 10px 5px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .team-value {
        color: #fff;
        font-size: 24px;
      }

      .discipline-details {
        display: flex;
        align-items: center;

        .y {
          padding: 4px;
          color: #fff;
          background-color: rgba(255, 181, 62, 1);
          border-radius: 8px;
          margin-left: 4px;
        }
        
        .r {
          padding: 4px;
          color: #fff;
          background-color: rgba(231, 45, 52, 1);
          border-radius: 8px;
          margin-left: 4px;
        }
      }
    }
  }
}