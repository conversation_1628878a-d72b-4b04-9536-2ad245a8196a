import { Avatar } from 'antd-mobile';
import { translate } from 'iscommon/i18n/utils';
import { changePlayerName, formatRateColor } from 'iscommon/utils/dataUtils';
import { inject, observer } from 'mobx-react';
import styles from './MatchLineup.less';
const MatchLineup = inject('store')(
  observer((props: any) => {
    const { matchLineupInfo = {}, homeFirstRound = [], awayFirstRound = [], formation } = props;
    const { homeTeam, awayTeam } = matchLineupInfo;
    const Coach = translate('Coach');

    const iconH5Maps = {
      1: 'icongoal', //进球
      3: 'icon-yellow-card', //黄牌,
      4: 'icon-red-card', //红牌
      8: 'icon-Penalty', // 点球
      9: 'iconout1', //换人
      15: 'icon-twoyellow-red', //两黄变红
      16: 'iconPenaltySaved', //点球未进
      17: 'icon-own-goal', //乌龙球
    };

    const renderLineup = (renderList = [], lineupType = 'home') => {
      return renderList.map((item: any) => (
        <div
          className={styles.player_box}
          style={
            lineupType === 'home'
              ? {
                  top: `${item.y}%`,
                  left: `${item.x}%`,
                  transform: 'translateX(-50%) translateY(-50%) scale(0.9)',
                }
              : {
                  bottom: `${item.y}%`,
                  right: `${item.x}%`,
                  transform: 'translateX(50%) translateY(50%) scale(0.9)',
                }
          }
          key={item.name}
        >
          {/* 1:进球 3:黄牌 4:红牌 9:换人 15:两黄变红 */}
          <div className={styles.player_avatar_box}>
            <div className={styles.player_img}>
              <Avatar src={item.logo} className={styles.player_img} style={{ backgroundColor: '#ffffff' }} />
              <span className={styles.player_shirtNumber}>
                <span style={{ transform: 'scale(0.75)' }}>{item.shirtNumber}</span>
              </span>
            </div>
            {item.rating != '0.0' && (
              <span
                className={`${styles.rating_box} ${`${styles.color9above}`}`}
                style={{ background: formatRateColor(item.rating) }}
              >
                {item.rating}
              </span>
            )}
            <div className={styles.leftTopStyle}>
              <div className={styles.even}>
                {item.incidents &&
                  Object.keys(item.incidents).map((val: any) =>
                    ['3', '4', '9', '15'].includes(val) ? (
                      <div className={styles.align_center} key={val + Math.random()}>
                        <svg className="svg-icon" style={{ width: 16, height: 16 }}>
                          <use xlinkHref={`#${iconH5Maps[val]}`} className="h16"></use>
                        </svg>
                        {val === '9' ? <span className={styles.event_time}>{`${item.incidents[val][0]?.minute}'`}</span> : null}
                      </div>
                    ) : null,
                  )}
              </div>
            </div>
            <div className={styles.other_even}>
              {item.incidents &&
                Object.keys(item.incidents).map((val: any) =>
                  val === '1' ? (
                    <svg className="svg-icon" style={{ width: 16, height: 16 }} key={val + Math.random()}>
                      <use xlinkHref={`#${iconH5Maps[val]}`} className="h16"></use>
                    </svg>
                  ) : null,
                )}
            </div>
          </div>
          <span className={styles.player_name}>{changePlayerName(item.name)}</span>
        </div>
      ));
    };

    const renderTeamInfo = (teamInfo = {}) => {
      return (
        <div className={styles.teamInfo_box}>
          <div className={styles.teamInfo}>
            <Avatar src={teamInfo?.logo} className={styles.player_img} />
            <span>{teamInfo?.name}</span>
          </div>
          {/* <div className={styles.teamInfo}>{formationText}</div> */}
        </div>
      );
    };

    const RenderGroundBox = (props: any) => {
      const { type = 'home' } = props;
      return (
        <div className={styles.halfBox} style={type === 'away' ? { borderBottom: 0 } : {}}>
          <div className={`${type !== 'home' && styles.bgLine}`}>
            <div className={styles.square2}>
              <div className={styles.square1}></div>
            </div>
            <div className={styles.circleBox}>
              <div className={styles.circle}></div>
            </div>
          </div>
          {props.children}
        </div>
      );
    };

    return (
      <div className={styles.backGreen}>
        {renderTeamInfo(homeTeam)}
        <div className={styles.groundBox}>
          <div className={`${styles.formationInfo} ${styles.t}`}>{formation.homeFormation}</div>
          <RenderGroundBox type="home">{renderLineup(homeFirstRound, 'home')}</RenderGroundBox>
          <RenderGroundBox type="away">{renderLineup(awayFirstRound, 'away')}</RenderGroundBox>
          <div className={`${styles.formationInfo} ${styles.f}`}>{formation.awayFormation}</div>
          <div className={styles.midCircle}>
            <div className={styles.midInner}></div>
          </div>
        </div>
        {renderTeamInfo(awayTeam)}
      </div>
    );
  }),
);

export default MatchLineup;
