import Loading from '@/components/Loading';
import { Collapse, Image } from 'antd-mobile';
import { getTeamChampions } from 'iscommon/api/football-team';
import { inject, observer } from 'mobx-react';
import React, { useEffect, useState } from 'react';

import styles from './index.less';
import { translate, useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { FallbackImage } from 'iscommon/const/icon';
import './index.less'

interface Props {
  store?: any;
}

const groupDataById = (data: any) => {
  return data.reduce((acc: any, item: any) => {
    if (!acc[item.id]) {
      acc[item.id] = [];
    }
    acc[item.id].push(item);
    return acc;
  }, {});
};

const TeamChampionsTab: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: { Team },
    } = props;
    const { teamId } = Team;

    const [listData, setHonorList] = useState<any[]>([]);
    const [loading, setLoading] = useState(true);

    const labelMaps = useTranslateKeysToMaps(['Trophies' ]);

    useEffect(() => {
      if (teamId) {
        getTeamChampions({ teamId }).then(({ honorList: list = [] }: any) => {
          if (list) {
            setHonorList(list);
          }
          setLoading(false);
        });
      }
    }, [teamId]);

    console.log('listdata123', listData)

    return (
      <div className='team-champion-history-container'>
      <div className='container-title'>{labelMaps.Trophies}</div>
      <div className='container-body'>
        <Loading loading={loading} isEmpty={!listData?.length}/>
        <Collapse className='custom-collapse'>
          {listData.map((honorList) => (
            <Collapse.Panel
              key={honorList.honorId}
              title={
                <div className="panel-header">
                  <div className='honor-container'>
                    <img className='honor-icon' src={honorList?.logo || FallbackImage}/>
                    <span className="honor-name">{honorList.honorName}</span>
                  </div>
                  <span className="honor-count">{honorList.count}</span>
                </div>
              }
            >
              <div className='seasons-list'>
                {honorList.seasons.length > 0 ? (
                  honorList.seasons.map((season: string, index: number) => (
                    <div className='season-item' key={index}>{season}</div>
                  ))
                ) : (
                  <div>No seasons available</div>
                )}
              </div>
              {/* <div className="seasons-grid">
                {items.map((item: any, index: any) => (
                  <div key={index} className="season-details">
                    <span className='season-value'>{item.season}</span>
                  </div>
                ))}
              </div> */}
            </Collapse.Panel>
          ))}
        </Collapse> 
      </div>
    </div>
      // <Loading loading={loading} isEmpty={listData.length === 0}>
      //   {listData.map((data) => (
      //     <div key={data.id} className={styles.section}>
      //       <div className={styles.header}>
      //         <Image src={data.logo} width={28} height={28} />
      //         <div className={styles.title}>{data.honorName}</div>
      //         <div className={styles.count}>{data.count}</div>
      //       </div>
      //       <div className={styles.list}>
      //         {data.seasons.map((item: any, index: number) => {
      //           const split = index === data.seasons.length - 1 ? '' : ',';
      //           return (
      //             <div key={index} className={styles.year}>
      //               {item}
      //               {split}
      //             </div>
      //           );
      //         })}
      //       </div>
      //     </div>
      //   ))}
      // </Loading>
    );
  }),
);

export default TeamChampionsTab;
