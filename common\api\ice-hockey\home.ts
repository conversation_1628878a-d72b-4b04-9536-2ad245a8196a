// @ts-nocheck
import igsRequest from 'iscommon/request/instance';
import { HomeGameTab } from 'iscommon/const/constant';
import { getArrayFromString, getTodayDateFilter } from 'iscommon/utils';
import smallBallhomeDataController from 'iscommon/mqtt/smallball/homeDataController';
import { IckHockeyInLiveStatusEnum } from '../../const/iceHockey/constant';
import { filterEachCompetitions } from '../../utils/smallball';

let controller = null;

// 04:00 ===> 0400
export const formatMatchTimeToNumber = (timeString = '') => {
  return Number(timeString.replace(/[\s|:]/g, ''));
};

export const getCompetitionList = async ({ dateFilter = '', listType = 0 } = {}) => {
  if (controller) {
    controller.abort();
  }
  controller = new AbortController();
  const signal = controller.signal;
  return igsRequest
    .post(
      'competition/list',
      {
        listType, //列表类型，0-Live， 1-ALL, 2-Finished, 3-Scheduled
        dateFilter: listType !== 0 ? dateFilter.replace('UTC', '') : getTodayDateFilter(), // "2022-08-27"
        skipOdds: true,
      },
      {
        signal,
      },
    )
    .then((result) => {
      const { competitions, total, matchIds } = filterEachCompetitions(result, IckHockeyInLiveStatusEnum);
      getMatchOddsAndSave({ matchIds });
      return { competitions, total };
    })
    .finally(() => {
      // controller = null
    });
};

export const getUpComingList = async () => {
  return igsRequest
    .post('competition/list', {
      listType: HomeGameTab.Scheduled,
      skipOdds: true,
      dateFilter: getTodayDateFilter(),
    })
    .then((result) => {
      const { competitions, total, matchIds } = filterEachCompetitions(result, IckHockeyInLiveStatusEnum);
      getMatchOddsAndSave({ matchIds });
      return { competitions, total };
    });
};

export const getMatchOddsAndSave = async ({ matchIds = [] }) => {
  if (matchIds.length > 0) {
    igsRequest.post('match/odds/last', { matchIds }).then(({ matchRecentOdds }) => {
      smallBallhomeDataController.syncHomeOdds(matchRecentOdds);
    });
  }
};
