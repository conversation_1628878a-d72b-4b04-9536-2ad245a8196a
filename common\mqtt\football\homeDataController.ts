// @ts-nocheck
import _ from 'lodash';

import store from 'iscommon/mobx';
import goalAudio from 'iscommon/events/goalAudio';
import { getArrayFromString } from 'iscommon/utils';
import { GlobalConfig } from '../../const/globalConfig';

let tempScorePool = {};
const tempScorePoolLength = 30;
let delayScoreTimer = null;
const timeSeconds = 3000;

export const startTimer = () => {
  if (delayScoreTimer) clearTimeout(delayScoreTimer);
  delayScoreTimer = setTimeout(() => {
    console.log('timing compensation update, will update score data:', !!Object.keys(tempScorePool).length);
    delaySyncHomeScore();
  }, timeSeconds);
};

const delaySyncHomeScore = () => {
  // console.log('score', data)

  // Timing compensation update
  startTimer();

  if (Object.keys(tempScorePool).length === 0) {
    return;
  }

  // batch update
  const tempListMap = JSON.parse(JSON.stringify(tempScorePool));

  tempScorePool = {};

  const { WebHome } = store;
  const homeCompetitions = _.cloneDeep(WebHome.homeCompetitions);
  const allStatusMap = _.cloneDeep(WebHome.latestMatchStatus);
  let playAudio = false;

  if (!homeCompetitions || homeCompetitions.length === 0) {
    return false;
  }


  // ["1l4rjnhj9kn9m7v", 8, [1, 0, 0, 0, -1, 0, 0], [1, 0, 0, 0, -1, 0, 0], 0, ""]
  // 2:Array[7]
  // 0:"Home Team Score (regular time)                                                                          (Integer type)"
  // 1:"Home Team Halftime score                                                                                (Integer type)"
  // 2:"Home Team Red cards                                                                                     (Integer type)"
  // 3:"Home Team Yellow cards                                                                                  (Integer type)"
  // 4:"Home Team Corners，-1 means no corner kick data                                                         (Integer type)"
  // 5:"Home Team Overtime score (120 minutes，including regular time)，only available in overtime              (Integer type)"
  // 6:"Home Team Penalty shootout score，only penalty shootout
  // 当主客加时比分不为零时：
  // 最终比分=加时比分（Home Team Overtime score）+点球大战比分（Home Team Penalty shootout score）
  // 当主客加时比分为零时：
  // 最终比分=常规时间比分（Home Team Score (regular time)）+点球大战比分
  for (let c of homeCompetitions) {
    for (let match of c.matches) {
      const { matchId } = match;

      if (tempListMap[matchId]) {
        const { score = [], kickOffTime } = tempListMap[matchId];

        if (!score || score.length <= 0) {
          continue;
        }

        // calculatedAwayScore, calculatedHomeScore homeScores awayScores matchStatus
        const matchStatus = score[1];
        const homeScores = getArrayFromString(score[2]);
        const awayScores = getArrayFromString(score[3]);
        let calculatedHomeScore = match.calculatedHomeScore;
        let calculatedAwayScore = match.calculatedAwayScore;

        if (homeScores[5] !== 0) {
          calculatedHomeScore = homeScores[5] + homeScores[6];
        }
        if (awayScores[5] !== 0) {
          calculatedAwayScore = awayScores[5] + awayScores[6];
        }
        if (GlobalConfig.goalAudio.sound && !playAudio) {
          if (
            !GlobalConfig.goalAudio.favorite ||
            (GlobalConfig.goalAudio.favorite && (WebHome.favoriteMatches || []).find((item) => item.matchId === matchId))
          ) {
            if (calculatedHomeScore !== match.calculatedHomeScore || calculatedAwayScore !== match.calculatedAwayScore) {
              playAudio = true;
            }
          }
        }

        if (playAudio && goalAudio && typeof goalAudio.play === 'function') {
          goalAudio.play({
            matchId,
            homeTeam: match.homeTeam,
            awayTeam: match.awayTeam,
            homeScore: calculatedHomeScore,
            awayScore: calculatedAwayScore,
          });
        }

        allStatusMap[matchId] = matchStatus;
        match.matchStatus = matchStatus;
        match.homeScores = homeScores;
        match.awayScores = awayScores;
        match.calculatedHomeScore = calculatedHomeScore;
        match.calculatedAwayScore = calculatedAwayScore;
        match.matchActiveTime = kickOffTime;

        break;
      }
    }
  }

  WebHome.setHomeCompetitions(homeCompetitions);
  WebHome.setLatestMatchStatus({ allStatusMap });
};

const syncHomeScore = (data) => {
  const { id } = data;
  // console.log('score', data)
  tempScorePool[id] = data;
  if (Object.keys(tempScorePool).length >= tempScorePoolLength) {
    console.log('data backlog and update all, will update score data:', !!Object.keys(tempScorePool).length);
    delaySyncHomeScore();
  }
};

const syncIncidents = (data) => {
  // console.log('incidents', data)
  // incidents = [
  //   {type: 9, position: 1, time: 15},
  //   {type: 1, position: 1, time: 25},
  //   {type: 3, position: 1, time: 30}
  // ]
};

// the stats data is same as the scores data
const syncStats = (data) => {
  // console.log('stats', data)
  // stats = [
  //   {type: 3, home: 2, away: 2},
  //   {type: 23, home: 74, away: 68},
  //   {type: 2, home: 3, away: 5}
  // ]
  // const { WebHome } = store;
  // const { id, stats = [] } = data;
  // if (WebHome.homeCompetitions && WebHome.homeCompetitions.length && stats.length > 0) {
  //   let cornerStat = null;
  //   let redCardStat = null;
  //   let htStat = null;
  //   for (const stat of stats) {
  //       if (stat.type === 2) {
  //           cornerStat = stat;
  //       } else if (stat.type === 4) {
  //           redCardStat = stat;
  //       } else if (stat.type === 13) {
  //           htStat = stat;
  //       }
  //   }
  //   if (cornerStat || redCardStat || htStat) {
  //     const homeCompetitions = _.cloneDeep(WebHome.homeCompetitions);
  //     for (const league of homeCompetitions) {
  //       for (const match of league.matches) {
  //         const { matchId } = match;
  //         if (matchId === id) {
  //           if (htStat) {
  //             match.homeScores[1] = htStat.home;
  //             match.awayScores[1] = htStat.away;
  //           }
  //           if (redCardStat) {
  //             match.homeScores[2] = redCardStat.home;
  //             match.awayScores[2] = redCardStat.away;
  //           }
  //           if (cornerStat) {
  //             match.homeScores[4] = cornerStat.home;
  //             match.awayScores[4] = cornerStat.away;
  //           }
  //           break;
  //         }
  //       }
  //     }
  //     WebHome.setHomeCompetitions(homeCompetitions);
  //   }
  // }
};

const mixinCompetitionOdds = (matchRecentOdds, oldOdds = {}, oldStatus = {}) => {
  const cloneList = _.cloneDeep(oldOdds);
  const allStatusMap = _.cloneDeep(oldStatus);
  for (let matchId in matchRecentOdds) {
    // 首页只展示 companyId == 2 的数据
    const homeValidOdds = matchRecentOdds[matchId] ? matchRecentOdds[matchId].filter((item) => item.companyId == 2) : [];
    if (cloneList[matchId]) {
      if (homeValidOdds.length > 0) {
        const { matchStatus } = homeValidOdds[0];
        allStatusMap[matchId] = matchStatus;
        for (let h of homeValidOdds) {
          const { oddsType } = h;
          for (let i = 0; i < cloneList[matchId].length; i++) {
            const item = cloneList[matchId][i];
            if (oddsType === item.oddsType) {
              cloneList[matchId][i] = {
                ...h,
                lastOddsData: item.oddsData,
              };
            }
          }
        }
      }
    } else {
      cloneList[matchId] = homeValidOdds;
    }
  }
  return { cloneList, allStatusMap };
};

const syncHomeOdds = (matchRecentOdds) => {
  const { WebHome } = store;
  const { homeCompetitionOdds, latestMatchStatus } = WebHome;
  const { cloneList, allStatusMap } = mixinCompetitionOdds(matchRecentOdds, homeCompetitionOdds, latestMatchStatus);
  WebHome.setHomeOdds(cloneList);
  WebHome.setLatestMatchStatus({ allStatusMap });
};

export const homeDataControllerType = {
  score: 'score',
  stats: 'stats',
  incidents: 'incidents',
  syncHomeOdds: 'syncHomeOdds',
};

const homeDataController = {
  [homeDataControllerType.score]: syncHomeScore,
  [homeDataControllerType.stats]: syncStats,
  [homeDataControllerType.incidents]: syncIncidents,
  [homeDataControllerType.syncHomeOdds]: syncHomeOdds,
};

export default homeDataController;
