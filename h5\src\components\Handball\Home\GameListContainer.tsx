import { getCompetitionList, getUpComingList } from 'iscommon/api/handball/home';

import SmallBallGameListContainer from '@/components/SmallGameCommon/Home/GameListContainer';
import GameCategory from './GameCategory';

const GameListContainer = (props: any) => {
  const { propsTab, isSortByTime = false } = props;

  return (
    <SmallBallGameListContainer
      {...props}
      renderCateGory={(item) => <GameCategory competition={item} />}
      getUpComingList={getUpComingList}
      getCompetitionList={getCompetitionList}
    />
  );
};

export default GameListContainer;
