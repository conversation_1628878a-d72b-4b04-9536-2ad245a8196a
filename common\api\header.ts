// @ts-nocheck
import igsRequest from 'iscommon/request/instance';
import { GlobalConfig, GlobalSportPathname } from '../const/globalConfig';
import { H5_ICON_FONT, ICON_FONT } from '../const/icon';

// interface LiveCount {
//   icon?: "string"; // 角标
//   name: "string"; // 名称
//   sportType: "string"; // 类型
//   liveCount: number; // 正在直播数量
//   totalCount: number; //
// }

function handleLiveCounts(liveCounts, platform) {
  let list = [];
  try {
    for (let v in liveCounts) {
      const liveCount = liveCounts[v];
      const sportType = v;
      list.push({
        ...liveCount,
        iconfont: platform === 'pc' ? ICON_FONT[sportType] : H5_ICON_FONT[sportType],
        sportType,
        active: GlobalSportPathname[GlobalConfig.pathname].toLocaleLowerCase() === v.toLocaleLowerCase(),
      });
    }
  } catch (e) {
    console.log(e);
  }
  return list;
}

export const getLiveCount = (platform = 'pc') => {
  return igsRequest
    .post('common/match/live/count', {
      noBaseUrl: true,
    })
    .then(function (result) {
      console.log(result, 'match/live/count');
      const liveCounts = result.liveCounts || [
        {
          sportType: 'FOOTBALL',
          liveCount: 0, //integer
          totalCount: 0, // integer
        },
      ];
      if (liveCounts) {
        return handleLiveCounts(liveCounts, platform);
      }
      return [];
    });
};

const CacheAdData = {
  web: {},
  mobile: {},
};
export const queryAdList = async (position = '', platform = 'web') => {
  if (CacheAdData[platform][position]) {
    return CacheAdData[platform][position];
  }
  try {
    const params = {
      noBaseUrl: true,
      country: 'US',
      position,
      platform,
    };
    const result = await igsRequest.post('common/ad/load', params);
    const list = result.map((i) => ({ ...i, height: i.length }));
    CacheAdData[platform][position] = list;
    return list;
  } catch (e) {
    return [];
  }
};

/**
* @returns {Promise<{
*     showOdds: boolean,
*     showVideo: boolean,
*     showFootball: boolean,
*     showBasketball: boolean,
*     showBaseball: boolean,
*     showTennis: boolean,
*     showTabletennis: boolean,
*     showVolleyball: boolean,
*     showEsports: boolean,
*     showAmfootball: boolean,
*     showBadminton: boolean,
*     showSnooker: boolean,
*     showHandball: boolean,
*     showIcehockey: boolean,
*     showCricket: boolean,
*     showWaterpolo: boolean,
*     showChatTab: boolean,
*     }>}
*/
export const getOddsStatus = () => {
  return igsRequest.post('common/displays', {
    noBaseUrl: true,
  });
};
