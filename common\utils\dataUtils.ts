// @ts-nocheck
import {
  BasketBallInLiveStatusEnum,
  BasketBallInLiveWithTimeEnum,
  BasketBallLiveSectionEnum,
  BasketBallMatchStatusCodeToText,
  BasketBallStatusCodeEnum,
} from '../const/basketball/constant';
import {
  DisplayTimeLineIconCode,
  iconH5Maps,
  iconMaps,
  InProgressStatusEnum,
  LineupRatingColor,
  MatchStatusCodeToText,
  PlatformEnum,
  StatusCodeEnum,
} from '../const/constant';
import { GlobalConfig } from '../const/globalConfig';
import { CompetitionsTab, CompetitionsTabH5 } from '../mobx/modules/competition';
import { TennisInLiveStatusEnum, TennisInLiveWithTimeEnum, TennisMatchStatusCodeToText } from '../const/tennis/constant';
import { BaseballInLiveStatusEnum } from '../const/baseball/constant';
import { TableTennisInLiveStatusEnum } from '../const/tabletennis/constant';
import { BadmintonInLiveStatusEnum, BadmintonInLiveWithTimeEnum, BadmintonMatchStatusCodeToText } from '../const/badminton/constant';
import { HandballInLiveStatusEnum } from '../const/handball/constant';
import { getRemainTimeString } from '.';
import { IckHockeyInLiveStatusEnum, IckHockeyInLiveWithTimeEnum, IckHockeyMatchStatusCodeToText, } from '../const/iceHockey/constant';
import _ from 'lodash';
import { SnookerInLiveStatusEnum } from '../const/snooker/constant';
import { CricketInLiveStatusEnum } from '../const/cricket/constant';
import { EsportsInLiveStatusEnum } from '../const/esports/constant';

/**
 * @description get competition tab list by season data
 * @param data {{ hasPlayerStats?: 0 | 1, hasTeamStats?: 0 | 1, hasTable?: 0 | 1 }}
 * @param hasTransfer {boolean}
 */
export const getCompetitionTabKeys = (data = {}, hasTransfer = false) => {
  const { platform } = GlobalConfig;
  const { hasPlayerStats = 0, hasTeamStats = 0, hasTable = 0 } = data;
  // h5
  if (platform === PlatformEnum.mobile) {
    return [
      CompetitionsTabH5.overview,
      Boolean(hasTable) && CompetitionsTabH5.standings,
      CompetitionsTabH5.schedule,
      // Boolean(hasPlayerStats) && CompetitionsTabH5.playerStats,
      // Boolean(hasTeamStats) && CompetitionsTabH5.teamStats,
      Boolean(hasPlayerStats || hasTeamStats) && CompetitionsTabH5.stats,
      hasTransfer && CompetitionsTabH5.transfer,
      CompetitionsTabH5.champions,
      // CompetitionsTabH5.teamStats,
    ].filter(Boolean);
  }
  // pc
  return [
    CompetitionsTab.overview,
    CompetitionsTab.schedule,
    Boolean(hasTable) && CompetitionsTab.standings,
    Boolean(hasPlayerStats || hasTeamStats) && CompetitionsTab.stats,
    hasTransfer && CompetitionsTab.transfer,
    CompetitionsTab.champions,
  ].filter(Boolean);
};

// get Home competition match Time
export const getHomeCompetitionMatchTime = ({
  serverTime,
  matchStatus,
  matchActiveTime,
  secondHalfKickOffTime,
  currentGameTab,
} = {}) => {
  const fixedServerTime = serverTime || GlobalConfig.serverTime;
  let statusText;
  let isAni = false;
  let isIng;
  if (matchStatus === StatusCodeEnum.PenaltyShootout) {
    statusText = 'PSO';
    isAni = true;
    isIng = true;
  } else if (InProgressStatusEnum.includes(matchStatus) && matchActiveTime && Number(matchActiveTime) > 0) {
    const remainTime = fixedServerTime - matchActiveTime + (secondHalfKickOffTime ? 45 * 60 : 0);
    if (remainTime > 0) {
      if (remainTime > 90 * 60 && (matchStatus === StatusCodeEnum.SecondHalf || matchStatus === StatusCodeEnum.Overtime)) {
        statusText = '90+';
      } else if (remainTime > 45 * 60 && (matchStatus === StatusCodeEnum.FirstHalf || matchStatus === StatusCodeEnum.Overtime)) {
        statusText = '45+';
      } else {
        statusText = getRemainTimeString(remainTime);
      }
    } else {
      statusText = '-';
      console.error('live time error', remainTime, fixedServerTime, matchActiveTime);
    }
    isAni = true;
    isIng = true;
  } else {
    isIng = currentGameTab === 0;
    statusText = MatchStatusCodeToText[matchStatus];
  }

  return {
    statusText,
    isAni,
    isIng,
  };
};

//
export const getMatchDetailPageTime = (matchItem, serverTime = 0) => {
  const { statusId: matchStatus, firstHalfKickOffTime, secondHalfKickOffTime } = matchItem;
  let matchActiveTime = 0;
  if (InProgressStatusEnum.includes(matchStatus)) {
    matchActiveTime = secondHalfKickOffTime || firstHalfKickOffTime;
  }
  return getHomeCompetitionMatchTime({
    serverTime,
    matchActiveTime,
    matchStatus,
    secondHalfKickOffTime,
    currentGameTa: 1,
  });
};

// matchHeaderInfo ===》 matches
export const formatMatchHeaderInfoToMatch = (matchId, matchHeaderInfo) => {
  const { statusId } = matchHeaderInfo;
  return {
    ...matchHeaderInfo,
    matchStatus: statusId,
    matchId,
  };
};

export const formatRateColor = (t) => {
  const h = LineupRatingColor;
  return t < 6
    ? h[0]
    : t <= 6.49 && t >= 6
    ? h[1]
    : t <= 6.99 && t >= 6.5
    ? h[2]
    : t <= 7.99 && t >= 7
    ? h[3]
    : t <= 8.99 && t >= 8
    ? h[4]
    : t >= 9
    ? h[5]
    : void 0;
};

export const changePlayerName = (name) => {
  const [f, s] = name.split(' ');
  if (s) {
    return `${f[0]}.${s}`;
  }
  return name;
};

export const isNullOrUndefined = (v) => {
  return _.isUndefined(v) || _.isNull(v);
};

// {
//   isNeutral: true,
//   tagText: 'FT 1-2',
//   mTime: 45
// }
// {
//   isNeutral: false,
//   isHome: false,
//   mTime: 45,
//   players: [
//     {
//       playName: '',
//       iconId: ''
//     }
//   ]
// }
export const formatOneIncidents = (incident, homeScores = [], awayScores = []) => {
  const {
    type,
    time,
    position,
    playerName = '',
    assist1Name = '',
    assist2Name = '',
    outPlayerName = '',
    inPlayerName = '',
    homeScore,
    awayScore,
  } = incident;
  const icon = iconMaps[type];
  const h5Icon = iconH5Maps[type];
  const mTime = time > 90 ? `90+${time - 90}` : time;
  const h5Mtime = time;
  let item = {};
  if (position === 0) {
    // 中立事件
    if (type === 12) {
      // 完场
      item = {
        isNeutral: true,
        tagText: `FT ${homeScores[0]} - ${awayScores[0]}`,
      };
    } else if (type === 19 && time === 45) {
      // 中场 ,且第二个中场不要
      item = {
        isNeutral: true,
        tagText: `HT ${homeScores[1]} - ${awayScores[1]}`,
      };
    } else if (type === 26) {
      // 加时赛
      item = {
        isNeutral: true,
        tagText: `ET ${homeScores[5]} - ${awayScores[5]}`,
      };
    }
  } else if (icon) {
    // 有icon
    const isHome = position === 1;
    item = {
      isNeutral: false,
      isHome,
    };
    let list = [];
    // 有进球的情况下，没有 playername 也要显示
    if (playerName || DisplayTimeLineIconCode.includes(type)) {
      list.push({
        playerName,
        iconId: icon,
      });
    }
    if (assist1Name) {
      list.push({
        playerName: assist1Name,
        iconId: iconMaps['assist'],
        tagKey: 'Assists',
      });
    }
    if (assist2Name) {
      list.push({
        playerName: assist2Name,
        iconId: iconMaps['assist'],
        tagKey: 'Assists',
      });
    }
    if (inPlayerName) {
      list.push({
        playerName: inPlayerName,
        iconId: iconMaps['9'] + '-up',
        tagKey: 'In',
      });
    }
    if (outPlayerName) {
      list.push({
        playerName: outPlayerName,
        iconId: iconMaps['9'] + '-down',
        tagKey: 'Out',
      });
    }
    if (isHome) {
      item['homePlayers'] = list;
    } else {
      item['awayPlayers'] = list;
    }
  }
  item['score'] = !isNullOrUndefined(homeScore) && isNullOrUndefined(awayScore) ? `${homeScore}-${awayScore}` : '';
  item['h5Icon'] = h5Icon;
  item['h5Mtime'] = h5Mtime;
  item['mTime'] = mTime;
  item['isGoal'] = type === 1;
  return item;
};
export const formatTimeLineData = ({ awayScores, homeScores, incidents = [] }) => {
  // homeScores = [2, 2, 0, 0, 0, 0, 0];
  // awayScores = [0, 0, 0, 0, 0, 0, 0];
  // incidents = [
  //   {
  //     type: 1,
  //     position: 1,
  //     time: 10,
  //     playerId: null,
  //     playerName: null,
  //     assist1Id: null,
  //     assist1Name: null,
  //     assist2Id: null,
  //     assist2Name: null,
  //     inPlayerId: null,
  //     inPlayerName: null,
  //     outPlayerId: null,
  //     outPlayerName: null,
  //     homeScore: 1,
  //     awayScore: 0,
  //     varReason: null,
  //     varResult: null,
  //     reasonType: null,
  //   },
  //   {
  //     type: 1,
  //     position: 1,
  //     time: 15,
  //     playerId: null,
  //     playerName: null,
  //     assist1Id: null,
  //     assist1Name: null,
  //     assist2Id: null,
  //     assist2Name: null,
  //     inPlayerId: null,
  //     inPlayerName: null,
  //     outPlayerId: null,
  //     outPlayerName: null,
  //     homeScore: 2,
  //     awayScore: 0,
  //     varReason: null,
  //     varResult: null,
  //     reasonType: null,
  //   },
  // ]
  console.log(awayScores, homeScores, 'awayScores');
  const list = [];
  if (incidents && incidents.length > 0) {
    for (let v of incidents) {
      const item = formatOneIncidents(v, homeScores, awayScores);
      if (item) {
        list.push(item);
      }
    }
  }

  return list.reverse();
};

export const calculateScore = (scoreList) => {
  if (!scoreList || scoreList.length === 0) return 0;
  return scoreList[5] !== 0 ? scoreList[5] + scoreList[6] : scoreList[0];
};

export const sortRating = (list) => {
  return list.sort((a, b) => {
    const ar = Number(a.rating);
    const br = Number(b.rating);
    if (ar != 0.0 || br != 0.0) {
      return br - ar;
    }
    return false;
  });
};

export const getProgress = (a, b) => {
  return a === 0 ? 0 : (a * 100) / (a + b);
};

export const getStaticsItemData = (statList = [], type) => {
  return statList.filter((item) => item.type == type)[0] || {};
};

export const getBasketballStatsValue = (statsList = [0, 0, 0], type = 0) => {
  if (!statsList || statsList.length === 0) return [0, 0, 0];
  const result = statsList.filter((item) => item[0] == type)[0];
  return result.length === 3 ? result : [0, 0, 0];
};

export const getScoreDisplayText = (scoreList) => {
  if (!_.isArray(scoreList)) return '';
  if (scoreList[5] != 0) {
    return `${scoreList[5]} ${scoreList[6] != 0 ? `(${scoreList[6]})` : ''}`;
  }
  return `${scoreList[0]}`;
};

export const showEmptyLineUp = (homeList = [], awayList = []) => {
  if (
    !Array.isArray(homeList) || 
    !Array.isArray(awayList) || 
    !homeList[1] || 
    !awayList[1] || 
    (homeList[1].length === 0 && awayList[1].length === 0)
  ) {
    return -1;
  }
  // 全为0也不展示
  return homeList[1].concat(awayList[1]).reduce((p, c) => {
    return p + (c.x || 0) + (c.y || 0);
  }, 0);
};

export const getCurrentLiveCount = (sportType, liveCounts) => {
    try {
    if (liveCounts.length === 0) {
      return 0;
    } else {
      const item = liveCounts.filter((item) => item.sportType.toLocaleLowerCase() === sportType.toLocaleLowerCase())[0];
      return item.liveCount;
    }
  } catch (e) {
    return 0;
  }
};

// validKey = 'hasTable' (有积分榜) || hasTeamStats （有球队统计）|| hasPlayerStats（有球员统计）
export const filterValidSeasons = (result, validKey = '') => {
  if (!result || !result.competitions) return [];
  if (!validKey) return result.competitions;
  const competitions = [];
  for (let c of result.competitions) {
    const seasons = [];
    for (let s of c.seasons) {
      if (s[validKey] === 1) {
        seasons.push(s);
      }
    }
    if (seasons.length > 0) {
      competitions.push({
        ...c,
        seasons,
      });
    }
  }
  return competitions;
};

// 0:"Section 1 Score  (Integer type)"
// 1:"Section 2 Score  (Integer type)"
// 2:"Section 3 Score  (Integer type)"
// 3:"Section 4 Score  (Integer type)"
// 4:"Overtime score   (Integer type)"
// return [
//   {
//     h: 0,
//     w: 0,
//     isRed: false,
//     compareStatus: 0 // 1 home > away 2 home < away 0 =
//   }
// ]
export const getBasketBallScoreList = (matchStatus, homeScores, awayScores, len = 5) => {
  const list = [];
  let totalH = 0,
    totalW = 0;
  const isTBD = matchStatus === BasketBallStatusCodeEnum.ToBeDetermined;
  for (let i = 0; i < homeScores.length; i++) {
    const currentSection = BasketBallLiveSectionEnum[i] || [];
    if (isTBD || matchStatus < currentSection[0]) continue;
    const h = homeScores[i];
    const w = awayScores[i];
    totalH += h;
    totalW += w;
    // 首页不展示第五个 加时赛比分，但是aiscore展示
    if (i === len - 1) continue;
    const compareV = h - w;
    const item = {
      h,
      w,
      isRed: currentSection.includes(matchStatus),
      compareStatus: compareV > 0 ? 1 : compareV === 0 ? 0 : 2, // 1 home > away 2 home < away 0 =
    };
    list.push(item);
  }
  if (!isTBD) {
    const compareV = totalH - totalW;
    list.unshift({
      h: totalH,
      w: totalW,
      isRed: BasketBallInLiveStatusEnum.includes(matchStatus),
      compareStatus: compareV > 0 ? 1 : compareV === 0 ? 0 : 2,
    });
  }

  return list.slice(0, len);
};

export const getBasketBallTotalScore = (scores = []) => {
  let total = 0;
  for (let i = 0; i < scores.length; i++) {
    const h = scores[i];
    total += h;
  }
  return total;
};

const getCountDownText = (remainSeconds) => {
  if (!remainSeconds) return '00:00';
  const m = `0${Math.floor(remainSeconds / 60)}`.slice(-2);
  const s = `0${remainSeconds - m * 60}`.slice(-2);
  return `${m}:${s}`;
};

// get BasketBall Home competition match Time
export const getHomeBasketBallMatchTimeText = ({ matchStatus, remainSeconds } = {}) => {
  // const fixedServerTime = serverTime || GlobalConfig.serverTime;
  const isIng = BasketBallInLiveStatusEnum.includes(matchStatus);
  const hasTime = BasketBallInLiveWithTimeEnum.includes(matchStatus);
  let statusText = BasketBallMatchStatusCodeToText[matchStatus];

  if (hasTime) {
    statusText += getCountDownText(remainSeconds);
  }

  return {
    statusText,
    isIng,
  };
};

export const getBadmintonMatchTimeText = ({ matchStatus, remainSeconds } = {}) => {
  // const fixedServerTime = serverTime || GlobalConfig.serverTime;
  const isIng = BadmintonInLiveStatusEnum.includes(matchStatus);
  const hasTime = BadmintonInLiveWithTimeEnum.includes(matchStatus);
  let statusText = BadmintonMatchStatusCodeToText[matchStatus];

  if (hasTime) {
    statusText += getCountDownText(remainSeconds);
  }

  return {
    statusText,
    isIng,
  };
};

export const getTennisMatchTimeText = ({ matchStatus, remainSeconds } = {}) => {
  // const fixedServerTime = serverTime || GlobalConfig.serverTime;
  const isIng = TennisInLiveStatusEnum.includes(matchStatus);
  const hasTime = TennisInLiveWithTimeEnum.includes(matchStatus);
  let statusText = TennisMatchStatusCodeToText[matchStatus];

  if (hasTime) {
    statusText += getCountDownText(remainSeconds);
  }

  return {
    statusText,
    isIng,
  };
};

export const getIckHockeyBallMatchTimeText = ({ matchStatus, remainSeconds } = {}) => {
  // const fixedServerTime = serverTime || GlobalConfig.serverTime;
  let statusText;
  let isIng = IckHockeyInLiveStatusEnum.includes(matchStatus);
  statusText = IckHockeyMatchStatusCodeToText[matchStatus];

  if (IckHockeyInLiveWithTimeEnum.includes(matchStatus)) {
    statusText += `-${getCountDownText(remainSeconds)}`;
  }

  return {
    statusText,
    isIng,
  };
};

// ft - Total score
// p* - Single set score(* - Number of sets, 1、2、3...)
// x* - Grab 7 points(* - Number of sets, 1、2、3...)
// pt - Real-time score
// 0:"Home team score (Integer type)"
// 1:"Away team score (Integer type)"
// scores  "{\"ft\":[0,0]}"
// 最多 p5
// const TennisScoreKeys = [1, 2, 3, 4, 5];
// "Serving side, 1. Home team, 2. Away team
const getTennisScoreListItem = ([h, w], [hx, wx], matchStatus, currentSection = false, enumList) => {
  const compareV = h - w;
  return {
    h,
    w,
    isRed: enumList.includes(matchStatus) && currentSection,
    compareStatus: compareV > 0 ? 1 : compareV === 0 ? 0 : 2, // 1 home > away 2 home < away 0 =
    hx,
    wx,
    empty: false,
  };
};

export const getTennisSectionScoreList = (matchStatus, scores, needEmpty = false, enumList, sectionLength = 5) => {
  let list = [];
  for (let v = 1; v <= sectionLength; v++) {
    if (scores[`p${v}`]) {
      list.push(getTennisScoreListItem(scores[`p${v}`], scores[`x${v}`] || [], matchStatus, !scores[`p${v + 1}`], enumList));
    } else if (needEmpty) {
      list.push({
        empty: true,
      });
    }
  }
  return list;
};
export const getTennisScoreList = (
  matchStatus,
  scores,
  servingSide,
  needEmpty = false,
  enumList = TennisInLiveStatusEnum,
  sectionLength = 5,
) => {
  if (_.isEmpty(scores)) {
    return [];
  }
  let list = [
    {
      h: servingSide == 1,
      w: servingSide == 2,
    },
  ];
  if (scores['ft']) {
    list.push(getTennisScoreListItem(scores['ft'], [], matchStatus, true, enumList));
  }
  list = list.concat(getTennisSectionScoreList(matchStatus, scores, needEmpty, enumList, sectionLength));
  return list;
};

const AmFootballScoreKeys = [1, 2, 3, 4];
export const getAmFootballSectionScoreList = (matchStatus, scores, needEmpty = false, enumList) => {
  let list = [];
  for (let v of AmFootballScoreKeys) {
    if (scores[`p${v}`]) {
      list.push(getTennisScoreListItem(scores[`p${v}`], scores[`x${v}`] || [], matchStatus, !scores[`p${v + 1}`], enumList));
    } else if (needEmpty) {
      list.push({
        empty: true,
      });
    }
  }
  return list;
};
// AmFootball
export const getAmFootballScoreList = (
  matchStatus,
  scores,
  servingSide,
  needEmpty = false,
  enumList = TennisInLiveStatusEnum,
) => {
  if (_.isEmpty(scores)) {
    return [];
  }
  let list = [
    {
      h: servingSide == 1,
      w: servingSide == 2,
    },
  ];
  if (scores['ft']) {
    list.push(getTennisScoreListItem(scores['ft'], [], matchStatus, true, enumList));
  }
  list = list.concat(getAmFootballSectionScoreList(matchStatus, scores, needEmpty, enumList));
  // if(scores['ot']) {
  //   list.push(getTennisScoreListItem(scores['ot'], [], matchStatus, true, enumList))
  // }
  return list;
};

// const IceHockeyScoreKeys = [1, 2, 3];
// export const getIceHockeySectionScoreList = (matchStatus, scores, needEmpty = false, enumList) => {
//   let list = [];
//   for (let v of IceHockeyScoreKeys) {
//     if (scores[`p${v}`]) {
//       list.push(getTennisScoreListItem(scores[`p${v}`], scores[`x${v}`] || [], matchStatus, !scores[`p${v + 1}`], enumList));
//     } else if (needEmpty) {
//       list.push({
//         empty: true,
//       });
//     }
//   }
//   return list;
// };

// • 比分：最左侧一列展示总比分
// 1. 如果没有加时赛和点球，则总比分展示ft
// 2. 如果只有加时赛，没点球（加时赛没平局）：根据加时赛比分（加时赛比分=ot-ft），数字大的一方为1，另一方为0，加在ft上，即为总比分
// 3. 如果有加时赛（ot两个比分相减=0），有点球：根据点球大战比分（点球比分=ap-ot），数字大的一方为1，另一方为0，加在ft上，即为总比分
// 第二列以后展示盘比分p*已结束的盘，分高的黑色，分低的灰色，正在进行中的红色。第三列是加时赛比分（加时赛比分（加时赛比分=ot-ft），数字大的一方为1，另一方为0），第四列是点球大战比分（（点球比分=ap-ot），数字大的一方为1，另一方为0）
export const getIceHockeyScoreList = (matchStatus, scores, needEmpty = false) => {
  if (_.isEmpty(scores)) {
    return null;
  }
  let list = {
    ft: {
      w: 0,
      h: 0,
    },
  };
  if (scores['ft']) {
    list.ft = getTennisScoreListItem(scores['ft'], [], matchStatus, true, IckHockeyInLiveStatusEnum);
  }

  list.sectionList = getTennisSectionScoreList(matchStatus, scores, needEmpty, IckHockeyInLiveStatusEnum, 3);

  if (scores['ot']) {
    const ot = getTennisScoreListItem(scores['ot'], [], matchStatus, false, IckHockeyInLiveStatusEnum);
    ot.w = ot.w - list.ft.w;
    ot.h = ot.h - list.ft.h;

    if (ot.w > ot.h) {
      list.ft.w = list.ft.w + 1;
      ot.w = 1;
      ot.h = 0;
    } else if (ot.w < ot.h) {
      list.ft.h = list.ft.h + 1;
      ot.w = 0;
      ot.h = 1;
    } else {
      ot.w = 0;
      ot.h = 0;
    }
    list.ot = ot;
  }
  if (scores['ap']) {
    const ap = getTennisScoreListItem(scores['ap'], [], matchStatus, false, IckHockeyInLiveStatusEnum);
    ap.w = ap.w - (scores['ot'] ? scores['ot'][1] : 0);
    ap.h = ap.h - (scores['ot'] ? scores['ot'][0] : 0);
    if (ap.w > ap.h) {
      list.ft.w = list.ft.w + 1;
      ap.w = 1;
      ap.h = 0;
    } else if (ap.w < ap.h) {
      list.ft.h = list.ft.h + 1;
      ap.w = 0;
      ap.h = 1;
    } else {
      ap.w = 0;
      ap.h = 0;
    }
    list.ap = ap;
  }

  return list;
};

export const getSnookerScoreList = (matchStatus, scores) => {
  if (_.isEmpty(scores)) {
    return null;
  }
  let list = {};
  if (scores['ft']) {
    list.ft = getTennisScoreListItem(scores['ft'], [], matchStatus, true, SnookerInLiveStatusEnum);
  }
  return list;
};

// export const getIceHockeyMatchScoreList = (matchStatus, scores, servingSide, needEmpty = false) => {
//   if (_.isEmpty(scores)) {
//     return [];
//   }
//   let list = [];
//   list = list.concat(getTennisSectionScoreList(matchStatus, scores, needEmpty, IckHockeyInLiveStatusEnum));
//
//   if (scores['ot']) {
//     list.push(getTennisScoreListItem(scores['ot'], [], matchStatus, true, IckHockeyInLiveStatusEnum));
//   }
//   if (scores['ap']) {
//     list.push(getTennisScoreListItem(scores['ap'], [], matchStatus, true, IckHockeyInLiveStatusEnum));
//   }
//   if (scores['ft']) {
//     list.push(getTennisScoreListItem(scores['ft'], [], matchStatus, true, IckHockeyInLiveStatusEnum));
//   }
//   return list;
// };

const BaseballScoreKeys = [1, 2, 3, 4, 5, 6, 7, 8, 9];
const getBaseballScoreListItem = ([h, w], [hx, wx], matchStatus, currentSection = false) => {
  const compareV = h - w;
  return {
    h,
    w,
    isRed: BaseballInLiveStatusEnum.includes(matchStatus) && currentSection,
    compareStatus: compareV > 0 ? 1 : compareV === 0 ? 0 : 2, // 1 home > away 2 home < away 0 =
    hx,
    wx,
    empty: false,
  };
};

//match 表格用这个
export const getBaseballSectionScoreList = (matchStatus, scores, servingSide, needEmpty = false) => {
  if (_.isEmpty(scores)) {
    return [];
  }
  let list = [
    {
      h: servingSide == 1,
      w: servingSide == 2,
    },
  ];
  for (let v of BaseballScoreKeys) {
    if (scores[`p${v}`]) {
      list.push(getBaseballScoreListItem(scores[`p${v}`], scores[`x${v}`] || [], matchStatus, !scores[`p${v + 1}`]));
    } else if (needEmpty) {
      list.push({
        h: 0,
        w: 0,
      });
    }
  }
  if (scores['ft']) {
    list.push(getBaseballScoreListItem(scores['ft'], [], matchStatus, true));
  }
  if (scores['h']) {
    list.push(getBaseballScoreListItem(scores['h'], [], matchStatus, false));
  }
  if (scores['e']) {
    list.push(getBaseballScoreListItem(scores['e'], [], matchStatus, false));
  }
  return list;
};
export const getBaseballScoreList = (matchStatus, scores, servingSide) => {
  let list = [
    {
      h: servingSide == 1,
      w: servingSide == 2,
    },
  ];
  if (scores && scores['ft']) {
    list.push(getBaseballScoreListItem(scores['ft'], [], matchStatus, true));
  }
  return list;
};

const TableTennisScoreKeys = [1, 2, 3, 4, 5];
// "Serving side, 1. Home team, 2. Away team
const getTableTennisScoreListItem = ([h, w], [hx, wx], matchStatus, currentSection = false) => {
  const compareV = h - w;
  return {
    h,
    w,
    isRed: TableTennisInLiveStatusEnum.includes(matchStatus) && currentSection,
    compareStatus: compareV > 0 ? 1 : compareV === 0 ? 0 : 2, // 1 home > away 2 home < away 0 =
    hx,
    wx,
    empty: false,
  };
};
export const getTableTennisScoreList = (matchStatus, scores, servingSide, needEmpty = false) => {
  let list = [
    {
      h: servingSide == 1,
      w: servingSide == 2,
    },
  ];
  if (scores['ft']) {
    list.push(getTableTennisScoreListItem(scores['ft'], [], matchStatus, true));
  } else {
    list.push({
      h: 0,
      w: 0,
      isRed: TableTennisInLiveStatusEnum.includes(matchStatus),
    });
  }
  for (let v of TableTennisScoreKeys) {
    if (scores[`p${v}`]) {
      list.push(getTableTennisScoreListItem(scores[`p${v}`], scores[`x${v}`] || [], matchStatus, !scores[`p${v + 1}`]));
    } else if (needEmpty) {
      list.push({
        empty: true,
      });
    }
  }
  return list;
};

// const BadmintonScoreKeys = [1, 2, 3, 4, 5];
// "Serving side, 1. Home team, 2. Away team
const getBadmintonScoreListItem = ([h, w], [hx, wx], matchStatus, currentSection = false) => {
  const compareV = h - w;
  return {
    h,
    w,
    isRed: BadmintonInLiveStatusEnum.includes(matchStatus) && currentSection,
    compareStatus: compareV > 0 ? 1 : compareV === 0 ? 0 : 2, // 1 home > away 2 home < away 0 =
    hx,
    wx,
    empty: false,
  };
};

export const getBadmintonScoreList = (matchStatus, scores, servingSide, needEmpty = false) => {
  let list = [
    {
      h: servingSide == 1,
      w: servingSide == 2,
    },
  ];
  if (scores['ft']) {
    list.push(getBadmintonScoreListItem(scores['ft'], [], matchStatus, true));
  } else {
    list.push({
      h: 0,
      w: 0,
      isRed: BadmintonInLiveStatusEnum.includes(matchStatus),
    });
  }
  for (let v of TableTennisScoreKeys) {
    if (scores[`p${v}`]) {
      list.push(getBadmintonScoreListItem(scores[`p${v}`], scores[`x${v}`] || [], matchStatus, !scores[`p${v + 1}`]));
    } else if (needEmpty) {
      list.push({
        empty: true,
      });
    }
  }
  return list;
};

const HandballScoreKeys = [1, 2];
// "Serving side, 1. Home team, 2. Away team
const getHandballScoreListItem = ([h, w], [hx, wx], matchStatus, currentSection = false) => {
  const compareV = h - w;
  return {
    h,
    w,
    isRed: HandballInLiveStatusEnum.includes(matchStatus) && currentSection,
    compareStatus: compareV > 0 ? 1 : compareV === 0 ? 0 : 2, // 1 home > away 2 home < away 0 =
    hx,
    wx,
    empty: false,
  };
};

export const getHandballScoreList = (matchStatus, scores, servingSide, needEmpty = false) => {
  let list = [
    {
      h: servingSide == 1,
      w: servingSide == 2,
    },
  ];
  if (scores['ft']) {
    list.push(getHandballScoreListItem(scores['ft'], [], matchStatus, true));
  } else {
    list.push({
      h: 0,
      w: 0,
      isRed: HandballInLiveStatusEnum.includes(matchStatus),
    });
  }
  for (let v of HandballScoreKeys) {
    if (scores[`p${v}`]) {
      list.push(getHandballScoreListItem(scores[`p${v}`], scores[`x${v}`] || [], matchStatus, !scores[`p${v + 1}`]));
    } else if (needEmpty) {
      list.push({
        empty: true,
      });
    }
  }
  return list;
};

export const getCricketScore = (matchStatus, scores) => {
  if (!scores || typeof scores !== 'object' || !Array.isArray(scores.innings)) {
    return [];
  }

  const homeScore = scores.innings.find((item) => item[0] === 1) || [0, 0, 0, 0];
  const awayScore = scores.innings.find((item) => item[0] === 2) || [0, 0, 0, 0];
  const compareV = homeScore[1] - awayScore[1];
  const item = {
    h: `${homeScore[1]} / ${homeScore[3]} (${homeScore[2]})`,
    w: `${awayScore[1]} / ${awayScore[3]} (${awayScore[2]})`,
    isRed: CricketInLiveStatusEnum.includes(matchStatus),
    compareStatus: compareV > 0 ? 1 : compareV === 0 ? 0 : 2, // 1 home > away 2 home < away 0 =
    empty: false,
  };

  return [item];
};

export const getWaterpoloScoreList = (matchStatus, scores, needEmpty = false) => {
  if (_.isEmpty(scores)) {
    return null;
  }
  let list = {
    ft: {
      w: 0,
      h: 0,
    },
  };
  if (scores['ft']) {
    list.ft = getTennisScoreListItem(scores['ft'], [], matchStatus, true, IckHockeyInLiveStatusEnum);
  }

  list.sectionList = getTennisSectionScoreList(matchStatus, scores, needEmpty, IckHockeyInLiveStatusEnum, 4);
  return list;
};

export const getEsportsScore = (matchStatus, homeScore, awayScore) => {
  const compareV = homeScore - awayScore;
  const item = {
    h: homeScore,
    w: awayScore,
    isRed: EsportsInLiveStatusEnum.includes(matchStatus),
    compareStatus: compareV > 0 ? 1 : compareV === 0 ? 0 : 2, // 1 home > away 2 home < away 0 =
    empty: false,
  };
  return [item];
};
