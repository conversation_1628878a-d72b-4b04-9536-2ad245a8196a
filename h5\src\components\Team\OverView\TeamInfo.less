.team-overview-info-container {
  // display: flex;
  // flex-direction: column;

  .team-competition-container,
  .team-venue-container,
  .team-info-container {
    display: flex;
    flex-direction: column;
    border-radius: 24px;
    margin: 10px;
    flex: 1;
    overflow: hidden;

    .container-title {
      font-size: 26px;
      font-weight: 700;
      background: #2c2c2c;
      color: #fff;
      border-radius: 24px 24px 0px 0px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding: 10px 15px 10px 15px;
      align-items: center;
    }

    .container-body {
      background: #1e1e1e;
      padding: 10px;
    }
  }

  .team-competition-container {
    .container-body {
      display: grid;
      grid-template-columns: repeat(2, 1fr);

      .competition-card {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: 15px;
        padding: 15px;
        border-radius: 24px;
        background: #121212;

        .competition-icon {
          width: 80px;
          height: 80px;
          border-radius: 50%;
          margin: 10px;
          overflow: hidden;
          object-fit: cover;
        }

        .competition-name {
          font-size: 22px;
          color: #fff;
          text-align: center;
        }
      }
    }
  }

  .team-venue-container {
    .container-body {
      background: #121212;
      
      .venue-details {
        padding: 10px 5px;
        display: flex;
        align-items: center;
        justify-content: space-between;
  
        .venue-value {
          color: #fff;
          font-size: 24px;
        }
      }
    }
  }

  .team-info-container {
    .container-body {
      background: #121212;

      .team-details {
        padding: 10px 5px;
        display: flex;
        align-items: center;
        justify-content: space-between;
  
        .team-value {
          color: #fff;
          font-size: 24px;

          .country-info {
            display: flex;
            align-items: center;

            .country-logo {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 30px;
              height: 30px;
              border-radius: 50%;
              margin-right: 10px;
              overflow: hidden;
              background-size: cover;
            }
          }
        }
      }
    }
  }
}