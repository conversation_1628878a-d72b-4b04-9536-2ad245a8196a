import React, { useState, useEffect } from 'react';
import { List, Avatar, Typography, Button, Empty, Tooltip } from 'antd';
import { ClockCircleOutlined, DeleteOutlined, TeamOutlined, TrophyOutlined, UserOutlined } from '@ant-design/icons';
import moment from 'moment';
import { 
  getRecentActivity, 
  clearRecentActivity, 
  removeRecentActivity, 
  RecentActivityItem,
  isLocalStorageAvailable 
} from '../../utils/recentActivity';
import './RecentActivity.less';

const { Text, Title } = Typography;

interface RecentActivityProps {
  maxItems?: number;
  showClearButton?: boolean;
  onItemClick?: (item: RecentActivityItem) => void;
}

const RecentActivity: React.FC<RecentActivityProps> = ({ 
  maxItems = 10, 
  showClearButton = true,
  onItemClick 
}) => {
  const [recentItems, setRecentItems] = useState<RecentActivityItem[]>([]);
  const [loading, setLoading] = useState(false);

  // Load recent activity from localStorage
  const loadRecentActivity = () => {
    if (!isLocalStorageAvailable()) {
      console.warn('localStorage is not available');
      return;
    }

    try {
      const items = getRecentActivity();
      setRecentItems(items.slice(0, maxItems));
    } catch (error) {
      console.error('Error loading recent activity:', error);
    }
  };

  // Clear all recent activity
  const handleClearAll = () => {
    setLoading(true);
    try {
      clearRecentActivity();
      setRecentItems([]);
    } catch (error) {
      console.error('Error clearing recent activity:', error);
    } finally {
      setLoading(false);
    }
  };

  // Remove specific item
  const handleRemoveItem = (item: RecentActivityItem, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent item click
    try {
      removeRecentActivity(item.id, item.type);
      loadRecentActivity(); // Reload the list
    } catch (error) {
      console.error('Error removing item:', error);
    }
  };

  // Handle item click
  const handleItemClick = (item: RecentActivityItem) => {
    if (onItemClick) {
      onItemClick(item);
    } else {
      // Default behavior - navigate to item
      if (item.url) {
        window.open(item.url, '_blank');
      }
    }
  };

  // Get icon for item type
  const getItemIcon = (type: RecentActivityItem['type']) => {
    switch (type) {
      case 'team':
        return <TeamOutlined />;
      case 'match':
        return <TrophyOutlined />;
      case 'competition':
      case 'league':
        return <TrophyOutlined />;
      case 'player':
        return <UserOutlined />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  // Get item description
  const getItemDescription = (item: RecentActivityItem) => {
    const timeAgo = moment(item.timestamp).fromNow();
    const parts = [];

    if (item.sport) {
      parts.push(item.sport.charAt(0).toUpperCase() + item.sport.slice(1));
    }

    if (item.metadata?.competitionName) {
      parts.push(item.metadata.competitionName);
    }

    if (parts.length > 0) {
      return `${parts.join(' • ')} • ${timeAgo}`;
    }

    return timeAgo;
  };

  // Load data on component mount and set up storage listener
  useEffect(() => {
    loadRecentActivity();

    // Listen for storage changes (when user opens multiple tabs)
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'user_recent_activity') {
        loadRecentActivity();
      }
    };

    window.addEventListener('storage', handleStorageChange);

    // Also listen for custom events from same tab
    const handleCustomUpdate = () => {
      loadRecentActivity();
    };

    window.addEventListener('recentActivityUpdated', handleCustomUpdate);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('recentActivityUpdated', handleCustomUpdate);
    };
  }, [maxItems]);

  if (!isLocalStorageAvailable()) {
    return (
      <div className="recent-activity-container">
        <Text type="secondary">Recent activity is not available (localStorage disabled)</Text>
      </div>
    );
  }

  return (
    <div className="recent-activity-container">
      <div className="recent-activity-header">
        <Title level={4}>
          <ClockCircleOutlined /> Recent Activity
        </Title>
        {showClearButton && recentItems.length > 0 && (
          <Button 
            type="text" 
            size="small" 
            icon={<DeleteOutlined />}
            onClick={handleClearAll}
            loading={loading}
          >
            Clear All
          </Button>
        )}
      </div>

      {recentItems.length === 0 ? (
        <Empty 
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="No recent activity"
          style={{ padding: '20px 0' }}
        />
      ) : (
        <List
          className="recent-activity-list"
          dataSource={recentItems}
          renderItem={(item) => (
            <List.Item
              className="recent-activity-item"
              onClick={() => handleItemClick(item)}
              actions={[
                <Tooltip title="Remove">
                  <Button
                    type="text"
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={(e) => handleRemoveItem(item, e)}
                  />
                </Tooltip>
              ]}
            >
              <List.Item.Meta
                avatar={
                  item.logo ? (
                    <Avatar src={item.logo} size="small" />
                  ) : (
                    <Avatar icon={getItemIcon(item.type)} size="small" />
                  )
                }
                title={
                  <Text strong className="item-title">
                    {item.name}
                  </Text>
                }
                description={
                  <Text type="secondary" className="item-description">
                    {getItemDescription(item)}
                  </Text>
                }
              />
            </List.Item>
          )}
        />
      )}
    </div>
  );
};

export default RecentActivity;
