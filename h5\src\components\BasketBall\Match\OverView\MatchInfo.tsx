import './MatchInfo.less';
import './SingleMatchLine.less';

import GlobalUtils, { GlobalConfig, PageTabs } from 'iscommon/const/globalConfig';
import { inject, observer } from 'mobx-react';
import { translate, useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { useMemo, useState } from 'react';

import calendarDays from 'iscommon/assets/images/calendarDays.png'
import courtSport from 'iscommon/assets/images/courtSport.png'
import locationDot from 'iscommon/assets/images/locationDot.png'
import moment from 'moment';
import { momentTimeZone } from 'iscommon/utils';
import RightOutlined from '@ant-design/icons/lib/icons/RightOutlined';
import { useCrumbList } from 'iscommon/hooks/content';
import { useParams } from 'umi';

const MatchInfo = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Basketball: {
          Match: { matchHeaderInfo, shouldShowVideo },
        },
      },
    } = props;
    const { tabId } = useParams<{ categoryId: string; tabId: string }>();

    if (!matchHeaderInfo) return null;

    const { homeTeam, awayTeam, matchTime, competition, venue } = matchHeaderInfo;
    const startTime = momentTimeZone(matchTime, 'DD MMM YYYY HH:mm:ss');

    const matchName = useMemo(
      () => `${matchHeaderInfo?.homeTeam?.name} vs ${matchHeaderInfo?.awayTeam?.name}, ${tabId.toLowerCase()}`,
      [matchHeaderInfo?.awayTeam?.name, matchHeaderInfo?.homeTeam?.name, tabId],
    );
    const { crumbs } = useCrumbList(PageTabs.match, '', matchName, [], {
      competition: {
        id: competition?.id,
        name: competition?.name,
      },
    });

    const [isExpanded, setIsExpanded] = useState(false);
    const labelMaps = useTranslateKeysToMaps(['MatchInfo']);

    const toggleContent = () => {
      setIsExpanded(prev => !prev);
    };

    const htmlContent = useMemo(() => {
      const videoText = shouldShowVideo ? ' (and video online live stream)' : '';
      const matchLink = `<a target="_blank" style="color:#0F80DA; font-weight:700" href="${window.location.href}">
        ${homeTeam.name} vs ${awayTeam.name}</a>`;
      const competitionLink = `<a target="_blank" style="color:#0F80DA; font-weight:700" href="${crumbs?.[1]?.url || ''}">${competition?.name}</a>`;

      return {
        __html: `
          <p style="font-size:12px;color: #fff;">
            <span style="font-size:13px;font-weight:700">About The Match</span><br />
            ${matchLink} live score${videoText} starts on ${matchHeaderInfo?.matchTime} UTC time in ${matchHeaderInfo?.competition?.name}.
            Here on ${matchLink} livescore you can find all ${matchLink} previous results sorted by their H2H matches.<br />
            <span style="font-size:13px;font-weight:700">Match Details:</span><br />
            Event: ${competitionLink}<br />
            Name: ${matchLink}<br />
            Date: ${moment.unix(matchHeaderInfo?.matchTime).format('YYYY/MM/DD')}<br />
            Time: ${moment.unix(matchHeaderInfo?.matchTime).format('HH:mm:ss')}<br />
            Stadium: ${venue?.name || '--'}<br />
            <span style="font-size:13px;font-weight:700">More details:</span><br />
            <a target="_blank" style="color:#0F80DA; font-weight:700" href="${GlobalUtils.getPathname(PageTabs.team, homeTeam.id)}">
              ${homeTeam.name} fixtures
            </a><br />
            <a target="_blank" style="color:#0F80DA; font-weight:700" href="${GlobalUtils.getPathname(PageTabs.team, awayTeam.id)}">
              ${awayTeam.name} fixtures
            </a><br />
            IGScore ${GlobalConfig.pathname} livescore is available as iPhone app, Android app on Google Play.
            Install IG Score app and follow ${matchLink} live on your mobile!
          </p>`,
      };
    }, [crumbs, shouldShowVideo, matchHeaderInfo]);

    return (
      <div className='basketball-overview-match-info-container'>
        <div className='container-title'>{labelMaps.MatchInfo}</div>
        <div className='container-body'>
          <div className='item-container'>
            <div className='item-icon-text'>
              <img className='item-icon' src={calendarDays} alt='calendar' loading="lazy"/>
              <span className='item-text'>{translate('Date & Time')}</span>
            </div>
            <span className='item-value'>{startTime}</span>
          </div>
          <div className='item-container'>
            <div className='item-icon-text'>
              <img className='item-icon' src={courtSport} alt='stadium' loading="lazy"/>
              <span className='item-text'>{translate('Stadium')}</span>
            </div>
            <span className='item-value'>{venue?.name || '-'}</span>
          </div>
          <div className='item-container'>
            <div className='item-icon-text'>
              <img className='item-icon' src={locationDot} alt='location' loading="lazy"/>
              <span className='item-text'>{translate('Location')}</span>
            </div>
            <span className='item-value'>{venue?.city || '-'}</span>
          </div>
          <div className='item-container toggle' onClick={toggleContent}>
            <div className='item-icon-text'>
              <RightOutlined className={isExpanded ? 'rotated-icon' : 'default-icon'} />
              <span className='item-text'>Click to show/hide more content</span>
            </div>
          </div>
          {isExpanded && <div className="about-match-content" dangerouslySetInnerHTML={htmlContent} />}
        </div>
      </div>
    );
  }),
);

export default MatchInfo;