// @ts-nocheck
import igsRequest from "iscommon/request/instance";
import { genValidObj } from "iscommon/utils";

/**
 * @param {{ playerId: string }}
 * @returns {any}
 */
export const getPlayerHeader = async ({ playerId } = {}) => {
  try {
    return await igsRequest.post("player/detail/header", { playerId });
  } catch (e) {
    console.error("getPlayerHeader error:", e);
    return null;
  }
};

export const getPlayerHonor = async ({ playerId } = {}) => {
  try {
    return await igsRequest.post("player/detail/honor", { playerId });
  } catch (e) {
    console.error("getPlayerHonor error:", e);
    return null;
  }
};

export const getPlayerMatches = async ({
  playerId = "",
  month = "",
  competitionId = "",
} = {}) => {
  try {
    return await igsRequest.post(
      "player/detail/schedule",
      genValidObj({ playerId, month, competitionId })
    );
  } catch (e) {
    console.error("getPlayerHonor error:", e);
    return null;
  }
};

export const getPlayerStats = async ({
  playerId = "",
  seasonId = "",
  competitionId = "",
} = {}) => {
  try {
    return await igsRequest.post(
      "player/detail/competition/statistics",
      genValidObj({ playerId, seasonId, competitionId })
    );
  } catch (e) {
    console.error("getPlayerStats error:", e);
    return null;
  }
};

export const getPlayerTransfers = async ({ playerId = "" } = {}) => {
  try {
    return await igsRequest.post("player/detail/transfer", { playerId });
  } catch (e) {
    console.error("getPlayerTransfers error:", e);
    return null;
  }
};

export const getPlayerStatistics = async ({
  playerId,
  seasonId,
  competitionId,
} = {}) => {
  try {
    return await igsRequest.post(
      "player/detail/statistics",
      genValidObj({
        playerId,
        seasonId,
        competitionId,
      })
    );
  } catch (e) {
    console.log("getPlayerStatistics error:", e);
    return null;
  }
};

export const getPlayerStatisticsSelector = async ({ playerId } = {}) => {
  try {
    return await igsRequest.post("player/detail/statistics/selector", { playerId,
    });
  } catch (e) {
    console.log("getPlayerStatisticsSelector error:", e);
    return null;
  }
};

export const getPlayerStatisticsSelectorV2 = async ({ playerId } = {}) => {
  try {
    return await igsRequest.post("player/detail/statistics/selectorv2", { playerId,
    });
  } catch (e) {
    console.log("getPlayerStatisticsSelectorV2 error:", e);
    return null;
  }
};
