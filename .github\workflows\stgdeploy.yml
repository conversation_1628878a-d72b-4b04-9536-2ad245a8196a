# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://help.github.com/actions/language-and-framework-guides/using-nodejs-with-github-actions

name: STG CI

on:
  pull_request:
    types: [closed]
    branches: [ "staging2" ]

jobs:
  build:

    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16.14.0]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
    - uses: actions/checkout@v3
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'yarn'
    - name: Build/Common
      run: yarn

    - name: Build/PC
      run: |
        ls
        echo "install common packages"
        yarn
        echo "cd pc"
        cd pc/
        echo "install pc packages"
        yarn
        echo "build:staging"
        yarn build:staging
        echo "upload"
        yarn run upload
        ls
      shell: bash

    - name: Build/H5
      run: |
        ls
        echo "install common packages"
        yarn
        echo "cd h5"
        cd h5/
        echo "install h5 packages"
        yarn
        echo "build:staging"
        yarn build:staging
        echo "upload"
        yarn run upload
        ls
      shell: bash

    - name: deploy PC File to server
      uses: cross-the-world/scp-pipeline@master
      with:
        host: ${{ secrets.STG_SERVER_IP }}
        user: ${{ secrets.STG_SSH_USERNAME }}
        key: ${{ secrets.STG_SSH_TOKEN }}
        port: ${{ secrets.STG_SSH_PORT }}
        connect_timeout: 100s
        local: './pc/dist/*'
        remote: '/home/<USER>/staging2/ssr/pc/dist'

    - name: deploy PC Node File to server
      uses: cross-the-world/scp-pipeline@master
      with:
        host: ${{ secrets.STG_SERVER_IP }}
        user: ${{ secrets.STG_SSH_USERNAME }}
        key: ${{ secrets.STG_SSH_TOKEN }}
        port: ${{ secrets.STG_SSH_PORT }}
        connect_timeout: 100s
        local: './node-pc/*'
        remote: '/home/<USER>/staging2/ssr/ssr-pc'

    - name: deploy H5 File to server
      uses: cross-the-world/scp-pipeline@master
      with:
        host: ${{ secrets.STG_SERVER_IP }}
        user: ${{ secrets.STG_SSH_USERNAME }}
        key: ${{ secrets.STG_SSH_TOKEN }}
        port: ${{ secrets.STG_SSH_PORT }}
        connect_timeout: 100s
        local: './h5/dist/*'
        remote: '/home/<USER>/staging2/ssr/h5/dist'

    - name: deploy H5 Node File to server
      uses: cross-the-world/scp-pipeline@master
      with:
        host: ${{ secrets.STG_SERVER_IP }}
        user: ${{ secrets.STG_SSH_USERNAME }}
        key: ${{ secrets.STG_SSH_TOKEN }}
        port: ${{ secrets.STG_SSH_PORT }}
        connect_timeout: 100s
        local: './node-h5/*'
        remote: '/home/<USER>/staging2/ssr/ssr-h5'
