import { Button, Image, Picker, Space } from 'antd-mobile';
import { inject, observer } from 'mobx-react';
import React, { useCallback, useMemo, useState } from 'react';

import { CategoryIcon, FallbackCategoryImage, FallbackImage } from 'iscommon/const/icon';

import styles from './CompetitionHeader.less';
import FavoriteIcon from '../Common/FavoriteIcon';

import './CompetitionHeader.less'
import { PickerValue } from 'antd-mobile/es/components/picker';
import { DownOutlined } from '@ant-design/icons';

interface Props {
  requestHeader: (seasonId?: string) => Promise<void>;
  showSeason?: boolean;
  store?: any;
}

const CompetitionHeader: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: { Competitions },
      showSeason = true,
      requestHeader,
    } = props;
    const {
      commonInfo: { competition, competitionPlayerStatsDto },
      seasons = [],
      currentSeason,
    } = Competitions;

    const [visible, setVisible] = useState(false);
    const [selectedSeason, setSelectedSeason] = useState<PickerValue>(currentSeason?.id); 
    
    const countryInfo = useMemo(() => {
      if (competition?.type === 2) {
        const { logo, name } = competition.categoryDto;
        return {
          ...competition.categoryDto,
          logo: (CategoryIcon as any)[logo || ''] || logo || FallbackImage,
        };
      }
      return competition.country;
    }, [competition.categoryDto, competition.country, competition?.type]);

    const onSelect = useCallback(
      (id: string) => {
        const currentSeason = seasons.find((item: any) => item.id === id);
        Competitions.changeCurrentSeason(currentSeason);
        if (currentSeason?.id) {
          requestHeader(currentSeason?.id);
        }
      },
      [Competitions, requestHeader, seasons],
    );

    const handleConfirm = (value: PickerValue[]) => {
      const selectedId = value[0];
      if (typeof selectedId === 'string') { 
        onSelect(selectedId);
        setSelectedSeason(selectedId); 
      }
      setVisible(false); 
    };

    return (
      <div className='common-competition-header-container'>
        <div className='competition-container'>
          <img className='competition-icon' src={competition?.logo || FallbackCategoryImage} alt={competition?.name} loading="lazy"/>
          <div className='competition-info'>
            <div className='competition-name-container'>
              <span className='competition-name'>{competition?.name}</span>
              {/* <FavoriteIcon isMatch={false} competition={{ ...competition, curStageId: currentSeason.id }}/> */}
            </div>
            <div className='competition-detail'>
              <img className='country-icon' src={countryInfo?.logo || FallbackCategoryImage} alt={countryInfo?.name} loading="lazy"/>
              <span className='country-name'>{countryInfo?.name}</span>
            </div>
          </div>
        </div>
        {showSeason && !!seasons.length && currentSeason && (
          <>
            <Button className='competition-header-btn' size="small" onClick={() => setVisible(true)}>
              <Space>
                {currentSeason.year && (() => {
                  const [startYear, endYear] = currentSeason.year.split('-').map((year: any) => year.slice(-2));
                  return endYear ? `${startYear}-${endYear}` : startYear;
                })()}
                <DownOutlined />
              </Space>
            </Button>

            <Picker
              columns={[
                seasons.map((item: any) => ({
                  label: item.year,
                  value: item.id,
                })),
              ]}
              visible={visible}
              onClose={() => setVisible(false)} 
              onConfirm={handleConfirm}
            />
          </>
        )}
      </div>
    );
  }),
);

export default CompetitionHeader;
