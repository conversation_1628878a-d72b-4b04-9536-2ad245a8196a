import CommonStandingsIntegral from '@/components/Common/CommonStanding/CommonStandingsIntegral';
import Loading from '@/components/Loading';
import { getCompetitionStandings } from 'iscommon/api/basketball/basketball-competition';
import { translate, useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { inject, observer } from 'mobx-react';
import { useEffect, useState } from 'react';
import styles from './index.less';
import './index.less'
import { Collapse } from 'antd-mobile';
// interface Props {
//   competitions: {
//     competitionId: number;
//     currentSeason: {
//       value: number;
//     };
//     competitionName: string;
//   };
//   [key: string]: any;
// }

const Standings = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Basketball: { Match },
      },
    } = props;
    const { matchHeaderInfo } = Match;
    const {
      competition: { id: competitionId, logo },
      seasonId,
      homeTeam: {id: hid},
      awayTeam: {id: aid}
    } = matchHeaderInfo || {};
    const labelMaps = useTranslateKeysToMaps(['All', 'Home', 'Away', 'Standings']);

    const [sourceList, setSourceList] = useState<any[]>([]);
    const [type, setType] = useState<0 | 1 | 2>(0);

    const [loading, setLoading] = useState<boolean>(true);

    const buttonGroupMaps = [
      {
        label: labelMaps.All,
        value: 0,
      },
      {
        label: labelMaps.Home,
        value: 1,
      },
      {
        label: labelMaps.Away,
        value: 2,
      },
    ];

    useEffect(() => {
      if (competitionId && seasonId) {
        getCompetitionStandings({ seasonId, competitionId, scope: 5 })
          .then(({ tables }: any) => {
            setSourceList(tables);
            // setPromotions(promotions || []);
          })
          .finally(() => {
            setLoading(false);
          });
      } else if (competitionId && !seasonId) {
        setLoading(false);
      }
    }, [type, seasonId, competitionId]);

    const titleMap = [
      {
        label: 'W',
        value: 'won',
      },
      {
        label: 'L',
        value: 'lost',
      },
      {
        label: 'Win%',
        value: ({ won_rate }) => (won_rate * 100).toFixed(1),
      },
      {
        label: 'STR',
        value: 'streaks',
      },
      {
        label: 'GB',
        value: ({game_back}) => game_back === '-' ? '0.0' : game_back,
      },
    ];

    return (
      <div className='basketball-overview-standing-container'>
        <div className='container-title'>{labelMaps.Standings}</div>
        <div className='container-body'>
          <Loading loading={loading} isEmpty={!sourceList?.length}/>
          <Collapse className='custom-collapse'>
            {sourceList.map((source, index) => (
              <Collapse.Panel
                key={index}
                title={
                  <div className="panel-header">
                    <span className='panel-title'>{source.name}</span>
                  </div>
                }
              >
                <CommonStandingsIntegral
                  data={source.standings}
                  titleMap={titleMap}
                  type={type}
                  promotions={[]}
                  isMatch
                  key={source.name}
                  currentTeamIds={[hid, aid]}
                />
              </Collapse.Panel>
            ))}
          </Collapse> 
        </div>
      </div>
      // <Loading loading={loading} isEmpty={sourceList.length === 0}>
      //   <div className={styles.container_box}>
      //     {/* <div className={styles.radio_container}>
      //       {buttonGroupMaps.map((item: any) => (
      //         <span className={`${styles.radio_btn} ${type === item.value ? styles.active : ''}`} key={item.value}>
      //           {translate(item.label)}
      //         </span>
      //       ))}
      //     </div> */}
      //     {sourceList.map((item: any) => {
      //       return (
      //         <div key={item.name}>
      //           <div className={styles.comp}>
      //             <img src={logo} />
      //             <span>{item.name}</span>
      //           </div>
                // <CommonStandingsIntegral
                //   data={item.standings}
                //   titleMap={titleMap}
                //   type={type}
                //   promotions={[]}
                //   isMatch
                //   key={item.name}
                //   currentTeamIds={[hid, aid]}
                // />
      //         </div>
      //       );
      //     })}
      //   </div>
      // </Loading>
    );
  }),
);

export default Standings;
