/* eslint-disable react/no-unknown-property */

import { HomeGameTab } from 'iscommon/const/constant';
import { translate } from 'iscommon/i18n/utils';
import { inject, observer } from 'mobx-react';
import moment from 'moment';
import { useMemo, useState } from 'react';
import { Link } from 'umi';

import CalendarBlock from '@/components/Common/Calendar';

import { GlobalConfig } from 'iscommon/const/globalConfig';
import './GameTab.less';

const TabLeftMap = [
  {
    key: HomeGameTab.All,
    name: 'AllGames',
  },
  {
    key: HomeGameTab.Live,
    name: 'LiveH5',
  },
  {
    key: HomeGameTab.Leagues,
    name: 'Leagues',
  },
];

const GameTab = inject('store')(
  observer((props: any) => {
    const {
      Smallball: { SmallballCommon: WebHome },
      WebConfig,
    } = props.store;
    const { currentGameTab, date } = WebHome;
    const { currentLiveCount } = WebConfig;
    const sortByTime = translate('SortByTime');
    const [calendarVis, setCalendarVis] = useState<boolean>(false);

    const dateText = useMemo(() => moment(date).format('DD'), [date]);

    console.log(currentLiveCount, 'currentLiveCount')

    return (
      <>
        <div className="tabContainer">
          <div>
            {TabLeftMap.map((item) => {
              return (
                <div
                  className={`tabItem ${item.key === currentGameTab ? (item.key == 0 ? 'activelive' : 'active') : ''}`}
                  key={item.key}
                  onClick={() => {
                    WebHome.switchHomeGameTab(item.key);
                  }}
                >
                  <span className="innerText">{translate(item.name)}</span>
                </div>
              );
            })}
          </div>
          {currentGameTab == HomeGameTab.All && (
            <div className="calendarIcon" onClick={() => setCalendarVis(!calendarVis)}>
              <span className="icon iconfont iconrili myriliIcon"></span>
              <div className="dateIcon">{dateText}</div>
            </div>
          )}
        </div>
        {currentGameTab === HomeGameTab.All && (
          <Link to={`/${GlobalConfig.pathname}/timesort`} className="sortByTime">
            <span style={{ color: '#666' }}>{sortByTime}</span>
            {currentLiveCount ? (
              <span>
                <i className="current">{currentLiveCount.liveCount || 0}</i>/{currentLiveCount.totalCount || 0}
                <span className="icon iconfont fs-12 iconjiantou"></span>
              </span>
            ) : null}
          </Link>
        )}
        {calendarVis ? (
          <CalendarBlock
            defaultSingle={date}
            onChange={(date: any) => {
              WebHome.changeDate(date);
              setCalendarVis(false);
            }}
            onCancel={() => {
              setCalendarVis(false);
            }}
          />
        ) : null}
      </>
    );
  }),
);

export default GameTab;
