import classNames from 'classnames';
import { momentTimeZone } from 'iscommon/utils';
import React, { useMemo } from 'react';

import { GlobalConfig, GlobalSportPathname } from 'iscommon/const/globalConfig';
import { calculateScore } from 'iscommon/utils/dataUtils';
import styles from './index.less';

interface Props {
  item: any;
  currentTeamId: string;
  showResult?: boolean;
  renderExtra?: (item: any) => React.ReactNode;
}

const getResult = (a: number, b: number) => {
  if (a > b) return { key: 'W', value: 1 };
  if (a === b) return { key: 'D', value: 0 };
  return { key: 'L', value: -1 };
};

const MatchListItem = React.memo<Props>(({ item, currentTeamId, showResult = true, renderExtra }) => {
  const isFootBall = GlobalConfig.pathname === GlobalSportPathname.football;
  const result = useMemo(() => {
    const aScore = isFootBall ? calculateScore(item.homeScores) : item.calculatedHomeScore;
    const bScore = isFootBall ? calculateScore(item.awayScores) : item.calculatedAwayScore;
    const homeScore = item?.homeTeam?.id === currentTeamId ? aScore : bScore;
    const awayScore = item?.homeTeam?.id === currentTeamId ? bScore : aScore;
    return getResult(homeScore, awayScore);
  }, [currentTeamId, item.awayScores, item.homeScores, item?.homeTeam?.id, item?.calculatedHomeScore, item?.calculatedAwayScore]);

  return (
    <div className={styles.container}>
      <div className={styles.left}>
        <div className={styles.leftText}>{momentTimeZone(item.matchTime, 'YYYY-MM-DD')}</div>
        <div className={styles.leftText}>{item.competition?.name}</div>
      </div>
      <div className={styles.teamWrap}>
        <div className={styles.team}>
          <img className={styles.logo} src={item.homeTeam.logo} alt={item.homeTeam.name} loading="lazy"/>
          <div className={styles.teamName}>{item.homeTeam.name}</div>
        </div>
        <div className={styles.team}>
          <img className={styles.logo} src={item.awayTeam.logo} alt={item.awayTeam.name} loading="lazy"/>
          <div className={styles.teamName}>{item.awayTeam.name}</div>
        </div>
      </div>
      {showResult && (
        <>
          <div className={styles.score}>
            <div className={styles.scoreText}>{isFootBall ? calculateScore(item.homeScores) : item.calculatedHomeScore}</div>
            <div className={styles.scoreText}>{isFootBall ? calculateScore(item.awayScores) : item.calculatedAwayScore}</div>
          </div>
          <div className={styles.result}>
            <div className={classNames(styles.resultIcon, styles[result.key])}>{result.key}</div>
          </div>
        </>
      )}
      {renderExtra?.(item)}
    </div>
  );
});

export default MatchListItem;
