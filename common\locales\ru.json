{"All": "Все", "Live": "LIVE", "LiveH5": "LIVE", "MatchLive": "LIVE", "TimeSort": "Сортировка по времени", "SortByTime": "Сортировка по времени", "AllGames": "ВСЕ ИГРЫ", "Leagues": "ЛИГИ", "h5_Leagues": "ЛИГИ", "Today": "Сегодня", "Cancel": "Отмена", "Popular": "Популярные", "Settings": "Настройки", "Language": "Язык", "Overview": "Обзор", "LiveOverview": "Обзор", "Standings": "Турнирная таблица", "Stats": "Статистика", "Transfer": "Трансфер", "Champions": "Чемпионы", "TeamChampions": "Чемпионы", "teamChampions": "Чемпионы", "Football": "ФУТБОЛ", "Basketball": "БАСКЕТБОЛ", "Baseball": "Бейсбол", "Icehockey": "Хоккей", "Tennis": "ТЕННИС", "Volleyball": "ВОЛЕЙБОЛ", "Esports": "КИБЕРСПОРТ", "Handball": "ГАНДБОЛ", "Cricket": "КРИКЕТ", "WaterPolo": "ВОДНОЕ ПОЛО", "TableTennis": "Настольный теннис", "Snooker": "СНУКЕР", "Badminton": "БАДМИНТОН", "BusinessCooperation": "Business Cooperation", "TermsOfService": "Условия предоставления услуг", "PrivacyPolicy": "Политика конфиденциальности", "Players": "ИГРОКИ", "ForeignPlayers": "Легионеры", "NumberOfTeams": "Количество команд", "YellowCards": "Желтые карточки", "RedCards": "Красные карточки", "Capacity": "Вместимость места", "City": "Город", "Info": "Сведения", "Matches": "МАТЧИ", "Team": "Команда", "Teams": "Команды", "Goals": "Голы", "Assists": "Голевые передачи", "assists": "Голевые передачи", "Home": "Дома", "Away": "На выезде", "topScorers": "Рекордсмены", "TopScorers": "Рекордсмены", "homeTopScorers": "Рекордсмены", "season": "Сезон", "Season": "Сезон", "ShotsOnTarget": "Удары в створ", "Clearances": "Вынос мяча", "Tackles": "Отбор мяча", "keyPasses": "Острые передачи", "KeyPasses": "Острые передачи", "Fouls": "Фолы", "totalFouls": "Фолы", "WasFouled": "Заработано фолов", "Penalty": "Пенальти", "MinutesPlayed": "Сыграно минут", "BasketballMinutesPlayed": "Сыграно минут", "Interceptions": "Перехваты", "Steals": "Перехваты", "steals": "Перехваты", "Passes": "Переходя", "Saves": "Сэйвы", "BlockedShots": "Блокирование ударов", "Signed": "Под<PERSON>и<PERSON><PERSON>н", "league": "", "offensiveData": "Данные об атаке", "defenseData": "Данные о защите", "otherData": "Прочие данные", "ballPossession": "Владения мячом", "shotsPerGame": "Ударов за игру", "ShotsPerGame": "Ударов за игру", "keyPassesPerGame": "Ключевые пасы за игру", "accurateLongBallsPerGame": "Acc. long balls per game", "accurateCrossesPerGame": "Acc. crosses per game", "tacklesPerGame": "Отборы за игру", "TacklesPerGame": "Отборы за игру", "interceptionsPerGame": "Перехваты за игру", "InterceptionsPerGame": "Перехваты за игру", "clearancesPerGame": "Выносы за игру", "ClearancesPerGame": "Выносы за игру", "blockedShotsPerGame": "Блокирование ударов за игру", "turnoversPerGame": "Потери за игру", "foulsPerGame": "Фолы за игру", "scoringFrequencyFiveGoals": "", "Coach": "Тренер", "Goalkeeper": "Вратарь", "Stadium": "Стадион", "Login": "Войти", "Corner": "Угол", "ShotsOffTarget": "Удары мимо ворот", "H2H": "H2H", "Date": "Дата", "OwnGoal": "Автогол", "PenaltyMissed": "Пропущенный штрафной", "SecondYellow": "Вторая желтая карточка", "Odds": "Коэффициенты", "attacks": "Атака", "Started": "Начал в основе", "Chat": "Чат", "Strengths": "Сильные стороны", "Weaknesses": "Слабые стороны", "Group": "Группа", "Birthday": "День рождения", "Club": "К<PERSON>у<PERSON>", "MainPosition": "Основная позиция", "PassesAccuracy": "", "Preseason": "", "RegularSeason": "", "PointsPerGame": "Очки за матч", "Glossary": "Глоссарий", "h5Glossary": "Глоссарий", "Career": "Карьера", "Bench": "В запасе", "ReboundsPerGame": "Подборы за матч", "AssistsPerGame": "Голевые передачи за матч", "OddsFormat": "Формат коэффиц.", "Squad": "Составы команд", "TotalMarketValue": "Общая рыночная стоимость", "Rounds": "Количество матчей", "LowerDivision": "Дивизион ниже", "TeamStats": "Статистика команды", "GoalsPk": "Голы(PK)", "Crosses": "Кроссы", "CrossesAccuracy": "Передайте мяч успешно", "Dribble": "Необычайный", "DribbleSucc": "Чрезвычайный успех", "LongBalls": "Длинные мячи", "LongBallsAccuracy": "Шанс успеха Long прохождения", "Duels": "Фолы", "DuelsWon": "Чистый лист", "Dispossessed": "Сэйвы", "Punches": "Успех атаки Вратарь", "RunsOut": "Игра на выходах", "RunsOutSucc": "Блокирование ударов", "GoodHighClaim": "Точность прохождения", "Loan": "Аренда", "EndOfLoan": "Окончание аренды", "Unknown": "неизвестно", "AverageAge": "Средний возраст", "cornersPerGame": "Угловые за игру", "goalsConceded": "Пропущенные голы", "Defender": "Защитник", "Discipline": "Дисциплина", "Pass": "", "FB_Login": "Продолжить с Facebook", "Google_Login": "Продолжить с Google", "Substitutes": "Игроки на замену", "PenaltyKick": "", "ShareYourViews": "Поделитесь своим мнением", "Nodata": "Нет данных", "Foot": "Нога", "dangerousAttack": "", "venue": "Арена", "playerStatistics": "Статистика игрока", "TotalPlayed": "Всего сыграно", "MinutesPerGame": "Минут за игру", "GoalsFrequency": "", "GoalsPerGame": "Цели на местах", "Arrivals": "Прибывшие", "Departures": "Ушедшие", "LeftFoot": "Левая", "RightFoot": "Правая", "LatestTransfers": "Последние трансферы", "DraftInfo": "Инфо о драфте", "OK": "Ок", "AsianHandicap": "", "TotalGoals": "", "AmFootballTotalGames": "", "TotalCorners": "", "OverBall": "Больше", "Over": "Больше", "h5Over": "Больше", "UnderBall": "Меньше", "Under": "Меньше", "h5Under": "Меньше", "OtherLeagues": "Другие лиги [А-Я]", "GoalPopup": "", "FullStandings": "Полные положения", "teamWeek": "Команда недели", "weekTop": "Команда недели", "TeamOfTheWeek": "Команда недели", "round": "<PERSON><PERSON><PERSON>", "Released": "Отпущен", "Retirement": "", "Draft": "Дра<PERSON><PERSON>", "TransferIn": "", "TransferOut": "", "MarketValue": "Рыночная стоимость", "Salary": "Зарплата", "Next": "Далее", "Position": "Позиция", "CTR": "Запись о текущем переводе", "FoulBall": "", "FreeKick": "", "GoalKick": "", "Midfield": "", "HalftimeScore": "", "InjuryTime": "", "OvertimeIsOver": "", "PenaltyKickEnded": "", "Last": "Последний", "Win": "Побед подряд", "Draw": "Нич<PERSON>их", "Lose": "Поражения", "Lineup": "", "Substitution": "Замена", "Offsides": "Офсайды", "Playoffs": "", "Qualifier": "", "GroupStage": "", "Rebounds": "Подборы", "rebounds": "Подборы", "OffensiveRebounds": "Подборы в атаке", "offensiveRebounds": "Подборы в атаке", "DefensiveRebounds": "Подборы в обороне", "defensiveRebounds": "Подборы в обороне", "Turnovers": "Потери", "turnovers": "Потери", "Blocks": "Блоки", "blocks": "Блоки", "BoxScore": "Состав команды", "Foul": "Фол", "FreeThrows": "Штрафные броски", "freeThrowsScored": "Штрафные броски", "fieldGoalsScored": "", "fieldGoalsTotal": "", "threePointersScored": "", "threePointersTotal": "", "freeThrowsTotal": "", "Finished": "Закончилось", "Scheduled": "Арматура", "Favourite": "", "OddsMarkets": "Шансы на рынки", "Search": "", "Timezone": "", "SoccerGoalReminderSettings": "", "RemindersOnlyForRacesToWatch": "", "GoalSound": "", "LiveScore": "", "Schedule": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Rugby": "", "FooterContentFootball": "IGScore Football LiveScore предоставляет вам непревзойденные футбольные результаты в реальном времени и результаты более чем 2600 футбольных лиг, кубков и турниров. Получайте текущий счет, результаты матчей в перерыве между таймами и в основной игре, бомбардиров и ассистентов, карточки, замены, статистику матчей и прямые трансляции из Премьер-лиги, Ла Лиги, Серии А, Бундеслиги, Лиги 1, Эредивизи, Российской Премьер-лиги, Бразилии, MLS, Суперлига и Чемпионат на igscore.net. IGScore предлагает всем футбольным фанатам результаты в реальном времени, результаты футбольных матчей, результаты футбольных матчей, таблицы лиг и матчи для лиг, кубков и турниров, и не только из самых популярных футбольных лиг, таких как Премьер-лига Англии, Испания, Ла Лига, Италия Серия А, Германия. Бундеслига, Франция, Лига 1, а также из большого числа футбольных стран по всему миру, в том числе из Северной и Южной Америки, Азии и Африки. Наши табло текущих результатов по футболу обновляются в режиме реального времени, чтобы вы были в курсе всех обновлений текущих результатов футбольных матчей, происходящих сегодня, а также результатов футбольных матчей в реальном времени для всех завершенных футбольных матчей для каждой футбольной и футбольной лиги. На странице матча наши футбольные карточки результатов позволяют просматривать результаты прошлых игр для всех ранее сыгранных матчей для каждого футбольного соревнования. Получите все свои футбольные результаты в прямом эфире на igscore.net!", "FooterContentBasketball": "IGScore Basketball LiveScore предоставляет вам прямые результаты лиги NBA, результаты, таблицы, статистику, расписание, таблицы и предыдущие результаты по кварталам, тайм-аутам или окончательным результатам. IGScore предлагает услуги по оценке более чем 200 баскетбольных соревнований со всего мира (таких как NCAA, Лига ABA, Балтийская лига, Евролига, национальные баскетбольные лиги). Здесь вы найдете не только живые результаты, результаты за четверть, окончательные результаты и составы команд, но также количество попыток в 2 и 3 очка, штрафные броски, процент попаданий, подборы, обороты, перехваты, личные фолы, историю матчей и статистику игрока. ,На IGScore Basketball в режиме реального времени вы можете смотреть баскетбол онлайн, просто щелкнув по нему, и предоставив вам онлайн-трансляцию матчей высшей лиги. На странице матча также будет таблица со всей статистикой баскетбола о последних играх команд. наши баскетбольные карты обновляются в режиме реального времени, чтобы держать вас в курсе всех результатов баскетбола, происходящих сегодня, и позволяет просматривать результаты прошлых игр для всех ранее сыгранных матчей на всех баскетбольных соревнованиях. Получите все ваши живые результаты NBA на igscore.net! Следите за результатами матчей НБА, матчей НБА, турнирной таблицы НБА и страниц команд!", "FooterContentAmFootball": "IGScore american football live score предоставляет вам все результаты и живые оценки из самой большой и самую популярной американской футбольной лиги в мире - НФЛ, и когда регулярный сезон НФЛ завершен, следуйте в прямом эфире плей-офф NFL и Superbowl. В дополнение к НФЛ мы также предоставим вам жизненные озаты, результаты, закупосы и графики для колледжа NCAA American Football и Canadian CFL.", "FooterContentBaseball": "IGScore baseball live score предоставляет вам текущий счет, результаты и положение в самой популярной в мире бейсбольной лиге - USSSA Baseball, NCAA Baseball, MLB Baseball, MLB Allstar game. Мы также предоставляем текущий счет для профессиональной лиги Японии, Мексиканской лиги, 1-й немецкой бундеслиги, NCAA, а также международного бейсбольного турнира World Baseball Classic. Вы также можете в любое время увидеть турнирную таблицу бейсбольной лиги, прошедшие игры с результатами по иннингам и расписание предстоящих бейсбольных матчей на IGScore baseball live score.", "FooterContentIcehockey": "IGScore ice hockey live score ti fornisce i punteggi dei risultati di hockey su ghiaccio in tempo reale per campionati, coppe e tornei di hockey su ghiaccio. IGScore ice hockey live score ti forniamo risultati in tempo reale di hockey, tabelle, statistiche, partite, risultati e punteggi di NHL, SHL, KHL e forniamo anche campionati nazionali di hockey finlandesi, campionati di hockey svedesi, campionati di hockey slovacchi, campionati di hockey cechi, classifiche, marcatori, terzi e risultati finali di hockey su ghiaccio in diretta. Dopo la fine della stagione regolare dell'hockey su ghiaccio, ti offriamo risultati in tempo reale di hockey, classifiche e risultati dei migliori eventi di hockey su ghiaccio - Campionato mondiale IIHF Stanley Cup e anche risultati di hockey del torneo olimpico invernale. Su IGScore ice hockey live score puoi anche trovare streaming live di hockey gratuito per NHL, SHL e altri.", "FooterContentTennis": "IGScore tennis live score предоставляет вам текущие результаты, результаты, рейтинги ATP и WTA, расписание и статистику всех крупнейших теннисных турниров, таких как Дэвис и Кубок Федерации, Открытый чемпионат Франции по теннису или все турниры Большого шлема - Открытый чемпионат Австралии по теннису, Открытый теннис США, Роланд. Гаррос и Уимблдон, женщины и мужчины, в одиночном и парном разряде. Также для любого теннисиста вы можете детально увидеть его сыгранные матчи индивидуально и результаты их по сетам и в каком турнире был сыгран этот матч. IGScore tennis live score предоставляет вам личные результаты, статистику, текущий счет и прямую трансляцию между двумя игроками, которые играют в матче.", "FooterContentVolleyball": "IGScore volleyball live score предлагает вам освещение от всех важных мужских и женских национальных волейбольных лиг, включая итальянскую серию А1 и итальянскую серию А1 среди женщин, российскую Суперлигу, Польскую лигу PlusLiga, первую лигу Турции и многие другие. Помимо национальных волейбольных лиг, мы также предоставляем вам информацию о текущих результатах крупных международных турниров по волейболу, таких как чемпионат мира FIVB и чемпионат Европы, а также результаты прямых трансляций по волейболу на Олимпийских играх. Вы также можете проверить старые результаты вашей любимой волейбольной команды, увидеть будущие расписания волейбола и проверить турнирную таблицу лиги по волейболу на IGScore volleyball live score.", "FooterContentEsports": "Esports Live Cars Service на IGScore Live Raction предлагает журналы Live Esports, график<PERSON>, результаты и таблицы. Следуйте своим любимым командам прямо здесь Live! Оценка esports Live на igscore.net Live Batter автоматически обновляется, и вам не нужно обновлять его вручную. С добавлением игр вы хотите следовать в «Моя играх», следующие за вашими матчами Livescores, результаты и статистика будут еще проще.", "FooterContentHandball": "IGScore handball live score предоставляет вам текущий счет гандбола и результаты матчей самых популярных гандбольных лиг, таких как Немецкая Бундеслига, Испания Liga Asobal, Дания мужская Handboldligaen и Франция D1. Мы также предоставляем текущий счет, результаты, статистику, турнирные таблицы, таблицы и матчи важных кубков, таких как Лига чемпионов Европы по гандболу, лига SEHA и Кубок EHF по гандболу. На IGScore handball live score вы можете найти текущие результаты и бесплатную прямую трансляцию для международных командных турниров по гандболу, таких как чемпионат Европы и чемпионат мира, как для женщин, так и для мужчин. В любое время вы можете проверить результаты гандбола и статистику последних 10 игр, сыгранных вашей командой, а также личный счет между командами, которые запланированы для игры со статистикой.", "FooterContentCricket": "IGScore cricket live score позволяет вам следить в режиме реального времени за результатами по крикету, турнирной таблицей по крикету и матчами по крикету. Все это доступно для самых популярных лиг и кубков: Индийская премьер-лига, Лига чемпионов Twenty20, Big Bash League, Карибская премьер-лига, Friends Life T20 и чемпионат мира по крикету ICC. Все результаты по крикету на IGScore обновляются автоматически, и нет необходимости обновлять их вручную. При этом есть возможность посмотреть бесплатную прямую трансляцию по крикету и проверить последние коэффициенты на окончательный результат самых интересных матчей по крикету по всему миру.", "FooterContentWaterPolo": "IGScore water polo live score предоставляет вам текущий счет и результаты матчей по водному поло в Италии, Серии A1, Венгрии, OB1, чемпионатах и Адриатической лиге на клубном уровне, а на международном уровне IGScore water polo live score предоставляет крупные турниры, такие как чемпионат мира по водному поло и чемпионат Европы по водному поло. . Мы предоставляем вам результаты в прямом эфире и бесплатную прямую трансляцию.", "FooterContentTableTennis": "IGScore table tennis live score предоставляет вам текущие результаты, таблицу, результаты, рейтинг по настольному теннису, расписание и статистику всех крупнейших турниров по настольному теннису, таких как настольный теннис в России, олимпиада по настольному теннису. Также для любого игрока в настольный теннис вы можете детально увидеть его сыгранные матчи индивидуально и их результаты по сетам и в каком турнире был сыгран этот матч. IGScore table tennis live score предоставляет вам личные результаты, статистику, текущий счет и прямую трансляцию между двумя игроками, которые играют в матче.", "FooterContentSnooker": "IGScore snooker live score дает вам возможность следить за текущим счетом, результатами и турнирной таблицей всех турниров по снукеру. Мы также предоставляем прямые трансляции с чемпионатов Великобритании и мира, а также результаты снукера в реальном времени, матчи по снукеру и финальные результаты по снукеру на международных турнирах, таких как World Snooker Tour. В любое время вы можете увидеть расписание запланированных турниров по снукеру, результаты прошлых турниров по снукеру и последние 10 игр для каждого игрока. Кроме того, вы можете проверить прошлые матчи между игроками. На IGScore snooker live score вы можете найти список матчей, которые транслируются в бесплатном прямом эфире снукера.", "FooterContentBadminton": "IGScore badminton live score предоставляет вам живые результаты по бадминтону, турнирную таблицу, расписание и статистику международных турниров, таких как чемпионаты мира, суперсерии BWF и результаты Олимпийских игр по бадминтону. Вы также можете проверить результаты игр в бадминтон, просмотреть расписание игр в бадминтон и проверить результаты игроков в бадминтон индивидуально на IGScore badminton live score.", "ContactUs": "Связаться с нами", "UpdateLineups": "Update Lineups", "FreeLiveScoresWidget": "Free Livescores Widget", "PlayerSalary": "", "DivisionLevel": "Уровень события", "Foreigners": "Количество иностранных игроков", "LeagueInfo": "Турни<PERSON>ы", "TeamInfo": "Информация о команде", "Venues": "", "MostValuablePlayer": "", "UpperDivision": "Высший дивизион", "NewcomersFromUpperDivision": "", "NewcomersFromLowerDivision": "", "TitleHolder": "Действующий чемпион", "MostTitle": "До победы номер", "Pts": "Очки за игру", "FullStats": "", "Relegation": "", "Result": "Результат", "Score": "Счет", "PlayerStats": "", "fixtures": "", "topPlayers": "Ключевые игроки", "Shots": "Стрельба", "SelectTeam": "", "SelectBasketBallTeam": "", "SelectAll": "Выбрать все", "SquadSize": "Количество игроков", "ViewAll": "Показать все", "penaltiesWon": "Забит пенальти", "accuracyCrosses": "", "totalShorts": "", "accuracyLongBalls": "", "blockShots": "", "MatchesToday": "Матчи сегодня", "Strikers": "Вперед", "Midfielders": "Полузащитник", "Year": "", "Loans": "", "TopValuablePlayers": "", "total": "", "Total": "", "Results": "", "Prev": "", "Apps": "Появления", "ShotsPg": "Усреднение выстрел", "Possession": "", "TotalAndAve": "", "Suspended": "Травмирован или отстранен", "injuredOrSuspended": "Травмирован или отстранен", "Since": "Время ранения", "Overall": "Overall", "Age": "Возраст", "LastMatchFormations": "Последняя игра линейка", "Formation": "", "GoalDistribution": "Распределение цели", "Favorite": "", "FoundedIn": "Была основана в", "LocalPlayers": "Местные игроки", "ShowNext": "Показать следующие события", "HideNext": "Скрыть следующие события", "FIFAWorldRanking": "Рейтинг ФИФА", "Register": "", "OP": "1х2", "Handicap": "", "h5Handicap": "", "TennisHandicap": "", "OU": "Больше/меньше", "CardUpgradeConfirmed": "Обновление карточки подтверждено", "VAR": "", "LatestMatches": "Последние матчи", "ScheduledMatches": "", "Only": "", "LeagueFilter": "", "WinProb": "", "ScoreHalf": "", "Animation": "", "FigureLegends": "", "YellowCard": "Желтая карточка", "RedCard": "Красная карточка", "Chatroom": "Чат-рум", "Send": "Отправить", "Logout": "", "CurrentLeague": "", "ShirtNumber": "", "ContractUntil": "Контракт до", "PlayerInfo": "PLAYER INFO", "Height": "Высота", "Weight": "<PERSON>е<PERSON>", "PlayerValue": "Социальный статус", "View": "", "Time": "Время", "onTarget": "", "offTarget": "", "Played": "", "Rating": "", "Attacking": "Нападение", "Creativity": "Креативность", "Defending": "Защита", "Tactical": "Тактика", "Technical": "Техника", "Other": "", "Cards": "Штрафные карты", "AccuratePerGame": "Точное прохождение", "AccLongBalls": "Ассоциированный длинный пас", "AccCrosses": "Точная биография", "SuccDribbles": "Чрезвычайный успех", "TotalDuelsWon": "Фолы", "YellowRedCards": "", "AiScoreRatings": "", "Pergame": "", "Player": "", "Upcoming": "", "FreeKicks": "", "Corners": "Угловой", "DaysUntil": "Дней до", "In": "<PERSON>ы<PERSON><PERSON>л", "Out": "<PERSON><PERSON><PERSON><PERSON>", "NoStrengths": "Нет особенных преимуществ", "NoWeaknesses": "Нет особенных недостатков", "UpcomingMatches": "", "Pos": "", "Ht(cm)": "", "wt(kg)": "", "Exp": "EXP", "College": "", "Salary/Year": "", "Manager": "", "TotalPlayers": "Количество игроков", "Form": "", "Points": "Очки", "GamesPlayed": "", "WinPercentage": "", "FieldGoalsMade": "", "FieldGoalsAttempted": "", "FieldGoalPercentage": "", "threePointFieldGoalsMade": "", "threePointFieldGoalsAttempted": "", "threePointFieldGoalsPercentage": "", "FieldGoaldsMade": "", "FreeThrowsAttempted": "", "FreeThrowPercentage": "", "BlockedFieldGoalAttempts": "", "PersonalFouls": "", "PersonalFoulsDrawn": "", "Efficiency": "", "PlusMinus": "", "Atlantic": "", "Central": "", "Southeast": "", "Northwest": "", "Pacific": "", "Southwest": "", "EasternConference": "", "WesternConference": "", "ToWin": "", "Spread": "Spread", "AmFootballHand": "Spread", "TotalPoints": "", "TotalPoint": "", "TableTennisTotalGames": "", "Wins": "Побед подряд", "Losses": "Поражения", "WinningPercentage": "", "GamesBack": "", "HomeRecord": "", "AwayRecord": "", "DivisionRecord": "", "ConferenceRecord": "", "OpponentPointsPerGame": "", "AveragePointDifferential": "", "CurrentStreak": "", "RecordLast5Games": "", "Match": "мат<PERSON>", "OnTheCourt": "На корте", "Starters": "Игроки начального состава", "FieldGoals": "Гол в ворота", "Timeout": "", "OpeningOdds": "", "PreMatchOdds": "", "PointPerGame": "", "PointsAllowed": "", "StartingLineups": "Начальные составы команд", "HandicapGames": "", "TennisHand": "", "TotalGames": "", "TennisTotalGames": "", "Defensive": "", "Offensive": "", "Points(PerGame)": "", "Rebounds(PerGame)": "", "Other(PerGame)": "Другое (усреднение)", "Attists": "", "FirstServe": "", "FirstServePoints": "", "BreakPoints": "", "Aces": "<PERSON><PERSON><PERSON><PERSON>", "DoubleFaults": "Двойные ошибки", "Winner": "Winner", "HandicapSets": "", "TableTennisHand": "", "MoneyLine": "", "TableTennisHandicapGames": "", "HandicapRuns": "", "BaseballHand": "", "TotalRuns": "", "BaseballTotalGames": "", "DIFF": "", "BasketPergame": "", "HandicapPoints": "", "HandicapFrames": "", "TotalFrames": "", "SeeAll": ""}