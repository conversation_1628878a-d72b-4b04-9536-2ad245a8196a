.dataDetail {
  width: 100%;
  padding-top: 88px;
  position: fixed;
  top: 0;
  background-color: #fff;
  z-index: 999;
  overflow: auto;

  &.picker {
    z-index: 999;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 10px;

    .btngroup {
      margin-top: 10px;
      background-color: #fff;
      border-radius: 10px;
      overflow: hidden;

      .btnli {
        width: 100%;
        height: 100px;
        background-color: #fff;
        text-align: center;
        line-height: 100px;
        font-size: 38px;
        color: #6c7792;
        border-bottom: 1px solid #f4f5f8;

        &.active {
          color: #0f80da;
        }
      }
    }
  }

  .dheader {
    width: 100%;
    height: 88px;
    position: fixed;
    top: 0;
    background: #1e1e1e;
    display: flex;

    .left {
      width: 88px;
      height: 88px;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
    }

    .dtitle {
      display: flex;
      flex: 1;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      font-size: 32px;
      font-weight: 500;
      color: #ffffff;

      .upTitle {
        line-height: 40px;
        font-size: 30px;
        font-weight: 500;
        color: #ffffff;
      }

      .subtitle {
        display: flex;
        align-items: center;
        justify-content: center;
        line-height: 32px;
        font-size: 24px;
      }
    }
  }

  .main {
    width: 100%;

    .tableHeader {
      display: flex;
      align-items: center;
      height: 56px;
      background-color: #f1f1f1;
    }

    .sectionitem {
      display: flex;
      height: 88px;
      border-bottom: 1px solid #e3e3e3;
    }

    .sort {
      width: 88px;
      text-align: center;
      line-height: 88px;
      font-size: 24px;
      font-weight: 500;
      color: #333333;
    }

    .content {
      flex: 1;
      display: flex;
      justify-content: space-between;
      padding: 0 24px 0 4px;
      align-items: center;

      .player {
        display: flex;
        align-items: center;

        img {
          width: 58px;
          height: 58px;
          border: 2px solid #dbdbdb;
          border-radius: 29px;
        }

        .name {
          display: flex;
          flex-direction: column;
          color: #333;
          font-size: 24px;
          font-weight: 500;
          justify-content: space-around;
          margin-left: 20px;

          .teamname {
            color: #999;
          }
        }
      }
    }
  }

  .asbbtn {
    position: absolute;
    width: 100px;
    height: 100px;
    bottom: 200px;
    right: 80px;
    background-color: rgba(0, 0, 0, 0.12);
    border-radius: 50px;
    text-align: center;
    line-height: 100px;
    font-size: 30px;
    color: #fff;
  }
}
