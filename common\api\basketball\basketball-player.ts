// @ts-nocheck
import igsRequest from "iscommon/request/instance";
import { genValidObj } from "iscommon/utils";

// import "iscommon/mock/football-player-mock";

export const getPlayerHeader = async ({ playerId } = {}) => {
  try {
    return await igsRequest.post("player/detail/header", { playerId });
  } catch (e) {
    console.error("getPlayerHeader error:", e);
    return null;
  }
};

export const getPlayerSchedule = async ({ playerId, teamId, competitionId} = {}) => {
  try {
    return await igsRequest.post("player/detail/schedule", { playerId, teamId, competitionId });
  } catch (e) {
    console.error("getPlayerSchedule error:", e);
    return null;
  }
};

export const getPlayerStatistics = async ({ playerId, seasonId } = {}) => {
  try {
    return await igsRequest.post("player/detail/statistics", { playerId, seasonId });
  } catch (e) {
    console.log("getPlayerStatistics error:", e);
    return null;
  }
};

export const getPlayerStatusPreview = async ({ playerId, competitionId } = {}) => {
  try {
    return await igsRequest.post("player/detail/stats/preview", { playerId, competitionId });
  } catch (e) {
    console.log("getPlayerStatusPreview error:", e);
    return null;
  }
};

export const getPlayerStatusAll = async ({ playerId, competitionId, scope } = {}) => {
  try {
    return await igsRequest.post("player/detail/stats/all", { playerId, competitionId, scope });
  } catch (e) {
    console.log("getPlayerStatusAll error:", e);
    return null;
  }
};

export const getPlayerSelector = async ({ playerId } = {}) => {
  try {
    return await igsRequest.post("player/detail/selector", { playerId });
  } catch (e) {
    console.log("getPlayerSelector error:", e);
    return null;
  }
};

export const getPlayerSelectorLikeApp = async ({ playerId } = {}) => {
  try {
    return await igsRequest.post("player/detail/app/selector", { playerId });
  } catch (e) {
    console.log("getPlayerSelectorLikeApp error:", e);
    return null;
  }
};
