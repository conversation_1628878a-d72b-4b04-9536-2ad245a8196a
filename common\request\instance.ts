// @ts-nocheck

import GlobalUtils, { GlobalConfig } from '../const/globalConfig';

import axios from 'axios';

// import crypto from 'crypto';

// Host URLs
const prodHost = 'https://api.igscore.net:8080/v1/';
const stagingHost = 'https://stagingapi.igscore.net:8080/v1/';
const localHost = 'https://stagingapi.igscore.net:8080/v1/';

const getBaseURL = () => {
  return localHost;
  // console.log('app_env', APP_ENV);
  if (APP_ENV === 'production') {
    return prodHost;
  } else if (APP_ENV === 'staging') {
    return stagingHost;
  } else {
    return localHost;
  }
};

const igsRequest = axios.create({
  baseURL: getBaseURL(),
  timeout: 100000,
  // headers: {
  //   'AUTH_KEY': crypto.createHash('md5').update(API_KEY).digest('hex'),
  //   'DEVICE_TYPE': DEVICE_TYPE,
  //   'Content-Type': 'application/json',
  // },
});

export const ApiConfigPathName = {
  football: 'football',
  basketball: 'basketball',
  amfootball: 'amfootball',
  baseball: 'baseball',
  icehockey: 'ice-hockey',
  tennis: 'tennis',
  volleyball: 'volleyball',
  esports: 'esports',
  handball: 'handball',
  cricket: 'cricket',
  waterpolo: 'waterpolo',
  tabletennis: 'tabletennis',
  snooker: 'snooker',
  badminton: 'badminton',
};

// 请求拦截,所有的网络请求都会先走这个方法
igsRequest.interceptors.request.use(
  function (config) {
    const { data } = config;
    const { noBaseUrl = false, ...restData } = data || {};
    const nextConfig = Object.assign(config, {
      // withCredentials: true,
      data: Object.assign(restData || {}, {
        lang: GlobalConfig.lng, // en 暂定
        timeZone: GlobalConfig.timezone.replace('UTC', ''), //GMT+08:00
        platform: GlobalConfig.platform,
        agentType: null,
        appVersion: null,
        sign: null,
      }),
    });
    if (!noBaseUrl) {
      nextConfig.baseURL += ApiConfigPathName[GlobalConfig.pathname] + '/';
    }
    return nextConfig;
  },
  function (err) {
    return Promise.reject(err);
  },
);

// 请求拦截,所有的网络请求都会先走这个方法
igsRequest.interceptors.response.use(
  function (response) {
    // add config
    if (response.server_time) {
      GlobalUtils.setServerTime(server_time);
    }
    try {
      return response.data.result;
    } catch (e) {
      return {};
    }
  },
  function (err) {
    return Promise.reject(err);
  },
);

export default igsRequest;
