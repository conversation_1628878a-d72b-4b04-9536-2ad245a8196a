// @ts-nocheck
import whistling from 'iscommon/assets/audio/whistling.mp3';
import { GlobalConfig } from 'iscommon/const/globalConfig';

class GoalAudio {
  constructor() {
    this.audioElement = new Audio(whistling);
  }

  init(showPopup = null) {
    this.isInit = true;
    this.showPopup = showPopup;
  }

  play(matchInfo = null) {
    if (this.isInit && GlobalConfig.goalAudio.sound) {
      if (this.isDelay) {
        return;
      }
      this.isDelay = true;
      this.timer = setTimeout(() => {
        this.isDelay = false;
      }, 2000);

      if (GlobalConfig.goalAudio.popup && typeof this.showPopup === 'function') {
        this.showPopup(matchInfo);
      }
      this.audioElement.play();
    }
  }

  pause() {
    if (this.timer) {
      clearTimeout(this.timer);
    }
    this.audioElement.pause();
  }
}

const goalAudio = new GoalAudio();

export default goalAudio;
