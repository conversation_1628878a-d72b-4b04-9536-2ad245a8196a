import 'iscommon/const/initPC';
import 'iscommon/mqtt';

import { addLiveHomePluginStyleSheet, addPluginStyleSheet } from './plugin';
import { getLiveCount, getOddsStatus } from 'iscommon/api/header';
import React, { useEffect, useState } from 'react';
import { Redirect, Route, Switch, useLocation } from 'react-router-dom';

import AdBanner from '@/components/Common/AdBanner';
import { ConfigProvider } from 'antd';
import enUS from 'antd/es/locale/en_US';
import { GlobalConfig } from 'iscommon/const/globalConfig';
import { Header } from '@/components/Header';
import { history } from 'umi';
import Loading from '@/components/Loading';
import { Provider } from 'mobx-react';
import ScrollToTopButton from '@/components/Common/ScrollToTopBtn/ScrollToTopBtn';
import SportUnavailable from '@/pages/NotFound/SportUnavailable';
import store from 'iscommon/mobx';

const HomeEditBottom = React.lazy(() => import('@/components/Common/HomeWidgetEdit/homeEditBottom'));
const HomeEditTop = React.lazy(() => import('@/components/Common/HomeWidgetEdit/homeEditTop'));
// const Footer = React.lazy(() => import('@/components/Footer'));
// const HomeEditTop = React.lazy(() => import('@/components/Common/HomeWidgetEdit/homeEditTop'));

// docs: https://analytics.google.com/analytics/web/#/a238811986p348551803/admin/streams/table/**********
// ReactGA.initialize('G-PY89BYQSQX');

interface PluginEventData {
  pluginName: string;
  id: string;
}

export default function (props: any) {
  const { WebConfig } = store;
  const location: any = useLocation();
  const [isBlocked, setIsBlocked] = useState(false);

  /** ✅ Local state for WebConfig */
  const [localWebConfig, setLocalWebConfig] = useState(() => {
    const storedConfig = localStorage.getItem('WebConfig');
    return storedConfig ? JSON.parse(storedConfig) : {};
  });

  /** ✅ Fetch WebConfig + LiveCounts once on mount */
  useEffect(() => {
    const fetchInitialData = async () => {
      try {
        const [displays, liveCounts] = await Promise.all([getOddsStatus(), getLiveCount()]);

        if (displays) {
          // Update MobX WebConfig store dynamically
          Object.entries(displays).forEach(([key, value]) => {
            const setterFunction = (WebConfig as any)[`set${key.charAt(0).toUpperCase() + key.slice(1)}Status`];
            if (setterFunction) setterFunction.call(WebConfig, value);
          });

          setLocalWebConfig(displays);
          localStorage.setItem('WebConfig', JSON.stringify(displays));
        }

        getLiveCount().then((liveCounts: any) => {
          store.WebConfig.setLiveCounts(liveCounts);
        });

        WebConfig.initSportPathname(GlobalConfig.pathname);
      } catch (error) {
        console.error('Error fetching WebConfig or live counts:', error);
      }
    };

    fetchInitialData();
  }, [location.pathname]);

  useEffect(() => {
    if (!localWebConfig || Object.keys(localWebConfig).length === 0) return;

    const pathname = location.pathname;

    WebConfig.initSportPathname(GlobalConfig.pathname);

    const sportsAccessMap = {
      '/football': localWebConfig.showFootball,
      '/basketball': localWebConfig.showBasketball,
      '/tennis': localWebConfig.showTennis,
      '/tabletennis': localWebConfig.showTabletennis,
      '/baseball': localWebConfig.showBaseball,
      '/amfootball': localWebConfig.showAmfootball,
      '/icehockey': localWebConfig.showIcehockey,
      '/badminton': localWebConfig.showBadminton,
      '/volleyball': localWebConfig.showVolleyball,
      '/handball': localWebConfig.showHandball,
      '/snooker': localWebConfig.showSnooker,
      '/cricket': localWebConfig.showCricket,
      '/waterpolo': localWebConfig.showWaterpolo,
      '/esports': localWebConfig.showEsports,
    };

    const blocked = Object.entries(sportsAccessMap).some(([prefix, isVisible]) => {
      return pathname.startsWith(prefix) && !isVisible;
    });

    setIsBlocked(blocked);
  }, [location.pathname, localWebConfig]);

  const { isplugin, pluginName, livescorewidget, theme = '', width = 1200 } = location?.query || {};
  const [currentTheme, setTheme] = useState(theme);
  const [sliderWidth, setSliderWidth] = useState(1200);

  useEffect(() => {
    setSliderWidth(width);
  }, [width]);

  useEffect(() => {
    if (isplugin) {
      window.gtag('event', 'pv_widget_module', { plugin_name: pluginName });
      addPluginStyleSheet();
      // @ts-ignore
      history.push = (pathUrl: string) => {
        window.open(window.location.origin + pathUrl);
      };
    }
  }, [isplugin, pluginName]);

  useEffect(() => {
    if (livescorewidget) {
      window.gtag('event', 'pv_widget_page');
      addLiveHomePluginStyleSheet();
    }
  }, [livescorewidget]);

  useEffect(() => {
    document.addEventListener(
      'pluginLoaded',
      (e: any) => {
        const pluginId: string = e.detail.pluginId!;
        const pluginDomBody = document.getElementById('plugin_container_body')!;
        if (pluginId) {
          pluginDomBody.innerHTML = '';
          pluginDomBody.append(document.getElementById(pluginId)!);
        }
        document.getElementById('normal_container_body')!.innerHTML = '';
      },
      false,
    );
  }, [isplugin]);

  const onChangeTheme = (theme: string) => {
    setTheme(theme);
  };

  const onChangeSliderWidth = (v: number) => {
    setSliderWidth(v);
  };

  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  return (
    <div style={{ backgroundColor: !isplugin ? '#000' : 'transparent' }} className={currentTheme}>
      <div id="normal_container_body" style={{ display: isplugin ? 'none' : 'block', position: 'relative' }}>
        <Provider store={store}>
          <ConfigProvider locale={enUS}>
            {/* ✅ Always render AdBanner and Header */}
            <AdBanner position="topBanner" style={{ margin: '0 auto 5px' }} />
            <Header />

            {/* ✅ Optional HomeEditTop */}
            {livescorewidget && <HomeEditTop onChangeSliderWidth={onChangeSliderWidth} onChangeTheme={onChangeTheme} />}

            {/* ✅ Conditional Content */}
            <div
              className={`flex-box ${livescorewidget ? 'livescorewidget' : ''}`}
              style={{ width: livescorewidget ? `${sliderWidth}px` : 'auto' }}
            >
              {isBlocked ? <SportUnavailable /> : React.cloneElement(props.children, { ...store })}
            </div>

            {/* ✅ Optional HomeEditBottom */}
            {livescorewidget && <HomeEditBottom />}
            {/* <AdBanner position="topBanner" style={{ margin: '0 auto 5px' }} />
            <Header/>
            {livescorewidget && <HomeEditTop onChangeSliderWidth={onChangeSliderWidth} onChangeTheme={onChangeTheme} />} 
            <div
              className={`flex-box ${livescorewidget ? 'livescorewidget' : ''}`}
              style={{ width: livescorewidget ? `${sliderWidth}px` : 'auto' }}
            >
              {React.cloneElement(props.children, { ...store })}
            </div>
            {livescorewidget && <HomeEditBottom />} */}
            {/* <Footer /> */}
          </ConfigProvider>
        </Provider>
        {isClient && <ScrollToTopButton />}
      </div>
      {isplugin ? (
        <div
          style={{
            display: 'block',
            overflow: 'auto',
            position: 'relative',
            width: `${sliderWidth}px`,
            margin: '0 auto'
          }}
        >
          <div id="plugin_container_body" className={livescorewidget ? 'livescorewidget' : ''}>
            <Loading loading />
          </div>
          {(() => {
            const params = new URLSearchParams(window.location.search);
          const hostFromQuery = (new URLSearchParams(window.location.search)).get('host')?.toLowerCase() || '';
          const hidePoweredBy = hostFromQuery.includes('www.gamebetreview.com');
            return !hidePoweredBy;
          })() && (
            <div style={{ padding: '10px 0 20px 0', textAlign: 'center' }}>
              <a
                style={{ textDecoration: 'none', color: '#0f80da', cursor: 'pointer' }}
                href="https://www.igscore.net/"
                target="_blank"
                rel="noreferrer"
              >
                Powered by IGScore
              </a>
            </div>
          )}
        </div>
      ) : null}
    </div>
  );
}
