import { inject, observer } from 'mobx-react';
import React, { useMemo } from 'react';
import { FallbackCategoryImage, FallbackImage } from 'iscommon/const/icon';
import { translate } from 'iscommon/i18n/utils';
import './TeamHeader.less'

interface Props {
  store?: any;
}

const TeamHeader: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: { Team },
    } = props;
    const { teamHeaderInfo } = Team;

    const worth = useMemo(() => {
      const { marketValueCurrency, marketValue = 0 } = teamHeaderInfo || {};
      return `${marketValue / 1000000}M${marketValueCurrency} `;
    }, [teamHeaderInfo]);

    return (
      <div className='common-team-header-container'>
      <div className='team-container'>
        <img className='team-icon' src={teamHeaderInfo?.logo || FallbackImage} alt={teamHeaderInfo?.name}/>
        <div className='team-info'>
          <div className='team-name-container'>
            <span className='team-name'>{teamHeaderInfo?.name}</span>
            {/* <FavoriteIcon isMatch={false} competition={{ ...competition, curStageId: currentSeason.id }}/> */}
          </div>
          <div className='team-detail'>
            <img className='country-icon' src={teamHeaderInfo?.country?.logo || FallbackCategoryImage} alt={teamHeaderInfo?.country?.name}/>
            <span className='country-name'>{teamHeaderInfo?.country?.name}</span>
          </div>
        </div>
      </div>
      <div className='value-container'>
        <div className='value-container-title'>{translate('MarketValue')}</div>
        <div className='value-container-body'>{`${teamHeaderInfo?.marketValueCurrency || '€'} ${(teamHeaderInfo?.marketValue || 0) / 1000000}M`}</div>
        {/* <div>{teamHeaderInfo?.team?.marketValue ? `$${(teamHeaderInfo?.team?.salary / 1000000).toFixed(2)}M` : '-'`}</div> */}
      </div>
    </div>
      // <div className={styles.container}>
      //   <Image className={styles.logo} src={teamHeaderInfo?.logo} fit="contain" />
      //   <div className={styles.name}>{teamHeaderInfo?.name}</div>
      //   <div className={styles.info}>
      //     <Image className={styles.countryLogo} src={teamHeaderInfo?.country?.logo} fit="cover" />
      //     <div className={styles.country}>{teamHeaderInfo?.country?.name} /&nbsp;</div>
      //     <div className={styles.country}>{teamHeaderInfo?.foundationTime} /</div>
      //     <div className={styles.worth}>{worth}</div>
      //   </div>
      // </div>
    );
  }),
);

export default TeamHeader;
