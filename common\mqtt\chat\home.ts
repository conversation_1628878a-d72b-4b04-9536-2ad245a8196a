// @ts-nocheck
import { MqttChatroomTopicEnum } from '../constants';
import MqttClientInstance from '../index';

export const subscribeChatData = (roomId, cb) => {
  const matchChatTopic = MqttChatroomTopicEnum.chatroom + roomId;
  console.log("🚀 ~ subscribeChatData ~ matchChatTopic:", matchChatTopic)
  MqttClientInstance.subscribe(matchChatTopic, matchChatTopic, (topic, message) => {
    let newMsg = {
      sendTime: null,
      serial: null,
      message: message.message,
      user: {
        avatar: message.avatar,
        id: message.userId,
        name: message.name,
      },
    };
    cb(newMsg);
  });
};

export const unsubscribeChatData = (roomId) => {
  const fullTopic = MqttChatroomTopicEnum.chatroom + roomId;
  MqttClientInstance.unsubscribe(fullTopic);
};
