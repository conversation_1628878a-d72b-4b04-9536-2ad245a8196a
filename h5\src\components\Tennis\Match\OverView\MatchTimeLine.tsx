import React, { useState } from 'react';
import { useParams } from 'umi';

import { useRequest } from 'ahooks';
import { getMatchTimeLine } from 'iscommon/api/match';
import { formatTimeLineData } from 'iscommon/utils/dataUtils';
import './SingleMatchLine.less';
import TennisScores from './TennisScores-暂存';

interface ListItem {
  isNeutral: boolean;
  tagText: string;
  isHome: boolean;
  h5Mtime: string;
  score: string;
  h5Icon: string;
  homePlayers: {
    playerName: string;
    iconId: string;
    tagKey: string;
  }[];
  awayPlayers: {
    playerName: string;
    iconId: string;
    tagKey: string;
  }[];
}

const MatchTimeLine: React.FC = () => {
  const { categoryId: matchId } = useParams<{ categoryId: string }>();

  const [list, setList] = useState<ListItem[]>([]);

  const getTime = async (matchId) => {
    getMatchTimeLine({ matchId }).then(({ awayScores, homeScores, incidents }: any) => {
      const list: any = formatTimeLineData({
        awayScores,
        homeScores,
        incidents,
      });
      setList(list);
    });
  };

  useRequest(() => getTime(matchId), {
    refreshDeps: [matchId],
    pollingInterval: 10000,
  });

  return <TennisScores item={[]} />;
};

export default MatchTimeLine;
