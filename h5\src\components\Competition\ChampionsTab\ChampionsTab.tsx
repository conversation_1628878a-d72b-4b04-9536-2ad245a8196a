import Loading from "@/components/Loading";
import { Collapse } from "antd-mobile";
import { getCompetitionChampions } from "iscommon/api/competition";
import { FallbackImage } from "iscommon/const/icon";
import { translate } from "iscommon/i18n/utils";
import { inject, observer } from "mobx-react";
import { useEffect, useMemo, useState } from "react";
import './ChampionsTab.less'

const groupDataById = (data: any) => {
  return data.reduce((acc: any, item: any) => {
    if (!acc[item.id]) {
      acc[item.id] = [];
    }
    acc[item.id].push(item);
    return acc;
  }, {});
};

const ChampionsTab: React.FC = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Competitions: { competitionId, competitionName, currentSeason },
      },
    } = props;
    const [historyList, setHistoryList] = useState<any[]>([]);
    const [loading, setLoading] = useState(false);

    const tabTitle = translate('Champions');
    const title = useMemo(
      () => `${competitionName} ${tabTitle} ${currentSeason.year}`,
      [competitionName, tabTitle, currentSeason.year],
    );

    const groupedData = groupDataById(historyList);

    useEffect(() => {
      setLoading(true);
      getCompetitionChampions({ competitionId }).then((res: any) => {
        const { honors = [] } = res || {};
        const list = honors.map(({ team, season }: any) => ({
          desc: 'Winner',
          id: team.id,
          name: team.name,
          logo: team.logo,
          season,
        }));
        setHistoryList(list);
        setLoading(false);
      });
    }, [competitionId]);

    return (
      <div className='competition-champion-history-container'>
        <div className='container-title'>{title}</div>
        <div className='container-body'>
          <Loading loading={loading} isEmpty={!historyList?.length}/>
          <Collapse className='custom-collapse'>
            {Object.entries(groupedData).map(([id, items]) => (
              <Collapse.Panel
                key={id}
                title={
                  <div className="panel-header">
                    <img src={items[0].logo || FallbackImage} alt={items[0].name} className="team-logo"/>
                    <span className='team-name'>{items[0].name}</span>
                  </div>
                }
              >
                <div className="seasons-grid">
                  {items.map((item: any, index: any) => (
                    <div key={index} className="season-details">
                      <span className='season-value'>{item.season}</span>
                    </div>
                  ))}
                </div>
              </Collapse.Panel>
            ))}
          </Collapse> 
        </div>
      </div>
    );
  }),
);

export default ChampionsTab;