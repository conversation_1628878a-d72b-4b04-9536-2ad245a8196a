import { useEffect, useState } from 'react';

import { getCompetitionHots } from 'iscommon/api/competition-hot';
import { CategoryIcon } from 'iscommon/const/icon';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

import Loading from '@/components/Loading';

interface Hot {
  id: string;
  name: string;
  logo: string;
  path: string;
  active: boolean;
}

import './HotCompetitions.less';
import { Link } from 'umi';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';

const HotCompetitions = () => {
  const labelMaps = useTranslateKeysToMaps(['Popular']);

  const [hots, setHots] = useState<Hot[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    setLoading(true);
    getCompetitionHots().then((res) => {
      setHots(res);
      setLoading(false);
    });
  }, []);

  return (
    <div className='common-hot-competition-container'>
      <div className='container-title'>{labelMaps.Popular}</div>
      <div className='container-body'>
        <Loading loading={loading} isEmpty={!hots.length}>
          {hots.map((hot) => {
            console.log('pagetabs', PageTabs.competition, hot.id)
            return (
              <Link to={GlobalUtils.getPathname(PageTabs.competition, hot.id)} className='hot-competition-item' key={hot.id}>
                <img className="hot-competition-logo" src={(CategoryIcon as any)[hot.logo] || hot.logo} alt={hot.name} loading="lazy"/>
                <span className="hot-competition-name">{hot.name}</span>
              </Link>
            );
          })}
        </Loading>
      </div>
    </div>
    // <>
    //   <p className="title">{labelMaps.Popular}</p>
    //   <Loading loading={loading} isEmpty={!hots.length}>
    //     {hots.map((hot) => {
    //       return (
    //         <div className="item" key={hot.id}>
    //           <div className="target-blank">
    //             <div className="left">
    //               <img className="logo" src={(CategoryIcon as any)[hot.logo] || hot.logo} alt="" />
    //               <span className="text">{hot.name}</span>
    //             </div>
    //             {/* <i className="icon iconfont iconjiantou fs-12" /> */}
    //           </div>
    //         </div>
    //       );
    //     })}
    //   </Loading>
    // </>
  );
};

export default HotCompetitions;
