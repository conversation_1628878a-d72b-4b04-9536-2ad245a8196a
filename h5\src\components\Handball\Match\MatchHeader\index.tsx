import SmallballMatchHeader from '@/components/SmallGameCommon/Match/MatchHeader';
import {
  HandballInLiveStatusEnum,
  HandballMatchStatusCodeToText,
  HandballShowHalfScoreWithTimeEnum,
  HandballStatusCodeEnum,
} from 'iscommon/const/handball/constant';
import { getHandballScoreList } from 'iscommon/utils/dataUtils';

const MatchHeader = () => {
  const getHomeBasketBallMatchTimeText = (matchHeaderInfo) => {
    const { matchStatus, scores, servingSide } = matchHeaderInfo;
    const list: any[] = getHandballScoreList(matchStatus, scores, servingSide, false);
    const isIng = HandballInLiveStatusEnum.includes(matchStatus);
    const halfScore = list[list.length - 2];
    const text = (HandballMatchStatusCodeToText as any)[matchStatus];
    const res = {
      statusText: text,
      isIng,
    };
    return res;
  };

  const getHalfScoreText = (matchHeaderInfo) => {
    const { matchStatus, scores, servingSide } = matchHeaderInfo;
    const list: any[] = getHandballScoreList(matchStatus, scores, servingSide, false);
    const showHalf = HandballShowHalfScoreWithTimeEnum.includes(matchStatus);
    const halfScore = list[list.length - 2];

    return showHalf ? `HT ${halfScore.h}:${halfScore.w}` : '';
  };

  return (
    <SmallballMatchHeader
      checkIsIng={(matchStatus) => HandballInLiveStatusEnum.includes(matchStatus)}
      getHomeScore={({ scores }) => scores?.ft?.[0] || 0}
      getAwayScore={({ scores }) => scores?.ft?.[1] || 0}
      notStartedCode={HandballStatusCodeEnum.NotStarted}
      getHomeBasketBallMatchTimeText={getHomeBasketBallMatchTimeText}
      getHalfScoreText={getHalfScoreText}
      // ballIconCode="#icontabletennis_ball"
    />
  );
};

export default MatchHeader;
