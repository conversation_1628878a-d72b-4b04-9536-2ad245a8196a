{"All": "Visas", "Live": "LIVE", "LiveH5": "LIVE", "MatchLive": "LIVE", "TimeSort": "<PERSON><PERSON><PERSON><PERSON> pēc laika", "SortByTime": "<PERSON><PERSON><PERSON><PERSON> pēc laika", "AllGames": "SPĒLES", "Leagues": "LĪGAS", "h5_Leagues": "LĪGAS", "Today": "Šodien", "Cancel": "Atcelt", "Popular": "<PERSON><PERSON><PERSON><PERSON>", "Settings": "Iestatītījumi", "Language": "Valoda", "Overview": "<PERSON><PERSON><PERSON><PERSON>", "LiveOverview": "<PERSON><PERSON><PERSON><PERSON>", "Standings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Stats": "Statistika", "Transfer": "Pārskaitīju<PERSON>", "Champions": "Čempioni", "TeamChampions": "Čempioni", "teamChampions": "Čempioni", "Football": "FUTBOLS", "Basketball": "BASKETBOLS", "Baseball": "Beisbols", "Icehockey": "Ho<PERSON>js", "Tennis": "TENISS", "Volleyball": "VOLEJBOLS", "Esports": "E-SPORTS", "Handball": "ROKASBUMBA", "Cricket": "KRIKETS", "WaterPolo": "ŪDENS POLO", "TableTennis": "<PERSON><PERSON><PERSON> ten<PERSON>", "Snooker": "SNŪKERS", "Badminton": "BADMINTONS", "BusinessCooperation": "Business Cooperation", "TermsOfService": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PrivacyPolicy": "Privātuma politikai", "Players": "SPĒLĒTĀJI", "ForeignPlayers": "Ārz<PERSON><PERSON> spēl<PERSON>āji", "NumberOfTeams": "<PERSON><PERSON><PERSON> s<PERSON>ts", "YellowCards": "<PERSON><PERSON><PERSON><PERSON><PERSON> kart<PERSON>", "RedCards": "<PERSON><PERSON><PERSON><PERSON><PERSON> kart<PERSON>", "Capacity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "City": "Pilsē<PERSON>", "Info": "Informācija", "Matches": "MAČI", "Team": "<PERSON><PERSON><PERSON>", "Teams": "<PERSON><PERSON><PERSON>", "Goals": "<PERSON><PERSON><PERSON>", "Assists": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assists": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Home": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Away": "Prom", "topScorers": "Lielākie punktu guvēji", "TopScorers": "Lielākie punktu guvēji", "homeTopScorers": "Lielākie punktu guvēji", "season": "Sezona", "Season": "Sezona", "ShotsOnTarget": "<PERSON><PERSON><PERSON> mērķī", "Clearances": "Aplenkums", "Tackles": "<PERSON><PERSON><PERSON>", "keyPasses": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "KeyPasses": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Fouls": "<PERSON><PERSON><PERSON><PERSON>", "totalFouls": "<PERSON><PERSON><PERSON><PERSON>", "WasFouled": "<PERSON><PERSON><PERSON><PERSON>, kas tika p<PERSON>i", "Penalty": "Sods", "MinutesPlayed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BasketballMinutesPlayed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Interceptions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Steals": "<PERSON><PERSON>", "steals": "<PERSON><PERSON>", "Passes": "Passing", "Saves": "<PERSON><PERSON><PERSON><PERSON>", "BlockedShots": "Bloķēts sitiens", "Signed": "Parakstīts", "league": "", "offensiveData": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dati", "defenseData": "Aizsardzī<PERSON> dati", "otherData": "Citi dati", "ballPossession": "<PERSON><PERSON><PERSON>", "shotsPerGame": "Sit<PERSON>i vienā spēlē", "ShotsPerGame": "Sit<PERSON>i vienā spēlē", "keyPassesPerGame": "G<PERSON><PERSON><PERSON><PERSON> pie<PERSON> vien<PERSON> spēlē", "accurateLongBallsPerGame": "<PERSON><PERSON><PERSON><PERSON> garas bumbas vienā spēlē", "accurateCrossesPerGame": "Precīzi krusti vienā spēlē", "tacklesPerGame": "Aizgūtie vienas spēles laik<PERSON>", "TacklesPerGame": "Aizgūtie vienas spēles laik<PERSON>", "interceptionsPerGame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vien<PERSON> spēlē", "InterceptionsPerGame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vien<PERSON> spēlē", "clearancesPerGame": "Vienas spēles klīrenss", "ClearancesPerGame": "Vienas spēles klīrenss", "blockedShotsPerGame": "Bloķēti sitieni vienā spēlē", "turnoversPerGame": "<PERSON><PERSON><PERSON> vien<PERSON> spēlē", "foulsPerGame": "<PERSON><PERSON><PERSON><PERSON> noteikumu pārk<PERSON>pumi vienā spēlē", "scoringFrequencyFiveGoals": "", "Coach": "<PERSON><PERSON><PERSON>", "Goalkeeper": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Stadium": "Stadons", "Login": "Pierakstī<PERSON>", "Corner": "<PERSON><PERSON><PERSON>", "ShotsOffTarget": "<PERSON><PERSON><PERSON>, kas netrāpīja mērķī", "H2H": "H2H", "Date": "Datums", "OwnGoal": "<PERSON><PERSON><PERSON> trā<PERSON>", "PenaltyMissed": "Palaists garām soda sitiens", "SecondYellow": "<PERSON><PERSON><PERSON> kartīte", "Odds": "Likmes", "attacks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Started": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Chat": "Čats", "Strengths": "<PERSON><PERSON><PERSON><PERSON><PERSON> puses", "Weaknesses": "<PERSON><PERSON><PERSON><PERSON><PERSON> puses", "Group": "Grupa", "Birthday": "<PERSON><PERSON><PERSON><PERSON><PERSON> diena", "Club": "Klubs", "MainPosition": "Galvenā pozīcija", "PassesAccuracy": "", "Preseason": "", "RegularSeason": "", "PointsPerGame": "Punkti par spēli", "Glossary": "<PERSON><PERSON><PERSON>", "h5Glossary": "<PERSON><PERSON><PERSON>", "Career": "<PERSON><PERSON><PERSON><PERSON>", "Bench": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ReboundsPerGame": "Atsitieni vienā spēlē", "AssistsPerGame": "Pa<PERSON>ī<PERSON><PERSON><PERSON><PERSON> vienā spēlē", "OddsFormat": "<PERSON><PERSON><PERSON><PERSON> form<PERSON>", "Squad": "Sast<PERSON><PERSON><PERSON>", "TotalMarketValue": "<PERSON><PERSON><PERSON><PERSON><PERSON> tirgus vērtība", "Rounds": "<PERSON><PERSON><PERSON>", "LowerDivision": "Apakš<PERSON><PERSON><PERSON>", "TeamStats": "<PERSON><PERSON><PERSON> statistika", "GoalsPk": "<PERSON><PERSON><PERSON>(PK)", "Crosses": "K<PERSON>s", "CrossesAccuracy": "Pass bumbu veiksmīgi", "Dribble": "Neparasts", "DribbleSucc": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LongBalls": "<PERSON><PERSON><PERSON> bumbas", "LongBallsAccuracy": "<PERSON> <PERSON><PERSON><PERSON> lī<PERSON>", "Duels": "<PERSON><PERSON><PERSON><PERSON>", "DuelsWon": "<PERSON><PERSON><PERSON> pala<PERSON>", "Dispossessed": "<PERSON><PERSON><PERSON><PERSON>", "Punches": "Vārtsargs uzbrukums veiksme", "RunsOut": "<PERSON><PERSON><PERSON><PERSON>", "RunsOutSucc": "Šāvie<PERSON> ir bloķēts", "GoodHighClaim": "Passing precizitāte", "Loan": "Aizdevums", "EndOfLoan": "Aizdevuma beigas", "Unknown": "nezināms", "AverageAge": "<PERSON><PERSON><PERSON><PERSON><PERSON> vecums", "cornersPerGame": "<PERSON><PERSON><PERSON> vien<PERSON> spēlē", "goalsConceded": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>", "Defender": "Aizsargs", "Discipline": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Pass": "", "FB_Login": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar <PERSON>", "Google_Login": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "Substitutes": "Aizvietotāji", "PenaltyKick": "", "ShareYourViews": "Izteikt savu viedokli", "Nodata": "Nav datu", "Foot": "<PERSON><PERSON><PERSON>", "dangerousAttack": "", "venue": "Norises vieta", "playerStatistics": "Spē<PERSON><PERSON>tāju statistika", "TotalPlayed": "<PERSON><PERSON><PERSON>", "MinutesPerGame": "<PERSON><PERSON><PERSON> spēlē", "GoalsFrequency": "", "GoalsPerGame": "<PERSON><PERSON><PERSON> v<PERSON>", "Arrivals": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Departures": "Aiz<PERSON><PERSON><PERSON><PERSON>", "LeftFoot": "<PERSON><PERSON><PERSON>", "RightFoot": "Labā", "LatestTransfers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DraftInfo": "Informācija par <PERSON>u", "OK": "<PERSON><PERSON>", "AsianHandicap": "", "TotalGoals": "", "AmFootballTotalGames": "", "TotalCorners": "", "OverBall": "<PERSON><PERSON><PERSON><PERSON>", "Over": "<PERSON><PERSON><PERSON><PERSON>", "h5Over": "<PERSON><PERSON><PERSON><PERSON>", "UnderBall": "Zem", "Under": "Zem", "h5Under": "Zem", "OtherLeagues": "Citas līgas [A-Z]", "GoalPopup": "", "FullStandings": "Galvenie spēlētāji", "teamWeek": "Nedēļ<PERSON> k<PERSON>", "weekTop": "Nedēļ<PERSON> k<PERSON>", "TeamOfTheWeek": "Nedēļ<PERSON> k<PERSON>", "round": "Rounds", "Released": "Atbrīvots", "Retirement": "", "Draft": "Atlase", "TransferIn": "", "TransferOut": "", "MarketValue": "<PERSON><PERSON><PERSON> v<PERSON><PERSON>", "Salary": "Alga", "Next": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Position": "Pozīcija", "CTR": "Pašreizējo ma<PERSON>ņu i<PERSON>ks<PERSON>", "FoulBall": "", "FreeKick": "", "GoalKick": "", "Midfield": "", "HalftimeScore": "", "InjuryTime": "", "OvertimeIsOver": "", "PenaltyKickEnded": "", "Last": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Win": "<PERSON><PERSON><PERSON><PERSON>", "Draw": "Neizšķirts", "Lose": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Lineup": "", "Substitution": "Aizvie<PERSON>ša<PERSON>", "Offsides": "<PERSON><PERSON><PERSON><PERSON>", "Playoffs": "", "Qualifier": "", "GroupStage": "", "Rebounds": "<PERSON><PERSON><PERSON><PERSON>", "rebounds": "<PERSON><PERSON><PERSON><PERSON>", "OffensiveRebounds": "Uzb<PERSON><PERSON><PERSON><PERSON><PERSON> atlēkuš<PERSON> bumbas", "offensiveRebounds": "Uzb<PERSON><PERSON><PERSON><PERSON><PERSON> atlēkuš<PERSON> bumbas", "DefensiveRebounds": "Aizsard<PERSON><PERSON><PERSON>", "defensiveRebounds": "Aizsard<PERSON><PERSON><PERSON>", "Turnovers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "turnovers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Blocks": "Bloķē", "blocks": "Bloķē", "BoxScore": "<PERSON><PERSON><PERSON>", "Foul": "<PERSON><PERSON><PERSON><PERSON>", "FreeThrows": "<PERSON><PERSON><PERSON><PERSON>", "freeThrowsScored": "<PERSON><PERSON><PERSON><PERSON>", "fieldGoalsScored": "", "fieldGoalsTotal": "", "threePointersScored": "", "threePointersTotal": "", "freeThrowsTotal": "", "Finished": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Scheduled": "<PERSON><PERSON><PERSON>", "Favourite": "", "OddsMarkets": "Odds markets", "Search": "", "Timezone": "", "SoccerGoalReminderSettings": "", "RemindersOnlyForRacesToWatch": "", "GoalSound": "", "LiveScore": "", "Schedule": "<PERSON><PERSON><PERSON>", "Rugby": "", "FooterContentFootball": "IGScore Football LiveScore nodrošina nepārspējamus futbola tiešraides rezultātus un futbola rezultātus no vairāk nekā 2600 + futbola līgām, kausiem un turnīriem. Iegūst<PERSON> tiešraides, puslaika un pilna laika futbola rezultātus, vārtu guvējus un asistentus, kartes, mai<PERSON><PERSON>, spēļu statistiku un tiešraidi no premjerlīgas, La Liga, A sērijas, Bundeslīgas, 1. Ligue, Eredivisie, Krievijas premjerlīgas, Brasileirão, MLS, Super Lig un čempionāts vietnē igscore.net. IGScore visiem futbola faniem piedāvā tiešraides, futbola rezultātus, futbola rezultātus, līgu tabulas un līgu, kausu un turnīru armatūru, un ne tikai no populārākajām futbola līgām kā Anglijas <PERSON>m<PERSON>, Spānijas La Liga, Itālijas A sē<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Francijas 1.<PERSON>ī<PERSON><PERSON>, bet arī no daudzām futbola valstīm visā pasaul<PERSON>, tostarp no Ziemeļamerikas un Dienvidamerikas, Āzijas un Āfrikas. Mūsu futbola rezultātu rādītāju kartes tiek atjauninātas reāllaikā, lai jūs būtu informēts par visiem šodien notiekošajiem futbola spēļu rezultātu rezultātiem, kā arī par futbola rezultātu rezultātiem par visām pabeigtajām futbola spēlēm katrā futbola un futbola līgā. Spēļu lapā mūsu futbola rādītāju kartes ļauj jums apskatīt iepriekšējo spēļu rezultātus par visiem iepriekš spēlētajiem futbolistiem visās futbola sacensībās. Iegūstiet visus savus futbola tiešraides rezultātus vietnē igscore.net!", "FooterContentBasketball": "IGScore Basketball LiveScore nodrošina jūs ar NBA līgas tiešraidēm, rezult<PERSON><PERSON>m, tabulām, statistik<PERSON>, armat<PERSON><PERSON>, tabulu un iepriekšējiem rezultātiem pa ceturkšņiem, puslaiku vai gala rezultātu. IGScore piedāvā rezultātu apkopojumu no vairāk nekā 200 basketbola sacensībām no visas pasaules (piemēram, NCAA, ABA League, Baltijas līga, Eirolīga, nacionālās basketbola līgas). Šeit atradīsit ne tikai tiešos rezultātus, ceturk<PERSON><PERSON><PERSON> rezultātus, gala rezultātus un sastāvus, bet arī 2- un 3 punktu mēģinājumus, brī<PERSON> metienus, metienu procentus, atl<PERSON><PERSON><PERSON><PERSON><PERSON> bumba<PERSON>, ap<PERSON>zījumus, zagt<PERSON><PERSON> ripas, personīgās nedienas, spēles vēsturi un spēlētāju statistiku. .Vietnes IGScore basketbola tiešraidē jūs varat skatīties basketbolu tiešsaistē, vienk<PERSON>rši noklikšķinot uz tā, un tas jums nodrošina visu virslīgu maču tiešsaistes atspoguļojumu. Mača lapā būs arī tabula ar visu basketbola statistiku par komandu jaunākajām spēlēm. mūsu basketbola rezultātu kartes tiek atjauninātas tiešraidē reāllaikā, lai jūs vienmēr būtu informēts par visiem šodien notiekošajiem basketbola rezultātiem un ļautu jums apskatīt iepriekšējo spēļu rezultātus par visām iepriekš izspēlētām armatūru visās basketbola sacensībās. Iegūstiet visus savus NBA tiešraides rezultātus vietnē igscore.net! Sekojiet NBA tiešraidēm, NBA armatūrai, NBA kopvērtējumam un komandu lapām!", "FooterContentAmFootball": "IGScore american football live score sniedz jums visus rezultātus un dzīvos rādītājus no lielākajiem un populārākajiem amerikāņu futbola līgas pasaulē - NFL un kad ir pabeigta regulāra NFL sezona, izpildiet Live rādītājus par NFL spēlēm un Superbowl. Papildus NFL mēs nodrošināsim jūs ar LiveCores, rezult<PERSON>tiem, kopvērtībām un grafikiem NCAA koledžas amerikāņu futbolam un Kanādas CFL.", "FooterContentBaseball": "IGScore baseball live score nod<PERSON><PERSON><PERSON>, rezultātus un kopvērtējumu no pasaules populārākās beisbola līgas - USSSA beisbola, NCAA beisbola, MLB beisbola, MLB Allstar spēles. Mēs piedāvājam arī tiešraides Japānas profesionālajai līgai, <PERSON><PERSON><PERSON><PERSON> l<PERSON>gai, Vācijas 1. Bundeslīgai, NCAA, kā arī starptautiskajam beisbola turnīram World Baseball Classic. Varat arī jebkurā laikā redzēt beisbola līgas kopvērtējumu, iepriekšējās spēles ar rezultātiem pēc kārtas un grafiku nākamajām beisbola spēlēm vietnē IGScore baseball live score.", "FooterContentIcehockey": "IGScore ice hockey live score nod<PERSON>šina reāllaika hokeja rezultātu rādītājus par hokeja līgām, kausiem un turnīriem. . trešdaļas un galīgie hokeja rezultāti tiešraidē. Pēc hokeja regulārās sezonas beigām mēs piedāvājam jums hokeja tiešraides, kopvērtējuma tabulas un labāko hokeja notikumu rezultātus- IIHF Pasaules čempionāta Stenlija kausu, kā arī hokeja rezultātus no ziemas olimpiskā turnīra. Vietnē IGScore ice hockey live score varat arī atrast bezmaksas hokeja tiešraidi NHL, SHL un citiem.", "FooterContentTennis": "IGScore tennis live score nod<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ATP rangu un WTA klasifikāciju, spēles un statistiku no visiem lielākajiem tenisa turnīriem, <PERSON><PERSON><PERSON><PERSON>, Deiv<PERSON> un Fed kausa, <PERSON><PERSON><PERSON><PERSON> atklātā tenisa, vai par visiem Grand Slam turnīriem - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> atklātais teniss, ASV atklātais teniss, <PERSON><PERSON> un Vimbldona gan sievietes, gan vīrieši vienspēlēs un dubultspēlēs. Arī jebkuram tenisistam var redzēt detalizēti viņa individuāli aizvadītās spēles un to rezultātus pa setiem un kurā turnīrā šī spēle tika aizvadīta. IGScore tennis live score nodro<PERSON><PERSON> tiešus rezultātus, statist<PERSON><PERSON>, tie<PERSON>rai<PERSON> rezultātus un tiešraidi starp diviem spēlētājiem, kuri spēlē spēli.", "FooterContentVolleyball": "IGScore volleyball live score piedāvā informāciju no visām svarīgākajām vīriešu un sieviešu nacionālajām volejbola līgām, tostarp Itālijas Serie A1 un Itālijas Seria A1 Women, Krievijas Superlīgas, Polijas PlusLiga, Turcijas 1. Lig un daudzām citām. Papildus nacionālajām volejbola līgām mēs arī sniedzam jums informāciju par galvenajiem starptautiskajiem volejbola turnīriem, piemēram, FIVB pasaules čempionātu un Eiropas čempionātu, kā arī volejbola tiešraides rezultātus olimpiskajās spēlēs. Varat arī pārbaudīt savas iecienītās volejbola komandas vecos rezultātus, skatīt nākamos volejbola grafikus un pārbaudīt līgas volejbola turnīra tabulu vietnē IGScore volleyball live score.", "FooterContentEsports": "Esports Live Scores pakalpojums IGScore Live Score piedāvā Esports Live rādīt<PERSON>ji, gra<PERSON><PERSON>, rezultāti un tabulas. Sekojiet savām iecienītākajām komandām šeit dzīvot! Esports Live Score igscore.net Live Score tiek automātiski atjaunināts un jums nav nepieciešams atsvaidzināt to manuā<PERSON>. Ar pievie<PERSON><PERSON><PERSON> spē<PERSON>, kuras vēlaties sekot \"Manām spēlēm\", kas seko jūsu atbilstības LiveCores, rezultāti un statistika būs vēl vienkāršāka.", "FooterContentHandball": "IGScore handball live score piedāvā tiešraides ar handbola rezultātiem un tiešraides rezultātus no populārākajām handbola līgām, <PERSON><PERSON><PERSON><PERSON>, V<PERSON>cijas Bundeslīgas, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> vīriešu Handboldligaen un Francijas D1. <PERSON><PERSON><PERSON> arī piedāvājam tiešraides, rezultātus, statistiku, tabulas, tabulas un rezultātus no svarīgiem kausiem, piem<PERSON>ram, Eiropas handbola Čempionu līgas, SEHA līgas un EHF handbola kausa. Vietnē IGScore handball live score jūs varat atrast tiešraides un bezmaksas tiešraidi starptautiskiem komandu handbola turnīriem, piem<PERSON>ram, Eiropas čempionātam un pasaules čempionātam, gan sievietēm, gan vīriešiem. Jebkurā laikā varat pārbaudīt handbola rezultātus un statistiku par pēdējām 10 spēlēm, kuras jūsu komanda spēlēja, kā arī galvu pret galvu starp komandām, kur<PERSON><PERSON> plānots spēlēt ar statistiku.", "FooterContentCricket": "IGScore cricket live score nod<PERSON><PERSON><PERSON> iespēju sekot kriketa rezultātiem reāllaikā, kriketa tabulām un kriketa spēlēm. Tas viss ir pieejams populārākajām līgām un kausiem: Indijas Premjerlīga, Čempionu līga Twenty20, Big Bash League, Kar<PERSON>bu premjerlīga, Friends Life T20 un ICC Pasaules kauss kriketā. Visi kriketa rezultāti vietnē IGScore tiek automātiski atjaunināti, un tas nav jāatsvaidzina manuāli. Ņemot to visu, ir iespēja skatīties bezmaksas kriketa tiešraidi un pārbaudīt jaunākās izredzes uz interesantāko kriketa spēļu galīgo iznākumu visā pasaulē.", "FooterContentWaterPolo": "IGScore water polo live score sniedz jums ūdenspolo rezultātus un rezultātus no Itālijas Sērijas A1, Ungārijas OB1, Čempionu un Adrijas līgas klubu līmenī, savuk<PERSON>rt starptautiskā līmenī IGScore water polo live score nod<PERSON><PERSON><PERSON> lielus turnīrus, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> čempionātu ūdenspolo un Eiropas ūdenspolo čempionātu . Mēs piedāvājam jums tiešraidi pēc mērķa un bezmaksas tiešraidi.", "FooterContentTableTennis": "IGScore table tennis live score sniedz jums rezultātus, ta<PERSON><PERSON>, rezu<PERSON><PERSON><PERSON>, galda tenisa rangu, spēles un statistiku no visiem lielākajiem galda tenisa turnīriem, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>jas galda tenisa, galda tenisa olimpiskajām spēlēm. Arī jebkuram galda tenisistam var redzēt detalizēti viņa nospēlētās spēles un to rezultātus pa setiem un kurā turnīrā šī spēle tika aizvadīta. IGScore table tennis live score nodro<PERSON><PERSON> tiešus rezultātus, statistik<PERSON>, tie<PERSON>raides rezultātus un tiešraidi starp diviem spēlētājiem, kuri spē<PERSON><PERSON> spēli.", "FooterContentSnooker": "IGScore snooker live score nod<PERSON><PERSON><PERSON> iespēju sekot līdzi reālajam rezultātam, rezultātiem un kopvērtējumam no visiem snūkera turnīriem. Mēs piedāvājam arī tiešraides no Apvienotās Karalistes un pasaules čempionāta, k<PERSON> <PERSON>r<PERSON> snūkera tiešraides, sn<PERSON><PERSON>a spēles un galīgos snūkera rezultātus no starptautiskiem turnīriem, piem<PERSON><PERSON>, World Snooker tour. Jebkurā laikā jūs varat redzēt snūkera turnīru grafiku, kuriem ir paredzēts doties, rezultātus no iepriekšējiem snūkera turnīriem un pēdējām 10 spēlēm katram spēlētājam. Turklāt jūs varat pārbaudīt spēlētāju savstarpējās spēles. Vietnē IGScore snooker live score varat atrast to spēļ<PERSON> sarakstu, kurām ir pieejama bezmaksas snūkera tiešraide.", "FooterContentBadminton": "IGScore badminton live score nod<PERSON><PERSON><PERSON> badmintona tiešraides rezultātus, kop<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, spēles un statistiku no starptautiskiem turnīriem, pie<PERSON><PERSON><PERSON>, pasa<PERSON> čempionātiem, BWF supersērijas un olimpisko spēļu badmintona rezultātiem. Varat ar<PERSON> pārbaudīt badmintona spēļu rezultātus, skatīt badmintona spēļu grafiku un pārbaudīt spēlētāju badmintona rezultātus individuāli vietnē IGScore badminton live score.", "ContactUs": "Sa<PERSON>ies ar mums", "UpdateLineups": "Update Lineups", "FreeLiveScoresWidget": "Free Livescores Widget", "PlayerSalary": "", "DivisionLevel": "Event Level", "Foreigners": "Ārvalstu spē<PERSON><PERSON><PERSON><PERSON><PERSON> skaits", "LeagueInfo": "<PERSON><PERSON><PERSON>", "TeamInfo": "<PERSON><PERSON><PERSON>", "Venues": "", "MostValuablePlayer": "", "UpperDivision": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NewcomersFromUpperDivision": "", "NewcomersFromLowerDivision": "", "TitleHolder": "Virsraksta rezervācija", "MostTitle": "L<PERSON><PERSON>z uzvarēt numurs", "Pts": "PTS", "FullStats": "", "Relegation": "", "Result": "Re<PERSON>ltā<PERSON>", "Score": "Re<PERSON>ltā<PERSON>", "PlayerStats": "", "fixtures": "", "topPlayers": "Galvenie spēlētāji", "Shots": "<PERSON><PERSON><PERSON><PERSON>", "SelectTeam": "", "SelectBasketBallTeam": "", "SelectAll": "<PERSON><PERSON><PERSON> visu", "SquadSize": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>ts", "ViewAll": "<PERSON><PERSON><PERSON><PERSON> visu", "penaltiesWon": "<PERSON><PERSON><PERSON> penalti", "accuracyCrosses": "", "totalShorts": "", "accuracyLongBalls": "", "blockShots": "", "MatchesToday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Strikers": "Uz priekšu", "Midfielders": "<PERSON><PERSON><PERSON><PERSON>", "Year": "", "Loans": "", "TopValuablePlayers": "", "total": "", "Total": "", "Results": "", "Prev": "", "Apps": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ShotsPg": "Parametram <PERSON>", "Possession": "", "TotalAndAve": "", "Suspended": "Ievainots vai apturēts", "injuredOrSuspended": "Ievainots vai apturēts", "Since": "<PERSON><PERSON><PERSON><PERSON> laiks", "Overall": "Overall", "Age": "Vecums", "LastMatchFormations": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> spē<PERSON> sastā<PERSON>s", "Formation": "", "GoalDistribution": "Mērķis izp<PERSON>īšana", "Favorite": "", "FoundedIn": "<PERSON><PERSON>", "LocalPlayers": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ShowNext": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HideNext": "<PERSON><PERSON><PERSON><PERSON>", "FIFAWorldRanking": "FIFA pasaules reitings", "Register": "", "OP": "1X2", "Handicap": "", "h5Handicap": "", "TennisHandicap": "", "OU": "O/U", "CardUpgradeConfirmed": "<PERSON><PERSON><PERSON><PERSON> j<PERSON>", "VAR": "", "LatestMatches": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ScheduledMatches": "", "Only": "", "LeagueFilter": "", "WinProb": "", "ScoreHalf": "", "Animation": "", "FigureLegends": "", "YellowCard": "<PERSON><PERSON><PERSON><PERSON> kartīte", "RedCard": "Sarkanā kartīte", "Chatroom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Send": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Logout": "", "CurrentLeague": "", "ShirtNumber": "", "ContractUntil": "<PERSON><PERSON><PERSON><PERSON> be<PERSON>", "PlayerInfo": "PLAYER INFO", "Height": "Garums", "Weight": "<PERSON><PERSON><PERSON>", "PlayerValue": "Sociālais statuss", "View": "", "Time": "Laiks", "onTarget": "", "offTarget": "", "Played": "", "Rating": "", "Attacking": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Creativity": "<PERSON><PERSON><PERSON><PERSON>", "Defending": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Tactical": "Taktisks", "Technical": "Tehnisks", "Other": "", "Cards": "Soda kartes", "AccuratePerGame": "<PERSON><PERSON><PERSON><PERSON>", "AccLongBalls": "Associate il<PERSON> ca<PERSON>e", "AccCrosses": "Precīza biogrāfija", "SuccDribbles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TotalDuelsWon": "<PERSON><PERSON><PERSON><PERSON>", "YellowRedCards": "", "AiScoreRatings": "", "Pergame": "", "Player": "", "Upcoming": "", "FreeKicks": "", "Corners": "<PERSON><PERSON><PERSON>", "DaysUntil": "<PERSON><PERSON> l<PERSON>", "In": "<PERSON><PERSON><PERSON><PERSON>", "Out": "<PERSON><PERSON><PERSON>", "NoStrengths": "Nav būtisku priekšrocību", "NoWeaknesses": "Nav būtisku trūkumu", "UpcomingMatches": "", "Pos": "", "Ht(cm)": "", "wt(kg)": "", "Exp": "EXP", "College": "", "Salary/Year": "", "Manager": "", "TotalPlayers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>ts", "Form": "", "Points": "<PERSON><PERSON>", "GamesPlayed": "", "WinPercentage": "", "FieldGoalsMade": "", "FieldGoalsAttempted": "", "FieldGoalPercentage": "", "threePointFieldGoalsMade": "", "threePointFieldGoalsAttempted": "", "threePointFieldGoalsPercentage": "", "FieldGoaldsMade": "", "FreeThrowsAttempted": "", "FreeThrowPercentage": "", "BlockedFieldGoalAttempts": "", "PersonalFouls": "", "PersonalFoulsDrawn": "", "Efficiency": "", "PlusMinus": "", "Atlantic": "", "Central": "", "Southeast": "", "Northwest": "", "Pacific": "", "Southwest": "", "EasternConference": "", "WesternConference": "", "ToWin": "", "Spread": "<PERSON>z<PERSON><PERSON><PERSON><PERSON><PERSON>ā<PERSON>", "AmFootballHand": "<PERSON>z<PERSON><PERSON><PERSON><PERSON><PERSON>ā<PERSON>", "TotalPoints": "", "TotalPoint": "", "TableTennisTotalGames": "", "Wins": "Uzvarēja", "Losses": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "WinningPercentage": "", "GamesBack": "", "HomeRecord": "", "AwayRecord": "", "DivisionRecord": "", "ConferenceRecord": "", "OpponentPointsPerGame": "", "AveragePointDifferential": "", "CurrentStreak": "", "RecordLast5Games": "", "Match": "<PERSON><PERSON><PERSON>", "OnTheCourt": "<PERSON><PERSON> lauka", "Starters": "Sākot", "FieldGoals": "<PERSON><PERSON><PERSON> punkti uz lauka", "Timeout": "", "OpeningOdds": "", "PreMatchOdds": "", "PointPerGame": "", "PointsAllowed": "", "StartingLineups": "<PERSON><PERSON><PERSON><PERSON> sastā<PERSON>s", "HandicapGames": "", "TennisHand": "", "TotalGames": "", "TennisTotalGames": "", "Defensive": "", "Offensive": "", "Points(PerGame)": "", "Rebounds(PerGame)": "", "Other(PerGame)": "Citi (vidēji)", "Attists": "", "FirstServe": "", "FirstServePoints": "", "BreakPoints": "", "Aces": "<PERSON><PERSON><PERSON><PERSON>", "DoubleFaults": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Winner": "Winner", "HandicapSets": "", "TableTennisHand": "", "MoneyLine": "", "TableTennisHandicapGames": "", "HandicapRuns": "", "BaseballHand": "", "TotalRuns": "", "BaseballTotalGames": "", "DIFF": "", "BasketPergame": "", "HandicapPoints": "", "HandicapFrames": "", "TotalFrames": "", "SeeAll": ""}