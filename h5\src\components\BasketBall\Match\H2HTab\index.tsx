import { getMatchFutureList, getMatchHistoryList, getMatchRecentList } from 'iscommon/api/basketball/basketball-match';
import { inject, observer } from 'mobx-react';
import React, { useEffect, useMemo } from 'react';

import CommonH2HTab from '@/components/Common/H2HTab';

interface Props {
  store?: any;
}

const H2HTab: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: {
        Basketball: {
          Match: { matchHeaderInfo }},
      },
    } = props;
    const { competition, homeTeam, awayTeam } = matchHeaderInfo;

    if (!homeTeam.id || !awayTeam.id) return null;
    
    const h2hTabContent = useMemo(() => (
      <CommonH2HTab
        homeTeam={homeTeam}
        awayTeam={awayTeam}
        getMatchFutureList={getMatchFutureList}
        getMatchHistoryList={getMatchHistoryList}
        getMatchRecentList={getMatchRecentList}
        competition={competition}
      />
    ), [homeTeam.id, awayTeam.id, competition?.id]);

    return h2hTabContent;
  }),
);

export default H2HTab;
