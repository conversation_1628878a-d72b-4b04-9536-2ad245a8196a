
export const BasketBallStatusCodeEnum = {
  Abnormal: 0,
  NotStarted: 1,
  SectionOne: 2,
  SectionOneOver: 3,
  SectionTwo: 4,
  SectionTwoOver: 5,
  SectionThree: 6,
  SectionThreeOver: 7,
  SectionFour: 8,
  Overtime: 9,
  End: 10,
  Interrupt: 11,
  Cancel: 12,
  Extension: 13,
  CutInHalf: 14,
  ToBeDetermined: 15
};


export const BasketBallMatchStatusCodeToText = {
  0: '-',
  1: '-',
  2: 'Q1-',
  3: 'Q1-Ended',
  4: 'Q2-',
  5: 'Q2-Ended',
  6: 'Q3-',
  7: 'Q3-Ended',
  8: 'Q4-',
  9: 'OT',
  10: 'FT',
  11: 'Interrupt',
  12: 'Cancel',
  13: 'Extension',
  14: '-',
  15: 'TBD'
};

// 2-9
export const BasketBallInLiveStatusEnum = [
  BasketBallStatusCodeEnum.SectionOne,
  BasketBallStatusCodeEnum.SectionOneOver,
  BasketBallStatusCodeEnum.SectionTwo,
  BasketBallStatusCodeEnum.SectionTwoOver,
  BasketBallStatusCodeEnum.SectionThree,
  BasketBallStatusCodeEnum.SectionThreeOver,
  BasketBallStatusCodeEnum.SectionFour,
  BasketBallStatusCodeEnum.Overtime
]

// 显示具体time
export const BasketBallInLiveWithTimeEnum = [
  BasketBallStatusCodeEnum.SectionOne,
  BasketBallStatusCodeEnum.SectionTwo,
  BasketBallStatusCodeEnum.SectionThree,
  BasketBallStatusCodeEnum.SectionFour
]

export const BasketBallLiveSectionEnum = [
  [
    BasketBallStatusCodeEnum.SectionOne,
    BasketBallStatusCodeEnum.SectionOneOver,
  ],
  [
    BasketBallStatusCodeEnum.SectionTwo,
    BasketBallStatusCodeEnum.SectionTwoOver,
  ],
  [
    BasketBallStatusCodeEnum.SectionThree,
    BasketBallStatusCodeEnum.SectionThreeOver,
  ],
  [
    BasketBallStatusCodeEnum.SectionFour,
  ],
  [
    BasketBallStatusCodeEnum.Overtime
  ],
]

export const basketballPlayerIcon = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAMAAAC7IEhfAAAAq1BMVEUAAADz+//y+//////z+//y+v/z+v/z+//z+//z+v/0+//z/P/4///z///y+v/c5/T79e7u9v54krz269/m7/nj7vjs9fzf6vbr8fjp8Pfp8vvt6uiDm8G8xdewvNKfsMt8lr7z9fXO2OePpMbu7/Dy7uvn5uemt9Gks8yUqMmZq8jz+Pvc5vDY4u/18On17ubi4uXE0eXw6eLEzNq2xdrY2uDN0t3W09S+ws6rjEyiAAAADnRSTlMAgPML6NnTv6qlclIjFjy9diYAAAH+SURBVDjLnZXpeqsgEEDV7NsgCGLUGNM0a3vb3rZ3ef8nKxBwoPvX80vhMAMIYxQyn05GgyQZjCbTefQhi3gICqoAxTBevKv14gQgF8QicoAk7r31Zn2gXAu8zPOSC/1IoT977cUA3IRxmGEcIA7TjoFKHSGACiIpjP30Y8gJwWio6taxn5fqsRfEYXt6ps6UJMfsM+3ZHn5ihsapklCY2Qn2zZvhV8ssdd5lh37PJuYktx5DanAmvyRfJECEbTywdmus9ZGxKzdpQpOFDUjt4Jo9pNVTs3pI07/s3IUUJuQQA96yVdpxZNQtkMBQnRfIux28qlNkz3gXsoR5NNUDnLhOPWqXSKecRhOQohMbXzxx6JYDk2gExDVssp0vVtkGJzmKBihmWRqQ3duOktBBlKipfiRmYNDLTT4V7zzRT31zH4p3N15qXIxmta6cpR4BvMUE2wPP7MmJdbsBf3uCDQfZ1vuL17ADgL/h+AkNv1m72qe7P2e2NY34Ce2hQLNmhrVpw0OBx8xR/T9uz80/N0E8ZnhwDWWVGa5LgODghleB82JnxUKU3lUILxcVxXJ5fRHV01Lg5QquK5WqV5JH7VVEvRQSr6tXALSnq9Tto/YUBS8EFgAsKbqDBKgEWFK8IiWd55BLLFJh2XtFWPbCQopgIf1Zaf662H/79/ECjIFUYeI1OcEAAAAASUVORK5CYII='

export const basketballStatsKeys = {
  fieldGoalsScored: 'fieldGoalsScored', // 命中投篮
  fieldGoalsTotal: 'fieldGoalsTotal', // 总投篮数
  threePointersScored: 'threePointersScored', // 3-Point
  threePointersTotal: 'threePointersTotal', // 3-Point
  freeThrowsScored: 'freeThrowsScored', // 
  freeThrowsTotal: 'freeThrowsTotal', // 
  rebounds: 'rebounds', // 
  offensiveRebounds: 'offensiveRebounds', // 
  defensiveRebounds: 'defensiveRebounds',
  assists: 'assists',
  blocks: 'blocks',
  steals: 'steals',
  turnovers: 'turnovers',
  totalFouls: 'totalFouls'
}