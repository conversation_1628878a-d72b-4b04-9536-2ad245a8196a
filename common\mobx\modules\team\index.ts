// @ts-nocheck
import { makeAutoObservable } from 'mobx';
import _ from 'lodash';

export const TeamTab = {
  overview: 'Overview',
  salary: 'Salary',
  schedule: 'Schedule',
  squad: 'Squad',
  transfer: 'Transfer',
  champions: 'Champions',
  stats: 'Stats',
};

export const TeamTabH5 = {
  overview: 'Overview',
  standings: 'Standings',
  salary: 'Salary',
  schedule: 'Schedule',
  squad: 'Squad',
  playerStats: 'PlayerStats',
  champions: 'Champions',
  transfer: 'Transfer',
};

const DefaultProps = {
  teamId: '',
  teamHeaderInfo: {
    logo: '',
    name: '',
    countryId: '',
    totalPlayers: 0,
    foreignPlayers: 0,
    avgAge: 0,
    marketValue: 0,
    marketValueCurrency: '',
    playerSalary: 0,
    competition: {
      id: '',
      name: '',
      logo: '',
      countryId: '',
      categoryId: '',
      type: 0,
    },
    country: {
      id: '',
      name: '',
      logo: '',
    },
    currentSeason: {
      id: '',
      year: '',
    },
  },
};

export default class Team {
  constructor() {
    this.reset();
    makeAutoObservable(this);
  }

  reset() {
    for (const key in DefaultProps) {
      this[key] = _.cloneDeep(DefaultProps[key]);
    }
  }

  changeTeamId(teamId) {
    this.teamId = teamId;
  }

  changeTeamHeaderInfo(data) {
    for(let k in data) {
      if(!data[k]) {
        data[k] = DefaultProps.teamHeaderInfo[k]
      } 
    }
    console.log(data)
    this.teamHeaderInfo = data
  }
}
