import React, { useEffect, useMemo } from 'react';
import { useParams } from 'umi';

import { getPlayerTransfers } from 'iscommon/api/player';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { momentTimeZone } from 'iscommon/utils';


import './index.less'


const PlayerTransfer: React.FC = () => {
  const { categoryId: playerId } = useParams<{ categoryId: string }>();

  const labelMaps = useTranslateKeysToMaps([
    'TransferIn',
    'TransferOut',
    'Loan',
    'EndOfLoan',
    'Transfer',
    'Retirement',
    'Draft',
    'Unknown',
  ]);

  const [list, setList] = React.useState<any[]>([]);

  const transferTypeLabelList = useMemo(
    () => [
      '',
      labelMaps.Loan,
      labelMaps.EndOfLoan,
      labelMaps.Transfer,
      labelMaps.Retirement,
      labelMaps.Draft,
      labelMaps.Released,
      labelMaps.Signed,
      labelMaps.Unknown,
    ],
    [labelMaps],
  );

  useEffect(() => {
    getPlayerTransfers({ playerId }).then((res: any) => {
      if (res?.playerTransfers?.length) {
        let transfers: any = [];
        const playerTransfers = res.playerTransfers
          .filter((i: any) => !!(i.fromTeam && i.toTeam))
          .sort((a: any, b: any) => b.transferTime - a.transferTime);
        const playerTransfers2 = playerTransfers
          .filter((item: any) => {
            if (item.fromTeam) {
              const transfer = '' + item.fromTeam.id + item.toTeam.id + item.transferFee;
              if (!transfers.includes(transfer)) {
                transfers.push(transfer);
                return item;
              }
            }
        });
        setList(playerTransfers2);
      }
    });
  }, [playerId]);

  return (
    <div className='player-transfer-container'>
      <div className='container-title'>{labelMaps.Transfer}</div>
      <div className='container-body'>
        {list.map((item: any) => (
          <div className='transfer-container'>
            <div className='transfer-title'>
              <span>{momentTimeZone(item.transferTime * 1000, 'DD MMM YYYY')}</span>
              <span>{transferTypeLabelList[item.transferType]}&nbsp;{!!item.transferFee ? `${(item.transferFee / 1000000).toFixed(1)}M€` : ''}</span>
            </div>
            <div className='transfer-body'>
              <div className='home-team-container'>
                <span>{item.fromTeam?.name}</span>
                <img className='team-icon' src={item.fromTeam?.logo} alt={item.fromTeam?.name}/>
              </div>
              <img className='arrow-right' src={require('@/assets/images/transfer_rightArrow.png')} alt=""/>
              <div className='away-team-container'>
                <img className='team-icon' src={item.toTeam?.logo} alt={item.toTeam?.name}/>
                <span>{item.toTeam?.name}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
    // <>
    //   <div className="playerOverviewTitle">{labelMaps.Transfer}</div>
    //   <div className={styles.baseDesc}>
    //     {list.reverse().map((item) => (
    //       <div key={item.transferTime} className={styles.baseRow}>
    //         <div className={styles.left}>
    //           <div className={styles.time}>{momentTimeZone(item.transferTime * 1000, 'YYYY/MM/DD')}</div>
    //           <div className={styles.team}>
    //             <div className={styles.fromTeam}>
    //               <img className={styles.fromTeamLogo} src={item.fromTeam?.logo || defaultTeam} alt="" />
    //               <div className={styles.teamName}>{item.fromTeam?.name || item.toTeam?.name}</div>
    //             </div>
    //             <div className={styles.arrow} />
    //             <div className={styles.toTeam}>
    //               <img className={styles.toTeamLogo} src={item.toTeam?.logo} alt="" />
    //               <div className={styles.teamName}>{item.toTeam?.name || '--'}</div>
    //             </div>
    //           </div>
    //         </div>
    //         <div className={styles.right}>
    //           <div className={styles.cost}>{!!item.transferFee ? `${(item.transferFee / 1000000).toFixed(1)}M€` : ''}</div>
    //           <div>{transferTypeLabelList[item.transferType]}</div>
    //         </div>
    //       </div>
    //     ))}
    //   </div>
    // </>
  );
};

export default PlayerTransfer;
