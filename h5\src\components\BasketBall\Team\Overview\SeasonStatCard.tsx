import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { inject, observer } from "mobx-react";
import './SeasonStatCard.less'

const SeasonStatCard = inject('store')(
  observer((props: any) => {
    const {
      store: { 
        Basketball: { 
          Team : { teamHeaderInfo }
        }
      }
    } = props;

    const labelMaps = useTranslateKeysToMaps([ 'Stats', 'Season', 'Position' ]);

    const StatItem = ({ label, value, rank }) => {
      return (
        <div className="stat-item">
          <div className="stat-label">{label}</div>
          <div className="stat-value">{value}</div>
          <div className="stat-rank">{rank}</div>
        </div>
      );
    };
    
    return (
      <div className="basketball-season-stat-container">
        <div className="container-title">
          {teamHeaderInfo?.currentSeason?.year}&nbsp;{labelMaps.Season}&nbsp;{labelMaps.Stats}
        </div>
        <div className="container-body">
          <div className="stats-container">
            <span className="stats-value">{teamHeaderInfo.won}&nbsp;-&nbsp;{teamHeaderInfo.lost}</span>
            <span className="stats-value">{labelMaps.Position}&nbsp;{teamHeaderInfo.position}</span>
          </div>
          <div className="stats-row">
            <StatItem label='PPG' value={teamHeaderInfo?.pointPerGame} rank={teamHeaderInfo?.pointPerGameRank}/>
            <StatItem label='RPG' value={teamHeaderInfo?.reboundsPerGame} rank={teamHeaderInfo?.reboundsPerGameRank}/>
            <StatItem label='APG' value={teamHeaderInfo?.assistPerGame} rank={teamHeaderInfo?.assistPerGameRank}/>
            <StatItem label='FG%' value={teamHeaderInfo?.fieldGoalsAccuracy} rank={teamHeaderInfo?.fieldGoalsAccuracyRank}/>
          </div>
        </div>
      </div>
    );
  }),
);

export default SeasonStatCard;