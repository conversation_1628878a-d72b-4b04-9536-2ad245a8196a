.basketball-overview-team-card-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;

  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .container-body {
    background: #1e1e1e;
    padding: 10px;

    .custom-player-standing-table {
      width: 100%;
      display: flex;
      flex-direction: column;
      border-collapse: collapse;
      margin-bottom: 10px;

      .table-header {
        display: flex;
        color: #fff;
        background-color: #2c2c2c;
        font-weight: bold;
        border-bottom: 1px solid #121212;
  
        .header-cell {
          padding: 8px;
          text-align: center;
          background-color: #2c2c2c;
        }
  
        .team-cell {
          width: 300px;
          flex-shrink: 0;
          background-color: inherit;
          text-align: left;
        }
  
        .header-cell:not(.team-cell) {
          padding: 8px;
          flex: 1;
          min-width: 10%;
        }
      }

      .table-body {
        .table-row {
          display: flex;
          align-items: center;
          background-color: #121212;
          border-bottom: 1px solid #2c2c2c;
          color: #fff;
  
          .table-cell {
            text-align: center;
            padding: 8px;
            background-color: #121212;
          }
  
          .team-cell {
            display: flex;
            flex-direction: row;
            align-items: center;
            background-color: inherit;
            padding: 8px;
            width: 300px;
            flex-shrink: 0;
  
            .team-icon {
              width: 58px;
              height: 58px;
              border-radius: 50%;
              margin: 5px 10px;
              background-size: cover;
            }
  
            .team-name {
              font-size: 22px;
              font-weight: 600;
              color: #fff;
              text-overflow: ellipsis;
              overflow: hidden;
              text-align: left;
            }
          }
  
          .table-cell:not(.team-cell) {
            padding: 8px;
            flex: 1;
            min-width: 10%;
          }
        }
      }
    }
  }
}