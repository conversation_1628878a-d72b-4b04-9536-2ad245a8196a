import { getCompetitionStatsPlayer } from "iscommon/api/basketball/basketball-competition";
import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { CompetitionsTabH5 } from "iscommon/mobx/basketball/modules/competition";
import { RightOutline } from 'antd-mobile-icons'
import { inject, observer } from "mobx-react";
import { useState, useEffect, useCallback } from "react";
import { Link } from "umi";
import './PlayerCard.less'
import Loading from "@/components/Loading";

interface Props {
  store?: any;
}

const PlayerCard: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: {
        Basketball: {
          Competitions: {
            competitionId,
            currentSeason: { id: seasonId },
          },
        },
      },
    } = props;

    const labelMaps = useTranslateKeysToMaps([
      'Player',
      'Points',
      'topPlayers',
    ]);

    const [tablesList, setTablesList] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false);
    
    useEffect(() => {
      if (competitionId && seasonId) {
        setLoading(true); 
        getCompetitionStatsPlayer({
          competitionId,
          seasonId,
          scope: 5,
        }).then(({ list }: any) => {
          if (list.length > 0) {
            const topThree = list
              .sort((a: any, b: any) => b.points - a.points)
              .slice(0, 3);
            setTablesList(topThree);
          }
          setLoading(false);
        });
      }
    }, [competitionId, seasonId]);

    const goStanding = useCallback(() => {
      GlobalUtils.goToPage(PageTabs.competition, competitionId, {
        target: 'history',
        customTab: CompetitionsTabH5.playerStats
      });
    }, [competitionId]);

    return (
      <div className="basketball-overview-player-card-container">
        <div className="container-title">
          {labelMaps.Player}
          <div onClick={goStanding}>
            <RightOutline />
          </div>
        </div>
        <div className="container-body">
          <div className='custom-player-standing-table'>
            <div className='table-header'>
              <div className="header-cell">#</div>
              <div className="header-cell player-cell">{labelMaps.topPlayers}</div>
              <div className="header-cell">{labelMaps.Points}</div>
            </div>
            <div className="table-body">
              <Loading loading={loading} isEmpty={tablesList.length === 0}>
                {tablesList.map((item: any, index) => {
                  return (
                    <div className="table-row" key={index}>
                      <div className="table-cell">{index+1}</div>
                      <Link className='table-cell player-cell' key={index} to={GlobalUtils.getPathname(PageTabs.player, item.player.id)}>
                        <img src={item.player.logo} alt={item.player.name} className="player-icon" />
                        <div className="player-info">
                          <span className="player-name">{item.player.name}</span>
                          <div className="player-team-detail">
                            <img src={item.team.logo} alt={item.team.name} className="team-icon"/>
                            <span className="team-name">{item.team.name}</span>
                          </div>
                        </div>
                      </Link>
                      <div className="table-cell">{item.points}</div>
                    </div>
                  )
                })}
              </Loading>
            </div>
          </div>
        </div>
      </div>
    )
  }),
);

export default PlayerCard;