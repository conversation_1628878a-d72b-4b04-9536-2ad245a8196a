import { useEffect, useState } from 'react';

import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

import _ from 'lodash';
import styles from './CommonStandingsIntegral.less';
import './CommonStandingsIntegral.less'
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { FallbackImage } from 'iscommon/const/icon';
import { Link } from 'umi';

interface Props {
  type?: number;
  data: any[];
  promotions: any[];
  titleMap: {
    label: string;
    value: string | Function;
  }[];
  currentTeamIds?: string[];
}

const CommonStandingsIntegral: React.FC<any> = (props: Props) => {
  const { type, data = [], promotions = [], titleMap = [], currentTeamIds = [] } = props;
  const [isGroup, setIsGroup] = useState(0); //isGroup：1：杯赛   0：联赛

  const labelMaps = useTranslateKeysToMaps(['Team', 'Pts']);

  useEffect(() => {
    if (data.length) {
      setIsGroup(data[0].isGroup);
    }
  }, [data]);

  const isLastOne = (i: number) => titleMap.length - 1 === i;

  const getPromotionColor = (promotionId: any) => {
    const promotion = promotions.find(promo => promo.id === promotionId);
    return promotion ? promotion.color : 'transparent';
  };

  return (
    <div className='common-standing-integral-container'>
      <div className='custom-common-standing-table'>
        <div className='header-row'>
          <div className="header-cell">#</div>
          <div className="header-cell team-cell">{labelMaps.Team}</div>
          {titleMap.map((item, index) => {
            return (
              <div className='header-cell' key={index}>{item.label}</div>
            )
          })}
        </div>
        {data.map((item: any, index: any) => {
          return (
            <Link className="table-row" key={item?.team?.id} to={GlobalUtils.getPathname(PageTabs.team, item?.team?.id)}>
              <div className="table-cell">{item?.position}</div>
              <div className="table-cell team-cell">
                <img className="team-logo" src={item?.team?.logo || FallbackImage} alt={item?.team?.name} loading="lazy"/>
                <span className='team-name'>{item?.team?.name}</span>
              </div>
              {titleMap.map((title, index) => {
                console.log('titlemap', titleMap)
                return (
                  <div className='table-cell' key={index}>
                    {_.isString(title.value) ? item[title.value] : title.value(item)}
                  </div>
                );
              })}
            </Link>
          )
        })}
      </div>
    </div>

    // <div className={styles.integral_table_container}>
    //   <div className={styles.heater_style}>
    //     <div className={styles.index_column}>#</div>
    //     <div className={styles.team_column}>{labelMaps.Team}</div>
    //     {titleMap.map((item, index) => {
    //       return (
    //         <div key={item.label} className={styles.data_column}>
    //           {item.label}
    //         </div>
    //       );
    //     })}
    //   </div>
    //   {data.map((item: any, index: any) => {
    //     return (
    //       <div className={styles.rows_style} key={item.teamId}>
    //         <div className={`${styles.index_column} ${currentTeamIds.includes(item.team?.id) && styles.bg_eff6fc}`}>
    //           {index + 1}
    //         </div>
    //         <div className={`${styles.team_column} ${currentTeamIds.includes(item.team?.id) && styles.bg_eff6fc}`}>
    //           <img src={item.team?.logo} className={styles.team_logo} alt="" />
    //           <span className={styles.team_name}>{item.team?.name}</span>
    //         </div>

            // {titleMap.map((title, index) => {
            //   return (
            //     <div
            //       key={index}
            //       className={`${
            //         isLastOne(index) ? `${styles.goals_column} ${styles.bg_F6F8F9}` : `${styles.data_column} ${styles.bg_eff6fc}`
            //       } `}
            //     >
            //       {_.isString(title.value) ? item[title.value] : title.value(item)}
            //     </div>
            //   );
            // })}
    //         {/* <div className={`${styles.pts_column} ${styles.bg_FEF4E1}`}>
    //           {type === 0 ? item.points : type === 1 ? item.homePoints : item.awayPoints}
    //         </div> */}
    //       </div>
    //     );
    //   })}
    //   {type === 0 && !isGroup ? (
    //     <div className={styles.legend_container}>
    //       {promotions.map((item: any) => (
    //         <div className={styles.legend_box} key={item.id}>
    //           <div className={`${styles.legend}`} style={{ background: item.color }} />
    //           <span className={styles.ml_16}>{item.name}</span>
    //         </div>
    //       ))}
    //     </div>
    //   ) : null}
    // </div>
  );
};

export default CommonStandingsIntegral;
