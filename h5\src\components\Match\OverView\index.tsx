import { inject, observer } from 'mobx-react';

import H2H from './H2H';
import LiveAndVideo from '@/components/Common/LiveAndVideo/LiveAndVideo';
import MatchInfo from './MatchInfo';
import MatchStatistics from './MatchStatistics';
import PlayerCompare from './PlayerCompare';
import SingleMatchLine from './SingleMatchLine';
import Venue from './Venue';

const OverView = inject('store')(
  observer((props: any) => {
    const {
      store: { Match },
    } = props;
    const { matchHeaderInfo, matchId } = Match;
    
  return (
    <>
      <LiveAndVideo matchHeaderInfo={matchHeaderInfo} matchId={matchId}/>
      <SingleMatchLine />
      <PlayerCompare />
      <MatchStatistics />
      {/* <Venue /> */}
      {/* <H2H /> */}
      <MatchInfo />
    </>
  );
}));

export default OverView;
