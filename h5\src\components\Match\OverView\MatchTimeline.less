.overview-match-timeline-container {
  display: flex;
  flex-direction: column;

  .time-item {
    display: flex;
    justify-content: center;
    width: 100%;
    position: relative;
    // line-height: 18px;
    font-size: 20px;
    color: #333;
  
    .item-half {
      flex: 1;
      overflow: hidden;
  
      .card-container {
        display: inline-block;
        vertical-align: top;
        position: relative;
        padding: 0px 15px;
        margin: 10px 0;
        max-width: 100%;
  
        .card-text {
          font-size: 20px;
          display: flex;
          align-items: center;
          color: #fff;
  
          > span {
            flex: 1;
          }
  
          // > svg {
          //   width: 18px;
          // }

          // &.text-left {
          //   text-align: left;
          // }

          // &.text-right {
          //   text-align: right;
          // }
        }
  
        &.card-left {
          float: right;
        }

        .card-text {
          justify-content: flex-end;
          padding: 5px;

          .inline-text {
            text-overflow: ellipsis;
            margin: 0px 10px;
          }

          .incident-icon {
            margin: 0px 5px;
          }
        }
      }
    }
  
    .timeline-container {
      position: relative;
      text-align: center;
      padding: 10px 0;
  
      .timestamp {
        height: 45px;
        width: 80px ;
        border-radius: 16px;
        // line-height: 28px;
        font-size: 20px;
        font-weight: 700;
        display: inline-block;
        align-content: center;
        position: relative;
        background: #121212;
        color: #fff;
      }
    }
  }

  .future-icon-container {
    position: relative;
    margin: 10px auto;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;

    .icon-circle {
      width: 14px;
      height: 14px;
      border-radius: 50%;
      background-color: #fff;
      display: inline-block;
      margin-bottom: 10px;
    }

    .icon-timelock {
      width: 14px;
      height: 14px;
      color: #fff;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 10px;
    }
  }

  .time-title-container {
    display: flex;
    justify-content: center;
    margin: 20px 0;

    .time-pill {
      display: flex;
      align-items: center;
      background-color: #121212;
      border-radius: 50px 32px 32px 50px;
      padding-right: 10px;
      // line-height: 28px;
      font-size: 20px;
      height: 50px;
      white-space: nowrap;

      .time-circle {
        background-color: #333;
        color: #fff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        margin-right: 10px;
        padding: 0 5px;
        font-weight: 700;
      }

      .time-score {
        font-size: 20px;
        // line-height: 28px;
        font-weight: 700;
        color: #fff;
      }
    }

    .time-title {
      display: flex;
      justify-content: center;
      width: 100%;
      position: relative;
      line-height: 18px;
      font-size: 20px;
      color: #333;
    }
  }
}
