import { CloseOutlined } from '@ant-design/icons';
import { inject, observer } from 'mobx-react';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { queryAdList } from 'iscommon/api/header';

import styles from './index.less';

interface Props {
  position: string;
  style?: React.CSSProperties;
  store?: any;
}

interface AdItem {
  id: number;
  name: string;
  country: string;
  client: string;
  platform: string;
  position: string;
  height: number;
  width: number;
  status: number;
  imageUrl: string;
  jumpUrl: string;
  description: string;
  modifiedBy: string;
  createdBy: string;
  startTime: number | null;
  endTime: number | null;
}

const getNum = (num: string | number | undefined, defaultNum: number) => (Number.isNaN(Number(num)) ? defaultNum : Number(num));

const px2vw = (px: number) => {
  const vw = (px / 750) * 100;
  return `${vw}vw`;
};

const AdBanner: React.FC<Props> = inject('store')(
  observer((props) => {
    const { position, style } = props;
    const [ads, setAds] = useState<AdItem[]>([]);
    const [index, setIndex] = useState<number>(0);
    const [isClose, setClose] = useState<boolean>(false);

    const adItem = ads[index] || null;

    const size = useMemo(() => {
      return {
        width: px2vw(getNum(adItem?.width, 750)),
        height: px2vw(getNum(adItem?.height, 88)),
      };
    }, [adItem?.width, adItem?.height]);

    const reportGtag = useCallback(
      (eventName: string) => {
        if (adItem) {
          window.gtag?.('event', eventName, {
            platform: 'mobile',
            ad_name: adItem.name,
            ad_position: position,
            ad_description: adItem.description,
            ad_country: adItem.country,
          });
        }
      },
      [adItem, position]
    );

    const onClick = useCallback(() => {
      if (adItem?.jumpUrl) {
        reportGtag('click_ad_banner');
        window.open(adItem.jumpUrl, '_blank');
      }
    }, [adItem, reportGtag]);

    const onClose = useCallback(
      (e: any) => {
        e.stopPropagation();
        setClose(true);
        reportGtag('close_ad_banner');
      },
      [reportGtag]
    );

    useEffect(() => {
      queryAdList(position, 'mobile').then((list: AdItem[]) => {
        const filtered = list.filter((item) => item.imageUrl);
        setAds(filtered);
        if (filtered.length > 0) {
          reportGtag('ad_banner_view');
        }
      });
    }, [position, reportGtag]);

    useEffect(() => {
      if (ads.length <= 1) return;
      const timer = setInterval(() => {
        setIndex((prev) => (prev + 1) % ads.length);
      }, 5000);
      return () => clearInterval(timer);
    }, [ads]);
    
    return adItem?.imageUrl && !isClose ? (
      <div className={styles.container} style={{ ...size, ...style }} onClick={onClick}>
        <CloseOutlined className={styles.close} onClick={onClose} />
        <img style={size} className={styles.image} src={adItem.imageUrl} alt={adItem.name} />
      </div>
    ) : null;
  }),
);

export default AdBanner;
