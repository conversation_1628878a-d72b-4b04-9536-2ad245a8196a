import { FallbackCategoryImage, FallbackPlayerImage } from "iscommon/const/icon";
import { inject, observer } from "mobx-react";
import './PlayerHeader.less'

interface Props {
  store?: any;
}

const PlayerHeader: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: { 
        Basketball: { Player },
      }
    } = props;
    const { playerHeaderInfo } = Player;

    console.log('player info123', JSON.stringify(playerHeaderInfo))

    return (
      <div className='basketball-player-header-container'>
        <div className='player-container'>
          <img className='player-icon' src={playerHeaderInfo?.player?.logo || FallbackPlayerImage} alt={playerHeaderInfo?.player?.name} loading="lazy"/>
          <div className='player-info'>
            <div className='player-name-container'>
              <span className='player-name'>{playerHeaderInfo?.player?.name}</span>
            </div>
            <div className='team-detail'>
              <img className='team-icon' src={playerHeaderInfo?.team?.logo || FallbackCategoryImage} alt={playerHeaderInfo?.team?.name} loading="lazy"/>
              <span className='team-name'>{playerHeaderInfo?.team?.name}</span>
            </div>
          </div>
        </div>
      </div>
    );
  }),
);

export default PlayerHeader;