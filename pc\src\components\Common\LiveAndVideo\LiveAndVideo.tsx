import { inject, observer } from 'mobx-react';
import { useEffect, useState } from 'react';

import Empty from '../Empty';
import { FullscreenOutlined } from '@ant-design/icons';
import { getGameVideoInfo } from 'iscommon/api/match';
import { getLiveUrl } from 'iscommon/utils/live';
import { GlobalConfig } from 'iscommon/const/globalConfig';
import { isPluginMode } from '@/layouts/plugin';
import { momentTimeZone } from 'iscommon/utils';
import PluginVideo from '@/pages/Plugin/pluginVideo';
import { requestFullscreen } from '@/components/Match/Live/LiveAndVideo';
import showLoginModal from '../LoginModal';
import styles from './LiveAndVideo.less';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import WidgetBox from '../Widget';

const LiveStream = inject('store')(
  observer((props: any) => {
    const {
      store: { WebConfig },
      videoLink,
    } = props;

    const pluginMode = isPluginMode(); // ✅ detect plugin mode
    const {
      userData: { isLogin },
    } = WebConfig;

    const loginText = useTranslateKeysToMaps(['Login', 'LoginTips']);
    const [expired, setExpired] = useState(false);

    useEffect(() => {
      if (pluginMode) {
        const hostname = window.location.hostname;
        const shouldSkipExpiry = hostname.includes('www.gamebetreview.com');
    
        if (shouldSkipExpiry) {
          setExpired(false)
          return; // ✅ Skip expiry logic for this domain
        }
        
        const now = Date.now();
        const expiryTimestampKey = 'igscorePluginExpiry';
        const savedExpiry = localStorage.getItem(expiryTimestampKey);
    
        let expiryTime = savedExpiry ? parseInt(savedExpiry) : now + 5 * 60 * 1000;
    
        // If no expiry was previously saved, save it now
        if (!savedExpiry) {
          localStorage.setItem(expiryTimestampKey, expiryTime.toString());
        }
    
        const remaining = expiryTime - now;
    
        if (remaining <= 0) {
          setExpired(true);
        } else {
          const timeout = setTimeout(() => {
            setExpired(true);
          }, remaining);
    
          return () => clearTimeout(timeout);
        }
      }
    }, [pluginMode]);

    useEffect(() => {
      if ((isLogin || pluginMode) && videoLink) {
        window.gtag('event', 'stream_module_view', { platform: 'web' });
      }
    }, [isLogin, pluginMode, videoLink]);

    useEffect(() => {
      if (isLogin || pluginMode) {
        document.addEventListener(
          'keydown',
          (e) => {
            if (e.key === 'Enter') {
              requestFullscreen();
            }
          },
          false,
        );
      }
    }, [isLogin, pluginMode]);

    if (pluginMode && expired) {
      return (
        <div className={styles.liveContent}>
          <div className={styles.box}>
            <div className={styles.text}>Enjoy full live streaming experience on our mobile app.</div>
            <a
              href="https://onelink.to/igscore-newapp"
              target="_blank"
              rel="noopener noreferrer"
              className={styles.button}
            >
              Download Now
            </a>
          </div>
        </div>
      );
    }

    if (isLogin || pluginMode) {
      // running
      if (videoLink) {
        return (
          <div className={styles.liveContent}>
            <iframe id="liveStream" src={videoLink} width="100%" height="100%" />
          </div>
        );
      }
      return (
        <div className={styles.liveContent}>
          <div className={styles.noData}>
            <i className="iconfont icon-nodata" />
            <div className={styles.noDataText}>No Data</div>
          </div>
        </div>
      );
    }
    return (
      <div className={styles.liveContent}>
        <div className={styles.box}>
          <div className={styles.text}>{loginText.LoginTips}</div>
          <div
            className={styles.button}
            onClick={() => {
              showLoginModal('stream');
            }}
          >
            {loginText.Login}
          </div>
        </div>
      </div>
    );
  }),
);

const Animation = (props: any) => {
  const { matchId } = props;
  const url = getLiveUrl(matchId);
  if (url) {
    return (
      <div className={styles.liveContent}>
        <iframe src={url}></iframe>
      </div>
    );
  }
  return (
    <div style={{ flex: 1 }}>
      <Empty />
    </div>
  );
};

const LiveVideoTitle = (props: any) => {
  const { competition, uniqueTournament, matchTime, showFullscreen = false } = props || {};
  return (
    <div className={styles.title}>
      <span>
        {competition?.name || uniqueTournament?.name} {momentTimeZone(matchTime)}
      </span>
      {showFullscreen && (
        <FullscreenOutlined className={styles.fullscreen} style={{ color: '#fff', fontSize: 30 }} onClick={requestFullscreen} />
      )}
    </div>
  );
};

const LiveAndVideo = (props: any) => {
  // const [tab, setTab] = useState(0);
  const { matchId, matchHeaderInfo, showVideo } = props;
  const [tab, setTab] = useState(-1);
  const [hasVideo, setHasVideo] = useState(false);
  const [videoLink, setVideoLink] = useState('');
  const labelMaps = useTranslateKeysToMaps(['Animation', 'LiveStream']);
  const pluginMode = isPluginMode();

  useEffect(() => {
    if (matchId && matchId !== -1) {
      const getVideoLink = async () => {
        try {
          const { link, ch }: any = await getGameVideoInfo(matchId);
          setHasVideo(link || (ch && matchHeaderInfo.statusId === 1));
          if (link && showVideo) {
            setTab(1);
            setVideoLink(link);
          } else {
            setTab(0);
          }
        } catch (e) {
          setTab(0);
        }
      };
      getVideoLink();
    }
  }, [matchHeaderInfo, matchId]);

  if (matchId === -1) return null;
  const id = `${GlobalConfig.pathname}LivePlugin`;
  
  return (
    <div className={styles.liveAndVideo} id={id}>
      {tab > -1 && (
        <div className={styles.content}>
          {showVideo && hasVideo && (<div key={2} className={`${styles.item} ${tab === 1 ? styles.current : ''}`} onClick={() => setTab(1)}>
            {labelMaps.LiveStream}
          </div>)}
          <div key={1} className={`${styles.item} ${tab === 0 ? styles.current : ''}`} onClick={() => setTab(0)}>
            {labelMaps.Animation}
          </div>
        </div>
      )}
      <LiveVideoTitle   
        competition={matchHeaderInfo?.competition}
        uniqueTournament={matchHeaderInfo?.uniqueTournament}
        matchTime={matchHeaderInfo?.matchTime}
        showFullscreen={tab === 1}
      />
      {tab === 1 && <LiveStream videoLink={videoLink} />}
      {tab === 0 && <Animation matchId={matchId} />}
      {!pluginMode && props.children}
      {pluginMode && <PluginVideo/>}
      <div className={styles.wigetBox}>
        <WidgetBox pluginId={id} titleColor="#fff" title="Live" titlePosition="right" tab="Overview" />
      </div>
    </div>
  );
};

export default LiveAndVideo;
