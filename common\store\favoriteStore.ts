import { GlobalConfig } from '../const/globalConfig';
import { getStore, saveStore } from './index';

const FavoriteStoreKey = {
  get match() {
    return `${GlobalConfig.pathname}_favoriteMatches`;
  },
  get competition() {
    return `${GlobalConfig.pathname}_favoriteCompetitionsIds`;
  },
};

export const FavoriteStoreH5Key = {
  get match() {
    return `${GlobalConfig.pathname}_h5StickIdList`;
  },
};

const FavoriteStore = {
  addFavorite: (config) => {
    const key = FavoriteStoreKey.match;
    const favoriteMatches = getStore(key) || [];
    favoriteMatches.push(config);
    saveStore(key, favoriteMatches);
    return favoriteMatches;
  },
  getFavorites: () => {
    const key = FavoriteStoreKey.match;
    return getStore(key) || [];
  },
  deleteFavorite: (config = {}) => {
    const key = FavoriteStoreKey.match;
    const { matchId } = config;
    const favoriteMatches = getStore(key) || [];
    const index = favoriteMatches.findIndex((item) => item.matchId == matchId);
    if (index > -1) {
      favoriteMatches.splice(index, 1);
      saveStore(key, favoriteMatches);
    }
    return favoriteMatches;
  },
  addFavoriteCompetitions: (config) => {
    const key = FavoriteStoreKey.competition;
    // const {id, competitionName, logo} = config
    const favoriteCompetitions = getStore(key) || [];
    favoriteCompetitions.push(config);
    saveStore(key, favoriteCompetitions);
    return favoriteCompetitions;
  },
  getFavoritesCompetitions: () => {
    const key = FavoriteStoreKey.competition;
    const favoriteCompetitions = getStore(key) || [];
    return favoriteCompetitions;
  },
  deleteFavoriteCompetitions: (config) => {
    const key = FavoriteStoreKey.competition;
    const { competitionId } = config;
    const favoriteCompetitions = getStore(key) || [];
    const index = favoriteCompetitions.findIndex((item) => item.competitionId == competitionId);
    if (index > -1) {
      favoriteCompetitions.splice(index, 1);
      saveStore(key, favoriteCompetitions);
    }
    return favoriteCompetitions;
  },
};

export default FavoriteStore;
