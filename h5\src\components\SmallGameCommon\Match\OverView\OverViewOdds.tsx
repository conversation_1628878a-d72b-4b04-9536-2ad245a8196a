import SectionTitle from '@/components/Competition/Overview/SectionTitle';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { translate } from 'iscommon/i18n/utils';
import { MatchTabH5 } from 'iscommon/mobx/modules/match';
import { inject, observer } from 'mobx-react';
import { useCallback, useMemo } from 'react';
import { history } from 'umi';

import IMG_188bet from 'iscommon/assets/images/football/188bet.png';
import IMG_1xbet from 'iscommon/assets/images/football/1xbet_c.png';
import IMG_bet365 from 'iscommon/assets/images/football/bet365.png';
import IMG_Crown from 'iscommon/assets/images/football/crownbet.png';

import './OverViewOdds.less';

const ODDS_IMG: any = {
  '2': {
    url: IMG_bet365,
    name: 'Bet365',
  },
  '3': {
    url: IMG_1xbet,
    name: 'Crown',
  },
  '4': {
    url: IMG_Crown,
    name: '<PERSON>',
  },
  '21': {
    url: IMG_188bet,
    name: '188bet',
  },
};

const getFilterOdd = (data: any[] = [], type: string, index: number): any[] => {
  if (data.length < index + 1) return [];
  const list = data[index].companyOdds || [];
  if (list.length === 0) return [];
  try {
    const o = list.filter((item: any) => item.oddsType === type)[0].odds;
    let result: any = {};
    for (let v of data) {
      if (v.matchStatus === 1) {
        result = v;
        break;
      }
    }
    return result?.oddsData || o[o.length - 1]?.oddsData || [];
  } catch (e) {
    return [];
  }
};

const OverViewMaps = [
  {
    key: 0,
    label: 1,
  },
  {
    key: 1,
    label: 'X',
  },
  {
    key: 2,
    label: 2,
  },
];

const SmallballOverViewOdds = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Smallball: {
          SmallballMatch: { matchId, matchOdds: data },
        },
      },
      overViewMaps = OverViewMaps,
    } = props;
    const oddText = translate('Odds');
    const oddsData = useMemo(() => {
      if (data?.length) {
        return [
          { list: getFilterOdd(data, 'eu', 0) },
          { list: getFilterOdd(data, 'eu', 1) },
          { list: getFilterOdd(data, 'eu', 2) },
          { list: getFilterOdd(data, 'eu', 3) },
        ];
      }
      return [];
    }, [data]);

    const click = useCallback(() => {
      history.replace(GlobalUtils.getPathname(PageTabs.match, matchId, MatchTabH5.odds));
    }, [matchId]);

    const isEmpty = oddsData.filter(({ list }) => list.length > 0).length === 0;
    if (isEmpty) return null;

    return (
      <div className="singleMatchLine">
        <SectionTitle background="#fff" leftTitle={oddText} rightTitle="More" onClick={click} />
        <div className="plugins" style={{ minHeight: 0, marginBottom: 0 }}>
          {oddsData.map(({ list }, index) => {
            if (list?.length === 0) {
              return null;
            }
            const { companyId } = data[index];
            return (
              <div key={index} className="oddItem">
                <img className="card g" id={companyId} src={ODDS_IMG[companyId]?.url} alt={ODDS_IMG[companyId]?.name || ''} />
                {overViewMaps.map((item: any) => {
                  return (
                    <div key={item.key} className="card">
                      <span>{item.label}</span>
                      <span className="line"></span>
                      <div className="point">{list[item.key]}</div>
                    </div>
                  );
                })}
                {/* <div className="card g2">
                  <span>X</span>
                  <span className="line"></span>
                  <div className="point">{list[1]}</div>
                </div>
                <div className="card">
                  <span>2</span>
                  <span className="line"></span>
                  <div className="point">{list[2]}</div>
                </div> */}
              </div>
            );
          })}
        </div>
      </div>
    );
  }),
);

export default SmallballOverViewOdds;
