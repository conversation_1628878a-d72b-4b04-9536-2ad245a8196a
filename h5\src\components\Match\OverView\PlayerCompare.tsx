import { getMatchLineup } from 'iscommon/api/match';
import { inject, observer } from 'mobx-react';
import { useEffect, useState } from 'react';

import './PlayerCompare.less'
import { getValue } from 'iscommon/i18n/utils';
import { useTranslation } from 'react-i18next';
import { LeftOutline, RightOutline } from 'antd-mobile-icons'
import { FallbackPlayerImage } from 'iscommon/const/icon';
import Loading from '@/components/Loading';
import { Badge } from 'antd-mobile';

interface Player {
  logo: string;
  rating: string;
  name: string;
}

const PlayerCompare = inject('store')(
  observer((props: any) => {
    const { store } = props;
    const { matchId } = store.Match;
    const [matchLineupInfo, setMatchLineupInfo] = useState<any>({});
    const [homeHighestRatedPlayer, setHomeHighestRatedPlayer] = useState<Player | null>(null);
    const [awayHighestRatedPlayer, setAwayHighestRatedPlayer] = useState<Player | null>(null);
    const [loading, setLoading] = useState(true);

    const { t } = useTranslation();

    const translate = (key = '', options = {}) => {
      return getValue(t, key, options);
    }

    useEffect(() => {
      if (matchId) {
        setLoading(true)
        getMatchLineup({ matchId })
          .then((data: any) => {
            if (data) {
              setMatchLineupInfo(data);
            }
          })
          .finally(() => {
            setLoading(false);
          });
      }
    }, [matchId]);

    useEffect(() => {
      if (matchLineupInfo?.homeLineup) {
        const homeLineup = [
          ...matchLineupInfo?.homeLineup[0] || [],
          ...(matchLineupInfo?.homeLineup[1] || [])
        ];
        const highestRated = homeLineup.reduce((highest, player) => {
          return parseFloat(player.rating) > parseFloat(highest.rating) ? player : highest;
        }, homeLineup[0]);

        setHomeHighestRatedPlayer(highestRated);
      }
      if (matchLineupInfo?.awayLineup) {
        const awayLineup = [
          ...matchLineupInfo?.awayLineup[0] || [],
          ...(matchLineupInfo?.awayLineup[1] || [])
        ];
        const highestRated = awayLineup.reduce((highest, player) => {
          return parseFloat(player.rating) > parseFloat(highest.rating) ? player : highest;
        }, awayLineup[0]);

        setAwayHighestRatedPlayer(highestRated);
      }
    }, [matchLineupInfo]);

    if (loading) {
      return <div>Loading...</div>;
    }

    const getComparisonIcon = (homeRating: any, awayRating: any) => {
      if (homeRating > awayRating) {
        return <RightOutline className='compare-icon'/>;
      } else if (awayRating > homeRating) {
        return <LeftOutline className='compare-icon'/>;
      } else if (awayRating === homeRating) {
        return <div className='compare-icon'>=</div>
      } else {
        return <></>
      }
    };

    const getBadgeBackgroundColor = (count: number) => {
      if (count >= 8.0 && count <= 10.0) {
        return '#00ADC4';
      } else if (count >= 7.0 && count <= 7.9) {
        return '#1EC853';
      } else if (count >= 6.6 && count <= 6.9) {
        return '#FFAE0F';
      } else if (count >= 6.0 && count <= 6.5) {
        return '#F08022';
      } else if (count >= 0.1 && count <= 5.9) {
        return '#ED5454';
      }
    };

    return (
      <div className="overview-player-compare-container">
        <div className="container-title">{translate('Highest Rated Player')}</div>
        <div className='container-body'>
          <Loading isEmpty={!homeHighestRatedPlayer && !awayHighestRatedPlayer}>
            {homeHighestRatedPlayer && (
              <div className='player-info'>
                <img src={homeHighestRatedPlayer?.logo || FallbackPlayerImage} className='player-icon'/>
                <Badge className='rating-badge' style={{background: getBadgeBackgroundColor(parseFloat(homeHighestRatedPlayer?.rating))}} content={parseFloat(homeHighestRatedPlayer?.rating).toFixed(1)} />
                <div className='player-name'>{homeHighestRatedPlayer?.name}</div>
              </div>
            )}
            <div>{getComparisonIcon(homeHighestRatedPlayer?.rating, awayHighestRatedPlayer?.rating)}</div>
            {awayHighestRatedPlayer && (
              <div className='player-info'>
                <div className='player-name'>{awayHighestRatedPlayer?.name}</div>
                <Badge className='rating-badge' style={{background: getBadgeBackgroundColor(parseFloat(awayHighestRatedPlayer?.rating))}} content={parseFloat(awayHighestRatedPlayer?.rating).toFixed(1)} />
                <img src={awayHighestRatedPlayer?.logo || FallbackPlayerImage} className='player-icon'/>
              </div>
            )}
          </Loading>
        </div>
      </div>
    );
  })
);

export default PlayerCompare;