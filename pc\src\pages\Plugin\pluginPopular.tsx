import './pluginPopular.less'

import { CategoryIcon, FallbackCategoryImage } from 'iscommon/const/icon';
import { useCallback, useEffect, useState } from 'react';

import { getCompetitionHots } from 'iscommon/api/competition-hot';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

interface PopularCompetition {
  id: string;
  name: string;
  logo: string;
  hasLive: boolean;
}

interface PopularCompetitionsProps {
  selectedCompetition: string;
  onCompetitionClick: (competitionName: string) => void;
  liveCompetitionIds?: Set<string>; 
}

const PopularCompetitions: React.FC<PopularCompetitionsProps> = ({
  selectedCompetition,
  onCompetitionClick,
  liveCompetitionIds
}) => {
  const [popularCompetitions, setPopularCompetitions] = useState<PopularCompetition[]>([]);

  const labelMaps = useTranslateKeysToMaps(['Popular League'])

  const fetchPopularCompetitions = useCallback(async () => {
    try {
      // Get popular competitions
      const hots = await getCompetitionHots();

      const popularWithLive: PopularCompetition[] = hots.map((hot: any) => ({
        id: hot.id,
        name: hot.name,
        logo: hot.logo,
        hasLive: liveCompetitionIds?.has(hot.id?.toString()) || false,
      }));

      setPopularCompetitions(popularWithLive);
    } catch (error) {
      console.error('Failed to fetch popular competitions:', error);
    }
  }, [liveCompetitionIds]);

  useEffect(() => {
    fetchPopularCompetitions();
    const interval = setInterval(fetchPopularCompetitions, 3000);
    return () => clearInterval(interval);
  }, [fetchPopularCompetitions]);

  const handleCompetitionClick = (competitionName: string) => {
    // If the same competition is clicked, deselect it (set to 'All')
    if (selectedCompetition === competitionName) {
      onCompetitionClick('All');
    } else {
      onCompetitionClick(competitionName);
    }
  };

  return (
    <div className="popular-competitions-section">
      <div className="popular-competitions-title">{labelMaps['Popular League']}</div>
      <div className="popular-competitions-body">
        {popularCompetitions.map((competition) => (
          <div
            key={competition.id}
            className={`popular-competition-item ${selectedCompetition === competition.name ? 'selected' : ''}`}
            onClick={() => handleCompetitionClick(competition.name)}
          >
            <div className='left-content'>
              <img loading="lazy" className="popular-competition-logo" src={(CategoryIcon as any)[competition.logo] || competition.logo || FallbackCategoryImage} alt={competition.name}/>
              <span className="popular-competition-name">{competition.name}</span>
            </div>
            {competition.hasLive && <span className="live-indicator">LIVE</span>}
          </div>
        ))}
      </div>
    </div>
  );
};

export default PopularCompetitions;