// import { MatchStatusCodeTospan } from 'iscommon/const/constant';
import GlobalUtils, { GlobalConfig, PageTabs } from 'iscommon/const/globalConfig';
import { translate, useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import './ScheduleGame.less';
import { inject, observer } from 'mobx-react';
import { Link, useHistory } from 'umi';
import { getCompetitionFilter, getCompetitionList } from 'iscommon/api/competition';
import moment from 'moment';
import Loading from '@/components/Loading';
import { FallbackImage } from 'iscommon/const/icon';
import { Button, Picker } from 'antd-mobile';
import { DownOutline } from 'antd-mobile-icons'
import { InLiveStatusEnum, StatusCodeEnum } from 'iscommon/const/constant';

// const MatchStatusspan = (props: any) => {
//   const { matchStatus, matchMinutes } = props;
//   // status code  Match State：3=HT，8=FT
//   let statusspan = '';
//   if (matchStatus == 3) {
//     // statusspan = 'HT';
//   } else if (matchStatus == 8) {
//     // statusspan = 'FT';
//     // 2 first half 4 second half , 7 penalty shoot
//   } else if (matchMinutes && Number(matchMinutes) > 0) {
//     statusspan = matchMinutes;
//     return <span className="date">{statusspan}</span>;
//   }
//   return <span className="date">{(MatchStatusCodeTospan as any)[matchStatus]}</span>;
// };

// const VsScore: React.FC<any> = (props: any) => {
//   const { item } = props;
//   const { matchStatus } = item;
//   if (matchStatus <= 7) {
//     return (
//       <div className="listRight">
//         <span className="score">-</span>
//       </div>
//     );
//   }
//   if (matchStatus === 8) {
//     if (!item.hasOwnProperty('calculatedHomeScore')) {
//       item.calculatedHomeScore = item.homeScores[0];
//       item.calculatedAwayScore = item.awayScores[0];
//     }
//     const isHomeMore = item.calculatedHomeScore >= item.calculatedAwayScore;
//     return (
//       <div className="listRight">
//         <span className={`score ${isHomeMore ? 'color-333' : ''}`}>{item.calculatedHomeScore}</span>
//         <span className={`score ${!isHomeMore ? 'color-333' : ''}`}>{item.calculatedAwayScore}</span>
//       </div>
//     );
//   }
//   if (matchStatus === 9) return <div style={{ color: '#e74c5b' }}>postponed</div>;
//   if (matchStatus === 12) return <div style={{ color: '#e74c5b' }}>cancel</div>;
//   if (matchStatus === 13) return <div style={{ color: '#e74c5b' }}>TBD</div>;
//   return null;
// };

// const GroupNumspan: React.FC<{ num: number }> = React.memo(({ num }) => {
//   const groupspan = translate('Group');
//   return (
//     <div className="addGroup">
//       <img
//         className="van-image__img"
//         data-src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAMAAADW3miqAAAAdVBMVEUAAACqqqrf39/b29vg4ODZ2dnh4eHf39/f39/f39/e3t7e3t7e3t7f39/e3t7f39/e3t7e3t7e3t7f39+Xl5e3t7fLy8vY2Nje3t7f39/h4eHj4+Pk5OTp6enr6+vw8PDy8vL4+Pj7+/v8/Pz9/f3+/v7///9fHQRqAAAAFHRSTlMAAwgVISJEWGiYpMTKzdfX6/j+/pTcavsAAAE1SURBVDjLjZRrk4IgGIWpLO/mZV0Pmiuk8P9/YmlKBLbD+aQzz3DeOyGavCBK86LI0yjwyK4OflxCqYz9g81cMoD2jI9CjJz1FMguBnIMK3RskkoT61CFR505JaCDkB8SA0Vy0pgrWi4t8RZXRR0T3O5yR/cbks0xRLvLPKkW4ZpXRbn8Ik6rJcdDhmH+/1lkUgOyuV4+OvGGfhdpOXbwn1AMJt+Q9R5DTMi5pNN/0ERLjwTopfywMyLrEZBodXsHbkAMEUlh5q/Q1wdHSnKMNvQyfUEjclJA2JCegUDhBtl2dV2bdnbgTdOYgasSGNBHCVQx/xbZ0FxM1ZZmkQUtbVENbjSZDVajsg+to7IN3T60Dt02vpaRPr7bIqjsdhfBbaWcltNtzd0OhtvpcTxis85fzuEDf+N8SeUDRKwAAAAASUVORK5CYII="
//         src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAAkCAMAAADW3miqAAAAdVBMVEUAAACqqqrf39/b29vg4ODZ2dnh4eHf39/f39/f39/e3t7e3t7e3t7f39/e3t7f39/e3t7e3t7e3t7f39+Xl5e3t7fLy8vY2Nje3t7f39/h4eHj4+Pk5OTp6enr6+vw8PDy8vL4+Pj7+/v8/Pz9/f3+/v7///9fHQRqAAAAFHRSTlMAAwgVISJEWGiYpMTKzdfX6/j+/pTcavsAAAE1SURBVDjLjZRrk4IgGIWpLO/mZV0Pmiuk8P9/YmlKBLbD+aQzz3DeOyGavCBK86LI0yjwyK4OflxCqYz9g81cMoD2jI9CjJz1FMguBnIMK3RskkoT61CFR505JaCDkB8SA0Vy0pgrWi4t8RZXRR0T3O5yR/cbks0xRLvLPKkW4ZpXRbn8Ik6rJcdDhmH+/1lkUgOyuV4+OvGGfhdpOXbwn1AMJt+Q9R5DTMi5pNN/0ERLjwTopfywMyLrEZBodXsHbkAMEUlh5q/Q1wdHSnKMNvQyfUEjclJA2JCegUDhBtl2dV2bdnbgTdOYgasSGNBHCVQx/xbZ0FxM1ZZmkQUtbVENbjSZDVajsg+to7IN3T60Dt02vpaRPr7bIqjsdhfBbaWcltNtzd0OhtvpcTxis85fzuEDf+N8SeUDRKwAAAAASUVORK5CYII="
//         alt=""
//       />
//       <span>
//         {groupspan} {String.fromCharCode(64 + num)}
//       </span>
//     </div>
//   );
// });

// const ScheduleGame = React.memo<any>(({ competition: item, isCup = false, preGroupNum = 0 }: any) => {
//   const dateId = useMemo(() => `classId_${momentTimeZone(item.matchTime, 'YYMMDD')}`, [item.matchTime]);
//   const date = useMemo(() => momentTimeZone(item.matchTime, 'YY/MM/DD') as string, [item.matchTime]);
//   const goToMatch = useCallback(() => {
//     GlobalUtils.goToPage(PageTabs.match, item.id);
//   }, [item.id]);

//   return (
//     <div className={`gameList ${dateId}`} onClick={goToMatch}>
//       {isCup && preGroupNum !== item.groupNum ? <GroupNumspan num={item.groupNum} /> : null}
//       <div className="listItem">
//         <div className="listLeft">
//           <span className="date">{date}</span>
//           <MatchStatusspan {...item} />
//         </div>
//         <div className="listContent">
//           <div className="vsCountry">
//             <div className="vsCountryItem">
//               <i className="squareLogo" style={{ backgroundImage: `url(${item.homeTeam.logo})` }}></i>
//               <span className="teamName">{item.homeTeam.name}</span>
//               {item.homeScores[3] > 0 && <span className="card yellow">{item.homeScores[3]}</span>}
//               {item.homeScores[2] > 0 && <span className="card red">{item.homeScores[2]}</span>}
//             </div>
//             <div className="vsCountryItem">
//               <i className="squareLogo" style={{ backgroundImage: `url(${item.awayTeam.logo})` }}></i>
//               <span className="teamName">{item.awayTeam.name}</span>
//               {item.awayScores[3] > 0 && <span className="card yellow">{item.awayScores[3]}</span>}
//               {item.awayScores[2] > 0 && <span className="card red">{item.awayScores[2]}</span>}
//             </div>
//           </div>
//           <div>
//             {item.matchTime <= GlobalConfig.serverTime ? (
//               <div className="icon-setting">
//                 <span className="iconfont icondata" style={{ color: 'rgb(135, 195, 29)' }}></span>
//               </div>
//             ) : null}
//           </div>
//         </div>
//         <VsScore item={item} />
//       </div>
//     </div>
//   );
// });

// export default ScheduleGame;

interface Props {
  store?: any;
}

const ScheduleLeft: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: {
        Competitions: {
          competitionId = 1,
          competitionName,
          currentSeason: { id: seasonId, year: seasonText },
        },
      },
    } = props;
    const [stagesList, setStagesList] = useState<any[]>([]);
    const [roundList, setRoundList] = useState<any[]>([]);
    const [groupList, setGroupList] = useState<any[]>([]);
    const [competitions, setCompetitions] = useState<any[]>([]);
    const [curStageId, setCurStageId] = useState('');
    const [curRound, setCurRound] = useState(0);
    const [curGroup, setCurGroup] = useState(0);
    const [type, setType] = useState(0);
    const [loading, setLoading] = useState(false);
    const [roundSelect, setRoundSelect] = useState(false);
    const [stageSelect, setStageSelect] = useState(false)

    const labelMaps = useTranslateKeysToMaps(['Schedule']);

    const requestCompetitions = useCallback(
      async (stageId?: string | null, roundId?: number | null, groupId?: number | null) => {
        if (competitionId && seasonId) {
          setLoading(true);
          try {
            const res: any = await getCompetitionList({
              competitionId,
              seasonId,
              stageId: (stageId ?? curStageId) || 0,
              roundNum: typeof roundId === 'number' ? roundId : curRound || 0,
              groupNum: typeof groupId === 'number' ? groupId : curGroup || 0,
            });
            setCompetitions(res?.matches?.length ? res.matches : []);
            setLoading(false);
          } catch (e) {
            setLoading(false);
          }
        }
      },
      [competitionId, seasonId, curGroup, curRound, curStageId]
    );

    useEffect(() => {
      if (curRound && stagesList.length > 0) {
        requestCompetitions(curStageId, curRound);
      }
    }, [curRound, stagesList, curStageId, requestCompetitions]);

    const changeRound = useCallback(
      (roundId: number) => {
        const selectedRound = roundList.find((round) => round.id === roundId);
        if (selectedRound && selectedRound.id !== curRound) {
          setCurRound(selectedRound.id);
          setCurGroup(0);
          requestCompetitions(null, selectedRound.id, 0);
        }
      },
      [curRound, roundList, requestCompetitions]
    );

    const changeStage = useCallback(
      (stage: any) => {
        if (stage.id !== curStageId) {
          setCurStageId(stage.id);
          setRoundList(stage?.roundList || []);
          setGroupList(stage?.groupList || []);
          setCurRound(0);
          setCurGroup(0);
          requestCompetitions(stage.id, 0, 0);
        }
      },
      [curStageId, requestCompetitions]
    );

    useEffect(() => {
      if (competitionId && seasonId) {
        const requestSelectors = async () => {
          setLoading(true);
          const res: any = await getCompetitionFilter({ competitionId, seasonId });
          if (res) {
            const { type, stages, curStageId, curRound } = res;
            const stage = stages.find((item: any) => item.id === curStageId);

            requestCompetitions(curStageId, curRound);

            setType(type);
            setStagesList(stages);
            setRoundList(stage?.roundList || []);
            setGroupList(stage?.groupList || []);
            setCurStageId(curStageId);
            setCurRound(curRound);
          } else {
            setLoading(false);
          }
        };
        requestSelectors();
      }
    }, [competitionId, seasonId]);

    const groupMatchesByDate = useCallback((matches: any) => {
      if (!matches || matches.length === 0) return {};
      return matches
        .sort((a, b) => a.matchTime - b.matchTime)
        .reduce((acc, match) => {
          const date = moment.unix(match.matchTime).format('dddd, DD MMMM');
          acc[date] = acc[date] || [];
          acc[date].push(match);
          return acc;
        }, {});
    }, []);

    const groupedMatches = groupMatchesByDate(competitions);

    return (
      <div id="schedulePluginCard" className='competition-schedule-container'>
        <div className='container-title'>
          {labelMaps.Schedule}
          <div className='secondary-container-title'>
            {roundList.length > 0 && (
              <>
                <Button className='competition-round-btn' size="small" onClick={() => setRoundSelect(true)}>
                  <span className='btn-content'>
                    <span className='btn-value'>{curRound === 0 ? "All" : curRound}</span>
                    <DownOutline className='down-icon'/>
                  </span>
                </Button>

                <Picker
                  columns={[roundList.map(round => ({ label: round.text, value: round.id }))]}
                  visible={roundSelect}
                  value={curRound ? [curRound] : []}
                  onClose={() => setRoundSelect(false)}
                  onConfirm={(value) => {
                    const selectedRoundId = value[0]; 
                    const selectedRound = roundList.find(round => round.id === selectedRoundId);
                    if (selectedRound) {
                      changeRound(selectedRound.id); 
                    }
                  }}
                />
              </>
            )}

            {stagesList.length > 1 && (
              <>
                <Button className='primary-competition-round-btn' size="small" onClick={() => setStageSelect(true)}>
                  <span className='btn-content'>
                    <span className='btn-value'>
                      {stagesList.find(stage => stage.id === curStageId)?.name || "All"}
                    </span>
                    <DownOutline className='down-icon'/>
                  </span>
                </Button>

                <Picker
                  columns={[stagesList.map(stage => ({ label: stage.name, value: stage.id }))]}
                  visible={stageSelect}
                  value={curStageId ? [curStageId] : []}
                  onClose={() => setStageSelect(false)}
                  onConfirm={(value) => {
                    const selectedStageId = value[0];
                    const selectedStage = stagesList.find(stage => stage.id === selectedStageId);
                    if (selectedStage) {
                      changeStage(selectedStage); 
                    }
                  }}
                />
              </>
            )}

          </div>
        </div>
        <div className='container-body'>
          <Loading loading={loading} isEmpty={groupedMatches.length === 0}/>
          {Object.entries(groupedMatches).map(([date, matches]) => (
            <div key={date} className="date-group">
              <div className="date-header">{date}</div>
              {matches.map((match: any) => {
                const isLive = InLiveStatusEnum.includes(match.matchStatus);
                return (
                  <Link to={GlobalUtils.getPathname(PageTabs.match, match.id)} key={match.id} className="match-list-container">
                    <div className="match-time-container">
                      {/* <FavoriteIcon isMatch={true} match={match} /> */}
                      <span className="match-time">{moment.unix(match.matchTime).format('HH:mm')}</span>
                    </div>
                    <div className='match-team-container'>
                      <div className='match-team-info'>
                        <img className='team-logo' src={match.homeTeam?.logo || FallbackImage} alt={match.homeTeam?.name} loading="lazy"/>
                        <span className='team-name text-overflow'>{match.homeTeam?.name}</span>
                      </div>
                      <div className='match-team-info'>
                        <img className='team-logo' src={match.awayTeam?.logo || FallbackImage} alt={match.awayTeam?.name} loading="lazy"/>
                        <span className='team-name text-overflow'>{match.awayTeam?.name}</span>
                      </div>
                    </div>
                    {match.matchStatus === StatusCodeEnum.NotStarted ? (
                      <div className='match-score-container'>
                        <span>-</span>
                      </div>
                    ) : (
                      <div className="match-score-container">
                        <span
                          className="match-score"
                          style={{ color: isLive ? '#c1272d' : '#fff' }}
                        >
                          {match?.calculatedHomeScore ?? ''}
                        </span>
                        <span
                          className="match-score"
                          style={{ color: isLive ? '#c1272d' : '#fff' }}
                        >
                          {match?.calculatedAwayScore ?? ''}
                        </span>
                      </div>
                    )}
                  </Link>
                )
              })}
            </div>
          ))}
        </div>
      </div>
    );
  }),
);

export default ScheduleLeft;
