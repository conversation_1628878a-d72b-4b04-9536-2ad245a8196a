import Loading from "@/components/Loading";
import { DownOutline, LeftOutline, RightOutline } from 'antd-mobile-icons'
import { <PERSON><PERSON>, <PERSON>er } from "antd-mobile";
import { getStatsPlayer, getStatsTeam } from "iscommon/api/competition";
import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";
import { FallbackImage, FallbackPlayerImage } from "iscommon/const/icon";
import { translate, useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { inject, observer } from "mobx-react";
import { useEffect, useMemo, useState } from "react";
import { Link } from "umi";
import './StatsTab.less'

const Pagination = ({ pageInfo, onPageChange }: any) => {
  const { pageNum, pageSize, total } = pageInfo;
  const totalPages = Math.ceil(total / pageSize);

  const handlePrev = () => {
    if (pageNum > 1) {
      onPageChange(pageNum - 1);
    }
  };

  const handleNext = () => {
    if (pageNum < totalPages) {
      onPageChange(pageNum + 1);
    }
  };

  return (
    <div className="pagination">
      <Button onClick={handlePrev} disabled={pageNum === 1} size="mini" shape="rounded">
        <LeftOutline />
      </Button>
      <span>{`Page ${pageNum} of ${totalPages}`}</span>
      <Button onClick={handleNext} disabled={pageNum === totalPages} size="mini" shape="rounded">
        <RightOutline />
      </Button>
    </div>
  );
};

const StatsTab = inject('store')(
  observer((props: any) => {
    const {
      store: { Competitions, StatsStore, WebConfig },
    } = props;
    const { currentSportPathname } = WebConfig;
    const { currentTeam, currentCategory, currentKeyType } = StatsStore;
    const {
      competitionId,
      competitionName,
      currentSeason: { id: seasonId, year: seasonText },
    } = Competitions;
    const [playerList, setPlayerList] = useState<any[]>([]);
    const [teamList, setTeamList] = useState<any[]>([]);
    const [pageInfoPlayer, setPageInfoPlayer] = useState({
      pageNum: 1,
      pageSize: 10,
      total: 0,
    });
    const [pageInfoTeam, setPageInfoTeam] = useState({
      pageNum: 1,
      pageSize: 10,
      total: 0,
    });
    const [loading, setLoading] = useState<boolean>(true);
    const [playerStats, setPlayerStats] = useState(false);
    const [teamStats, setTeamStats] = useState(false)

    const labelMaps = useTranslateKeysToMaps([
      'Stats',
      'Matches',
      'Goals',
      'Assists',
      'YellowCards',
      'RedCards',
      'Shots',
      'ShotsOnTarget',
      'Clearances',
      'Tackles',
      'keyPasses',
      'Crosses',
      'CrossesAccuracy',
      'Fouls',
      'WasFouled',
      'Penalty',
      'MinutesPlayed',
      'Dribble',
      'DribbleSucc',
      'Interceptions',
      'Steals',
      'Passes',
      'LongBalls',
      'LongBallsAccuracy',
      'Duels',
      'DuelsWon',
      'Dispossessed',
      'Saves',
      'Punches',
      'RunsOut',
      'RunsOutSucc',
      'GoodHighClaim',
      'BlockedShots',
      'PassesAccuracy',
      'Players',
      'Teams',
    ]);

    const teamItems = useMemo(
      () => [
        { label: labelMaps.Goals, key: 'goals', value: 'goals' },
        { label: labelMaps.Assists, key: 'assists', value: 'assists' },
        { label: labelMaps.Matches, key: 'matches', value: 'matches' },
        { label: labelMaps.RedCards, key: 'redCards', value: 'redCards' },
        { label: labelMaps.YellowCards, key: 'yellowCards', value: 'yellowCards' },
        { label: labelMaps.Shots, key: 'shots', value: 'shots' },
        { label: labelMaps.ShotsOnTarget, key: 'shotOnTargets', value: 'shotOnTargets' },
        { label: labelMaps.Clearances, key: 'clearances', value: 'clearances' },
        { label: labelMaps.Tackles, key: 'tackles', value: 'tackles' },
        { label: labelMaps.keyPasses, key: 'keyPasses', value: 'keyPasses' },
        { label: labelMaps.Crosses, key: 'crosses', value: 'crosses' },
        { label: labelMaps.CrossesAccuracy, key: 'crossesAccuracy', value: 'crossesAccuracy' },
        { label: labelMaps.Fouls, key: 'fouls', value: 'fouls' },
        { label: labelMaps.WasFouled, key: 'wasFouled', value: 'wasFouled' },
        { label: labelMaps.Penalty, key: 'penalty', value: 'penalty' },
      ],
      [labelMaps],
    );
    
    const playerItems = useMemo(
      () => [
        { label: labelMaps.Goals, key: 'goals', value: 'goals' },
        { label: labelMaps.Assists, key: 'assists', value: 'assists' },
        { label: labelMaps.MinutesPlayed, key: 'minutesPlayed', value: 'minutesPlayed' },
        { label: labelMaps.RedCards, key: 'redCards', value: 'redCards' },
        { label: labelMaps.YellowCards, key: 'yellowCards', value: 'yellowCards' },
        { label: labelMaps.Shots, key: 'shots', value: 'shots' },
        { label: labelMaps.ShotsOnTarget, key: 'shotOnTargets', value: 'shotOnTargets' },
        { label: labelMaps.Dribble, key: 'dribble', value: 'dribble' },
        { label: labelMaps.DribbleSucc, key: 'dribbleSucc', value: 'dribbleSucc' },
        { label: labelMaps.Clearances, key: 'clearances', value: 'clearances' },
        { label: labelMaps.Interceptions, key: 'interceptions', value: 'interceptions' },
        { label: labelMaps.Steals, key: 'steals', value: 'steals' },
        { label: labelMaps.Passes, key: 'passes', value: 'passes' },
        { label: labelMaps.keyPasses, key: 'keyPasses', value: 'keyPasses' },
        { label: labelMaps.Crosses, key: 'crosses', value: 'crosses' },
        { label: labelMaps.CrossesAccuracy, key: 'crossesAccuracy', value: 'crossesAccuracy' },
        { label: labelMaps.LongBalls, key: 'longBalls', value: 'longBalls' },
        { label: labelMaps.LongBallsAccuracy, key: 'longBallsAccuracy', value: 'longBallsAccuracy' },
        { label: labelMaps.Duels, key: 'duels', value: 'duels' },
        { label: labelMaps.DuelsWon, key: 'duelsWon', value: 'duelsWon' },
        { label: labelMaps.Fouls, key: 'fouls', value: 'fouls' },
        { label: labelMaps.Dispossessed, key: 'dispossessed', value: 'dispossessed' },
        { label: labelMaps.WasFouled, key: 'wasFouled', value: 'wasFouled' },
        { label: labelMaps.Saves, key: 'saves', value: 'saves' },
        { label: labelMaps.Punches, key: 'punches', value: 'punches' },
        { label: labelMaps.RunsOut, key: 'runsOut', value: 'runsOut' },
        { label: labelMaps.RunsOutSucc, key: 'runsOutSucc', value: 'runsOutSucc' },
        { label: labelMaps.GoodHighClaim, key: 'goodHighClaim', value: 'goodHighClaim' },
        { label: labelMaps.BlockedShots, key: 'blockedShots', value: 'blockedShots' },
        { label: labelMaps.PassesAccuracy, key: 'passesAccuracy', value: 'passesAccuracy' },
      ],
      [labelMaps],
    );

    const [selectedPlayerStat, setSelectedPlayerStat] = useState(playerItems.length > 0 ? playerItems[0].value : '');
    const [selectedTeamStat, setSelectedTeamStat] = useState(teamItems.length > 0 ? teamItems[0].value: '');

    const onPlayerConfirm = (selected: any[]) => {
      const selectedStat = playerItems.find(item => item.value === selected[0]);
      if (selectedStat) {
        setSelectedPlayerStat(selectedStat.value); 
      }
      setPlayerStats(false);
    };

    const onTeamConfirm = (selected: any[]) => {
      const selectedStat = teamItems.find(item => item.value === selected[0]);
      if (selectedStat) {
        setSelectedTeamStat(selectedStat.value); 
      }
      setTeamStats(false);
    };

    // const onPageChange = (newPageNum: number) => {
    //   setPageInfo((prevPageInfo: any) => ({
    //     ...prevPageInfo,
    //     pageNum: newPageNum,
    //   }));
    // };

    useEffect(() => {
      const commonParams = {
        competitionId, seasonId, property: currentCategory,
      };

      getStatsPlayer({
        ...commonParams,
        teamId: currentTeam,
        pageNum: pageInfoPlayer.pageNum - 1,
        pageSize: pageInfoPlayer.pageSize,
      }).then(({ playerStatistics, total }: any) => {
        const processedPlayerStatistics = playerStatistics.map((player: any, index: number) => ({
          playerName: player?.player?.name,
          playerLogo: player?.player?.logo,
          teamLogo: player?.team?.logo,
          teamName: player?.team?.name,
          goals: player?.goals || 0,
          penalty: player?.penalty || 0,
          [selectedPlayerStat]: player[selectedPlayerStat],
          playerId: player?.player?.id,
          teamId: player?.team?.id,
        }));

        const sortedPlayerStatistics = processedPlayerStatistics.sort(
          (a: any, b: any) => b[selectedPlayerStat] - a[selectedPlayerStat]
        );

        setPlayerList(sortedPlayerStatistics);
        setLoading(false)
        setPageInfoPlayer((state: any) => ({ ...state, total }));
      });
    }, [currentCategory, currentKeyType, pageInfoPlayer.pageNum, pageInfoPlayer.pageSize, currentTeam, competitionId, seasonId, selectedPlayerStat, selectedTeamStat])

    useEffect(() => {
      const commonParams = {
        competitionId, seasonId, property: currentCategory,
      };
      getStatsTeam(commonParams).then(({ teamStatistics }: any) => {
        const processedTeamStatistics = teamStatistics.map((team: any, index: number) => ({
          key: index + 1, 
          teamLogo: team?.team?.logo,
          teamName: team?.team?.name,
          goals: team?.goals || 0,
          penalty: team?.penalty || 0,
          [selectedTeamStat]: team[selectedTeamStat], 
          teamId: team?.team?.id
        }));

        const sortedTeamStatistics = processedTeamStatistics.sort(
          (a: any, b: any) => b[selectedTeamStat] - a[selectedTeamStat]
        )
        setPageInfoTeam((state: any) => ({ ...state, total: sortedTeamStatistics.length }));

        const startIndex = (pageInfoTeam.pageNum - 1) * pageInfoTeam.pageSize;
        const endIndex = startIndex + pageInfoTeam.pageSize;
    
        const paginatedTeamStatistics = sortedTeamStatistics.slice(startIndex, endIndex);    

        setTeamList(paginatedTeamStatistics);
        setLoading(false)
      });
    },  [currentCategory, currentKeyType, pageInfoTeam.pageNum, pageInfoTeam.pageSize, currentTeam, competitionId, seasonId, selectedPlayerStat, selectedTeamStat]);

    return (
      <>
        <div className="competition-player-stats-container">
          <div className="container-title">
            {labelMaps.Players}
            <>
              <Button className='player-header-btn' size="small" onClick={() => setPlayerStats(true)}>
                <span className='btn-content'>
                  <span className='btn-value'>{playerItems.find(item => item.value === selectedPlayerStat)?.label || ''}</span>
                  <DownOutline className='down-icon'/>
                </span>
              </Button>
              <Picker
                columns={[playerItems.map(item => ({ label: item.label, value: item.value }))]}
                visible={playerStats}
                onClose={() => setPlayerStats(false)}
                onConfirm={onPlayerConfirm}
              />
            </>
          </div>
          <div className="container-body">
            <div className='player-stat-table'>
              <div className="header-row">
                <div className="header-cell">#</div>
                <div className="header-cell">Players</div>
                <div className="header-cell">{playerItems.find(item => item.value === selectedPlayerStat)?.label || ''}</div>
              </div>
              {playerList.length !== 0 ? (
                playerList.map((item: any, index) => {
                  return (
                    <Link className="table-row" key={item?.playerId} to={GlobalUtils.getPathname(PageTabs.player, item?.playerId)}>
                      <div className="table-cell">
                        <span>{index + 1}</span>
                      </div>
                      <div className="table-cell player-cell">
                        <img className="player-icon" src={item?.playerLogo || FallbackPlayerImage} alt={item?.playerName} loading="lazy"/>
                        <div className="player-info">
                          <span className="player-name">{item?.playerName}</span>
                          <div className="player-team-detail">
                            <img className="team-icon" src={item?.teamLogo || FallbackImage} alt={item?.teamName} loading="lazy"/>
                            <span className="team-name">{item?.teamName}</span>
                          </div>
                        </div>
                      </div>
                      <div className="table-cell">
                        {selectedPlayerStat === 'goals'
                          ? item.penalty > 0
                            ? `${item.goals} (${item.penalty})`
                            : `${item.goals}`
                          : item[selectedPlayerStat] || 0}
                      </div>
                    </Link>
                  )
                })
              ) : (
                <Loading loading={loading}/> 
              )}
            </div>
            <Pagination 
              pageInfo={pageInfoPlayer}   
              onPageChange={(newPageNum: number) => {
                setPageInfoPlayer((prev) => ({ ...prev, pageNum: newPageNum }));
              }}/>
          </div>
        </div>
        <div className="competition-team-stats-container">
          <div className="container-title">
            {labelMaps.Teams}
            <>
              <Button className='team-header-btn' size="small" onClick={() => setTeamStats(true)}>
                <span className='btn-content'>
                  <span className='btn-value'>{teamItems.find(item => item.value === selectedTeamStat)?.label || ''}</span>
                  <DownOutline className='down-icon'/>
                </span>
              </Button>
              <Picker
                columns={[teamItems.map(item => ({ label: item.label, value: item.value }))]}
                visible={teamStats}
                onClose={() => setTeamStats(false)}
                onConfirm={onTeamConfirm}
              />
            </>
          </div>
          <div className="container-body">
            <div className='team-stat-table'>
              <div className="header-row">
                <div className="header-cell">#</div>
                <div className="header-cell">Teams</div>
                <div className="header-cell">{teamItems.find(item => item.value === selectedTeamStat)?.label || ''}</div>
              </div>
              {teamList.length !== 0 ? (
                teamList.map((item: any, index) => {
                  return (
                    <Link className="table-row" key={item?.playerId} to={GlobalUtils.getPathname(PageTabs.team, item?.teamId)}>
                      <div className="table-cell">
                        <span>{index + 1}</span>
                      </div>
                      <div className="table-cell team-cell">
                        <img className="team-icon" src={item?.teamLogo || FallbackImage} alt={item?.teamName} loading="lazy"/>
                        <span className="team-name">{item?.teamName}</span>
                      </div>
                      <div className="table-cell">
                        {selectedTeamStat === 'goals'
                          ? item.penalty > 0
                            ? `${item.goals} (${item.penalty})`
                            : `${item.goals}`
                          : item[selectedTeamStat] || 0}
                      </div>
                    </Link>
                  )
                })
              ) : (
                <Loading loading={loading}/> 
              )}
            </div>
            <Pagination 
              pageInfo={pageInfoTeam} 
              onPageChange={(newPageNum: number) => {
                setPageInfoTeam((prev) => ({ ...prev, pageNum: newPageNum }));
              }} />
          </div>
        </div>
      </>
    );
  }),
);

export default StatsTab;