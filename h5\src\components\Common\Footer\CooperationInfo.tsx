import React from 'react';
import { Link } from 'umi';

import Facebook from 'iscommon/assets/images/facebook.png';
import Instagram from 'iscommon/assets/images/instagram.png';
import Tiktok from 'iscommon/assets/images/tiktok.png';
import Twitter from 'iscommon/assets/images/twitter.png';
import Youtube from 'iscommon/assets/images/youtube.png';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

import './CooperationInfo.less';

const CooperationInfo = React.memo(() => {
  const labelMaps = useTranslateKeysToMaps([
    'ContactUs',
    'UpdateLineups',
    'BusinessCooperation',
    'TermsOfService',
    'PrivacyPolicy',
    'FreeLiveScoresWidget',
  ]);

  return (
    <>
      <div className="clearfix-row text-center mt-24 mb-24">
        <a href="https://twitter.com/igscoreofficial" target="_blank" className="mytransition" rel="noreferrer">
          <img src={Twitter} className="h-24" alt="Twitter" />
        </a>
        <a href="https://www.facebook.com/igscoreglobal/" target="_blank" className="ml-15 mytransition" rel="noreferrer">
          <img src={Facebook} className="h-24" alt="Facebook" />
        </a>
        <a href="https://www.instagram.com/igscore_official/" target="_blank" className="ml-15 mytransition" rel="noreferrer">
          <img src={Instagram} className="h-24" alt="Instagram" />
        </a>
        <a href="https://www.tiktok.com/@igscore_official" target="_blank" className="ml-15 mytransition" rel="noreferrer">
          <img src={Tiktok} className="h-24" alt="Tiktok" />
        </a>
        <a href="https://www.youtube.com/@igscoreofficial" target="_blank" className="ml-15 mytransition" rel="noreferrer">
          <img src={Youtube} className="h-24" alt="Youtube" />
        </a>
      </div>

      <div className="clearfix-row text-center fs-12 flex">
        <a href="mailto:<EMAIL>" target="_blank" className="text-item" rel="noreferrer">
          {labelMaps.ContactUs}
        </a>
        <Link to="/policy/" target="_blank" rel="noreferrer" className="text-item">
          {labelMaps.TermsOfService}
        </Link>
        <Link to="/policy/" target="_blank" rel="noreferrer" className="text-item">
          {labelMaps.PrivacyPolicy}
        </Link>
      </div>

      <div className="clearfix-row text-center fs-12 mb-16 mt-10 color-w">
        <span className="text-item">Copyright @ 2023 IGSCORE</span>{' '}
      </div>
    </>
  );
});

export default CooperationInfo;
