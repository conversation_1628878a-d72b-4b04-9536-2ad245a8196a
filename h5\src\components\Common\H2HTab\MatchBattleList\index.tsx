import './index.less'

import { Checkbox, Collapse } from 'antd-mobile';
import { FallbackImage, FallbackPlayerImage } from 'iscommon/const/icon';
import GlobalUtils, { GlobalConfig, GlobalSportPathname, PageTabs } from 'iscommon/const/globalConfig';
import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { calculateScore } from 'iscommon/utils/dataUtils';
import { Link } from 'umi';
import Loading from '@/components/Loading';
import MatchListItem from '../MatchListItem';
import moment from 'moment';
import styles from './index.less';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

interface Props {
  titleLabel: string;
  competition: Record<string, any>;
  team: Record<string, any>;
  teamId: string;
  type: 'home' | 'away';
  teamName?: string;
  request: (params: { size: number; teamId: string }) => Promise<any>;
}

const {Panel} = Collapse;

const MatchBattleList: React.FC<Props> = (props) => {
  const { titleLabel, competition, team, teamName, request, type = 'home', teamId } = props;
  const labelMaps = useTranslateKeysToMaps(['CurrentLeague', 'Home', 'Score', 'Away', 'Last', 'Win', 'Draw', 'Lose', 'WinProb']);

  const [list, setList] = useState<any[]>([]);
  const [filterList, setFilterList] = useState<any[]>([]);
  const [same, setSame] = useState<boolean>(false);
  const [sameCompetition, setSameCompetition] = useState<boolean>(false);
  const [size, setSize] = useState<number>(5);
  const [loading, setLoading] = useState(false);

  // const stats = useMemo(() => {
  //   const stat = filterList.reduce(
  //     (st, { calculatedHomeScore, calculatedAwayScore, homeTeam, homeScores, awayScores }) => {

  //       console.log('calculatedHomeScore:', calculatedHomeScore);
  //       console.log('calculatedAwayScore:', calculatedAwayScore);
  //       console.log('homeScores:', homeScores);
  //       console.log('awayScores:', awayScores);
  //       const aScore = GlobalConfig.pathname === GlobalSportPathname.football ? calculateScore(homeScores) : calculatedHomeScore;
  //       const bScore = GlobalConfig.pathname === GlobalSportPathname.football ? calculateScore(awayScores) : calculatedAwayScore;
  //       const homeScore = homeTeam?.id === team.id ? aScore : bScore;
  //       const awayScore = homeTeam?.id === team.id ? bScore : aScore;

  //       console.log('homeScore:', homeScore, 'awayScore:', awayScore);
  //       if (homeScore > awayScore) {
  //         st.win += 1;
  //       } else if (homeScore === awayScore) {
  //         st.draw += 1;
  //       } else {
  //         st.lose += 1;
  //       }
  //       return st;
  //     },
  //     { win: 0, draw: 0, lose: 0 },
  //   );
  //   const prob = (((stat.win || 0) / (filterList.length || 1)) * 100).toFixed(2);
  //   return { ...stat, prob };
  // }, [filterList, team.id]);

  // const calculateStats = (matches: any, teamId: string) => {
  //   const stats = {
  //     win: 0,
  //     draw: 0,
  //     lose: 0
  //   };
  
  //   matches.forEach((match: any) => {
  //     if (teamId === match.awayTeam.id) {
  //       if (match.awayScores[0] > match.homeScores[0]) {
  //         stats.win += 1;
  //       } else if (match.awayScores[0] === match.homeScores[0]) {
  //         stats.draw += 1;
  //       } else {
  //         stats.lose += 1;
  //       }
  //     } else if (teamId === match.homeTeam.id) {
  //       if (match.homeScores[0] > match.awayScores[0]) {
  //         stats.win += 1;
  //       } else if (match.homeScores[0] === match.awayScores[0]) {
  //         stats.draw += 1;
  //       } else {
  //         stats.lose += 1;
  //       }
  //     }
  //   });
  
  //   return stats;
  // };

  const showMore = useCallback(() => {
    const nextSize = size + 10;
    setSize(nextSize > list.length ? list.length : nextSize);
  }, [list, size]);

  useEffect(() => {
    const filters = list.slice(0, size).filter((item: any) => {
      let show = true;
      if (sameCompetition && item.competition.id !== competition.id) {
        show = false;
      }
      if (same && ((type === 'home' && item.homeTeam.id !== team.id) || (type === 'away' && item.awayTeam.id !== team.id))) {
        show = false;
      }
      return show;
    });
    setFilterList(filters);
  }, [list, same, sameCompetition, type, competition, team, size]);

  useEffect(() => {
    if (team?.id) {
      setLoading(true);
      request({ size: 30, teamId: team.id })
        .then((res) => {
          if (res?.matches?.length) {
            setList(res.matches);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [request, team.id]);

  const sortedData = Object.entries(
    filterList.reduce((acc, match) => {
      const compName = match.competition.name;

      if (!acc[compName]) {
        acc[compName] = [];
      }

      acc[compName].push(match);
      return acc;
    }, {})
  );

  // const stats = calculateStats(sortedData.flatMap(([_, matches]) => matches), team.id);
  const stats = useMemo(() => {
    const stat = filterList.reduce(
      (st, { calculatedHomeScore, calculatedAwayScore, homeTeam, homeScores, awayScores }) => {
        const aScore = GlobalConfig.pathname === GlobalSportPathname.football ? calculateScore(homeScores) : calculatedHomeScore;
        const bScore = GlobalConfig.pathname === GlobalSportPathname.football ? calculateScore(awayScores) : calculatedAwayScore;
        const homeScore = homeTeam?.id === teamId ? aScore : bScore;
        const awayScore = homeTeam?.id === teamId ? bScore : aScore;
        if (homeScore > awayScore) {
          st.win += 1;
        } else if (homeScore === awayScore) {
          st.draw += 1;
        } else {
          st.lose += 1;
        }
        return st;
      },
      { win: 0, draw: 0, lose: 0 },
    );
    const prob = (((stat.win || 0) / (filterList.length || 1)) * 100).toFixed(2);
    return { ...stat, prob };
  }, [filterList, teamId]);

  return (
    <div className='match-battle-container'>
      <Loading loading={loading}>
      <div className='container-title'>{teamName ? teamName : titleLabel}</div>
        <div className='container-body'>
          <div className="badge-container">
            <div className="badge-item">
              <div className="custom-badge" style={{ backgroundColor: '#52af2f' }}>
                {stats?.win || 0}
              </div>
              <div className="label">Win</div>
            </div>
            <div className="badge-item">
              <div className="custom-badge" style={{ backgroundColor: '#ffba5a' }}>
                {stats?.draw || 0}
              </div>
              <div className="label">Draw</div>
            </div>
            <div className="badge-item">
              <div className="custom-badge" style={{ backgroundColor: '#e74c5b' }}>
                {stats?.lose || 0}
              </div>
              <div className="label">Lose</div>
            </div>
          </div>
          <Collapse defaultActiveKey={sortedData.map(([competitionName]) => competitionName)} className="match-collapse">
            {sortedData.map(([competitionName, matches]) => {
              const competitionLogo = matches[0].competition.logo; // Assuming logo is the same for all matches in the same competition
              return (
                <Panel 
                  key={competitionName}
                  title={
                    <div className="panel-header">
                      <img src={competitionLogo} alt={competitionName} className="competition-logo" />
                      <span className='competition-name'>{competitionName}</span>
                    </div>
                  }
                >
                  {matches.map((match: any) => {
                    console.log('matches123', matches)
                    return (
                    <div key={match.id} className="match-details">
                      <div className="match-time">
                        <span className='match-time-text'>{moment.unix(match.matchTime).format('DD/MM/YY')}</span>
                      </div>
                      <div className='match-info'>
                        <div className='team-score-row'>
                          <Link className='team-info' to={GlobalUtils.getPathname(PageTabs.team, match.homeTeam.id)}>
                            <img className='team-icon' src={match?.homeTeam?.logo || FallbackPlayerImage} alt={match?.homeTeam?.name}/>
                            <span className='team-name'>{match?.homeTeam?.name}</span>
                          </Link>
                          <span className='score-text'>{match?.calculatedHomeScore ?? (Array.isArray(match?.homeScores) && match.homeScores.length > 0 ? match.homeScores[0] : '-')}</span>
                        </div>
                        <div className='team-score-row'>
                          <Link className='team-info' to={GlobalUtils.getPathname(PageTabs.team, match.homeTeam.id)}>
                            <img className='team-icon' src={match?.awayTeam?.logo || FallbackPlayerImage} alt={match?.awayTeam?.name}/>
                            <span className='team-name'>{match?.awayTeam?.name}</span>
                          </Link>
                          <span className='score-text'>{match?.calculatedAwayScore ?? (Array.isArray(match?.awayScores) && match.awayScores.length > 0 ? match.awayScores[0] : '-')}</span>
                        </div>
                      </div>
                      {/* <div className="match-info">
                        <div className=''></div>
                        <Link className="team-row" to={GlobalUtils.getPathname(PageTabs.team, match.homeTeam.id)}>
                          <img src={match.homeTeam.logo || FallbackImage} alt={match.homeTeam.name} className="team-logo" />
                          <span className='team-name'>{match.homeTeam.name}</span>
                        </Link>
                        <Link className="team-row" to={GlobalUtils.getPathname(PageTabs.team, match.awayTeam.id)}>
                          <img src={match.awayTeam.logo || FallbackImage} alt={match.awayTeam.name} className="team-logo" />
                          <span className='team-name'>{match.awayTeam.name}</span>
                        </Link>
                      </div>
                      <div className="scores">
                        <span className='score-text'>{match?.calculatedHomeScore ?? (Array.isArray(match?.homeScores) && match.homeScores.length > 0 ? match.homeScores[0] : '-')}</span>
                        <span className='score-text'>{match?.calculatedAwayScore ?? (Array.isArray(match?.awayScores) && match.awayScores.length > 0 ? match.awayScores[0] : '-')}</span>
                      </div> */}
                    </div>
                  )})}
                </Panel>
              );
            })}
          </Collapse>
        </div>
      </Loading>
    </div>

      //  <div className={styles.header}>
      //   <div className={styles.title}>{titleLabel}</div>
      //   <div className={styles.selection}>
      //     <Checkbox
      //       onChange={(checked) => setSame(checked)}
      //       style={{ marginRight: 4, '--icon-size': '14px', '--font-size': '10px', '--gap': '4px' }}
      //     >
      //       {team.name} - {type === 'home' ? labelMaps.Home : labelMaps.Away}
      //     </Checkbox>
      //     <Checkbox
      //       onChange={(checked) => setSameCompetition(checked)}
      //       style={{ '--icon-size': '14px', '--font-size': '10px', '--gap': '4px' }}
      //     >
      //       {labelMaps.CurrentLeague}
      //     </Checkbox>
      //   </div>
      // </div>
      // <div className={styles.teamInfo}>
      //   <div className={styles.team}>
      //     <img className={styles.logo} src={team.logo} alt="" />
      //     <div className={styles.teamName}>{team.name}</div>
      //   </div>
      // </div>
      // <div className={styles.stats}>
      //   {labelMaps.Last} {filterList.length}, {teamName} {labelMaps.Win}
      //   <span className={styles.win}>{stats.win}</span>, {labelMaps.Draw} <span className={styles.draw}>{stats.draw}</span>,{' '}
      //   {labelMaps.Lose} <span className={styles.lose}>{stats.lose}</span>, {labelMaps.WinProb}{' '}
      //   <span className={styles.normal}>: {stats?.prob || '0.00'}%</span>
      // </div>
      // <div className={styles.list}>
      //   {filterList.map((item) => {
      //     return <MatchListItem key={item.id} item={item} currentTeamId={team.id} />;
      //   })}
      //   {!filterList?.length && <div className={styles.noData}>no data</div>}
      //   {list.length > 0 && size < list.length && (
      //     <div className={styles.showMore} onClick={showMore}>
      //       <img
      //         className={styles.showMoreIcon}
      //         src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAADqUlEQVR4Xu2bvWsUQRjGn3e9QKzEwkYtbLRQbBRBkt3ZOwvTCSnMpbUQKxv/Af0LtLdKHSEoKmIOOW/mEiV+4DciIiIiIiKKaBDdfWXuLpCP249LduaimWt3Znae37zz7nuzzxI2+I82uH44AFkRMCPl6Rg4QswzEfAkDMPprD79vK6U2uoxn4yA3QBmhBATafNJjQAlJS/r/II8b8z3/ef9FJl0b6XUccTxBRDtWGhDwIQvxImkPokAWoMxT67oyPxy08DA2NDQ0NP1BCFxvnqSUVQJKpXb3eabCKDRaJzziM4miHwForEgCB6vBwip4gEwMCqEuNwrgKMe0c0kgQy8BlAVQjzsJ4Qs8QC+/pyf3zkyMvKjJwC6sZRyjoBDKQLf/ImiaqVSud8PCDnEA8wXgzA81XMO6ADYS0STYN6XMsDbuB0JczYh5BR/KQjDsVU/BfJCAPCOPK/q+/5dGxCKEq/nmqsQklJmRgKA9zFzNQzDWZMQihSfG0APkfABRNUgCJomIBQtvicAurFSag+AqbScAOCjx1wdDkNZJAQT4nsGoDvU6/VdpVLpWgaET1Ecj5fL5XoREEyJXxUA3anZbG5n5ukMCJ8ZGBdC3FoLBJPiVw2gkxO2EVE99RFJ9AVRNO6Xy7XVQDAtfk0AdOdarbZlcHBwJg0CM3/TiVEIkVhVdoNjQ/yaAegBZmdnN0dRdC9jO3zv1Ak38kSCLfGFAOgkxlKpVHqUEQk/NhFVh4W4ngbBpvjCACwIUko9y4iE+U6dcLWfYb/43rkqwTxhmxcCM/8CkX46LPl7anvlF+ZbOIBOwZQVCb+JedwPw6lO++6HL4vJM2f+selloYwCyAkh0nUCEVHXkycL4gvPActXIEdO0GeO6VFoaOWNR0DenJAatobFG4+ANUGwIN4agFZOkPIJgP25EpUl8VYBdCA8AHCg32FvtA7IWmHVaNwB0eGu7SyuvLUk2E1oU8oGA2LJtT6It74FFgtWSp0B8ygz7/KIzvtCXMiKHhPXjVSCJiZqakwHwBTZf2XczAhw/oClS+n8AXD+gFZI/P/+gGazeYzj+EpSMtsQ/gAlpX7vfzAlozt/AAHOH+D8Ae094vwBAJw/wPkD2tvB+QMAOH8AOX+A9ik6f4DOCc4fwMzOHwDA+QOcP6BdJzh/gP6Ow/kDWh9zOH9A8nmLhddlmcfiRZzv53CKrLyNBfFW3w06f0DbJOH8Ac4foO32zh/Q+vpkXfgD/gIJ24NuVmiuBwAAAABJRU5ErkJggg=="
      //         alt=""
      //       />
      //     </div>
      //   )}
      // </div> 
  );
};

export default MatchBattleList;
