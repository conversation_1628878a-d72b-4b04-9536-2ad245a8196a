import { TennisInLiveStatusEnum } from 'iscommon/const/tennis/constant';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { getTennisSectionScoreList } from 'iscommon/utils/dataUtils';
import { inject, observer } from 'mobx-react';
import styles from './ScoreTable.less';

const ScoreTable = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Smallball: {
          SmallballMatch: { matchHeaderInfo },
        },
      },
    } = props;
    const labelMaps = useTranslateKeysToMaps(['Total']);
    const { scores, matchStatus, servingSide, homeTeam, awayTeam } = matchHeaderInfo;
    const list = getTennisSectionScoreList(matchStatus, scores, true, TennisInLiveStatusEnum);
    const hasPt = TennisInLiveStatusEnum.includes(matchStatus) && scores?.pt?.length > 0;

    const renderScore = (type = 1) => {
      const isHome = type === 1;
      const teamInfo = isHome ? homeTeam : awayTeam;
      const isServingSide = servingSide == type;
      return (
        <div className={styles.tableContent}>
          <div className={styles.teamNameBox}>
            <span className={styles.teamTip} style={{ background: isHome ? '#2196f3' : '#ffae0f' }}></span>
            <span className={styles.teamName}>{teamInfo?.name}</span>
            {isServingSide && (
              <svg aria-hidden="true" className={`icon fs-12 wangqiu svgPostop ${styles.wangqiu}`}>
                <use xlinkHref="#iconiconwangqiu"></use>
              </svg>
            )}
          </div>
          {list.map((item: any, index) => {
            return (
              <div
                key={index}
                className={`${styles.flex1} ${item.isRed && 'color-ff4747'} ${
                  item.compareStatus !== type && !item.isRed && styles.insideLoserColor
                }`}
              >
                {isHome ? item.h : item.w}
                <span className={styles.hxScore}>{isHome ? item.hx : item.wx}</span>
              </div>
            );
          })}
          {hasPt && <div className={`${styles.flex1} w`}>{scores?.pt?.[type - 1]}</div>}
        </div>
      );
    };
    return (
      <div className={styles.scoreTableContainer}>
        <div className={styles.tableTitle}>
          <div className={styles.teamNameBox} />
          {list.map((i, index) => {
            return (
              <div key={index} className={styles.flex1}>
                S{index + 1}
              </div>
            );
          })}
          {hasPt && <div className={styles.flex1}></div>}
        </div>
        {renderScore(1)}
        {renderScore(2)}
      </div>
    );
  }),
);

export default ScoreTable;
