
export const HandballStatusCodeEnum = {
  Abnormal: 0,
  NotStarted: 1,
  End: 100,
  POSTPONED: 14,
  DELAYED: 15,
  CANCELED: 16,
  INTERRUPTED: 17,
  CutInHalf: 19,
  TBD: 99
};


export const HandballMatchStatusCodeToText = {
  0: "-",
  1: "-",
  35: "H1",
  5: "HT",
  36: "H2",
  100: "FT",
  6: "OT",
  7: "OT",
  11: "OT",
  12: "OT",
  105: "OT",
  8: "Penalty",
  13: "Penalty",
  110: "Penalty",
  14: "POSTPONED",
  15: "DELAYED",
  16: "CANCELED",
  17: "INTERRUPTED",
  19: "Cut in half",
  99: "TBD"
};

// 2-9
export const HandballInLiveStatusEnum = [
  35, 5, 36, 6, 7, 11, 12, 105,
  8, 13, 110,
]

// 显示具体time
export const HandballInLiveWithTimeEnum = [
  30, 31, 43, 10, 13
]

// 显示半场比分
export const HandballShowHalfScoreWithTimeEnum = [
  5,36,100,6,11,7,12,105,8,13,110
]

export const TableTennisStatsKeys = {
}
