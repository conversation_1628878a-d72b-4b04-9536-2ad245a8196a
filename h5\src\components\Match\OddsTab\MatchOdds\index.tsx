import downIcon from 'iscommon/assets/images/odds-down.jpg';
import upIcon from 'iscommon/assets/images/odds-up.jpg';
import { oddsCompanyNameMaps } from 'iscommon/const/constant';
import { translate } from 'iscommon/i18n/utils';
import React, { useState } from 'react';
import styles from './index.less';
import OddsPopup from './OddsPopup';
// import { getMatchStandings } from 'iscommon/api/match';

// import './index.less'

const MatchOddsTab = ({ awayName, homeName, currentList, onChangeType, type }: any) => {
  const [visible, setVisible] = useState(false);
  const [popupData, setPopupData] = useState({});

  const radioItem = [
    {
      label: translate('1X2'),
      value: 'eu',
    },
    {
      label: translate('h5Handicap'),
      value: 'asia',
    },
    {
      label: translate('O/U'),
      value: 'bs',
    },
  ];

  const tableHeaderList: any = {
    eu: ['1', 'X', '2'],
    asia: [homeName, translate('h5Handicap'), awayName],
    bs: [translate('h5Over'), translate('Goals'), translate('h5Under')],
  };

  const openPopup = (info: any) => {
    if (info) {
      let newInfo = JSON.parse(JSON.stringify(info));
      // if (newInfo.odds) {
      //   newInfo.odds.push(newInfo.odds[0]);
      //   newInfo.odds.splice(0, 2);
      // }
      setPopupData(newInfo);
      setVisible(true);
    }
  };

  console.log(currentList);

  console.log('tableheaderlist123', tableHeaderList)

  return (
    // <div className='match-odds-container'>
    //   <div className='match-odds-radio-container'>
    //     {radioItem.map((item: any) => (
    //       <span
    //         className={`radio-btn ${type === item.value ? 'active' : ''}`}
    //         key={item.value}
    //         onClick={() => { onChangeType(item.value); }}
    //       >
    //         {item.label}
    //       </span>
    //     ))}
    //   </div>
    //   <div className='container-body'>
    //     <div className='custom-odd-table'>
    //       <div className="table-header">
    //         <div className="table-header-empty" />
    //         {tableHeaderList[type].map((item: any, index: number) => (
    //           <div className="table-header-item" key={index}>
    //             {item}
    //           </div>
    //         ))}
    //       </div>
    //       <div className='table-body'>

    //       </div>
    //     </div>
    //   </div>
    // </div>

    <div className={styles.container}>
      <div className={styles.radio_container}>
        {radioItem.map((item: any) => (
          <span
            className={`${styles.radio_btn} ${type === item.value ? styles.active : ''}`}
            key={item.value}
            onClick={() => {
              onChangeType(item.value);
            }}
          >
            {item.label}
          </span>
        ))}
      </div>
      <div className={styles.table_container}>
        <div className={styles.table_header_box}>
          <span className={styles.seat_box} />
          <div className={styles.common_center_box}>
            {tableHeaderList[type].map((item: any) => (
              <span className={`${styles.common_center_item_title} text-overflow`} key={item}>
                {item}
              </span>
            ))}
          </div>
          <span className={styles.active_seat_box} />
        </div>
        {currentList.map((item: any) => (
          <div className={styles.content_container} key={item.companyId}>
            <div className={styles.content_item_box}>
              <div className={styles.content_company_item}>
                <img src={oddsCompanyNameMaps[item.companyId]} alt="" style={{ width: 83, height: 25 }} />
              </div>
              <div className={styles.common_center_box} style={{ background: '#ffffff', color: 'black' }}>
                <span className={styles.common_center_item}>
                  <span
                    style={{
                      color:
                        item.currentCompanyOdds?.odds[1]?.contrastList &&
                        item.currentCompanyOdds?.odds[1]?.contrastList[0] === 'up'
                          ? 'green'
                          : item.currentCompanyOdds?.odds[1]?.contrastList &&
                            item.currentCompanyOdds?.odds[1]?.contrastList[0] === 'down'
                          ? 'red'
                          : '',
                    }}
                  >
                    {Number(item.currentCompanyOdds?.odds[1]?.oddsData[0])?.toFixed(2)}
                  </span>
                  {item.currentCompanyOdds?.odds[1]?.contrastList &&
                  item.currentCompanyOdds?.odds[1]?.contrastList[0] === 'up' ? (
                    <img className={styles.stats_img} src={upIcon} alt=""></img>
                  ) : item.currentCompanyOdds?.odds[1]?.contrastList &&
                    item.currentCompanyOdds?.odds[1]?.contrastList[0] === 'down' ? (
                    <img className={styles.stats_img} src={downIcon} alt=""></img>
                  ) : null}
                </span>
                <span className={styles.common_center_item}>
                  <span
                    style={{
                      color:
                        item.currentCompanyOdds?.odds[1]?.contrastList &&
                        item.currentCompanyOdds?.odds[1]?.contrastList[1] === 'up'
                          ? 'green'
                          : item.currentCompanyOdds?.odds[1]?.contrastList &&
                            item.currentCompanyOdds?.odds[1]?.contrastList[1] === 'down'
                          ? 'red'
                          : '',
                    }}
                  >
                    {Number(item.currentCompanyOdds?.odds[1]?.oddsData[1])?.toFixed(2)}
                  </span>
                  {item.currentCompanyOdds?.odds[1]?.contrastList &&
                  item.currentCompanyOdds?.odds[1]?.contrastList[1] === 'up' ? (
                    <img className={styles.stats_img} src={upIcon} alt=""></img>
                  ) : item.currentCompanyOdds?.odds[1]?.contrastList &&
                    item.currentCompanyOdds?.odds[1]?.contrastList[1] === 'down' ? (
                    <img className={styles.stats_img} src={downIcon} alt=""></img>
                  ) : null}
                </span>
                <span className={styles.common_center_item}>
                  <span
                    style={{
                      color:
                        item.currentCompanyOdds?.odds[1]?.contrastList &&
                        item.currentCompanyOdds?.odds[1]?.contrastList[2] === 'up'
                          ? 'green'
                          : item.currentCompanyOdds?.odds[1]?.contrastList &&
                            item.currentCompanyOdds?.odds[1]?.contrastList[2] === 'down'
                          ? 'red'
                          : '',
                    }}
                  >
                    {Number(item.currentCompanyOdds?.odds[1]?.oddsData[2])?.toFixed(2)}
                  </span>
                  {item.currentCompanyOdds?.odds[1]?.contrastList &&
                  item.currentCompanyOdds?.odds[1]?.contrastList[2] === 'up' ? (
                    <img className={styles.stats_img} src={upIcon} alt=""></img>
                  ) : item.currentCompanyOdds?.odds[1]?.contrastList &&
                    item.currentCompanyOdds?.odds[1]?.contrastList[2] === 'down' ? (
                    <img className={styles.stats_img} src={downIcon} alt=""></img>
                  ) : null}
                </span>
              </div>
              <div
                className={styles.active_seat_box}
                // style={{ background: '#f7f9fb' }}
                onClick={() => openPopup(item.currentCompanyOdds)}
              >
                <i className="icon iconfont iconjiantou" style={{ fontSize: 13, color: 'rgb(153, 153, 153)' }}></i>
              </div>
            </div>
          </div>
        ))}
      </div>
      <OddsPopup visible={visible} closePopup={() => setVisible(false)} data={popupData} headerInfo={tableHeaderList[type]} />
    </div>
  );
};

export default React.memo(MatchOddsTab);
