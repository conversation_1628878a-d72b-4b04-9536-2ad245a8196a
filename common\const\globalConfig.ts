// @ts-nocheck
import { history } from 'umi';
import moment from 'moment';
import { getStore, saveStore } from '../store';
import { H5_ICON_FONT, ICON_FONT } from './icon';
import { PlatformEnum } from './constant';
import { TeamTab, TeamTabH5 } from '../mobx/modules/team';
import { CompetitionsTab, CompetitionsTabH5 } from '../mobx/modules/competition';
import { PlayerTab, PlayerTabH5 } from '../mobx/modules/player';
import { MatchTab, MatchTabH5 } from '../mobx/modules/match';

export const langStoreKey = 'langStoreKey';
export const TIME_ZONE_KEY = 'TIME_ZONE';

export const GlobalSportPathname = {
  football: 'football',
  basketball: 'basketball',
  tennis: 'tennis',
  cricket: 'cricket',
  baseball: 'baseball',
  amfootball: 'amfootball',
  icehockey: 'icehockey',
  volleyball: 'volleyball',
  esports: 'esports',
  handball: 'handball',
  waterpolo: 'waterpolo',
  tabletennis: 'tabletennis',
  snooker: 'snooker',
  badminton: 'badminton',
};

export const GlobalSportPathnameSpellingStr = {
  football: 'FOOTBALL',
  basketball: 'BASKETBALL',
  tennis: 'TENNIS',
  cricket: 'CRICKET',
  baseball: 'BASEBALL',
  amfootball: 'A.M FOOTBALL',
  icehockey: 'HOCKEY',
  volleyball: 'VOLLEYBALL',
  esports: 'ESPORTS',
  handball: 'HANDBALL',
  waterpolo: 'WATERPOLO',
  tabletennis: 'TABLE TENNIS',
  snooker: 'SNOOKER',
  badminton: 'BADMINTON',
};

export const GlobalSportTypeEnum = {
  Football: 'Football',
  Basketball: 'Basketball',
  Amfootball: 'AmFootball',
  Baseball: 'Baseball',
  Icehockey: 'Icehockey',
  Tennis: 'Tennis',
  Volleyball: 'Volleyball',
  Esports: 'Esports',
  Handball: 'Handball',
  Cricket: 'Cricket',
  WaterPolo: 'WaterPolo',
  TableTennis: 'TableTennis',
  Snooker: 'Snooker',
  Badminton: 'Badminton',
};

const isValidSportPath = (path = '') => Object.values(GlobalSportPathname).find((value) => value === path);

const getPathname = (path = '') => {
  if (path || path !== '/') {
    let pathname = path.startsWith('/') ? path.slice(1) : path;
    pathname = pathname.endsWith('/') ? pathname.slice(0, -1) : pathname;
    pathname = pathname.includes('-') ? pathname.split('-')[0] : pathname;
    pathname = pathname === 'livescorewidget' ? 'football' : pathname;
    if (isValidSportPath(pathname)) {
      return pathname;
    }
  }
  return GlobalSportPathname.football;
};

const getInitialLang = () => {
  if (history.location && history.location.query && history.location.query.lang) {
    return history.location.query.lang;
  }
  return getStore(langStoreKey) || 'en';
};

const offsetMinutes = moment().utcOffset();
const offsetHours = Math.floor(offsetMinutes / 60);
const remainingMinutes = offsetMinutes % 60;
const utcTime = `${offsetHours >= 10 ? offsetHours : `0${offsetHours}`}:${remainingMinutes === 0 ? '00' : remainingMinutes}`;
const localTimezone = `${offsetMinutes >= 0 ? '+' : '-'}${utcTime}`;

export const GlobalConfig = {
  lng: getInitialLang(),
  platform: PlatformEnum.web,
  timezone: getStore(TIME_ZONE_KEY) || localTimezone,
  odd: 'EU', // EU (1.50) / HK (0.50) / US (-200)
  loginStatus: false,
  pathname: getPathname(window.location.pathname),
  profile: '',
  serverTime: Date.now() / 1000,
  goalAudio: {
    favorite: false,
    sound: true,
    popup: true,
  },
};

export const MenuBarList = [
  {
    name: 'Football',
    sportType: GlobalSportTypeEnum.Football,
    pathname: GlobalSportPathname.football,
    chatRoomId: 0,
  },
  {
    name: 'Basketball',
    sportType: GlobalSportTypeEnum.Basketball,
    pathname: GlobalSportPathname.basketball,
    chatRoomId: 1,
  },
  {
    name: 'Badminton',
    sportType: GlobalSportTypeEnum.Badminton,
    pathname: GlobalSportPathname.badminton,
    chatRoomId: 6,
  },
  {
    name: 'Tennis',
    sportType: GlobalSportTypeEnum.Tennis,
    pathname: GlobalSportPathname.tennis,
    chatRoomId: 2,
  },
  {
    name: 'Cricket',
    sportType: GlobalSportTypeEnum.Cricket,
    pathname: GlobalSportPathname.cricket,
    chatRoomId: 11,
  },
  {
    name: 'Baseball',
    sportType: GlobalSportTypeEnum.Baseball,
    pathname: GlobalSportPathname.baseball,
    chatRoomId: 4,
  },
  {
    name: 'Esports',
    sportType: GlobalSportTypeEnum.Esports,
    pathname: GlobalSportPathname.esports,
    chatRoomId: 20,
  },
  {
    name: 'Volleyball',
    sportType: GlobalSportTypeEnum.Volleyball,
    pathname: GlobalSportPathname.volleyball,
    chatRoomId: 8,
  },
  {
    name: 'Hockey',
    sportType: GlobalSportTypeEnum.Icehockey,
    pathname: GlobalSportPathname.icehockey,
    chatRoomId: 7,
  },
  {
    name: 'Handball',
    sportType: GlobalSportTypeEnum.Handball,
    pathname: GlobalSportPathname.handball,
    chatRoomId: 9,
  },
  {
    name: 'AM.Football',
    sportType: GlobalSportTypeEnum.Amfootball,
    pathname: GlobalSportPathname.amfootball,
    chatRoomId: 5,
  },
  {
    name: 'Snooker',
    sportType: GlobalSportTypeEnum.Snooker,
    pathname: GlobalSportPathname.snooker,
    chatRoomId: 10,
  },
  {
    name: 'WaterPolo',
    sportType: GlobalSportTypeEnum.WaterPolo,
    pathname: GlobalSportPathname.waterpolo,
    chatRoomId: 12,
  },
  {
    name: 'Table Tennis',
    sportType: GlobalSportTypeEnum.TableTennis,
    pathname: GlobalSportPathname.tabletennis,
    chatRoomId: 3,
  },
];

export const PageTabs = {
  competition: 'competition',
  team: 'team',
  player: 'player',
  match: 'match',
};

const PageTabMaps = {
  [PageTabs.competition]: {
    tabs: GlobalConfig.platform === PlatformEnum.web ? CompetitionsTab : CompetitionsTabH5,
  },
  [PageTabs.team]: {
    tabs: GlobalConfig.platform === PlatformEnum.web ? TeamTab : TeamTabH5,
  },
  [PageTabs.player]: {
    tabs: GlobalConfig.platform === PlatformEnum.web ? PlayerTab : PlayerTabH5,
  },
  [PageTabs.match]: {
    tabs: GlobalConfig.platform === PlatformEnum.web ? MatchTab : MatchTabH5,
  },
};

export default class GlobalUtils {
  static reloadPage() {
    window.location.reload();
  }

  static setPlatform(platform = GlobalConfig.platform) {
    GlobalConfig.platform = platform;
  }

  static setPathName(path = '') {
    GlobalConfig.pathname = getPathname(path);
  }

  static setGlobalLng(lng = GlobalConfig.lng) {
    GlobalConfig.lng = lng;
    saveStore(langStoreKey, lng);
    GlobalUtils.reloadPage();
  }

  static setTimezone(tz = GlobalConfig.timezone) {
    saveStore(TIME_ZONE_KEY, tz);
    GlobalConfig.timezone = tz;
    GlobalUtils.reloadPage();
  }

  static setOddFormat(odd = GlobalConfig.odd) {
    GlobalConfig.odd = odd;
  }

  static setGoalAudio({ type, checked }) {
    GlobalConfig.goalAudio[type] = checked;
  }

  static getMenuBarList() {
    const isWeb = GlobalConfig.platform === PlatformEnum.web;
    const iconfontMaps = isWeb ? ICON_FONT : H5_ICON_FONT;
    if (isWeb) {
      const index = MenuBarList.findIndex((item) => item.pathname === GlobalConfig.pathname);
      if (index > 6) {
        const next = MenuBarList[6];
        MenuBarList.splice(6, 1, MenuBarList[index]);
        MenuBarList.splice(index, 1, next);
      }
    }
    return MenuBarList.map((item) => ({
      ...item,
      iconfont: iconfontMaps[item.sportType],
    }));
  }

  static setServerTime(serverTime) {
    GlobalConfig.serverTime = serverTime;
  }

  static getCurrentMenuData() {
    const item = MenuBarList.find((item) => item.pathname === GlobalConfig.pathname);
    return item || MenuBarList[0];
  }

  static getPathname(page = PageTabs.competition, id = '', customTab = '') {
    const { tabs } = PageTabMaps[page];
    const defaultTab = page === PageTabs.match && GlobalConfig.platform === PlatformEnum.web ? tabs.live : tabs.overview;
    return `/${GlobalConfig.pathname}-${page}/${id}/${customTab || defaultTab}/`;
  }

  /**
   *
   * @param page
   * @param id
   * @param config {string | object} string: customTab, object: {tabName, tabId}
   */
  static goToPage(page = PageTabs.competition, id = '', config = null) {
    const _config = config && typeof config === 'object' ? config : { customTab: config };
    const { customTab, target = 'location' } = _config;
    if (target === 'history') {
      history.push(GlobalUtils.getPathname(page, id, customTab));
    } else {
      location.href = GlobalUtils.getPathname(page, id, customTab);
    }
  }

  static isPlugin() {
    return location.search.indexOf('isplugin') > -1;
  }
}
