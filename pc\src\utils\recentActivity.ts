// Recent Activity Storage Utility
// Manages user's recent click activity using localStorage

export interface RecentActivityItem {
  id: string;
  type: 'team' | 'match' | 'competition' | 'player' | 'league';
  name: string;
  logo?: string;
  url?: string;
  timestamp: number;
  sport?: string;
  metadata?: {
    competitionName?: string;
    teamNames?: string[];
    matchTime?: number;
    [key: string]: any;
  };
}

const STORAGE_KEY = 'user_recent_activity';
const MAX_ITEMS = 10;

/**
 * Get all recent activity items from localStorage
 */
export const getRecentActivity = (): RecentActivityItem[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (!stored) return [];
    
    const items: RecentActivityItem[] = JSON.parse(stored);
    
    // Sort by timestamp (most recent first)
    return items.sort((a, b) => b.timestamp - a.timestamp);
  } catch (error) {
    console.error('Error reading recent activity from localStorage:', error);
    return [];
  }
};

/**
 * Add a new item to recent activity
 * Automatically removes duplicates and maintains max 10 items
 */
export const addRecentActivity = (item: Omit<RecentActivityItem, 'timestamp'>): void => {
  try {
    const currentItems = getRecentActivity();
    
    // Create new item with timestamp
    const newItem: RecentActivityItem = {
      ...item,
      timestamp: Date.now()
    };
    
    // Remove any existing item with same id and type
    const filteredItems = currentItems.filter(
      existing => !(existing.id === item.id && existing.type === item.type)
    );
    
    // Add new item at the beginning
    const updatedItems = [newItem, ...filteredItems];
    
    // Keep only the most recent MAX_ITEMS
    const limitedItems = updatedItems.slice(0, MAX_ITEMS);
    
    // Save to localStorage
    localStorage.setItem(STORAGE_KEY, JSON.stringify(limitedItems));
    
    console.log(`📝 Added to recent activity: ${item.type} - ${item.name}`);
  } catch (error) {
    console.error('Error saving recent activity to localStorage:', error);
  }
};

/**
 * Clear all recent activity
 */
export const clearRecentActivity = (): void => {
  try {
    localStorage.removeItem(STORAGE_KEY);
    console.log('🗑️ Cleared all recent activity');
  } catch (error) {
    console.error('Error clearing recent activity:', error);
  }
};

/**
 * Remove a specific item from recent activity
 */
export const removeRecentActivity = (id: string, type: string): void => {
  try {
    const currentItems = getRecentActivity();
    const filteredItems = currentItems.filter(
      item => !(item.id === id && item.type === type)
    );
    
    localStorage.setItem(STORAGE_KEY, JSON.stringify(filteredItems));
    console.log(`🗑️ Removed from recent activity: ${type} - ${id}`);
  } catch (error) {
    console.error('Error removing recent activity item:', error);
  }
};

/**
 * Helper functions for specific item types
 */

// Add team to recent activity
export const addTeamActivity = (team: {
  id: string;
  name: string;
  logo?: string;
  sport?: string;
  competitionName?: string;
}) => {
  addRecentActivity({
    id: team.id,
    type: 'team',
    name: team.name,
    logo: team.logo,
    sport: team.sport,
    metadata: {
      competitionName: team.competitionName
    }
  });
};

// Add match to recent activity
export const addMatchActivity = (match: {
  id: string;
  homeTeam: { name: string; logo?: string };
  awayTeam: { name: string; logo?: string };
  competitionName?: string;
  matchTime?: number;
  sport?: string;
}) => {
  addRecentActivity({
    id: match.id,
    type: 'match',
    name: `${match.homeTeam.name} vs ${match.awayTeam.name}`,
    sport: match.sport,
    metadata: {
      competitionName: match.competitionName,
      teamNames: [match.homeTeam.name, match.awayTeam.name],
      matchTime: match.matchTime
    }
  });
};

// Add competition to recent activity
export const addCompetitionActivity = (competition: {
  id: string;
  name: string;
  logo?: string;
  sport?: string;
}) => {
  addRecentActivity({
    id: competition.id,
    type: 'competition',
    name: competition.name,
    logo: competition.logo,
    sport: competition.sport
  });
};

// Add league to recent activity
export const addLeagueActivity = (league: {
  id: string;
  name: string;
  logo?: string;
  sport?: string;
}) => {
  addRecentActivity({
    id: league.id,
    type: 'league',
    name: league.name,
    logo: league.logo,
    sport: league.sport
  });
};

/**
 * Get recent activity filtered by type
 */
export const getRecentActivityByType = (type: RecentActivityItem['type']): RecentActivityItem[] => {
  return getRecentActivity().filter(item => item.type === type);
};

/**
 * Get recent activity filtered by sport
 */
export const getRecentActivityBySport = (sport: string): RecentActivityItem[] => {
  return getRecentActivity().filter(item => item.sport === sport);
};

/**
 * Check if localStorage is available
 */
export const isLocalStorageAvailable = (): boolean => {
  try {
    const test = '__localStorage_test__';
    localStorage.setItem(test, test);
    localStorage.removeItem(test);
    return true;
  } catch {
    return false;
  }
};
