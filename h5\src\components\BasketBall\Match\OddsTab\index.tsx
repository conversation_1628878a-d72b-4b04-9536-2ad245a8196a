import MatchOddsTab from '@/components/Common/MatchOdds';
import Loading from '@/components/Loading';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { formatMatchDetailOdds } from 'iscommon/utils/oddData';
import { inject, observer } from 'mobx-react';
import React, { useEffect, useState } from 'react';
// import { getMatchStandings } from 'iscommon/api/match';

const OddsTab = inject('store')(
  observer((props) => {
    const [type, setType] = useState('eu');
    const {
      store: {
        Basketball: { Match },
      },
    } = props;
    const { matchHeaderInfo = {}, matchOdds } = Match;
    const { homeTeam: { name: homeName = '' } = {}, awayTeam: { name: awayName = '' } = {} } = matchHeaderInfo || {};
    const [currentList, setCurrentList] = useState([]);

    useEffect(() => {
      if (matchOdds.length) {
        const list = formatMatchDetailOdds(matchOdds, true);
        let currentList = [];
        list.forEach((item: any) => {
          let companyOddsInfo = {};
          companyOddsInfo.companyId = item.companyId;
          companyOddsInfo.currentCompanyOdds = item.companyOdds.filter((val: any) => val.oddsType === type)[0];
          currentList.push(companyOddsInfo || {});
        });
        setCurrentList(currentList);
      }
    }, [matchOdds, type]);

    const onChangeType = (type) => {
      setType(type);
    };

    const labelMaps = useTranslateKeysToMaps(['ToWin', 'Spread', 'TotalPoints', 'h5Handicap', 'h5Over', 'Goals', 'h5Under']);

    const radioItem = [
      {
        label: labelMaps.ToWin,
        value: 'eu',
      },
      {
        label: labelMaps.Spread,
        value: 'asia',
      },
      {
        label: labelMaps.TotalPoints,
        value: 'bs',
      },
    ];

    const tableHeaderList = {
      eu: {
        label: ['1', '2'],
        oddsValueIndexList: [0, 2]
      },
      asia: {
        label: [homeName, labelMaps.h5Handicap, awayName],
        oddsValueIndexList: [0, 1, 2]
      },
      bs: {
        label: [labelMaps.h5Over, labelMaps.Goals, labelMaps.h5Under],
        oddsValueIndexList: [0, 1, 2],
      },
    };

    console.log('inside odds tab')
    // if (loading) return <UmiLoading />;
    return (
      <Loading loading={false} isEmpty={currentList.length === 0}>
        <MatchOddsTab
          radioItem={radioItem}
          tableHeaderList={tableHeaderList}
          type={type}
          onChangeType={onChangeType}
          currentList={currentList}
          homeName={homeName}
          awayName={awayName}
        />
      </Loading>
    );
  }),
);

export default React.memo(OddsTab);
