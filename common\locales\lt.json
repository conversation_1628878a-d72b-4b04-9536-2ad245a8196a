{"All": "<PERSON><PERSON><PERSON>", "Live": "TIESIOGINĖS", "LiveH5": "TIESIOGINĖS", "MatchLive": "TIESIOGINĖS", "TimeSort": "Rušiuoti pagal laik<PERSON>", "SortByTime": "Rušiuoti pagal laik<PERSON>", "AllGames": "VISOS RUNG", "Leagues": "LYGOS", "h5_Leagues": "LYGOS", "Today": "Šiandien", "Cancel": "<PERSON><PERSON><PERSON><PERSON>", "Popular": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Settings": "Nustatymai", "Language": "Kalba", "Overview": "Apžvalga", "LiveOverview": "Apžvalga", "Standings": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Stats": "Statistika", "Transfer": "<PERSON><PERSON>", "Champions": "Čempionai", "TeamChampions": "Čempionai", "teamChampions": "Čempionai", "Football": "FUTBOLAS", "Basketball": "KREPŠINIS", "Baseball": "Beisbolas", "Icehockey": "<PERSON><PERSON> ritulys", "Tennis": "TENISAS", "Volleyball": "TINKLINIS", "Esports": "KIBERSPORTAS", "Handball": "RANKINIS", "Cricket": "KRIKETAS", "WaterPolo": "VANDENSVYDIS", "TableTennis": "Stalo tenisa<PERSON>", "Snooker": "SNUKERIS", "Badminton": "BADMINTONAS", "BusinessCooperation": "Business Cooperation", "TermsOfService": "Paslaugų sąlygomis", "PrivacyPolicy": "Privatumo politika", "Players": "ŽAIDĖJAI", "ForeignPlayers": "<PERSON>ž<PERSON><PERSON>", "NumberOfTeams": "<PERSON><PERSON><PERSON>", "YellowCards": "<PERSON><PERSON><PERSON><PERSON>", "RedCards": "<PERSON><PERSON><PERSON>", "Capacity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "City": "Miestas", "Info": "Informacija", "Matches": "RUNGTYNĖS", "Team": "<PERSON><PERSON><PERSON>", "Teams": "<PERSON><PERSON><PERSON>", "Goals": "Įvarčiai", "Assists": "Per<PERSON><PERSON><PERSON><PERSON>", "assists": "Per<PERSON><PERSON><PERSON><PERSON>", "Home": "<PERSON><PERSON> run<PERSON>", "Away": "<PERSON><PERSON><PERSON><PERSON>", "topScorers": "Pelnantys daugiausia taškų", "TopScorers": "Pelnantys daugiausia taškų", "homeTopScorers": "Pelnantys daugiausia taškų", "season": "Sezonas", "Season": "Sezonas", "ShotsOnTarget": "Smūgiai į vartus", "Clearances": "Smūgiai nuo vartų", "Tackles": "<PERSON><PERSON><PERSON>", "keyPasses": "<PERSON><PERSON><PERSON><PERSON><PERSON> perda<PERSON>i", "KeyPasses": "<PERSON><PERSON><PERSON><PERSON><PERSON> perda<PERSON>i", "Fouls": "Pražang<PERSON>", "totalFouls": "Pražang<PERSON>", "WasFouled": "Turėjo pražangų", "Penalty": "<PERSON><PERSON><PERSON><PERSON>", "MinutesPlayed": "Minučių žaista", "BasketballMinutesPlayed": "Minučių žaista", "Interceptions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Steals": "<PERSON><PERSON><PERSON>", "steals": "<PERSON><PERSON><PERSON>", "Passes": "Per<PERSON><PERSON><PERSON><PERSON>", "Saves": "<PERSON><PERSON><PERSON><PERSON>", "BlockedShots": "Blokuoti smūgiai", "Signed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "league": "", "offensiveData": "Gynybos duomenys", "defenseData": "Gynybos duomenys", "otherData": "Kiti duomenys", "ballPossession": "<PERSON><PERSON><PERSON>", "shotsPerGame": "Smūgiai per rungtynes", "ShotsPerGame": "Smūgiai per rungtynes", "keyPassesPerGame": "Svarbiausi perdavimai per rungtynes", "accurateLongBallsPerGame": "<PERSON>ik<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON>i iš toli per rungtynes", "accurateCrossesPerGame": "Tik<PERSON><PERSON>ū<PERSON> s<PERSON>i smūgiai per rungtynes", "tacklesPerGame": "<PERSON><PERSON><PERSON> ka<PERSON> per rungtynes", "TacklesPerGame": "<PERSON><PERSON><PERSON> ka<PERSON> per rungtynes", "interceptionsPerGame": "<PERSON><PERSON><PERSON><PERSON><PERSON> per rungtynes", "InterceptionsPerGame": "<PERSON><PERSON><PERSON><PERSON><PERSON> per rungtynes", "clearancesPerGame": "Smūgiai nuo vartų per rungtynes", "ClearancesPerGame": "Smūgiai nuo vartų per rungtynes", "blockedShotsPerGame": "Blokuoti smūgiai per rungtynes", "turnoversPerGame": "<PERSON><PERSON><PERSON> praradimai per rungtynes", "foulsPerGame": "Pražang<PERSON> per rungtynes", "scoringFrequencyFiveGoals": "", "Coach": "<PERSON><PERSON><PERSON>", "Goalkeeper": "Vartininkas", "Stadium": "Stadionas", "Login": "<PERSON><PERSON><PERSON><PERSON>", "Corner": "Kampas", "ShotsOffTarget": "Smūgiai už vartų", "H2H": "H2H", "Date": "Data", "OwnGoal": "Įvartis į savo vartus", "PenaltyMissed": "Nepelnytas įvartis", "SecondYellow": "<PERSON><PERSON> geltona kort<PERSON>", "Odds": "Šansai", "attacks": "<PERSON><PERSON><PERSON><PERSON>", "Started": "<PERSON><PERSON><PERSON><PERSON>", "Chat": "Pokalbis", "Strengths": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Weaknesses": "<PERSON><PERSON><PERSON><PERSON>", "Group": "Grupė", "Birthday": "<PERSON><PERSON><PERSON>na", "Club": "Klubas", "MainPosition": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "PassesAccuracy": "", "Preseason": "", "RegularSeason": "", "PointsPerGame": "Taš<PERSON> per rungtynes", "Glossary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "h5Glossary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Career": "<PERSON><PERSON><PERSON><PERSON>", "Bench": "Atsarginių žaidė<PERSON>", "ReboundsPerGame": "Atkovoti kamuoliai per rungtynes", "AssistsPerGame": "Perdavimai per rungtynes", "OddsFormat": "Lažybų formatas", "Squad": "<PERSON><PERSON><PERSON><PERSON>", "TotalMarketValue": "Bendra rink<PERSON> vertė", "Rounds": "<PERSON><PERSON><PERSON>", "LowerDivision": "Žemutinis divizionas", "TeamStats": "<PERSON>mand<PERSON> statistika", "GoalsPk": "Įvarčiai(PK)", "Crosses": "Skers<PERSON>ma<PERSON>", "CrossesAccuracy": "Perduoti kamuolį sėk<PERSON>ai", "Dribble": "Nepapras<PERSON>", "DribbleSucc": "<PERSON><PERSON><PERSON><PERSON>", "LongBalls": "Smūgiai iš toli", "LongBallsAccuracy": "Ilgas artimųjų sėkmės rod<PERSON>", "Duels": "Pražang<PERSON>", "DuelsWon": "<PERSON><PERSON><PERSON>", "Dispossessed": "<PERSON><PERSON><PERSON><PERSON>", "Punches": "Vartininkas ataka sėkmė", "RunsOut": "<PERSON><PERSON><PERSON><PERSON> kamu<PERSON> gaud<PERSON>", "RunsOutSucc": "Blokuot<PERSON> metimai", "GoodHighClaim": "Perdavimai tik<PERSON>lumas", "Loan": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EndOfLoan": "Skolinimosi pabaiga", "Unknown": "<PERSON><PERSON><PERSON><PERSON>", "AverageAge": "<PERSON><PERSON><PERSON><PERSON>", "cornersPerGame": "Kampiniai smūgiai per rungtynes", "goalsConceded": "Užskaityti įvarčiai", "Defender": "G<PERSON>ėjas", "Discipline": "Disciplina", "Pass": "", "FB_Login": "<PERSON>ęsti su <PERSON>", "Google_Login": "Tęsti su <PERSON>", "Substitutes": "<PERSON><PERSON><PERSON><PERSON>", "PenaltyKick": "", "ShareYourViews": "Pasidalinkite savo nuomonę", "Nodata": "Nėra informacijos", "Foot": "Pėda", "dangerousAttack": "", "venue": "Vieta", "playerStatistics": "Žaidėjo statistika", "TotalPlayed": "<PERSON><PERSON> viso žaista rungtynių", "MinutesPerGame": "<PERSON><PERSON><PERSON><PERSON> per rungtynes", "GoalsFrequency": "", "GoalsPerGame": "<PERSON><PERSON>", "Arrivals": "Atvykimai", "Departures": "Išvykimai", "LeftFoot": "Kair<PERSON>", "RightFoot": "<PERSON><PERSON><PERSON><PERSON>", "LatestTransfers": "Paskutiniai sand<PERSON>i", "DraftInfo": "Atrankų informacija", "OK": "G<PERSON><PERSON>", "AsianHandicap": "", "TotalGoals": "", "AmFootballTotalGames": "", "TotalCorners": "", "OverBall": "per", "Over": "per", "h5Over": "per", "UnderBall": "<PERSON><PERSON>", "Under": "<PERSON><PERSON>", "h5Under": "<PERSON><PERSON>", "OtherLeagues": "Kitos lygos [A-Z]", "GoalPopup": "", "FullStandings": "Piln<PERSON>", "teamWeek": "Sa<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "weekTop": "Sa<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "TeamOfTheWeek": "Sa<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "round": "<PERSON><PERSON><PERSON>", "Released": "Išleistas", "Retirement": "", "Draft": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TransferIn": "", "TransferOut": "", "MarketValue": "<PERSON><PERSON><PERSON> ve<PERSON>", "Salary": "Atlyginimas", "Next": "Kitas", "Position": "<PERSON><PERSON><PERSON><PERSON>", "CTR": "<PERSON><PERSON><PERSON><PERSON> įrašas", "FoulBall": "", "FreeKick": "", "GoalKick": "", "Midfield": "", "HalftimeScore": "", "InjuryTime": "", "OvertimeIsOver": "", "PenaltyKickEnded": "", "Last": "<PERSON><PERSON><PERSON><PERSON>", "Win": "Lai<PERSON>ė<PERSON><PERSON>", "Draw": "Atrankos burtų keliu", "Lose": "Pralaim<PERSON>i", "Lineup": "", "Substitution": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>", "Offsides": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Playoffs": "", "Qualifier": "", "GroupStage": "", "Rebounds": "Atkovoti kamuoliai", "rebounds": "Atkovoti kamuoliai", "OffensiveRebounds": "Atkovoti kamuoliai puolime", "offensiveRebounds": "Atkovoti kamuoliai puolime", "DefensiveRebounds": "Atkovoti kamuoliai gynyboje", "defensiveRebounds": "Atkovoti kamuoliai gynyboje", "Turnovers": "<PERSON><PERSON><PERSON>", "turnovers": "<PERSON><PERSON><PERSON>", "Blocks": "Blokai", "blocks": "Blokai", "BoxScore": "Rezultatas švieslentėje", "Foul": "Pražanga", "FreeThrows": "<PERSON><PERSON><PERSON>", "freeThrowsScored": "<PERSON><PERSON><PERSON>", "fieldGoalsScored": "", "fieldGoalsTotal": "", "threePointersScored": "", "threePointersTotal": "", "freeThrowsTotal": "", "Finished": "", "Scheduled": "", "Favourite": "", "OddsMarkets": "", "Search": "", "Timezone": "", "SoccerGoalReminderSettings": "", "RemindersOnlyForRacesToWatch": "", "GoalSound": "", "LiveScore": "", "Schedule": "<PERSON><PERSON><PERSON>", "Rugby": "", "FooterContentFootball": "„IGScore Football LiveScore“ pateikia neprilygstamus futbolo rezultatus ir futbolo rezultatus iš daugiau nei 2600 ir daugiau futbolo lygų, taurių ir turnyrų. Gaukite tiesioginius rezultatus, futbolo vidurio ir visą laik<PERSON> rezultatus, įvarčių autorius ir padėj<PERSON>ju<PERSON>, kort<PERSON><PERSON>, keitimus, rungtynių statistiką ir tiesioginę transliaciją iš „Premier“ lygos, „La Liga“, „Serie A“, „Bundesliga“, „Ligue 1“, „Eredivisie“, Rusijos „Premier League“, Brasileirão, MLS „Super Lig“ ir čempionatas igscore.net. „IGScore“ siūlo visiems futbolo sirgaliams tiesioginius rezultatus, futbolo rezultatus, futbolo rezultatus, lygų lenteles ir lygų, taurių ir turnyrų varžybas, ir ne tik iš populiariausių futbolo lygų, tokių kaip Anglijos „Premier“ lyga, Ispanijos „La Liga“, Italijos „Serie A“, Vokietija. „Bundesliga“, Prancūzijos „Ligue 1“, bet taip pat iš daugelio futbolo šalių visame pasaulyje, įskaitant Šiaurės ir Pietų Amerikos, Azijos ir Afrikos. Mūsų futbolo rezultatų suvestinės realiuoju laiku atnaujinamos realiuoju laiku, kad galėtumėte nuolat atnaujinti visus šiandien vykstančius futbolo rungtynių rezultatų atnaujinimus, taip pat visų futbolo ir futbolo lygų futbolo rezultatų rezultatus. Rungtynių puslapyje mūsų futbolo rezultatų kortelės leidžia jums peržiūrėti visų futbolo varžybų ankstesnių žaidimų rezultatus. Gaukite visus tiesioginius futbolo rezultatus igscore.net!", "FooterContentBasketball": "IGScore Basketball LiveScore“ pateikia NBA lygos gyvus rezultatus, rezultatus, lenteles, statistiką, varž<PERSON>bų kalendorių, lentelę ir ankstesnius rezultatus pagal ketvirčius, kėlinį arba galutinį rezultatą. „IGScore“ siūlo taškų tarnybą iš daugiau nei 200 krepšinio varžybų iš viso pasaulio (kaip NCAA, ABA lyga, Baltijos lyga, Eurolyga, nacionalinės krepšinio lygos). Čia rasite ne tik gyvus rezultatus, ket<PERSON><PERSON><PERSON> rezultatus, galutinius rezultatus ir sudėtį, bet taip pat 2 ir 3 taš<PERSON><PERSON> bandymus, lais<PERSON><PERSON> metimus, <PERSON><PERSON><PERSON><PERSON> pro<PERSON>, atkov<PERSON><PERSON> kamuolius, apyvartą, vagystes, asmenines pražangas, rungtynių istoriją ir žaidėjų statistiką. .„IGScore“ krepšinio gyvų žaidimų metu jūs galite žiūrėti krepšinį internete tiesiog spustelėję jį ir pateiksite aukščiausių lygų rungtynių stebėjimą internete. Rungtynių puslapyje taip pat bus lentelė su visa krepšinio statistika apie naujausias komandų rungtynes. mūsų krepšinio rezultatų suvestinės atnaujinamos realiu laiku, kad galėtumėte nuolat žinoti apie visus šiandien vykstančius krepšinio rezultatus ir leisti peržiūrėti ankstesnių rungtynių varžybų rezultatus kiekvienose krepšinio varžybose. Gaukite visus savo tiesioginius NBA rezultatus svetainėje igscore.net! Stebėkite NBA žaidimų apžvalgas, NBA rungtynes, NBA varžybų lentelę ir komandos puslapius!", "FooterContentAmFootball": "IGScore american football live score suteikia jums visus rezultatus ir gyvus rezultatus iš did<PERSON>ių ir populiariausių amerikiečių futbolo lygos pasaulyje - NFL ir kai baigtas reguliarus NFL sezonas, vadovaukitės gy<PERSON>is NFL atkūrimo balais ir \"Superbowl\". Be NFL mes taip pat suteiksime Ju<PERSON> su \"NCAA College American\" futbolo ir Kanados CFL \"Liveskores, rezultatais, standomis ir tvar<PERSON>.", "FooterContentBaseball": "IGScore baseball live score pateikia jums populiariausios pasaulio beisbolo lygos - USSSA beisbolo, NCAA beisbolo, MLB beisbolo, MLB „Allstar“ žaidimo rezultatus, rezultatus ir turnyrines lenteles. Mes taip pat pateikiame tiesioginius Japonijos profesionalų lygos, Meksikos lygos, Vokietijos 1. Bundeslygos, NCAA ir tarptautinio beisbolo turnyro „World Baseball Classic“ rezultatus. Be to, bet kuriuo metu galite pamatyti beisbolo lygos turnyrines lenteles, pra<PERSON><PERSON><PERSON><PERSON> rungtynes pagal rezultatus pagal padavimus ir būsimų beisbolo rungtynių tvarkaraštį IGScore baseball live score.", "FooterContentIcehockey": "IGScore ice hockey live score pateikia jums ledo ritulio lygos, taurių ir turnyrų rezultatų rezultatus realiuoju laiku. . trečdaliai ir galutiniai ledo ritulio rezultatai tiesiogiai. Pasibaigus ledo ritulio regulia<PERSON>, mes jums siū<PERSON>me ties<PERSON> ledo ritulio rezultatus, turnyrin<PERSON><PERSON> lentelės ir geriausių ledo ritulio renginių- IIHF pasaulio čempionato Stenlio taurės- rezultatus ir ledo ritulio rezultatus iš žiemos olimpinio turnyro. Taip pat IGScore ice hockey live score galite rasti nemokamą tiesioginį NHL, SHL ir kitų ledo ritulio srautą.", "FooterContentTennis": "IGScore tennis live score pateikia jums rezultatus, re<PERSON><PERSON><PERSON>, ATP reitingus ir WTA reitingus, susitikimus ir statistiką iš visų didžiausių teniso turnyrų, pvz., „<PERSON>“ ir „Fed Cup“, pran<PERSON><PERSON><PERSON><PERSON> atvirojo teniso, arb<PERSON> visų „Grand Slam“ turnyrų - atvirojo Australijos teniso, atvirojo JAV teniso, <PERSON><PERSON>arros<PERSON> ir „Wimbledon“ tiek moterys, tiek vyrai vienišiems ir dvejetams. Taip pat bet kuriam tenisininkui galite išsamiai pamatyti jo individualiai sužaistas rungtynes ir jų rezultatus pagal rinkinį ir kuriame turnyre tos rungtynės buvo ž<PERSON>ž<PERSON>. „IGScore tennis live score“ pateikia rezultatus, statist<PERSON><PERSON>, tiesioginius rezultatus ir tiesioginį srautą tarp dviejų žaidėj<PERSON>, žaidžiančių rungtynes.", "FooterContentVolleyball": "IGScore volleyball live score siūlo informaciją apie visas svarbias vyrų ir moterų nacionalines tinklinio lygas, įskaitant Italijos „Serie A1“ ir Italijos „Seria A1 Women“, Rusijos „Superliga“, „Polish PlusLiga“, „Turkey 1. Lig“ ir daugelį kitų. Be nacionalinių tinklinio lygų, mes taip pat teikiame jums informaciją apie svarbiausius tinklinio tarptautinius turnyrus, tokius kaip FIVB pasaulio čempionatas ir Europos čempionatas, taip pat tinklinio rezultatus olimpinėse žaidynėse. Taip pat galite patikrinti senus mėgstamos tinklinio komandos rezultatus, per<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> bū<PERSON><PERSON> tinklinio tvarkaraščius ir patikrinti lygos tinklinio turnyrinę lentelę IGScore volleyball live score.", "FooterContentEsports": "\"Esports\" gyvų balų aptarnavimas IGScore \"Live Bare\" siūlo \"Esports\" gyvu<PERSON> balus, t<PERSON><PERSON><PERSON><PERSON>, rezultatus ir lenteles. Sekite savo mėgstamas komandas čia gyventi! ESPORTS LIVE BALE ON igscore.net Gyvasis balas automatiškai atnaujinamas ir jums nereikia atnaujinti jį rankiniu būdu. Pridė<PERSON><PERSON>, kuriuos norite sekti \"Mano žaidimai\", vadovaudamiesi savo rungtynių, rezultatų ir statistikos bus dar paprastes<PERSON>.", "FooterContentHandball": "„IGScore handball live score“ pateikia tiesioginius rankinio rezultatus ir tiesioginius rezultatus iš populiariausių rankinio lygų, tokių kaip Vokietijos Bundeslyga, Ispanijos „Liga Asobal“, Danijos vyrų „Handboldligaen“ ir „France D1“. Mes taip pat pateikiame rezultatus gyvai, rezultatus, statistiką, turnyrines lenteles, lenteles ir susitikimus iš svarbių taurių, tokių kaip Europos rankinio Čempionų lyga, SEHA lyga ir EHF rankinio taurė. IGScore handball live score galite rasti rezultatų ir nemokamų tiesioginių srautų, skirtų tarptautiniams komandų rankinio turnyrams, pvz., Europos čempionatui ir pasaulio čempionatui, tiek moterims, tiek vyrams. Bet kuriuo metu galite patikrinti rankinio rezultatus ir paskutinių 10 žaidimų, kuriuos žaidė jūsų komanda, statistiką, taip pat galvą tarp komandų, kurios planuoja žaisti su statistika.", "FooterContentCricket": "IGScore cricket live score suteikia galimybę stebėti kriketo rezultatus realiuoju laiku, kriketo įskaitą ir kriketo varžybas. Visa tai galima rasti populiariausiose lygose ir taurėse: Indijos „Premier“ lygoje, Čempionų lygoje „Twenty20“, „Big Bash League“, Karibų jūros aukščiausioje lygoje, „Friends Life T20“ ir ICC pasaulio kriketo taurėje. Visi „IGScore“ kriketo rezultatai automatiškai atnaujinami ir nereikia jų atnaujinti rankiniu būdu. Su visa tai yra galimybė žiūrėti nemokamą kriketo tiesioginę transliaciją ir patikrinti naujausius šansus dėl įdomiausių kriketo rungtynių visame pasaulyje galutinio rezultato.", "FooterContentWaterPolo": "„IGScore water polo live score“ pateikia tiesioginius vandensvydžio rezultatus ir rezultatus iš Italijos Serie A1, Vengrijos OB1, Čempionų ir Adrijos lygos klubų lygiu, o tarptautiniu mastu IGScore water polo live score teikia svar<PERSON><PERSON><PERSON> turnyrus, to<PERSON><PERSON> kaip <PERSON> van<PERSON>džio čempionatas ir Europos vandensvydžio čempionatas . Mes teikiame jums tikslinius rezultatus ir nemokamą tiesioginį srautą.", "FooterContentTableTennis": "„IGScore table tennis live score“ patei<PERSON>a rezultatus, stalus, rezulta<PERSON>, stalo teniso reitingą, susitikimus ir statistiką iš visų didžiausių stalo teniso turnyrų, pvz., Rusijos stalo teniso, stalo teniso olimpinių žaidynių. Taip pat bet kuriam stalo tenisininkui galite išsamiai pamatyti jo individualiai sužaistas rungtynes ir jų rezultatus pagal rinkinį ir kuriame turnyre tos rungtynės buvo žaidžiam<PERSON>. „IGScore table tennis live score“ pateikia rezultatus, statistinius duomenis, tiesioginius rezultatus ir tiesioginį dviejų žaidėjų žaidimą.", "FooterContentSnooker": "IGScore snooker live score suteikia galimybę sekti visų snukerio turnyrų rezultatus, rezultatus ir turnyrines lenteles. Mes taip pat pateikiame rezultatus Jungtinėje Karalystėje ir pasaulio čempionate, taip pat snukerio rezultatus, snukerio varžybas ir galutinius snukerio rezultatus iš tarptautinių turnyrų, tokių kaip „World Snooker tour“. Bet kuriuo metu galite pamatyti numatyto snukerio turnyrų tvarkaraštį, praėjusių snukerio turnyrų rezultatus ir paskutinius 10 žaidėjų kiekvienam žaidėjui. Be to, galite patikrinti žaidėjų tarpusavio rungtynes. IGScore snooker live score rasite rungtynių, kuriose yra nemokamas tiesioginis „snukerio“ srautas, sąrašą.", "FooterContentBadminton": "„IGScore badminton live score“ pat<PERSON><PERSON><PERSON> ties<PERSON> badmintono rezultatus, turnyr<PERSON> lenteles, susitikimus ir statistiką iš tarptautinių turnyrų, pvz., Pasaulio čempionatų, „BWF Super Series“ ir badmintono rezultatų iš olimpinių žaidynių. Taip pat galite patikrinti badmintono žaidimų rezultatus, pamatyti badmintono žaidimo tvarkaraštį ir patikrinti žaidėjų badmintono rezultatus atskirai IGScore badminton live score.", "ContactUs": "Susisiekite su mumis", "UpdateLineups": "Update Lineups", "FreeLiveScoresWidget": "Free Livescores Widget", "PlayerSalary": "", "DivisionLevel": "Įvykio lygis", "Foreigners": "Užsienio <PERSON> skaičių", "LeagueInfo": "Turnyra<PERSON>", "TeamInfo": "Komandos informacija", "Venues": "", "MostValuablePlayer": "", "UpperDivision": "Aukštutinis divizionas", "NewcomersFromUpperDivision": "", "NewcomersFromLowerDivision": "", "TitleHolder": "<PERSON>it<PERSON>", "MostTitle": "<PERSON><PERSON> s<PERSON>", "Pts": "PTS", "FullStats": "", "Relegation": "", "Result": "Resultatas", "Score": "Rezultatas", "PlayerStats": "", "fixtures": "", "topPlayers": "Pagrindiniai žaidėjai", "Shots": "<PERSON><PERSON><PERSON><PERSON>", "SelectTeam": "", "SelectBasketBallTeam": "", "SelectAll": "<PERSON><PERSON><PERSON><PERSON> visus", "SquadSize": "Dalyvių", "ViewAll": "Žiūrė<PERSON> viską", "penaltiesWon": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "accuracyCrosses": "", "totalShorts": "", "accuracyLongBalls": "", "blockShots": "", "MatchesToday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Strikers": "Per<PERSON>ų<PERSON><PERSON>", "Midfielders": "Saugas", "Year": "", "Loans": "", "TopValuablePlayers": "", "total": "", "Total": "", "Results": "", "Prev": "", "Apps": "Pasirody<PERSON><PERSON>", "ShotsPg": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON>ka", "Possession": "", "TotalAndAve": "", "Suspended": "Sužeistas arba nušalint<PERSON>", "injuredOrSuspended": "Sužeistas arba nušalint<PERSON>", "Since": "<PERSON><PERSON><PERSON>", "Overall": "Overall", "Age": "<PERSON><PERSON><PERSON>", "LastMatchFormations": "Paskutinis žaidimas se<PERSON>ją", "Formation": "", "GoalDistribution": "<PERSON><PERSON><PERSON><PERSON> platini<PERSON>", "Favorite": "", "FoundedIn": "Buvo įkurta", "LocalPlayers": "<PERSON><PERSON><PERSON>", "ShowNext": "<PERSON><PERSON><PERSON> kitas rungtynes", "HideNext": "Slėpti kitas rungtynes", "FIFAWorldRanking": "FIFA pasaulio reitingas", "Register": "", "OP": "1X2", "Handicap": "", "h5Handicap": "", "TennisHandicap": "", "OU": "O/U", "CardUpgradeConfirmed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "VAR": "", "LatestMatches": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ScheduledMatches": "", "Only": "", "LeagueFilter": "", "WinProb": "", "ScoreHalf": "", "Animation": "", "FigureLegends": "", "YellowCard": "<PERSON><PERSON><PERSON><PERSON>", "RedCard": "<PERSON><PERSON><PERSON>", "Chatroom": "Pokalbių kambarys", "Send": "Si<PERSON>sti", "Logout": "", "CurrentLeague": "", "ShirtNumber": "", "ContractUntil": "Kontraktas iki", "PlayerInfo": "PLAYER INFO", "Height": "Ūgis", "Weight": "<PERSON><PERSON><PERSON>", "PlayerValue": "Socialinis statusas", "View": "", "Time": "<PERSON><PERSON>", "onTarget": "", "offTarget": "", "Played": "", "Rating": "", "Attacking": "<PERSON><PERSON><PERSON><PERSON>", "Creativity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Defending": "<PERSON><PERSON><PERSON><PERSON>", "Tactical": "<PERSON><PERSON><PERSON><PERSON>", "Technical": "<PERSON><PERSON><PERSON>", "Other": "", "Cards": "<PERSON><PERSON><PERSON>", "AccuratePerGame": "Tikslus artimųjų", "AccLongBalls": "Bendradarb<PERSON> ilgai perdavimas", "AccCrosses": "Tikslus biografija", "SuccDribbles": "<PERSON><PERSON><PERSON><PERSON>", "TotalDuelsWon": "Pražang<PERSON>", "YellowRedCards": "", "AiScoreRatings": "", "Pergame": "", "Player": "", "Upcoming": "", "FreeKicks": "", "Corners": "Kampas", "DaysUntil": "<PERSON><PERSON> iki", "In": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Out": "<PERSON><PERSON><PERSON><PERSON>", "NoStrengths": "Nėra jokių pastebimų p<PERSON>šumų", "NoWeaknesses": "Nėra jokių pastebimų silpnų savybių", "UpcomingMatches": "", "Pos": "", "Ht(cm)": "", "wt(kg)": "", "Exp": "EXP", "College": "", "Salary/Year": "", "Manager": "", "TotalPlayers": "Dalyvių", "Form": "", "Points": "Taškai", "GamesPlayed": "", "WinPercentage": "", "FieldGoalsMade": "", "FieldGoalsAttempted": "", "FieldGoalPercentage": "", "threePointFieldGoalsMade": "", "threePointFieldGoalsAttempted": "", "threePointFieldGoalsPercentage": "", "FieldGoaldsMade": "", "FreeThrowsAttempted": "", "FreeThrowPercentage": "", "BlockedFieldGoalAttempts": "", "PersonalFouls": "", "PersonalFoulsDrawn": "", "Efficiency": "", "PlusMinus": "", "Atlantic": "", "Central": "", "Southeast": "", "Northwest": "", "Pacific": "", "Southwest": "", "EasternConference": "", "WesternConference": "", "ToWin": "", "Spread": "paplit<PERSON>s", "AmFootballHand": "paplit<PERSON>s", "TotalPoints": "", "TotalPoint": "", "TableTennisTotalGames": "", "Wins": "<PERSON><PERSON><PERSON><PERSON>", "Losses": "Pralaim<PERSON>i", "WinningPercentage": "", "GamesBack": "", "HomeRecord": "", "AwayRecord": "", "DivisionRecord": "", "ConferenceRecord": "", "OpponentPointsPerGame": "", "AveragePointDifferential": "", "CurrentStreak": "", "RecordLast5Games": "", "Match": "<PERSON><PERSON><PERSON><PERSON><PERSON> (vienerios)", "OnTheCourt": "Aikštelėje", "Starters": "<PERSON><PERSON>", "FieldGoals": "<PERSON><PERSON><PERSON>", "Timeout": "", "OpeningOdds": "", "PreMatchOdds": "", "PointPerGame": "", "PointsAllowed": "", "StartingLineups": "Starto sudėtys", "HandicapGames": "", "TennisHand": "", "TotalGames": "", "TennisTotalGames": "", "Defensive": "", "Offensive": "", "Points(PerGame)": "", "Rebounds(PerGame)": "", "Other(PerGame)": "Kita (vidutiniškai)", "Attists": "", "FirstServe": "", "FirstServePoints": "", "BreakPoints": "", "Aces": "Padavi<PERSON><PERSON>", "DoubleFaults": "Dvigu<PERSON> k<PERSON>", "Winner": "Winner", "HandicapSets": "", "TableTennisHand": "", "MoneyLine": "", "TableTennisHandicapGames": "", "HandicapRuns": "", "BaseballHand": "", "TotalRuns": "", "BaseballTotalGames": "", "DIFF": "", "BasketPergame": "", "HandicapPoints": "", "HandicapFrames": "", "TotalFrames": "", "SeeAll": ""}