{"All": "Összes", "Live": "ÉLŐ", "LiveH5": "ÉLŐ", "MatchLive": "ÉLŐ", "TimeSort": "Rendezés idő szerint", "SortByTime": "Rendezés idő szerint", "AllGames": "ÖSSZES", "Leagues": "LIGÁK", "h5_Leagues": "LIGÁK", "Today": "Ma", "Cancel": "Kilépés", "Popular": "Népszerű", "Settings": "Beállítások", "Language": "Nyelv", "Overview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "LiveOverview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Standings": "<PERSON><PERSON>", "Stats": "Statisztika", "Transfer": "Átigazolás", "Champions": "Bajnokok", "TeamChampions": "Bajnokok", "teamChampions": "Bajnokok", "Football": "LABDARÚGÁS", "Basketball": "KOSÁRLABDA", "Baseball": "Beseball", "Icehockey": "Hoki", "Tennis": "TENISZ", "Volleyball": "RÖPLABDA", "Esports": "ESPORTOK", "Handball": "KÉZILABDA", "Cricket": "KRIKETT", "WaterPolo": "VÍZILABDA", "TableTennis": "Asztalitenisz", "Snooker": "SZNÚKER", "Badminton": "TOLLASLABDA", "BusinessCooperation": "Business Cooperation", "TermsOfService": "Szolgáltatási Feltételeinket", "PrivacyPolicy": "Adatvédelem", "Players": "JÁTÉKOSOK", "ForeignPlayers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "NumberOfTeams": "Csapatok száma", "YellowCards": "<PERSON><PERSON><PERSON> lapok", "RedCards": "<PERSON><PERSON>", "Capacity": "<PERSON><PERSON><PERSON><PERSON>", "City": "<PERSON><PERSON><PERSON>", "Info": "Információ", "Matches": "MÉRKŐZÉSEK", "Team": "Csapat", "Teams": "Csapatok", "Goals": "Gólok", "Assists": "Gólpasszok", "assists": "Gólpasszok", "Home": "<PERSON><PERSON>", "Away": "Idegenben", "topScorers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TopScorers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homeTopScorers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "season": "Szezon", "Season": "Szezon", "ShotsOnTarget": "<PERSON><PERSON>", "Clearances": "Tisztázások", "Tackles": "<PERSON><PERSON><PERSON>ések", "keyPasses": "Fontos passzok", "KeyPasses": "Fontos passzok", "Fouls": "Szabálytalanságok", "totalFouls": "Szabálytalanságok", "WasFouled": "Kiharcolt szabálytalanságok", "Penalty": "Büntető", "MinutesPlayed": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "BasketballMinutesPlayed": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "Interceptions": "Közbeavatkozások", "Steals": "Labdaszerzés", "steals": "Labdaszerzés", "Passes": "<PERSON><PERSON><PERSON><PERSON>", "Saves": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BlockedShots": "Blokkolt lövések", "Signed": "Aláírt", "league": "", "offensiveData": "<PERSON><PERSON><PERSON><PERSON><PERSON> adata<PERSON>", "defenseData": "<PERSON><PERSON><PERSON><PERSON>", "otherData": "<PERSON><PERSON><PERSON><PERSON>", "ballPossession": "Labdabirtoklás", "shotsPerGame": "Kapura lövések átlaga", "ShotsPerGame": "Kapura lövések átlaga", "keyPassesPerGame": "Me<PERSON>sen<PERSON><PERSON><PERSON>", "accurateLongBallsPerGame": "Hosszú passzok pontossága meccsenként", "accurateCrossesPerGame": "Passzpontosság <PERSON>", "tacklesPerGame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "TacklesPerGame": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "interceptionsPerGame": "Meccsenkénti közbeavatkozások", "InterceptionsPerGame": "Meccsenkénti közbeavatkozások", "clearancesPerGame": "Meccsenk<PERSON><PERSON> t<PERSON>ztá<PERSON>ások", "ClearancesPerGame": "Meccsenk<PERSON><PERSON> t<PERSON>ztá<PERSON>ások", "blockedShotsPerGame": "Meccsenkénti Blokkolt lövések", "turnoversPerGame": "Meccsenk<PERSON><PERSON>", "foulsPerGame": "Me<PERSON><PERSON><PERSON><PERSON><PERSON>ágok", "scoringFrequencyFiveGoals": "", "Coach": "Vezetőedző", "Goalkeeper": "<PERSON><PERSON>", "Stadium": "Stadion", "Login": "Bejelentkezés", "Corner": "Szöglet", "ShotsOffTarget": "<PERSON><PERSON>ker<PERSON> lövések", "H2H": "H2H", "Date": "<PERSON><PERSON><PERSON><PERSON>", "OwnGoal": "Öngól", "PenaltyMissed": "<PERSON><PERSON><PERSON>", "SecondYellow": "<PERSON><PERSON><PERSON><PERSON> s<PERSON> lap", "Odds": "Szorzók", "attacks": "Támadás", "Started": "<PERSON><PERSON><PERSON><PERSON>", "Chat": "Cha<PERSON>", "Strengths": "Erősségek", "Weaknesses": "Gyengeségek", "Group": "Csoport", "Birthday": "Születésnap", "Club": "Klub", "MainPosition": "Fő pozíció", "PassesAccuracy": "", "Preseason": "", "RegularSeason": "", "PointsPerGame": "Pontok játékonként", "Glossary": "Magyarázatos szójegyzék", "h5Glossary": "Magyarázatos szójegyzék", "Career": "Karrier", "Bench": "Cserepadon", "ReboundsPerGame": "Lepattanók mérkőzésenként", "AssistsPerGame": "Gólpasszok mérkőzésenként", "OddsFormat": "Odds", "Squad": "Kezdő Felállások", "TotalMarketValue": "Teljes p<PERSON>", "Rounds": "Körök s<PERSON>", "LowerDivision": "Alacson<PERSON>bb <PERSON>", "TeamStats": "Csapat statisztikák", "GoalsPk": "Gólok(PK)", "Crosses": "Keresztlabdák", "CrossesAccuracy": "<PERSON><PERSON> a <PERSON><PERSON>n", "Dribble": "Rendkí<PERSON><PERSON><PERSON>", "DribbleSucc": "Rendkí<PERSON><PERSON><PERSON>", "LongBalls": "Hosszú passzok", "LongBallsAccuracy": "Hosszú passzok siker mértéke", "Duels": "Szabálytalanságok", "DuelsWon": "<PERSON><PERSON><PERSON><PERSON> lap", "Dispossessed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Punches": "<PERSON><PERSON>", "RunsOut": "Kifutás", "RunsOutSucc": "Blokkolt lövések", "GoodHighClaim": "Átadás pontosság", "Loan": "K<PERSON>lcsön", "EndOfLoan": "Kölcsön vége", "Unknown": "ismeretlen", "AverageAge": "Átlagé<PERSON>", "cornersPerGame": "Meccsenkénti szögletek", "goalsConceded": "<PERSON><PERSON>t gólok", "Defender": "<PERSON><PERSON><PERSON><PERSON>", "Discipline": "Magatartás", "Pass": "", "FB_Login": "Folytatás a Facebook", "Google_Login": "Folytatás a Google", "Substitutes": "<PERSON><PERSON><PERSON>", "PenaltyKick": "", "ShareYourViews": "Ossza meg velünk nézeteit", "Nodata": "Nincs adat", "Foot": "<PERSON><PERSON><PERSON>", "dangerousAttack": "", "venue": "Stadion", "playerStatistics": "Játékos statisztikák", "TotalPlayed": "Összesen j<PERSON>tt", "MinutesPerGame": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "GoalsFrequency": "", "GoalsPerGame": "Mezőnygól", "Arrivals": "Érkeztek", "Departures": "Távoztak", "LeftFoot": "<PERSON>l", "RightFoot": "<PERSON><PERSON>", "LatestTransfers": "Leg<PERSON><PERSON><PERSON>", "DraftInfo": "Utánpótlás", "OK": "OK", "AsianHandicap": "", "TotalGoals": "", "AmFootballTotalGames": "", "TotalCorners": "", "OverBall": "<PERSON><PERSON>", "Over": "<PERSON><PERSON>", "h5Over": "<PERSON><PERSON>", "UnderBall": "<PERSON><PERSON>", "Under": "<PERSON><PERSON>", "h5Under": "<PERSON><PERSON>", "OtherLeagues": "Egyéb liga (A-Z)", "GoalPopup": "", "FullStandings": "<PERSON><PERSON><PERSON>", "teamWeek": "A hét csapata", "weekTop": "A hét csapata", "TeamOfTheWeek": "A hét csapata", "round": "<PERSON><PERSON><PERSON>", "Released": "<PERSON><PERSON><PERSON>", "Retirement": "", "Draft": "Draft", "TransferIn": "", "TransferOut": "", "MarketValue": "<PERSON><PERSON><PERSON>", "Salary": "<PERSON><PERSON><PERSON><PERSON>", "Next": "Következő", "Position": "Pozíció", "CTR": "<PERSON><PERSON><PERSON><PERSON><PERSON> nyilvántartás", "FoulBall": "", "FreeKick": "", "GoalKick": "", "Midfield": "", "HalftimeScore": "", "InjuryTime": "", "OvertimeIsOver": "", "PenaltyKickEnded": "", "Last": "<PERSON><PERSON><PERSON><PERSON>", "Win": "Győzelmek", "Draw": "Döntetlenek", "Lose": "Vesztes mérkőzések", "Lineup": "", "Substitution": "Csere", "Offsides": "<PERSON><PERSON>", "Playoffs": "", "Qualifier": "", "GroupStage": "", "Rebounds": "Lepattanók", "rebounds": "Lepattanók", "OffensiveRebounds": "<PERSON><PERSON><PERSON><PERSON>", "offensiveRebounds": "<PERSON><PERSON><PERSON><PERSON>", "DefensiveRebounds": "<PERSON><PERSON><PERSON><PERSON> le<PERSON>", "defensiveRebounds": "<PERSON><PERSON><PERSON><PERSON> le<PERSON>", "Turnovers": "Lab<PERSON>lad<PERSON>", "turnovers": "Lab<PERSON>lad<PERSON>", "Blocks": "Blokk", "blocks": "Blokk", "BoxScore": "Kezdő Felállások", "Foul": "Foult", "FreeThrows": "Büntetők", "freeThrowsScored": "Büntetők", "fieldGoalsScored": "", "fieldGoalsTotal": "", "threePointersScored": "", "threePointersTotal": "", "freeThrowsTotal": "", "Finished": "<PERSON><PERSON><PERSON> lett", "Scheduled": "Mérkőzések", "Favourite": "", "OddsMarkets": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Search": "", "Timezone": "", "SoccerGoalReminderSettings": "", "RemindersOnlyForRacesToWatch": "", "GoalSound": "", "LiveScore": "", "Schedule": "Menetrend", "Rugby": "", "FooterContentFootball": "Az IGScore Football LiveScore páratlan foci élő eredményekkel és több mint 2600 labdarúgó bajnokság, kupa és verseny labdarúgó eredményeivel rendelkezik. <PERSON><PERSON><PERSON> er<PERSON>, f<PERSON><PERSON><PERSON><PERSON> és teljes munkaidős foci ered<PERSON>ek, góllövők és asszisztensek, kártyák, cserék, meccsstatisztikák és élő közvetítés a Premier League-ből, a La Liga, a Serie A, a Bundesliga, a Ligue 1, az Eredivisie, az Orosz Premier League, a Brasileirão, az MLS-től, Szuper Lig és bajnokság az igscore.net-on. Az IGScore élő ered<PERSON>ek, foci er<PERSON>, foci er<PERSON><PERSON>, bajnokság asztalok és mérkőzések bajnokságokhoz, kupákhoz és bajnokságokhoz kínál, és nem csak a legnépszerűbb labdarúgó bajnokságokból, mint Anglia Premier League, Spain La Liga, Olaszország Serie A, Németország Bundesliga, Franciaország Ligue 1, de számos futball országból is szerte a világon, beleértve Észak- és Dél-Amerikát, Ázsiát és Afrikát is. Labdarúgás eredmények eredménylistáink valós időben frissülnek, hogy naprakészek legyenek a ma zajló összes labdarúgó mérkőzés élő eredmények frissítéssel, valamint az összes futball és futball bajnokság összes befejezett labdarúgó mérkőzésének eredményei. A mérkőzések oldalán futballmutatóink lehetővé teszik, hogy megnézhesd az összes futballverseny minden korábban lejátszott mérkőzésének korábbi eredményeit. Szerezd meg az összes foci élő eredményedet az igscore.net-on!", "FooterContentBasketball": "Az IGScore Basketball LiveScore az NBA bajnokság élő élő eredményekkel, er<PERSON><PERSON>nyekkel, tá<PERSON>l<PERSON>zatokkal, statisztikákkal, meccsekkel, tabellákkal és korábbi eredményekkel áll rendelkezésre negyedév, félid<PERSON> vagy végeredmény alapján. Az IGScore több mint 200 kosárlabda versenyen kínál pontszolgáltatást a világ minden tájáról (mint például az NCAA, az ABA League, a Balti bajnokság, az Euroliga, a nemzeti kosárlabda ligák). Itt nemcsak az élő eredmények, a negyedéves eredmények, a végeredmények és a felállások, hanem a 2- és 3 pontos kísérletek száma, a szabad dobások, a lövési százalék, a visszapattanás, a forgalom, a lopások, a sze<PERSON><PERSON><PERSON> s<PERSON>k, a meccselőzmények és a játékosok statisztikái találhatók. .Az IGScore kosárlabda élő eredmények alapján egyszerűen rákattintva nézheti a kosárlabdát, és online lefedettséget biztosít a felső bajnoki mérkőzésekről. A mérkőzés oldalon a táblázat jelenik meg a csapatok legutóbbi játékaira vonatkozó kosárlabda statisztikákkal együtt. kosárlabda eredménykártyáinkat élő valós időben frissítjük, hogy naprakész legyen a mai kosárlabda eredményekkel, és lehetővé tegye a korábbi játék eredményeinek megtekintését az összes korábban lejátszott mérkőzésről az összes kosárlabda versenyen. Minden NBA élő eredmények elérhető az igscore.net-on! Kövesse az NBA élő eredmények, NBA eredmények és az NBA eredmények és a csapat oldalait!", "FooterContentAmFootball": "IGScore american football live score biztosítja a találatokat és élő pontszámok a legnagyobb és legnépszerűbb amerikai futball liga a világon - NFL, és ha rendszeresen NFL s<PERSON><PERSON> be<PERSON><PERSON>, kövesse élő eredmények NFL rájátszás és Superbowl. Az NFL mellett az NCAA College Americal Football and Canadian CFL-hez is biztosítjuk Önt az NFL-k, er<PERSON><PERSON><PERSON><PERSON>, állások és ütemezések.", "FooterContentBaseball": "A IGScore baseball live score é<PERSON><PERSON> er<PERSON>, eredményeket és állásokat biztosít a világ legnépszerűbb baseball ligájából - USSSA Baseball, NCAA Baseball, MLB Baseball, MLB Allstar. <PERSON><PERSON><PERSON> p<PERSON> is biztosítunk a Japán Professzionális Liga, a Mexikói Liga, a Német 1. Bundesliga, az NCAA, valamint a World Baseball Classic nemzetközi baseball bajnokságához. Bármikor megtekintheti a baseball bajnokság állását, a korábbi meccseket, a játékrészek eredményei szerint, valamint a következő baseballmeccsek menetrendjét a következő napon: IGScore baseball live score.", "FooterContentIcehockey": "IGScore ice hockey live score valós idej<PERSON> jégkorong eredményeket biztosít a jégkorong ligák, kupák és bajnokságok számára. IGScore ice hockey live score jégkorong élő eredményeket, t<PERSON><PERSON><PERSON><PERSON><PERSON>tokat, statisztikákat, meccseket, eredményeket és pontszámokat biztosítunk az NHL, SHL, KHL csapatoktól, valamint finn nemzeti jégkorong ligákat, svéd jégkorong ligákat, szlovákiai jégkorong ligákat, cseh jégkorong ligákat, baj<PERSON><PERSON> tá<PERSON>lázatokat, gólszerzőket, harmadik és végső jégkorong eredmények élőben. A jégkorong alapszakaszának befejezése után élő jégkorong eredményeket, állásokat és eredményeket kínálunk Önnek a jégkorong legjobb eseményeiről- IIHF világbajnoksá<PERSON>, valamint a téli olimpiai bajnokság jégkorongjainak eredményeit. A IGScore ice hockey live score oldalon ingyenes jégkorong élő közvetítést is találhat NHL, SHL és mások számára.", "FooterContentTennis": "IGScore tennis live score <PERSON><PERSON><PERSON> er<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ATP -rangsorokat és WTA -rangsorokat, meccseket és statisztikákat biztosít az összes legnagyobb teniszversenyről, péld<PERSON><PERSON> a Davis és a Fed Cup, a francia nyílt tenisz, vagy az összes Grand Slam -bajnokságról - ausztr<PERSON><PERSON> ny<PERSON>lt tenisz, amerikai nyílt tenisz, <PERSON> Wimbledon nők és férfiak egyaránt és párosban. Szintén minden teniszező számára részletesen megtekintheti egyénileg lejátszott mérkőzéseit és azok eredményeit szettenként, valamint azt, hogy melyik bajnokságban játszották. A IGScore tennis live score fej -fej mellett tal<PERSON>lat<PERSON>t, stat<PERSON><PERSON><PERSON>k<PERSON><PERSON>, él<PERSON> eredményeket és élő mérkőzést biztosít két mérk<PERSON>z<PERSON>t játszó játékos között.", "FooterContentVolleyball": "A IGScore volleyball live score minden fontos férfi és női nemzeti röplabda -bajnoks<PERSON><PERSON>, beleértve az Olaszország Serie A1 és Olaszország Seria A1 Női, Orosz Szuperliga, Lengyel PlusLiga, Törökország 1. Lig és még sokan mások. A nemzeti röplabda bajnokságok mellett élő röplabda nemzetközi bajnokságokról, például FIVB világbajnokság és Európa -bajnokság, valamint röplabda é<PERSON><PERSON> eredményekről is tájékoztatást nyújtunk az olimpiai játékokról. Ellenőrizheti kedvenc röplabdacsapatának régi eredményeit, megtekintheti a röplabda jövőbeli menetrendjét, és megtekintheti a röplabda bajnoki állását a IGScore volleyball live score oldalon.", "FooterContentEsports": "Az ESPORTS Live Scores Service a IGScore élő pontszámon kínál az üzleti eredmé<PERSON>ek, ütemtervek, eredmények és táblázatok. Kövesse kedvenc csapatait itt: Live! Az ESPORTS LIVE pontszám a igscore.net élő pontszámon automatikusan frissül, és nem kell manuálisan frissítenie. A játékok hozzáadásával a \"My Játékok\" követése után a mérkőzések, az eredmények és a statisztikák még egyszerűbbek lesznek.", "FooterContentHandball": "IGScore handball live score kézilabda élő eredményeket és élő eredményeket biztosít a legnépszerűbb kézilabda ligákból, például a német Bundesligából, a spanyol Liga Asobalból, a dániai férfi Handboldligaenből és a France D1 -ből. Ezenkívül élő eredményeket, eredményeket, statisztikákat, tabellákat, tabellákat és meccseket is közlünk olyan fontos kupákról, mint az Európai Kézilabda Bajnokok Ligája, a SEHA liga és az EHF kézilabda Kupa. A (z) IGScore handball live score oldalon élő eredmények és ingyenes élő közvetítés található a nemzetközi csapatok kézilabda versenyeihez, például az Európa -bajnoksághoz és a világbajnoksághoz, nőknek és férfiaknak egyaránt. Bármikor ellenőrizheti a kézilabda eredményeit és statisztikáit a csapatának legutóbbi 10 meccséről, valamint fej -fej mellett gólt is szerezhet azoknak a csapatoknak, amelyek statisztikailag játszanak.", "FooterContentCricket": "A IGScore cricket live score lehetővé teszi a valós idejű krikett eredmények, a krikett állások és a krikett mérkőzések követését. Mindez elérhető a legnépszerűbb bajnokságokban és kupákban: Indiai Premier League, Champions League Twenty20, Big Bash League, Caribbean Premier League, Friends Life T20 és az ICC Cricket World Cup. Az IGScore összes krikett eredménye automatikusan frissül, és nincs szükség manuális frissítésre. Mindezek mellett lehetőség van ingyenes krikett élő közvetítés nézésére, és ellenőrizheti a legutóbbi esélyeket a világ legérdekesebb krikettmeccseinek végeredményére.", "FooterContentWaterPolo": "A IGScore water polo live score a vízilabda élő eredményeit és eredményeit mutatja az olasz Serie A1, Magyarország OB1, Bajnokok és Adria bajnokságról klubszinten, míg nemzetközi szinten IGScore water polo live score olyan nagy bajnokságokat biztosít, mint a vízilabda -világbajnokság és a vízilabda -Európa -bajnokság . Célról gólra élő eredményeket és ingyenes élő közvetítést biztosítunk Önnek.", "FooterContentTableTennis": "A IGScore table tennis live score <PERSON><PERSON><PERSON> er<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, asz<PERSON>ite<PERSON>z <PERSON>rang<PERSON><PERSON><PERSON><PERSON>, mérkőzésekről és statisztikákról biztosít az összes legnagyobb asztalitenisz -bajnokságról, péld<PERSON><PERSON> az orosz asztaliteniszről, az asztalitenisz -olimpiáról. Szintén minden asztaliteniszezőnél részletesen megtekintheti egyénileg lejátszott mérkőzéseit és azok eredményeit szett szerint, valamint azt, hogy melyik bajnokságban játszották azt a mérkőzést. A IGScore table tennis live score fej -fej mellett eredményeket, statisztikákat, él<PERSON> eredményeket és élő mérkőzést biztosít két mérkőzést já<PERSON>z<PERSON> j<PERSON> k<PERSON>.", "FooterContentSnooker": "A IGScore snooker live score lehetővé teszi az összes snooker bajnokság élő eredményének, eredményeinek és állásának követését. Emellett élő eredményeket biztosítunk az Egyesült Királyságból és a világbajnokságról, valamint a snooker élő eredményeit, a snooker meccseket és a nemzetközi bajnokságok, például a World Snooker tour végső snooker eredményeit. Bármikor megtekintheti a megkezdett snooker bajnokságok menetrendjét, a korábbi snooker bajnokságok eredményeit és minden játékos utolsó 10 mérkőzését. Ezenkívül ellenőrizheti a játékosok közötti fej -fej közötti mérkőzéseket. A (z) IGScore snooker live score oldalon megtalá<PERSON>hat<PERSON> azoknak a mérkőzéseknek a list<PERSON><PERSON><PERSON><PERSON>, amelyek ingyenes snooker élő közvetítéssel vannak ellátva.", "FooterContentBadminton": "A IGScore badminton live score a tollaslabda élő eredményeit, <PERSON><PERSON><PERSON><PERSON><PERSON>, mérkőzéseit és statisztikáit nyújtja a nemzetközi bajnokságokról, péld<PERSON><PERSON> a világbajnokságról, a BWF Super Series -ről és az olimpiai játékok tollaslabda eredményeiről. Ezen kívül ellenőrizheti a tollaslabdázott játékok eredményeit, megtekintheti a tollaslabda játék menetrendjét, és megtekintheti a játékosok tollaslabdaeredményeit egyenként a IGScore badminton live score oldalon.", "ContactUs": "<PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>ü<PERSON>", "UpdateLineups": "Update Lineups", "FreeLiveScoresWidget": "Free Livescores Widget", "PlayerSalary": "", "DivisionLevel": "Esemény Level", "Foreigners": "A külföldi játékosok számát", "LeagueInfo": "<PERSON><PERSON><PERSON><PERSON>", "TeamInfo": "Csapat Info", "Venues": "", "MostValuablePlayer": "", "UpperDivision": "<PERSON><PERSON><PERSON><PERSON>", "NewcomersFromUpperDivision": "", "NewcomersFromLowerDivision": "", "TitleHolder": "Címvédő", "MostTitle": "<PERSON><PERSON><PERSON><PERSON>", "Pts": "pont", "FullStats": "", "Relegation": "", "Result": "<PERSON><PERSON><PERSON><PERSON>", "Score": "<PERSON>", "PlayerStats": "", "fixtures": "", "topPlayers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Shots": "<PERSON>ö<PERSON><PERSON>", "SelectTeam": "", "SelectBasketBallTeam": "", "SelectAll": "<PERSON><PERSON> ki<PERSON>l", "SquadSize": "A játékosok száma", "ViewAll": "Összes megtekintése", "penaltiesWon": "Kiharcolt büntetők", "accuracyCrosses": "", "totalShorts": "", "accuracyLongBalls": "", "blockShots": "", "MatchesToday": "Meccsek ma", "Strikers": "Előre", "Midfielders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Year": "", "Loans": "", "TopValuablePlayers": "", "total": "", "Total": "", "Results": "", "Prev": "", "Apps": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ShotsPg": "Az átlagolás löv<PERSON>", "Possession": "", "TotalAndAve": "", "Suspended": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON>", "injuredOrSuspended": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON>", "Since": "Sér<PERSON><PERSON>s <PERSON>", "Overall": "Overall", "Age": "<PERSON><PERSON>", "LastMatchFormations": "Az utolsó játék felállás", "Formation": "", "GoalDistribution": "<PERSON><PERSON><PERSON> forgalmaz<PERSON>", "Favorite": "", "FoundedIn": "-<PERSON>", "LocalPlayers": "<PERSON><PERSON><PERSON>", "ShowNext": "Mutasd a következő eseményeket", "HideNext": "Rejtse el a következő mérkőzéseket", "FIFAWorldRanking": "FIFA rangsor", "Register": "", "OP": "1x2", "Handicap": "", "h5Handicap": "", "TennisHandicap": "", "OU": "O/U", "CardUpgradeConfirmed": "Második sárga megerősítve", "VAR": "", "LatestMatches": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ScheduledMatches": "", "Only": "", "LeagueFilter": "", "WinProb": "", "ScoreHalf": "", "Animation": "", "FigureLegends": "", "YellowCard": "<PERSON><PERSON><PERSON> lapok", "RedCard": "<PERSON><PERSON> lap", "Chatroom": "Csetszoba", "Send": "<PERSON><PERSON><PERSON><PERSON>", "Logout": "", "CurrentLeague": "", "ShirtNumber": "", "ContractUntil": "Szerződésben (-ig)", "PlayerInfo": "PLAYER INFO", "Height": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Weight": "Testsúly", "PlayerValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "View": "", "Time": "<PERSON><PERSON><PERSON>", "onTarget": "", "offTarget": "", "Played": "", "Rating": "", "Attacking": "Támadás", "Creativity": "Kreativitás", "Defending": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tactical": "Taktika", "Technical": "Technikai tudás", "Other": "", "Cards": "Penalty kártyák", "AccuratePerGame": "Pontos tompított", "AccLongBalls": "Associate ho<PERSON><PERSON><PERSON>t", "AccCrosses": "Pontos életrajz", "SuccDribbles": "Rendkí<PERSON><PERSON><PERSON>", "TotalDuelsWon": "Szabálytalanságok", "YellowRedCards": "", "AiScoreRatings": "", "Pergame": "", "Player": "", "Upcoming": "", "FreeKicks": "", "Corners": "Szöglet", "DaysUntil": "Hátralévő napok", "In": "Be", "Out": "Le", "NoStrengths": "<PERSON><PERSON><PERSON> erőssége", "NoWeaknesses": "<PERSON><PERSON><PERSON> k<PERSON> gyengesége", "UpcomingMatches": "", "Pos": "", "Ht(cm)": "", "wt(kg)": "", "Exp": "EXP", "College": "", "Salary/Year": "", "Manager": "", "TotalPlayers": "A játékosok száma", "Form": "", "Points": "Pontok", "GamesPlayed": "", "WinPercentage": "", "FieldGoalsMade": "", "FieldGoalsAttempted": "", "FieldGoalPercentage": "", "threePointFieldGoalsMade": "", "threePointFieldGoalsAttempted": "", "threePointFieldGoalsPercentage": "", "FieldGoaldsMade": "", "FreeThrowsAttempted": "", "FreeThrowPercentage": "", "BlockedFieldGoalAttempts": "", "PersonalFouls": "", "PersonalFoulsDrawn": "", "Efficiency": "", "PlusMinus": "", "Atlantic": "", "Central": "", "Southeast": "", "Northwest": "", "Pacific": "", "Southwest": "", "EasternConference": "", "WesternConference": "", "ToWin": "", "Spread": "Hendikep", "AmFootballHand": "Hendikep", "TotalPoints": "", "TotalPoint": "", "TableTennisTotalGames": "", "Wins": "Győzelmek", "Losses": "Vesztes mérkőzések", "WinningPercentage": "", "GamesBack": "", "HomeRecord": "", "AwayRecord": "", "DivisionRecord": "", "ConferenceRecord": "", "OpponentPointsPerGame": "", "AveragePointDifferential": "", "CurrentStreak": "", "RecordLast5Games": "", "Match": "mérkőzéshez", "OnTheCourt": "A pályán", "Starters": "Indítóbírók", "FieldGoals": "Mezőnygól", "Timeout": "", "OpeningOdds": "", "PreMatchOdds": "", "PointPerGame": "", "PointsAllowed": "", "StartingLineups": "Kezdő felállások", "HandicapGames": "", "TennisHand": "", "TotalGames": "", "TennisTotalGames": "", "Defensive": "", "Offensive": "", "Points(PerGame)": "", "Rebounds(PerGame)": "", "Other(PerGame)": "Egyéb (átlagolás)", "Attists": "", "FirstServe": "", "FirstServePoints": "", "BreakPoints": "", "Aces": "Ászok", "DoubleFaults": "<PERSON><PERSON><PERSON><PERSON>b<PERSON>", "Winner": "Winner", "HandicapSets": "", "TableTennisHand": "", "MoneyLine": "", "TableTennisHandicapGames": "", "HandicapRuns": "", "BaseballHand": "", "TotalRuns": "", "BaseballTotalGames": "", "DIFF": "", "BasketPergame": "", "HandicapPoints": "", "HandicapFrames": "", "TotalFrames": "", "SeeAll": ""}