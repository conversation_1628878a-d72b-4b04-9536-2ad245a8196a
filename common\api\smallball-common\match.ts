import igsRequest from "iscommon/request/instance";

/**
 * @param {{ matchId: string }}
 * @returns {any}
 */
 export const getMatchHeader = async ({ matchId } = {}) => {
  try {
    return await igsRequest.post("/match/info", { matchId });
  } catch (e) {
    console.log("getMatchHeader error:", e);
    return null;
  }
};


export const getMatchOdds = ({ matchId } = {}) => {
  return igsRequest
    .post("match/odds", {
      matchId
    })
    .then((result) => {
      return result;
    });
};

export const getMatchStatistics = ({ matchId } = {}) => {
  return igsRequest
    .post("match/stats/realtime", {
      matchId
    })
    .then((result) => {
      return result;
    });
};