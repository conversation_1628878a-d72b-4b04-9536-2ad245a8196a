// @ts-nocheck
import igsRequest from 'iscommon/request/instance';
import { getOneLocalUniqueValue } from '../store';

/**
 * @param {{ matchId: string }}
 * @returns {any}
 */
export const getMatchHeader = async ({ matchId } = {}) => {
  try {
    return await igsRequest.post('match/info', { matchId });
  } catch (e) {
    console.log('getMatchHeader error:', e);
    return null;
  }
};

/**
 * @param {{ teamId: string; size: number }}
 * @returns {Promise<any>}
 */
export const getMatchRecentList = async ({ teamId, size = 30 } = {}) => {
  try {
    return await igsRequest.post('match/analysis/recent', { teamId, size });
  } catch (e) {
    console.log('getMatchRecentList error:', e);
    return null;
  }
};

/**
 * @param {{ homeTeamId: string; awayTeamId: string; size: number }}
 * @returns {any}
 */
export const getMatchHistoryList = async ({ homeTeamId, awayTeamId, size = 30 } = {}) => {
  try {
    return await igsRequest.post('match/analysis/history', {
      homeTeamId,
      awayTeamId,
      size,
    });
  } catch (e) {
    console.log('getMatchRecentList error:', e);
    return null;
  }
};

/**
 * @param {{ teamId: string; size: number }}
 * @returns {any}
 */
export const getMatchFutureList = async ({ teamId, size = 5 } = {}) => {
  try {
    return await igsRequest.post('match/analysis/future', { teamId, size });
  } catch (e) {
    console.log('getMatchRecentList error:', e);
    return null;
  }
};

export const getMatchStandings = ({ matchId, type } = {}) => {
  return igsRequest.post('match/standings', {
    matchId,
    type,
  });
};

export const getMatchOdds = ({ matchId } = {}) => {
  return igsRequest.post('match/odds', {
    matchId,
  });
};

export const getMatchLineup = ({ matchId } = {}) => {
  return igsRequest.post('match/lineup', {
    matchId,
  });
};

export const getMatchTimeLine = ({ matchId } = {}) => {
  return igsRequest.post('match/timeline', {
    matchId,
  });
};

export const getMatchStatistics = ({ matchId } = {}) => {
  return igsRequest.post('match/statistics', {
    matchId,
  });
};

//v1/{sportName}/video/info
export const getGameVideoInfo = (matchId) => {
  return igsRequest.post('video/info', {
    matchId,
    uid: getOneLocalUniqueValue('offvideo', 5),
  });
};

// export const getUpcomingMatch = () => {
//   return igsRequest.post('common/upcomingmatch'), {

//   }
// }