import { But<PERSON>, DatePicker } from "antd-mobile";
import { getPlayerSchedule } from "iscommon/api/basketball/basketball-player";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { inject, observer } from "mobx-react";
import { useState, useEffect } from "react";
import { DownOutline } from 'antd-mobile-icons'
import moment from "moment";
import Loading from "@/components/Loading";
import { Link } from "umi";
import FavoriteIcon from "@/components/Common/FavoriteIcon";
import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";
import './MatchesCard.less'
import { FallbackImage } from "iscommon/const/icon";

interface Props {
  store?: any;
}

const MatchCard = inject('store')(
  observer((props: Props) => {
    const {
      store: {
        Basketball: {
          Player: { playerHeaderInfo, playerId },
        },
      },
    } = props;
    const { team } = playerHeaderInfo || {};
    const teamId = team.id;
    const competitionId = team.competitionId;

    const labelMaps = useTranslateKeysToMaps(['Schedule', 'Result']);

    const [matches, setMatches] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [calendarVisible, setCalendarVisible] = useState(false);
    const [filteredMatches, setFilteredMatches] = useState<any[]>([]);
    const [selectedDate, setSelectedDate] = useState<moment.Moment | null>(moment());

    const formatDate = (date: any) => {
      return date ? moment(date).format('MMM YY') : moment().format('MMM YY');
    };

    useEffect(() => {
      if (playerId && teamId) {
        setLoading(true);
        getPlayerSchedule({
          playerId,
          teamId,
          competitionId,
        }).then((res: any) => {
          let matchList = res?.matchList || [];
          setMatches(matchList);
          setLoading(false);
        });
      }
    }, [competitionId, playerId, teamId]);

    useEffect(() => {
      if (matches.length > 0) {
        filterMatchesForSelectedDate(selectedDate);
      }
    }, [matches, selectedDate]);

    const filterMatchesForSelectedDate = (date: any) => {
      const selectedMoment = moment(date);
      const selectedMonth = selectedMoment.month();
      const selectedYear = selectedMoment.year();

      const filteredMatches = matches.filter((match: any) => {
        const matchDate = moment.unix(match.matchTime);
        return matchDate.month() === selectedMonth && matchDate.year() === selectedYear;
      });

      setFilteredMatches(filteredMatches);
    };

    const onDateChange = (date: any) => {
      setSelectedDate(date);
      setCalendarVisible(false);
      if (date) {
        filterMatchesForSelectedDate(date);
      }
    };

    console.log('filter match', filteredMatches)

    return (
      <div className="basketball-player-match-card-container">
        <div className="container-title">
          <span>{labelMaps.Schedule}&nbsp;&&nbsp;{labelMaps.Result}</span>
          <>
            <Button className='team-month-btn' size="small" onClick={() => setCalendarVisible(true)}>
              <span className='btn-content'>
                <span className='btn-value'>{formatDate(selectedDate)}</span>
                <DownOutline className='down-icon'/>
              </span>
            </Button>
            <DatePicker
              visible={calendarVisible}
              precision='month'
              onConfirm={onDateChange}
              onClose={() => setCalendarVisible(false)}
            />
          </>
        </div>
        <div className="container-body">
          <Loading loading={loading} isEmpty={filteredMatches.length === 0}>
            {filteredMatches.map((match: any, index: number) => {
              const calculatedHomeScore = match.homeScores.reduce((acc: number, score: number) => acc + score, 0);
              const calculatedAwayScore = match.awayScores.reduce((acc: number, score: number) => acc + score, 0);
              return (
                <Link to={GlobalUtils.getPathname(PageTabs.match, match.id)} key={match.id} className="match-list-container">
                  <div className="match-time-container">
                    {/* <FavoriteIcon isMatch={true} match={match} /> */}
                    <span className="match-time">{moment.unix(match.matchTime).format('DD/MMM')}</span>
                  </div>
                  <div className='match-team-container'>
                    <div className='match-team-info'>
                      <img className='team-logo' src={match.homeTeam?.logo || FallbackImage} alt={match.homeTeam?.name} loading="lazy"/>
                      <span className='team-name text-overflow'>{match.homeTeam?.name}</span>
                    </div>
                    <div className='match-team-info'>
                      <img className='team-logo' src={match.awayTeam?.logo || FallbackImage} alt={match.awayTeam?.name} loading="lazy"/>
                      <span className='team-name text-overflow'>{match.awayTeam?.name}</span>
                    </div>
                  </div>
                  <div className="match-score-container">
                    <span className="match-score">{calculatedHomeScore}</span>
                    <span className="match-score">{calculatedAwayScore}</span>
                  </div>
                </Link>
              )
            })}
          </Loading>
        </div>
      </div>
    );
  }),
);

export default MatchCard;