export function checkFbLoginState() {
  return new Promise((resolve, reject) => {
    /**
     * response:
     * {
     *     status: 'connected',
     *     authResponse: {
     *         accessToken: '...',
     *         expiresIn:'...',
     *         signedRequest:'...',
     *         userID:'...'
     *     }
     * }
     */
    // https://developers.facebook.com/docs/reference/javascript/FB.getLoginStatus
    // @ts-ignore
    FB.getLoginStatus(function (response) {
      if (response) {
        resolve(response);
      } else {
        console.warn(`get fb login status fail: ${response}`);
        reject('get fb login status fail.');
      }
    });
  });
}
