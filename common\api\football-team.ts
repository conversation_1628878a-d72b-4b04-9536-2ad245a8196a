// @ts-nocheck
import igsRequest from 'iscommon/request/instance';
import { genValidObj } from 'iscommon/utils';

import { filterValidSeasons } from '../utils/dataUtils';

export const getFootBallTeam = ({ teamId } = {}) => {
  return igsRequest
    .post('team/detail/lineup', {
      teamId,
    })
    .then((result) => {
      // console.log(result);
      return result;
    });
};

export const getFootBallTeamMatches = ({ teamId, month, externalType = -1 }) => {
  const _externalType = externalType === -1 ? null : externalType;
  return igsRequest
    .post(
      'team/detail/schedule',
      genValidObj({
        teamId,
        month,
        externalType: _externalType,
      }),
    )
    .then((result) => {
      // console.log(result);
      return result;
    });
};

export const getFootBallTeamStats = ({ teamId }) => {
  return igsRequest
    .post('team/detail/newest/statistics', {
      teamId,
    })
    .then((result) => {
      // console.log(result);
      return result;
    });
};

export const getFootBallTeamInjury = ({ teamId }) => {
  return igsRequest
    .post('team/detail/injury', {
      teamId,
    })
    .then((result) => {
      // console.log(result);
      return result;
    });
};

export const getFootBallTeamLineupLatest = ({ teamId }) => {
  return igsRequest
    .post('team/detail/lineup/latest', {
      teamId,
    })
    .then((result) => {
      // console.log(result);
      return result;
    });
};

/**
 *
 * @param teamId
 * @returns {{
 *   "logo": string;
 *         "name": string;
 *         "countryId": string;
 *         "totalPlayers": number;
 *         "foreignPlayers": number;
 *         "avgAge": number;
 *         "marketValue": number;
 *         "marketValueCurrency": string;
 *         "playerSalary": number;
 * }}
 */
export const getTeamHeader = ({ teamId } = {}) => {
  return igsRequest.post('team/detail/header', { teamId }).then((result) => {
    return result;
  });
};

/**
 *
 * @param teamId
 * @returns {
 * "honorList": [
 *     {
 *       "honorId": "",
 *       "honorName": "",
 *       "logo": "",
 *       "count": 0,
 *       "seasons": [
 *         ""
 *       ]
 *     }
 *   ]}
 */
export const getTeamChampions = async ({ teamId } = {}) => {
  const result = await igsRequest.post('team/detail/titles', { teamId })
  try {
    for(let v of result.honorList) {
      v.seasons = v.seasons.sort((a, b) => {
        return b.split('-')[0] - a.split('-')[0]
      })
    }
  } catch(e) {console.log(e)}
  return result
};

/**
 *
 * @param teamId
 * @returns {{
 *   foundationTime: 1884,
 *     totalPlayers: 26,
 *     foreignPlayers: 16,
 *     nationalPlayers: 10,
 *     competitionList: [
 *       {
 *         id: "jednm9whz0ryox8",
 *         name: "English Premier League",
 *         shortName: "ENG Premier League",
 *         logo: "https://img.thesports.com/football/competition/d3cd84441d4de605d84a14521ef56920.png",
 *       },
 *     ],
 *     venue: {
 *       name: "King Power Stadium",
 *       city: "Leicester",
 *       capacity: 32312,
 *     },
 * }}
 */
export const getTeamInfoData = ({ teamId } = {}) => {
  return igsRequest.post('team/detail/detail', { teamId }).then((result) => {
    return result;
  });
};

// validKey = 'hasTable' (有积分榜) || hasTeamStats （有球队统计）|| hasPlayerStats（有球员统计）
export const getTeamCompetitions = ({ teamId = '', playerId = '', validKey = '' } = {}) => {
  return igsRequest.post('team/detail/competitions', genValidObj({ teamId, playerId })).then((result) => {
    return {
      competitions: filterValidSeasons(result, validKey),
    };
  });
};
