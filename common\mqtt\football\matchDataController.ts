// @ts-nocheck
import _ from 'lodash';

import store from 'iscommon/mobx';
import { getArrayFromString } from 'iscommon/utils';
import { formatTimeLineData } from '../../utils/dataUtils';

const syncmatchScore = (data) => {
  // console.log('score', data)
  const { Match } = store;
  const {matchId} = Match
  const matchHeaderInfo = _.cloneDeep(Match.matchHeaderInfo);
  if (!matchId) {
    return false;
  }

  const { id, score = [], kickOffTime } = data;
  if (score.length > 0 && matchId == id) {
    // ["1l4rjnhj9kn9m7v", 8, [1, 0, 0, 0, -1, 0, 0], [1, 0, 0, 0, -1, 0, 0], 0, ""]
    // 2:Array[7]
    // 0:"match Team Score (regular time)                                                                          (Integer type)"
    // 1:"match Team Halftime score                                                                                (Integer type)"
    // 2:"match Team Red cards                                                                                     (Integer type)"
    // 3:"match Team Yellow cards                                                                                  (Integer type)"
    // 4:"match Team Corners，-1 means no corner kick data                                                         (Integer type)"
    // 5:"match Team Overtime score (120 minutes，including regular time)，only available in overtime              (Integer type)"
    // 6:"match Team Penalty shootout score，only penalty shootout
    // 当主客加时比分不为零时：
    // 最终比分=加时比分（match Team Overtime score）+点球大战比分（match Team Penalty shootout score）
    // 当主客加时比分为零时：
    // 最终比分=常规时间比分（match Team Score (regular time)）+点球大战比分
    const matchStatus = score[1];
    const homeScores = getArrayFromString(score[2]);
    const awayScores = getArrayFromString(score[3]);
    matchHeaderInfo.statusId = matchStatus;
    matchHeaderInfo.homeScores = homeScores;
    matchHeaderInfo.awayScores = awayScores;
    matchHeaderInfo.firstHalfKickOffTime = kickOffTime
    matchHeaderInfo.secondHalfKickOffTime = kickOffTime

    Match.changeMatchHeaderInfo(matchHeaderInfo);
  }
};

// noinspection JSUnusedLocalSymbols
const syncIncidents = (data) => {
  // console.log('incidents', data)
  // incidents = [
  //   {type: 9, position: 1, time: 15},
  //   {type: 1, position: 1, time: 25},
  //   {type: 3, position: 1, time: 30}
  // ]
  const { Match } = store;
  const {matchId, matchHeaderInfo: {homeScores, awayScores}} = Match
  if (!matchId) {
    return false;
  }
  const { id, incidents } = data;
  if (matchId === id && incidents.length > 0 && homeScores.length > 0 && awayScores.length > 0) {
    const list = formatTimeLineData({
      awayScores,
      homeScores,
      incidents: incidents.map(item => {
        return {
          ...item,
          homeScore: item.home_score || null,
          awayScore: item.away_score || null,
          playerName: item.player_name || null,
          assist1Name: item.assist1_name || null,
          assist2Name: item.assist2_name || null,
          outPlayerName: item.out_player_name || null,
          inPlayerName: item.in_player_name || null
        }
      }),
    });
    Match.setTimeLineList(list);
  }
};

// the stats data is same as the scores data
// noinspection JSUnusedLocalSymbols
const syncStats = (data) => {
  // console.log('stats', data)
  // stats = [
  //   {type: 3, match: 2, away: 2},
  //   {type: 23, match: 74, away: 68},
  //   {type: 2, match: 3, away: 5}
  // ]
  const { Match } = store;
  const {matchId} = Match
  if (!matchId) {
    return false;
  }
  const { id, stats } = data;
  if (matchId === id && stats.length > 0) {
    Match.setStatsList(stats);
  }
};

const mixinCompetitionOdds = (matchRecentOdds, oldOdds = [], matchId) => {
  let cloneList = _.cloneDeep(oldOdds);
  if(!cloneList || cloneList.length === 0) {
    return []
  }
  // latest odd
  const validOddList = matchRecentOdds[matchId] // []
  if(validOddList && validOddList.length > 0) {
      // const newOddsMaps = validOddList.reduce((acc, cur) => {
      //   const { companyId } = cur;
      //   acc[companyId] = cur;
      //   return acc;
      // }, {});
      // for (let i = 0; i < cloneList.length; i++) {
      //   const item = cloneList[i];
      //   if (newOddsMaps[item.companyId]) {
      //     cloneList[i] = {
      //       ...newOddsMaps[item.oddsType],
      //       lastOddsData: item.oddsData
      //     };
      //   }
      // }
      // 循环添加
      for(let v of validOddList) {
        const {companyId, oddsType} = v
        for (let i = 0; i < cloneList.length; i++) {
            const item = cloneList[i];
            if (companyId === item.companyId) {
              for(let j = 0; j < item.companyOdds.length; j++) {
                if(oddsType === item.companyOdds[j].oddsType) {
                  item.companyOdds[j].odds.unshift(v)
                }
              }
            }
          }
      }
  }
  return cloneList;
}

const syncMatchOdds = (matchRecentOdds) => {
  const { Match } = store;
  const { matchOdds, matchId } = Match;
  const cloneList = mixinCompetitionOdds(matchRecentOdds, matchOdds, matchId);
  Match.setMatchOdds(cloneList);
};

export const matchDataControllerType = {
  score: 'score',
  stats: 'stats',
  incidents: 'incidents',
  syncMatchOdds: 'syncMatchOdds',
};

const matchDataController = {
  [matchDataControllerType.score]: syncmatchScore,
  [matchDataControllerType.stats]: syncStats,
  [matchDataControllerType.incidents]: syncIncidents,
  [matchDataControllerType.syncMatchOdds]: syncMatchOdds,
};

export default matchDataController;
