import { inject, observer } from 'mobx-react';
import React from 'react';

import { getMatchFutureList, getMatchHistoryList, getMatchRecentList } from 'iscommon/api/match';

import CommonH2HTab from '@/components/Common/H2HTab';

interface Props {
  store?: any;
}

const H2HTab: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: {
        Match: { matchHeaderInfo },
      },
    } = props;
    const { competition, homeTeam, awayTeam } = matchHeaderInfo;

    return (
      <CommonH2HTab
        homeTeam={homeTeam}
        awayTeam={awayTeam}
        getMatchFutureList={getMatchFutureList}
        getMatchHistoryList={getMatchHistoryList}
        getMatchRecentList={getMatchRecentList}
        competition={competition}
      />
    );
  }),
);

export default H2HTab;
