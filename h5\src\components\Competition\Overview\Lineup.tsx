import { pxToVw } from '@/utils/vw';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>er } from 'antd-mobile';
import { DownFill } from 'antd-mobile-icons';
import { DownOutline } from 'antd-mobile-icons'
import { getBestLineupDetails } from 'iscommon/api/competition';
import { translate } from 'iscommon/i18n/utils';
import { inject, observer } from 'mobx-react';
import React, { ReactNode, useEffect, useState } from 'react';

import { PlayerLink } from '@/components/Common/Football/Link';

import SectionTitle from './SectionTitle';

import styles from './Lineup.less';

import './Lineup.less'

interface PlayerItem {
  locationX: number;
  locationY: number;
  player: {
    id: string;
    logo: string;
    shortName: string;
  };
  team: {
    logo: string;
  };
  rating: number;
}

interface LineUpItem {
  teamLogo: string;
  playerId: string;
  playerName: string;
  playeyLogo: string;
  rating: string;
  locationX: number;
  locationY: number;
}

const getBadgeBackgroundColor = (count: number) => {
  if (count >= 8.0 && count <= 10.0) {
    return '#00ADC4';
  } else if (count >= 7.0 && count <= 7.9) {
    return '#1EC853';
  } else if (count >= 6.6 && count <= 6.9) {
    return '#FFAE0F';
  } else if (count >= 6.0 && count <= 6.5) {
    return '#F08022';
  } else if (count >= 0.1 && count <= 5.9) {
    return '#ED5454';
  }
};

const Lineup: React.FC<any> = inject('store')(
  observer((props: any) => {
    // direction : vertical || horizontal || ;
    const {
      width = '100%',
      direction = 'vertical',
      store: { Competitions },
    } = props;
    const [details, setDetails] = useState<{ detail: PlayerItem[]; name: string }[]>([]);
    const [round, setRound] = useState(null);
    const [visible, setVisible] = useState(false);
    const [lineup, setLineUp] = useState<LineUpItem[]>([]);
    const weekTopText = translate('weekTop');
    const roundText = translate('round');

    const {
      competitionId,
      currentSeason: { id: seasonId },
    } = Competitions;

    useEffect(() => {
      if (round) {
        const item = details.filter((item) => item.name === round)[0]?.detail;
        if (item?.length > 0) {
          setLineUp(
            item.map((d) => {
              const {
                player: { logo, id, shortName },
                team: { logo: teamLogo },
                locationX,
                locationY,
                rating,
              } = d;
              return {
                teamLogo,
                playerId: id,
                playeyLogo: logo,
                playerName: shortName,
                rating: (rating / 100).toFixed(1),
                locationX,
                locationY,
              };
            }),
          );
        }
      }
    }, [details, round]);

    useEffect(() => {
      if (seasonId && competitionId) {
        getBestLineupDetails({
          seasonId,
          competitionId,
        }).then(({ details }: any) => {
          if (details?.length > 0) {
            setDetails(details);
            setRound(details[0].name);
          }
        });
      }
    }, [competitionId, seasonId]);

    if (details.length === 0) return null;

    const columns: any[] = details.map((item: any) => {
      const { name } = item;
      return {
        label: `${roundText} ${name}`,
        value: name,
        key: name,
      };
    });

    const onConfirm = (value: any[]) => {
      setRound(value[0]);
    };

    // const renderOptions = (): ReactNode => {
    //   return (
    //     <>
    //       <div className={styles.round} onClick={() => setVisible(true)}>
    //         <div className={styles.roundText}>
    //           {roundText} {round}
    //         </div>
    //         <DownFill fontSize={12} color="#999" />
    //       </div>
    //       <Picker visible={visible} columns={[columns]} value={[round]} onClose={() => setVisible(false)} onConfirm={onConfirm} />
    //     </>
    //   );
    // };

    return (
      <div className='competition-overview-lineup-container'>
        <div className='container-title'>
          {weekTopText}
          <>
            <Button className='lineup-header-btn' size="small" onClick={() => setVisible(true)}>
              <span className='btn-content'>
                <span className='btn-value'>{roundText} {round}</span>
                <DownOutline className='down-icon'/>
              </span>
            </Button>
            <Picker
              columns={[columns]}
              visible={visible}
              onClose={() => setVisible(false)}
              onConfirm={onConfirm}
            />
          </>
        </div>
        <div className={`container-body ${direction}-container`} style={{ width }}>
          {lineup.length && lineup.map((item: LineUpItem) => (
            <div key={item.playerId} className='player-box' style={{ bottom: pxToVw(item.locationY), left: pxToVw(item.locationX) }} >
              <PlayerLink className='player-image-box'playId={item.playerId}>
                <img src={item.playeyLogo} className='player-icon' />
                <i className='team-icon' style={{ backgroundImage: `url(${item.teamLogo})`, }}/>
                {(parseFloat(item.rating) > 0) && (
                  <Badge className='rating-box' content={parseFloat(item.rating || '0').toFixed(1)} style={{ background: getBadgeBackgroundColor(parseFloat(item.rating || '0')) }}/>
                )}
                {/* <Badge className='rating-box' style={{ background: getBadgeBackgroundColor(parseFloat(item.rating || 0)) }}>{item.rating} */}
              </PlayerLink>
              <span className={`player-name text-overflow`}>{item.playerName}</span>
            </div>
          ))}
        </div>
      </div>
      // <>
      //   <SectionTitle leftTitle={weekTopText} rightContent={renderOptions} />
      //   <div
      //     className={styles[`${direction}_container`]}
      //     style={{
      //       width,
      //     }}
      //   >
      //     {lineup.length &&
      //       lineup.map((item: LineUpItem) => (
      //         <div
      //           key={item.playerId}
      //           className={styles.player_box}
      //           style={{ bottom: pxToVw(item.locationY), left: pxToVw(item.locationX) }}
      //         >
                // <PlayerLink className={styles.player_img_box} playId={item.playerId}>
                //   <img src={item.playeyLogo} className={styles.player_img} alt="" />
                //   <i
                //     className={styles.team_img}
                //     style={{
                //       backgroundImage: `url(${item.teamLogo})`,
                //     }}
                //   ></i>
                //   <span className={styles.rating_box}>{item.rating}</span>
                // </PlayerLink>
      //           <span className={`${styles.player_name} text-overflow fs-10-center`}>{item.playerName}</span>
      //         </div>
      //       ))}
      //   </div>
      // </>
    );
  }),
);

export default React.memo(Lineup);
