import { FullscreenOutlined, WindowsFilled } from '@ant-design/icons';
import { inject, observer } from 'mobx-react';
import { useEffect, useState } from 'react';

import { getCompetitionList } from 'iscommon/api/home';
import { getGameVideoInfo } from 'iscommon/api/match';
import { getLiveUrl } from 'iscommon/utils/live';
import { isPluginMode } from '@/layouts/plugin';
import LiveAndVideoCard from './LiveAndVideoCard';
import LiveFooterData from './LiveFooterData';
import { momentTimeZone } from 'iscommon/utils';
import showLoginModal from '@/components/Common/LoginModal';
import styles from './LiveAndVideo.less';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import WidgetBox from '@/components/Common/Widget';

export const requestFullscreen = () => {
  const liveStream = document.getElementById('liveStream');
  if (!document.fullscreenElement && liveStream && liveStream.requestFullscreen) {
    liveStream.requestFullscreen();
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen();
    }
  }
};

const LiveStream = inject('store')(
  observer((props: any) => {
    const {
      store: { WebConfig },
      videoLink,
    } = props;

    const pluginMode = isPluginMode(); // ✅ detect plugin mode
    let {
      userData: { isLogin },
    } = WebConfig;

    const loginText = useTranslateKeysToMaps(['Login', 'LoginTips']);
    const [expired, setExpired] = useState(false);

    const getQueryParam = (key: string): string | null => {
      const params = new URLSearchParams(window.location.search);
      return params.get(key);
    };
    
    useEffect(() => {
      if (pluginMode) {
        const hostFromQuery = getQueryParam('host');
        const shouldSkipExpiry = hostFromQuery?.includes('www.gamebetreview.com');    
    
        if (shouldSkipExpiry) {
          setExpired(false)
          return; // ✅ Skip expiry logic for this domain
        }
        
        const now = Date.now();
        const expiryTimestampKey = 'igscorePluginExpiry';
        const savedExpiry = localStorage.getItem(expiryTimestampKey);
    
        let expiryTime = savedExpiry ? parseInt(savedExpiry) : now + 5 * 60 * 1000;
    
        // If no expiry was previously saved, save it now
        if (!savedExpiry) {
          localStorage.setItem(expiryTimestampKey, expiryTime.toString());
        }
    
        const remaining = expiryTime - now;
    
        if (remaining <= 0) {
          setExpired(true);
        } else {
          const timeout = setTimeout(() => {
            setExpired(true);
          }, remaining);
    
          return () => clearTimeout(timeout);
        }
      }
    }, [pluginMode]);

    useEffect(() => {
      if ((isLogin || pluginMode) && videoLink) {
        window.gtag('event', 'stream_module_view', { platform: 'web' });
      }
    }, [isLogin, pluginMode, videoLink]);

    useEffect(() => {
      if (isLogin || pluginMode) {
        document.addEventListener(
          'keydown',
          (e) => {
            if (e.key === 'Enter') {
              requestFullscreen();
            }
          },
          false,
        );
      }
    }, [isLogin, pluginMode]);

    if (pluginMode && expired) {
      return (
        <div className={styles.liveContent}>
          <div className={styles.box}>
            <div className={styles.text}>Enjoy full live streaming experience on our mobile app.</div>
            <a
              href="https://onelink.to/igscore-newapp"
              target="_blank"
              rel="noopener noreferrer"
              className={styles.button}
            >
              Download Now
            </a>
          </div>
        </div>
      );
    }
    
    if (isLogin || pluginMode) {
      // running
      if (videoLink) {
        return (
          <div className={styles.liveContent}>
            <iframe id="liveStream" src={videoLink} width="100%" height="100%" />
          </div>
        );
      }
      return (
        <div className={styles.liveContent}>
          <div className={styles.noData}>
            <i className="iconfont icon-nodata" />
            <div className={styles.noDataText}>No Data</div>
          </div>
        </div>
      );
    }
    return (
      <div className={styles.liveContent}>
        <div className={styles.box}>
          <div className={styles.text}>{loginText.LoginTips}</div>
          <div
            className={styles.button}
            onClick={() => {
              showLoginModal('stream');
            }}
          >
            {loginText.Login}
          </div>
        </div>
      </div>
    );
  }),
);

const Animation = inject('store')(
  observer((props: any) => {
    const {
      store: { Match },
    } = props;
    const { matchId } = Match;
    const src = getLiveUrl(matchId);

    useEffect(() => {
      if (src) {
        window.gtag('event', 'animation_module_view', { platform: 'web' });
      }
    }, [src]);

    if (!src) {
      return null;
    }

    return (
      <div className={styles.liveContent}>
        <iframe src={src} />
      </div>
    );
  }),
);

const LiveVideoTitle = inject('store')(
  observer((props: any) => {
    const {
      store: { Match },
      showFullscreen = false,
    } = props;
    const { matchHeaderInfo } = Match;
    const { competition, matchTime } = matchHeaderInfo || {};
    return (
      <div className={styles.title}>
        <span className={styles.text}>
          {competition.name} {momentTimeZone(matchTime)}
        </span>
        {showFullscreen && (
          <FullscreenOutlined className={styles.fullscreen} style={{ color: '#fff', fontSize: 30 }} onClick={requestFullscreen} />
        )}
      </div>
    );
  }),
);

const LiveAndVideo = inject('store')(
  observer((props: any) => {
    const {
      store: { Match, WebConfig },
    } = props;
    const { showVideo } = WebConfig;
    const { matchId, matchHeaderInfo } = Match;
    const [tab, setTab] = useState(-1);
    const [hasVideo, setHasVideo] = useState(false);
    const [videoLink, setVideoLink] = useState('');
    const labelMaps = useTranslateKeysToMaps(['Animation', 'LiveStream']);
    const pluginMode = isPluginMode();

    console.log('🔍 LiveAndVideo Debug:', {
      pluginMode,
      matchId,
      hasVideo,
      tab,
      showVideo,
      matchHeaderInfo: !!matchHeaderInfo
    });

    useEffect(() => {
      console.log('🎬 Video effect triggered:', { matchId, matchHeaderInfo: !!matchHeaderInfo });

      if (matchId && matchId !== -1 && matchHeaderInfo) {
        const getVideoLink = async () => {
          try {
            console.log('📡 Fetching video info for matchId:', matchId);
            const { link, ch }: any = await getGameVideoInfo(matchId);
            console.log('📥 Video info response:', { link, ch, statusId: matchHeaderInfo.statusId });

            const hasVideoContent = link || (ch && matchHeaderInfo.statusId === 1);
            console.log('🎥 Has video content:', hasVideoContent);

            setHasVideo(hasVideoContent);
            Match.setShouldShowVideo(!!link);

            if (link && showVideo) {
              console.log('✅ Setting tab to 1 (LiveStream)');
              setTab(1);
              setVideoLink(link);
              Match.setShouldShowVideo(true);
            } else {
              console.log('✅ Setting tab to 0 (Animation)');
              setTab(0);
            }
          } catch (e) {
            console.error('❌ Error fetching video info:', e);
            setTab(0);
            Match.setShouldShowVideo(false);
          }
        };
        getVideoLink();
      } else {
        console.log('⚠️ Video effect skipped - missing requirements:', {
          matchId,
          matchIdValid: matchId !== -1,
          hasMatchHeaderInfo: !!matchHeaderInfo
        })
      }
    }, [matchId, matchHeaderInfo, Match, showVideo]);
    console.log('🎨 Render conditions:', {
      hasVideo,
      tab,
      showTabsCondition: hasVideo && tab > -1,
      showVideo,
      labelMaps
    });

    return (
      <div id="footballLivePlugin" className={styles.liveAndVideo}>
        {hasVideo && tab > -1 && (
          <div className={styles.content}>
            {showVideo && (<div key={2} className={`${styles.item} ${tab === 1 ? styles.current : ''}`} onClick={() => setTab(1)}>
              {labelMaps.LiveStream}
            </div>)}
            <div key={1} className={`${styles.item} ${tab === 0 ? styles.current : ''}`} onClick={() => setTab(0)}>
              {labelMaps.Animation}
            </div>
          </div>
        )}
        <LiveVideoTitle showFullscreen={tab === 1} />
        {tab === 1 && <LiveStream videoLink={videoLink} />}
        {tab === 0 && <Animation />}
        {!pluginMode && <LiveFooterData />}
        {pluginMode && <LiveAndVideoCard />}
        <WidgetBox titleColor="#fff" withOuter pluginId="footballLivePlugin" title="Live" titlePosition="right" tab="MatchLive" />
      </div>
    );
  }),
);

export default LiveAndVideo;
