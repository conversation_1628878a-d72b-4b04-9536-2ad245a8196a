.basketball-stats-table-container {
  margin-top: 16px;
  width: 100%;
  overflow-x: scroll;

  .custom-stats-table {
    width: 100%;
    display: flex;
    flex-direction: column;
    border-collapse: collapse;
    margin-bottom: 10px;

    .table-header {
      display: flex;
      color: #fff;
      background-color: #2c2c2c;
      font-weight: bold;
      border-bottom: 1px solid #121212;

      .header-cell {
        padding: 8px;
        text-align: center;
        background-color: #2c2c2c;
      }

      .player-cell {
        position: sticky; 
        left: 0;
        z-index: 11; 
        width: 250px;
        flex-shrink: 0;
        background-color: inherit;
        text-align: left;
      }

      .header-cell:not(.player-cell) {
        padding: 8px;
        flex: 1;
        min-width: 10%;
      }
    }

    .table-body {
      .table-row {
        display: flex;
        align-items: center;
        background-color: #121212;
        border-bottom: 1px solid #2c2c2c;
        color: #fff;

        .table-cell {
          text-align: center;
          padding: 8px;
          background-color: #121212;
        }

        .player-cell {
          display: flex;
          flex-direction: row;
          align-items: center;
          position: sticky;
          left: 0;
          z-index: 10;
          background-color: inherit;
          padding: 8px;
          width: 250px;
          flex-shrink: 0;

          .player-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-right: 10px;
            background-size: cover;
          }

          .player-info {
            display: flex;
            flex-direction: column;
            text-align: left;

            .player-name {
              font-size: 20px;
              font-weight: 600;
              color: #fff;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .player-minutes {
              font-size: 18px;
              color: #fff;
            }
          }
        }

        .table-cell:not(.player-cell) {
          padding: 8px;
          flex: 1;
          min-width: 10%;
        }
      }
    }
  }
}