import { List } from 'antd';
import { FallbackPlayerImage } from 'iscommon/const/icon';
import React, { useEffect, useMemo, useRef } from 'react';
import styles from './ChatList.less';

interface UserItem {
  sendTime: string;
  serial: string;
  message: string;
  user: {
    avatar: string;
    id: number;
    name: string;
  };
  key: number;
}

const fakeDataUrl = 'https://randomuser.me/api/?results=20&inc=name,gender,email,nat,picture&noinfo';
const ContainerHeight = 400;

const ChatList = (props: { data: UserItem[] }) => {
  const d = useMemo(() => {
    const ret = props.data?.map((item, index) => {
      item.key = index;
      return item;
    });
    return ret;
  }, [props.data]);

  const len = d.length;

  const chatRef = useRef<Element>(null);

  useEffect(() => {
    const current = chatRef.current;
    const top = len * 47 - 552;
    if (current && top > 0) {
      setTimeout(() => {
        current.scrollTop = top;
      }, 100);
      // current.offsetTop = -100
      // current.scrollTo({
      //   index: d.length - 1,
      //   align: 'bottom',
      // });
    }
  }, [len]);

  const decodeURI = (uri: string) => {
    let result = uri;
    try {
      result = decodeURIComponent(uri);
    } catch (e) {
      result = unescape(uri);
    }
    return result;
  };

  const renderItem = (item: UserItem, index: number) => {
    const username = item.user?.name.replace(/[^%a-zA-Z0-9 ]/g, '');
    return (
      <List.Item className={styles.chatItem} key={index}>
        <div className={styles.chatIcon} style={{ backgroundImage: `url(${item.user?.avatar || FallbackPlayerImage})` }}></div>
        <div className={styles.text}>
          <span className={styles.name}>{decodeURI(username)}：</span>
          {decodeURI(item.message)}
        </div>
      </List.Item>
    );
  };

  // const Chat = React.forwardRef((props, ref) => (
  //   <VirtualList data={d} height={552} itemHeight={47} itemKey={(item: UserItem) => item.key} ref={ref}>
  //     {(item: UserItem, index) => (
  //       <List.Item className={styles.chatItem} key={index}>
  //         <div className={styles.chatIcon} style={{ backgroundImage: `url(${item.user?.avatar || FallbackPlayerImage})` }}></div>
  //         <div className={styles.text}>
  //           <span className={styles.name}>{decodeURIComponent(item.user?.name)}：</span>
  //           {decodeURIComponent(item.message)}
  //         </div>
  //       </List.Item>
  //     )}
  //   </VirtualList>
  // ));

  return (
    <div className={styles.chatList} ref={chatRef}>
      <div>{d.map(renderItem)}</div>
    </div>
  );
};

export default React.memo(ChatList);
