// @ts-nocheck
import { useMemo, useRef } from 'react';

import GlobalUtils, { GlobalSportPathname, GlobalSportTypeEnum, MenuBarList, PageTabs } from 'iscommon/const/globalConfig';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { HomeMetaMaps } from 'iscommon/const/seo';
import { GlobalConfig } from 'iscommon/const/globalConfig';
const getFooterKey = (sportType = '') => `FooterContent${sportType}`;

const TRANSLATE_FOOTER_KEY = MenuBarList.map(({ sportType }) => getFooterKey(sportType));

const getMetaInfo = (sportType = GlobalSportTypeEnum.Football) => {
  if (HomeMetaMaps[sportType]) {
    return HomeMetaMaps[sportType];
  }
  return HomeMetaMaps[GlobalSportTypeEnum.Football];
};

export const useFooterTranslateValByKey = () => {
  const labelMaps = useTranslateKeysToMaps(TRANSLATE_FOOTER_KEY);
  const current = MenuBarList.find((i) => i.pathname === GlobalConfig.pathname);
  const currentLabel = current ? labelMaps[getFooterKey(current.sportType)] : '';
  return {
    labelMaps,
    currentLabel,
  };
};

/**
 *
 * @param pageTab {value of typeof PageTabs}
 * @param id {string | ''}
 * @param name {string}
 * @param getExtraList {{ name?: string, url?: string, meta?: any }[] | () => ({ name?: string, url?: string, meta?: any }[])}
 * @param options {{ competition: { name: string; id: string } | null }}
 */
export const useCrumbList = (pageTab = '', id = '', name = '', getExtraList = [], options = {}) => {
  const currentMenuData = useRef(GlobalUtils.getCurrentMenuData());
  const labelMaps = useTranslateKeysToMaps([currentMenuData.current.sportType, 'LiveScore', name]);
  const isFootball = useMemo(() => currentMenuData.current.pathname === GlobalSportPathname.football, []);

  const competitionCrumb = useMemo(() => {
    if (options && options.competition) {
      const { id: competitionId, name: competitionName, target } = options.competition || {};
      return {
        name: competitionName,
        url: !!competitionId && GlobalUtils.getPathname(PageTabs.competition, competitionId),
        target: target || '_self',
      };
    }
    return null;
  }, [options.competition]);

  const teamCrumb = useMemo(() => {
    if (options && options.team) {
      const { id: teamId, name: teamName, target } = options.team || {};
      return {
        name: teamName,
        url: !!teamId && GlobalUtils.getPathname(PageTabs.team, teamId),
        target: target || '_self',
      };
    }
    return null;
  }, [options.team]);

  const crumbs = useMemo(() => {
    const { sportType, pathname } = currentMenuData.current;
    const metaName = labelMaps[sportType];
    let result = [
      {
        name: `IGScore ${metaName} ${labelMaps.LiveScore}`,
        url: isFootball ? '/' : `/${pathname}/`,
        // meta: getMetaInfo(sportType),
      },
    ];

    if (competitionCrumb) {
      result.push(competitionCrumb);
    }

    if (teamCrumb) {
      result.push(teamCrumb);
    }

    if (name) {
      result.push({
        name: labelMaps[name],
        url: pageTab && id ? GlobalUtils.getPathname(pageTab, id) : null,
      });
    }

    if (Array.isArray(getExtraList) && getExtraList.length) {
      result = [...result, ...getExtraList];
    } else if (typeof getExtraList === 'function') {
      const values = getExtraList({ currentMenuData: currentMenuData.current });
      result = [...result, ...values];
    }

    return result;
  }, [id, name, labelMaps, getExtraList, competitionCrumb]);

  return { crumbs };
};
