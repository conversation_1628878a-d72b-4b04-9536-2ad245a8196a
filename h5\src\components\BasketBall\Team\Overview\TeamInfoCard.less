.basketball-team-info-card-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .container-body {
    background: #1e1e1e;
    padding: 10px;

    .info-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 38px;
      margin-bottom: 12px;
      border-bottom: 1px solid #EEEEEE;

      .info-label,
      .info-text {
        font-size: 22px;
        font-weight: 700;
        color: #fff;
        line-height: 22px;
      }

      &.no-border {
        border-bottom: none;
        margin-bottom: 0;
      }
    }

    .league-container {
      display: grid;
      grid-template-columns: repeat(2, 1fr);

      .competition-card {
        display: flex;
        background: #121212;
        flex-direction: column;
        align-items: center;
        padding: 10px;
        border-radius: 10px;
        margin: 10px;
        text-align: center;
        transition: background-color 0.3s ease;

        &:hover {
          background-color: lighten(#121212, 30%);
        }

        .competition-icon {
          height: 96px;
          width: 96px;
          border-radius: 50%;
          object-fit: contain;
          vertical-align: middle;
          margin: 10px;
          margin: 1.333vw;
        }

        .competition-name {
          color: #fff;
        }
      }
    }
  }
}