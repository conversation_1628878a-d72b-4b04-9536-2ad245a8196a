import StandingsIntegral from '@/components/Competition/Standings/StandingsIntegral';
import Loading from '@/components/Loading';
import { getMatchStandings } from 'iscommon/api/match';
import { translate, useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { inject, observer } from 'mobx-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import styles from './index.less';

import './StandingTab.less'
import { Badge, Button } from 'antd-mobile';
import { FallbackImage } from 'iscommon/const/icon';
import { Link } from 'umi';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';

const Standings = inject('store')(
  observer((props) => {
    const {
      store: { Match },
      withHeader = true,
    } = props;
    const { matchId } = Match;
    const [tablesList, setTablesList] = useState([]);
    const [promotions, setPromotions] = useState<any[]>([]);
    const [type, setType] = useState<0 | 1 | 2>(0);
    const [loading, setLoading] = useState(true);

    const labelMaps = useTranslateKeysToMaps([
      'Standings',
      'All',
      'Home',
      'Away',
      'Team',
    ]);

    const options = useMemo(
      () => [labelMaps.All, labelMaps.Home, labelMaps.Away].map((label, value) => ({ label, value })),
      [labelMaps.All, labelMaps.Away, labelMaps.Home],
    );

    const [activeButton, setActiveButton] = useState(options[0].value);

    useEffect(() => {
      getMatchStandings({ matchId, type }).then(({ tables, promotions }) => {
        setTablesList(tables || []);
        setPromotions(promotions || []);
        setLoading(false);
      });
    }, [type, matchId]);

    const onChange = useCallback((value) => {
      setActiveButton(value);
      setType(value);
    }, []);

    const getPromotionColor = (promotionId: any) => {
      const promotion = promotions.find(promo => promo.id === promotionId);
      return promotion ? promotion.color : 'transparent';
    };

    console.log('promotions123', promotions)
    console.log('tabledata123', tablesList)

    return (
      <div className='match-standing-tab-container'>
        <div className='container-title'>
          {labelMaps.Standings}
          <div className='container-title-right-aligned'>
            {options.map((option) => (
              <Button
                key={option.value}
                className={`button ${activeButton === option.value ? 'active' : ''}`}
                onClick={() => onChange(option.value)}>
                {option.label}
              </Button>
            ))}
          </div>
        </div>
        <div className='container-body'>
          <div className='custom-standing-table'>
            <div className="header-row">
              <div className="header-cell">#</div>
              <div className="header-cell">{labelMaps.Team}</div>
              <div className="header-cell">P</div>
              <div className="header-cell">GD</div>
              <div className="header-cell">Pts</div>
            </div>

            {tablesList.length !== 0 ? (
              tablesList.map((item: any, index) => {
                return (
                  item?.rows.map((row: any) => (
                    <Link className="table-row" key={row?.teamId} to={GlobalUtils.getPathname(PageTabs.team, row?.teamId)}>
                      <div className="table-cell promotion-cell">
                        <div className="promotion-indicator" style={{ backgroundColor: row?.promotionId ? getPromotionColor(row.promotionId) : 'transparent' }}></div>
                        <span>{row?.position}</span>
                      </div>
                      <div className="table-cell team-cell">
                        <img className="team-logo" src={row?.team?.logo || FallbackImage} alt={row?.team?.name} />
                        <span className='team-name'>{row?.team?.name}</span>
                      </div>
                      <div className="table-cell">{row?.total}</div>
                      <div className="table-cell">{row?.goalDiff}</div>
                      <div className="table-cell">{row?.points}</div>
                    </Link>
                  ))
                )
              })
            ) : (
              <Loading loading={loading}/> 
            )}
          </div>
          <div className='standing-promotion-container'>
            {promotions.map(promotion => (
              <div className='promotion-container' key={promotion.id}>
                <Badge className='promotion-badge' content={Badge.dot} style={{ backgroundColor: promotion.color }}/>
                {promotion.name}
              </div>
            ))}
          </div>
        </div>
      </div>
      // <Loading loading={loading} isEmpty={tablesList.length === 0}>
      //   <div className={styles.container_box}>
      //     {withHeader ? (
      //       <div className={styles.radio_container}>
      //         {radioItem.map((item: any) => (
      //           <span
      //             className={`${styles.radio_btn} ${type === item.value ? styles.active : ''}`}
      //             key={item.value}
      //             onClick={() => {
      //               setType(item.value);
      //             }}
      //           >
      //             {translate(item.label)}
      //           </span>
      //         ))}
      //       </div>
      //     ) : null}
      //     {tablesList.map((item: any, index) => (
      //       <div key={index}>
      //         <StandingsIntegral data={item.rows} type={type} promotions={promotions} isMatch />
      //       </div>
      //     ))}
      //   </div>
      // </Loading>
    );
  }),
);

export default Standings;
