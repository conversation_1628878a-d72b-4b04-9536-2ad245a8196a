import './GameCategory.less';

import { BadmintonInLiveStatusEnum, BadmintonStatusCodeEnum } from 'iscommon/const/badminton/constant';
import { CategoryIcon, FallbackImage } from 'iscommon/const/icon';
import { getBadmintonMatchTimeText, getBadmintonScoreList } from 'iscommon/utils/dataUtils';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { HomeGameTab, StatusCodeEnum } from 'iscommon/const/constant';
import { inject, observer } from 'mobx-react';
import React, { memo, useCallback, useEffect, useMemo, useRef } from 'react';

import classNames from 'classnames';
import { isEqual } from 'lodash';
import MatchLiveIcon from '@/components/MatchLiveIcon';
import { momentTimeZone } from 'iscommon/utils';

const PureMatchStatusText = React.memo(({ serverTime, matchStatus, remainSeconds, matchTime }: any) => {
  const { statusText, isIng } = useMemo(() => getBadmintonMatchTimeText({ matchStatus, remainSeconds, serverTime, matchTime }),
    [matchStatus, remainSeconds, serverTime, matchTime],
  );

  const isNotStarted = matchStatus === BadmintonStatusCodeEnum.NotStarted
  const isEnded = matchStatus === BadmintonStatusCodeEnum.End

  return (
    <span className={classNames('match-status', { live: isIng, neutral: isEnded })}>
      {isNotStarted ? momentTimeZone(matchTime, 'HH:mm') : statusText}
        <span className="match-status-text">
        {isIng ? "'" : ''}
      </span>
    </span>
  );
});

const ScoreText = ({ item }: any) => {
  const { matchStatus, scores, servingSide } = item;

  if (matchStatus === BadmintonStatusCodeEnum.NotStarted) {
    return <div className="match-score-container">-</div>;
  }

  const scoreList = getBadmintonScoreList(matchStatus, scores, servingSide, true);
  const validScores = scoreList.filter((s: any) => !s.empty && typeof s.h === 'number' && typeof s.w === 'number');
  const isLive = BadmintonInLiveStatusEnum.includes(matchStatus);

  if (validScores.length === 0) {
    return <div className="match-score-container">-</div>;
  }

  const totalScore = validScores[0];

  return (
    <div className="match-score-container">
      <span className={classNames('match-score', { 'color-c1272d': isLive })}>
        {totalScore.h}
      </span>
      <span className={classNames('match-score', { 'color-c1272d': isLive })}>
        {totalScore.w}
      </span>
    </div>
  );
};

const ScoreOrOdds = inject('store')(
  observer((props: any) => {
    const {
      store: {
        WebConfig,
        Smallball: { SmallballCommon: WebHome },
      },
      item,
    } = props;
    const { matchId, matchStatus } = item;
    const { homeCompetitionOdds, currentGameTab } = WebHome;
    const matchOdds = homeCompetitionOdds[matchId] || [];

    const euOdds = matchOdds.find((odd: any) => odd.oddsType === 'eu')?.oddsData || [];

    const previousOddsRef = useRef(euOdds);
    const colorRef = useRef(['#fff', '#fff', '#fff']);

    useEffect(() => {
      if (euOdds.length) {
        const updatedColors = euOdds.map((val: any, i: number) => {
          const newVal = Number(val);
          const oldVal = Number(previousOddsRef.current?.[i] ?? newVal);

          if (newVal > oldVal) return '#14AE5C'; // green
          if (newVal < oldVal) return '#FF3131'; // red
          return colorRef.current[i]; // retain last color
        });

        colorRef.current = updatedColors;
        previousOddsRef.current = [...euOdds];
      }
    }, [euOdds]);

    const displayOdds = WebConfig.showOdds && euOdds.length;

    if (matchStatus === StatusCodeEnum.NotStarted) {
      return displayOdds ? (
        <div className="match-odd-container">
          {[0, 2].map((i) => (
            <span key={i} className="odd" style={{ color: colorRef.current[i] }}>
              {Number(euOdds[i]).toFixed(2)}
            </span>
          ))}
        </div>
      ) : (
        <div className="match-odd-container">
          <span className="odd">-</span>
        </div>
      );
    }

    return (
      <div className="match-live-score-container">
        <ScoreText item={item} currentGameTab={currentGameTab} />
        <MatchLiveIcon vlive={item.vlive} mlive={item.mlive} />
        {displayOdds && (
          <div className="match-odd-container">
            {[0, 2].map((i) => (
              <span key={i} className="odd" style={{ color: colorRef.current[i] }}>
                {Number(euOdds[i]).toFixed(2)}
              </span>
            ))}
          </div>
        )}
      </div>
    );
  }),
);

const H5StickIcon = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Smallball: { SmallballCommon: WebHome },
      },
      competitionId,
    } = props;
    const { h5StickIdList = [], currentGameTab } = WebHome;
    const isStick = useMemo(() => h5StickIdList?.includes(competitionId), [h5StickIdList, competitionId]);

    if (currentGameTab !== HomeGameTab.All) {
      return null;
    }

    if (isStick) {
      return (
        <span
          onClick={(e) => {
            WebHome.removeH5Stick(competitionId);
            e.preventDefault();
            e.stopPropagation();
          }}
          className="icon iconfont iconSign_Sele myIcon touchArea"
          style={{ color: '#0f80da' }}
        />
      );
    }
    return (
      <span
        onClick={(e) => {
          WebHome.addH5Stick(competitionId);
          e.preventDefault();
          e.stopPropagation();
        }}
        className="icon iconfont iconSign_Def touchArea"
        style={{ color: 'rgb(131, 131, 131)' }}
      />
    );
  }),
);

export const MatchList = ({ matches }: any) => {
  return (
      matches.map((item: any) => {
        return (
          <div className='match-list-container' key={item.matchId} onClick={() => GlobalUtils.goToPage(PageTabs.match, item.matchId)}>
            <div className='match-time-container'>
              <PureMatchStatusText {...item} />
            </div>
            <div className='match-team-container'>
              <div className='match-team-info'>
                <img className='team-logo' src={item.homeTeam?.logo || FallbackImage} alt={item.homeTeam?.name} loading="lazy"/>
                <span className='team-name text-overflow'>{item.homeTeam?.name}</span>
              </div>
              <div className='match-team-info'>
                <img className='team-logo' src={item.awayTeam?.logo || FallbackImage} alt={item.awayTeam?.name} loading="lazy"/>
                <span className='team-name text-overflow'>{item.awayTeam?.name}</span>
              </div>
            </div>
            <ScoreOrOdds item={item} />
          </div>
        );
      })
  );
};

const GameCategory = memo (({ competition }: any) => {
  const { competitionName, country, category, onlineCount, matches, competitionId, curStageId, additionalCompetitionName } = competition;

  const goToCompetition = useCallback(() => {
    if (curStageId) {
      GlobalUtils.goToPage(PageTabs.competition, competitionId);
    }
  }, [competitionId, curStageId]);

  if (!matches?.length) return null;

  return (
    <div className='home-game-category-container'>
      <div className='game-category' onClick={goToCompetition}>
        <div className='game-category-left-aligned'>
          <i style={{ backgroundImage: `url(${country?.logo || (CategoryIcon as any)[category?.logo] || FallbackImage})`,}} className="category-icon" />
          <div className='competition-container'>
            <span className="competition-name">{competitionName}</span>
            <span className="country-name">{country?.name || category?.name}</span>
          </div>
        </div>
        <div className='game-category-right-aligned'>
          {onlineCount > 0 && (
            <div className="count-container">
              <span className="icon iconfont iconguanzhong icon-size"></span>
              <span className='online-count'>{onlineCount > 9999 ? '9999+' : onlineCount}</span>
            </div>
          )}
          <H5StickIcon competitionId={competitionId} />
        </div>
      </div>
      <MatchList matches={matches} />
    </div>
  );
},
(pre, next) => {
  return isEqual(pre, next);
})

export default GameCategory;
