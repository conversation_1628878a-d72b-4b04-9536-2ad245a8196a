import { Moment } from 'moment';
import React, { useEffect, useState } from 'react';

import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { momentTimeZone } from 'iscommon/utils';

import MatchListItem from '../MatchListItem';

import styles from './index.less';

interface Props {
  homeTeam: Record<string, any>;
  awayTeam: Record<string, any>;
  request: (teamId: string) => Promise<any>;
}

const MatchFutureList: React.FC<Props> = (props) => {
  const { homeTeam, awayTeam, request } = props;
  const labelMaps = useTranslateKeysToMaps(['ScheduledMatches']);

  const [homeList, setHomeList] = useState<any[]>([]);
  const [awayList, setAwayList] = useState<any[]>([]);

  useEffect(() => {
    if (homeTeam?.id) {
      request(homeTeam.id).then((res) => {
        if (res?.matches?.length) {
          setHomeList(res.matches);
        }
      });
    }
  }, [homeTeam.id, request]);

  useEffect(() => {
    if (awayTeam?.id) {
      request(awayTeam.id).then((res) => {
        if (res?.matches?.length) {
          setAwayList(res.matches);
        }
      });
    }
  }, [awayTeam.id, request]);

  return (
    <>
      <div className={styles.header}>
        <div className={styles.title}>{labelMaps.ScheduledMatches}</div>
      </div>
      <div className={styles.content}>
        <div className={styles.teamInfo}>
          <div className={styles.team}>
            <img className={styles.logo} src={homeTeam.logo} alt={homeTeam.name} loading="lazy"/>
            <div className={styles.teamName}>{homeTeam.name}</div>
          </div>
        </div>
        {homeList.map((item, index) => {
          return (
            <MatchListItem
              key={item.id + index}
              item={item}
              showResult={false}
              renderExtra={() => <div className={styles.days}>{(momentTimeZone(item.matchTime, '') as Moment).fromNow()}</div>}
            />
          );
        })}
        {!homeList?.length && <div className={styles.noData}>no data</div>}
        <div className={styles.teamInfo}>
          <div className={styles.team}>
            <img className={styles.logo} src={awayTeam.logo} alt={awayTeam.name} loading="lazy"/>
            <div className={styles.teamName}>{awayTeam.name}</div>
          </div>
        </div>
        {awayList.map((item, index) => {
          return (
            <MatchListItem
              key={item.id + index}
              item={item}
              showResult={false}
              renderExtra={() => <div className={styles.days}>{(momentTimeZone(item.matchTime, '') as Moment).fromNow()}</div>}
            />
          );
        })}
        {!awayList?.length && <div className={styles.noData}>no data</div>}
      </div>
    </>
  );
};

export default MatchFutureList;
