.basketball-competition-header-container {
  background: #1e1e1e;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 80px 32px;
  width: 100%;

  .competition-container {
    display: flex;
    flex-direction: row;
    align-items: center;

    .competition-icon {
      height: 96px;
      width: 96px;
      border-radius: 50%;
      object-fit: contain;
      vertical-align: middle;
      margin-right: 10px;
    }

    .competition-info {
      display: flex;
      flex-direction: column;

      .competition-name-container {
        display: flex;
        align-items: center;

        .competition-name {
          font-size: 28px;
          font-weight: bold;
          color: #fff;
        }
      }

      .competition-detail {
        display: flex;
        align-items: center;

        .country-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin-right: 10px;
          overflow: hidden;
          background-size: cover;
        }

        .country-name {
          font-size: 24px;
          color: #fff;
        }
      }
    }
  }

  .competition-header-btn {
    background: transparent;
    color: #fff;
    border-radius: 32px;
    width: 150px;
  }
}