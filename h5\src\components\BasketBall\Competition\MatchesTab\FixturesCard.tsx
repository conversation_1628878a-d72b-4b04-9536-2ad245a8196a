import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "antd-mobile";
import { getCompetitionMatches, getCompetitionTeam } from "iscommon/api/basketball/basketball-competition";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { inject, observer } from "mobx-react";
import { useEffect, useState } from "react";
import { DownOutline, LeftOutline, RightOutline } from 'antd-mobile-icons'
import './FixturesCard.less'
import FavoriteIcon from "@/components/Common/FavoriteIcon";
import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";
import { FallbackImage } from "iscommon/const/icon";
import moment from "moment";
import { Link } from "umi";
import Loading from "@/components/Loading";
import { StatusCodeEnum } from "iscommon/const/constant";

interface Props {
  store?: any;
}

const Pagination = ({ pageInfo, onPageChange }: any) => {
  const { pageNum, pageSize, total } = pageInfo;
  const totalPages = Math.ceil(total / pageSize);

  const handlePrev = () => {
    if (pageNum > 1) {
      onPageChange(pageNum - 1);
    }
  };

  const handleNext = () => {
    if (pageNum < totalPages) {
      onPageChange(pageNum + 1);
    }
  };

  return (
    <div className="pagination">
      <Button onClick={handlePrev} disabled={pageNum === 1} size="mini" shape="rounded">
        <LeftOutline />
      </Button>
      <span>{`Page ${pageNum} of ${totalPages}`}</span>
      <Button onClick={handleNext} disabled={pageNum === totalPages} size="mini" shape="rounded">
        <RightOutline />
      </Button>
    </div>
  );
};

const FixturesCard: React.FC<Props> = inject('store')(
  observer((props: Props) => {
    const {
      store: {
        Basketball: {
          Competitions: {
            competitionId,
            currentSeason: { id: seasonId },
          },
        },
      },
    } = props;

    const labelMaps = useTranslateKeysToMaps([
      'Matches',
      'SelectBasketBallTeam',
      'Teams',
      'Schedule',
      'Result',
    ]);

    const [matches, setMatches] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);
    const [currentTeam, setCurrentTeam] = useState<string>();
    const [teamList, setTeamList] = useState<any[]>([]);
    const [teamVisible, setTeamVisible] = useState<boolean>(false);
    const [selectedDate, setSelectedDate] = useState<any[]>([])
    const [selectedTeamLabel, setSelectedTeamLabel] = useState<string>(labelMaps.SelectBasketBallTeam); 
    const [calendarVisible, setCalendarVisible] = useState<boolean>(false);

    const [pageInfo, setPageInfo] = useState<any>({
      pageNum: 1,
      pageSize: 10,
      total: 0,
    });

    useEffect(() => {
      getCompetitionTeam({
        competitionId,
      }).then(({ teams }: any) => {
        if (teams && teams.length > 0) {
          teams.sort((a: any, b: any) => a.name.charCodeAt(0) - b.name.charCodeAt(0));
          setTeamList(teams);
        }
      });
    }, [competitionId]);
    
    useEffect(() => {
      if (competitionId && seasonId) {
        setLoading(true);

        getCompetitionMatches({
          competitionId,
          seasonId,
          matchKind: null,
          stageId: null,
          teamId: currentTeam
        }).then((res: any) => {
          let matchList = res?.matches || [];

          if (selectedDate) {
            const startOfMonth = moment(selectedDate).startOf('month').unix();
            const endOfMonth = moment(selectedDate).endOf('month').unix();
            matchList = matchList.filter(
              (match: any) => match.matchTime >= startOfMonth && match.matchTime <= endOfMonth
            );
          }

          matchList.sort((a: any, b: any) => a.matchTime - b.matchTime);
          const pageNum = findPageForUpcomingMatch(matchList, pageInfo.pageSize);

          setPageInfo((prev: any) => ({
            ...prev,
            pageNum,
            total: matchList.length,
          }));

          setMatches(matchList);
          setLoading(false);
        }).catch((error) => {
          setLoading(false);
        }); 
      }
    }, [competitionId, seasonId, currentTeam, selectedDate]);

    const handleTeamConfirm = (value: any) => {
      const selectedTeam = teamList.find((team: any) => team.id === value[0]);
      if (selectedTeam) {
        setCurrentTeam(selectedTeam.id)
        setSelectedTeamLabel(selectedTeam.name)
      }
      setTeamVisible(false)
    }

    const onDateChange = (date: any) => {
      setSelectedDate(date);
      setCalendarVisible(false);
    };

    const formatDate = (date: any) => {
      return date ? moment(date).format('MMM YY') : moment().format('MMM YY');
    };

    const onPageChange = (newPage: number) => {
      setPageInfo((prev: any) => ({
        ...prev,
        pageNum: newPage,
      }));
    };

    const findPageForUpcomingMatch = (matches: any[], pageSize: number) => {
      const index = matches.findIndex(
        (match) => match.matchStatus === StatusCodeEnum.NotStarted
      );
    
      if (index === -1) return 1;
    
      return Math.floor(index / pageSize) + 1;
    };

    const startIdx = (pageInfo.pageNum - 1) * pageInfo.pageSize;
    const currentMatches = matches.slice(startIdx, startIdx + pageInfo.pageSize);

    return (
      <div className="basketball-competition-match-card-container">
        <div className="container-title">
          {labelMaps.Schedule}&nbsp;&&nbsp;{labelMaps.Result}
          <>
            <Button className='custom-btn-team' size="small" onClick={() => setTeamVisible(true)}>
              <span className='btn-content'>
                <span className="btn-value">{selectedTeamLabel}</span>
                <DownOutline className='down-icon'/>
              </span>
            </Button>
            <Picker
              columns={[
                teamList.map((item: any) => ({
                  key: item.id,
                  value: item.id,
                  label: item.name,
                }))
              ]}
              visible={teamVisible}
              onConfirm={handleTeamConfirm}
              onClose={() => setTeamVisible(false)}
            />
          </>
        </div>
        <div className="container-body">
          <div className="picker-container">
            <>
              <Button className='team-month-btn' size="small" onClick={() => setCalendarVisible(true)}>
                <span className='btn-content'>
                  <span className='btn-value'>{formatDate(selectedDate)}</span>
                  <DownOutline className='down-icon'/>
                </span>
              </Button>
              <DatePicker
                className='game-category-calendar-picker'
                visible={calendarVisible}
                precision='month'
                onConfirm={onDateChange}
                onClose={() => setCalendarVisible(false)}
              />
            </>
          </div>
          <Loading loading={loading} isEmpty={currentMatches.length === 0}>
            {currentMatches.map((match: any) => {
              const calculatedHomeScore = match.homeScores.reduce((acc: number, score: number) => acc + score, 0);
              const calculatedAwayScore = match.awayScores.reduce((acc: number, score: number) => acc + score, 0);
              return (
                <Link to={GlobalUtils.getPathname(PageTabs.match, match.id)} key={match.id} className="match-list-container">
                  <div className="match-time-container">
                    <span className="match-time">{moment.unix(match.matchTime).format('DD/MMM')}</span>
                  </div>
                  <div className='match-team-container'>
                    <div className='match-team-info'>
                      <img className='team-logo' src={match.homeTeam?.logo || FallbackImage} alt={match.homeTeam?.name} loading="lazy"/>
                      <span className='team-name text-overflow'>{match.homeTeam?.name}</span>
                    </div>
                    <div className='match-team-info'>
                      <img className='team-logo' src={match.awayTeam?.logo || FallbackImage} alt={match.awayTeam?.name} loading="lazy"/>
                      <span className='team-name text-overflow'>{match.awayTeam?.name}</span>
                    </div>
                  </div>
                  {match.matchStatus === StatusCodeEnum.NotStarted ? (
                    <div className="match-score-container">
                      <span>-</span>
                    </div>
                  ) : (
                    <div className="match-score-container">
                      <span className="match-score">{calculatedHomeScore}</span>
                      <span className="match-score">{calculatedAwayScore}</span>
                    </div>
                  )}
                </Link>
              )
            })}
            <Pagination pageInfo={pageInfo} onPageChange={onPageChange} />
          </Loading>
        </div>
      </div>
    );
  }),
);

export default FixturesCard;