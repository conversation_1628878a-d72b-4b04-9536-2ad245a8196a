// @ts-nocheck
import _ from 'lodash';

import store from 'iscommon/mobx';
import { getArrayFromString } from 'iscommon/utils';


const delaySyncHomeScore = (data) => {

  const {id, score} = data

  if(!score || score.length === 0) return

  const { Smallball: { SmallballCommon: WebHome} } = store;
  const homeCompetitions = _.cloneDeep(WebHome.homeCompetitions);
  const allStatusMap = _.cloneDeep(WebHome.latestMatchStatus);
  if (!homeCompetitions || homeCompetitions.length === 0) {
    return false;
  }

//   Score field description
// [
// 	"4jwq2pf61y04m0v",//Match id
// 	53,//Match status, please refer to Status code->Match state
// 	2,//Serving side, 1. Home, 2. Away
// 	{
// 		"p1": [//p* - Single set score
// 			6,//Home team score
// 			7 //Away team score
// 		],
// 		"p2": [
// 			6,//Home team score
// 			4 //Away team score
// 		],
// 		"p3": [
// 			3,
// 			3
// 		],
// 		"x1": [//x* - <PERSON><PERSON> 7 points
// 			11,
// 			13
// 		],
// 		"pt": [//pt - Real-time score
// 			"30",
// 			""
// 		],
// 		"ft": [//Total score
// 			1,
// 			1
// 		]
// 	}
// ]
  for (let c of homeCompetitions) {
    for (let match of c.matches) {
      const { matchId } = match;
      if (matchId === id) {
        // calculatedAwayScore, calculatedHomeScore homeScores awayScores matchStatus
        const matchStatus = score[1];
        match.matchStatus = matchStatus;
        allStatusMap[matchId] = matchStatus
        match.scores = score[3];
        match.servingSide = score[2];
        break;
      }
    }
  }
  WebHome.setHomeCompetitions(homeCompetitions);
  WebHome.setLatestMatchStatus({allStatusMap});

}

const syncHomeScore = (data) => {
  delaySyncHomeScore(data)
};

export const homeDataControllerType = {
  score: 'score',
};

const homeDataController = {
  [homeDataControllerType.score]: syncHomeScore,
};

export default homeDataController;
