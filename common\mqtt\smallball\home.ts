// @ts-nocheck
import MqttClientInstance from '../index';
import homeDataController from './homeDataController';
import { MqttTopicEnum, MqttListenKeyEnum } from '../constants';

export const subscribeSmallBallHomeData = () => {
  // subscribe home odds
  MqttClientInstance.subscribe(MqttTopicEnum.odds, MqttListenKeyEnum.odds, (topic, message = {}) => {
    // console.log('MqttClientInstance odds', topic, message);
    if (message && typeof message === 'object' && Object.keys(message).length) {
      homeDataController.syncHomeOdds(message);
    }
  });
};

export const unsubscribeSmallBallHomeData = () => {
  MqttClientInstance.unsubscribe(MqttListenKeyEnum.odds);
};
