import { PlayerLink } from './Football/Link';
import './Mvp.less';

interface Props {
  mvp: any;
}

const oneM = 1000000;

const Mvp = (props: Props) => {
  const { mvp = {} } = props;
  // const [topValuablePlayers, setTopValuablePlayers] = useState<any[]>([]);
  // useEffect(() => {
  //   getTopValuablePlayers({
  //     competitionId,
  //     teamId,
  //   }).then(({ topValuablePlayers }) => {
  //     setTopValuablePlayers(teamId ? [topValuablePlayers[0]] : topValuablePlayers);
  //   });
  // }, [teamId, competitionId]);
  return mvp && mvp.id ? (
    <div className="mvp">
      <PlayerLink playId={mvp.id} className="mvpItem">
        <div className="playerInfo">
          <img src={mvp.logo} className="playerIcon" alt="" />
          <div className="nameInfo">
            <span>{mvp.name}</span>
            <div>
              <img src={mvp.countryDto?.logo} className="teamIcon" alt="" />
              <span className="color-999">{mvp.countryDto?.name}</span>
            </div>
          </div>
        </div>
        <span className="price">
          {(mvp.marketValue / oneM).toFixed(1)}M{mvp.marketValueCurrency}
        </span>
      </PlayerLink>
    </div>
  ) : null;
};

export default Mvp;
