import SmallballMatchHeader from '@/components/SmallGameCommon/Match/MatchHeader';
import { EsportsInLiveStatusEnum, EsportsMatchStatusCodeToText, EsportsStatusCodeEnum } from 'iscommon/const/esports/constant';
// import { getEsportsScore } from 'iscommon/utils/dataUtils';

const MatchHeader = () => {
  const getHomeBasketBallMatchTimeText = (matchHeaderInfo) => {
    const { matchStatus, scores, servingSide } = matchHeaderInfo;
    // const list: any[] = getEsportsScore(matchStatus);
    const isIng = EsportsInLiveStatusEnum.includes(matchStatus);
    const text = (EsportsMatchStatusCodeToText as any)[matchStatus];
    const res = {
      statusText: text,
      isIng,
    };
    return res;
  };

  return (
    <SmallballMatchHeader
      checkIsIng={(matchStatus) => EsportsInLiveStatusEnum.includes(matchStatus)}
      getHomeScore={({ homeScore }) => homeScore}
      getAwayScore={({ awayScore }) => awayScore}
      notStartedCode={EsportsStatusCodeEnum.NotStarted}
      getHomeBasketBallMatchTimeText={getHomeBasketBallMatchTimeText}
      // ballIconCode="#icontabletennis_ball"
    />
  );
};

export default MatchHeader;
