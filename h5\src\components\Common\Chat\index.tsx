import { getMessageList } from 'iscommon/api/chat';
import { MenuBarList } from 'iscommon/const/globalConfig';
import { translate } from 'iscommon/i18n/utils';
import { subscribeChatData, unsubscribeChatData } from 'iscommon/mqtt/chat/home';
import { callAppUrl } from 'iscommon/utils';
import { inject, observer } from 'mobx-react';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useParams } from 'umi';
import ChatList from './ChatList';
import styles from './index.less';

const Chat = inject('store')(
  observer((props: any) => {
    const {
      store: { WebConfig },
    } = props;
    const { currentSportPathname } = WebConfig;

    const [messageList, setMessage] = useState<any[]>([]);

    const { categoryId: matchId } = useParams<{ categoryId: string }>();

    const roomId = useMemo(() => {
      const id = MenuBarList.find((item) => item.pathname === currentSportPathname)?.chatRoomId;
      return `${id}-${matchId}`;
    }, [currentSportPathname, matchId]);

    const handleMqttMsg = (msg: string) => {
      setMessage((old) => [...old, msg]);
    };

    const sendMessage = useCallback(() => {
      window.gtag('event', 'click_chat_button', { platform: 'mobile', room_id: roomId });
      window.location.href = 'https://onelink.to/igscore-newapp';
      // callAppUrl('chat');
    }, [roomId]);

    useEffect(() => {
      window.gtag('event', 'chat_room_view', { platform: 'mobile', room_id: roomId });

      console.log('roomId', roomId);
      // Subscribe to MQTT topic
      subscribeChatData(roomId, (m: any) => handleMqttMsg(m));

      // Get initial message list
      getMessageList({ roomId }).then(({ messages }: any) => {
        if (messages && messages.length > 0) {
          setMessage(messages.reverse());
        }
      });

      // Cleanup function
      return () => {
        unsubscribeChatData(roomId);
      };
    }, [roomId]);

    return (
      <div className={styles.chat}>
        <ChatList data={messageList} />

        <div className={styles.footer} onClick={sendMessage}>
          <div className={styles.input}>{translate('Type a message')}</div>
          <div className={styles.send}>
            <i className="icon iconfont iconsend"></i>
          </div>
        </div>
      </div>
    );
  }),
);

export default Chat;
