// @ts-nocheck
import { GlobalConfig } from '../const/globalConfig';

const languageMaps = {
  en: {
    code: 'en',
    name: 'English',
  },
  aze: {
    code: 'aze',
    name: 'Azərbaycan dili',
  },
  id: {
    code: 'id',
    name: '<PERSON><PERSON>',
  },
  bs: {
    code: 'bs',
    name: '<PERSON><PERSON><PERSON>',
  },
  cs: {
    code: 'cs',
    name: '<PERSON><PERSON><PERSON>',
  },
  sr: {
    code: 'sr',
    name: 'Cрпски',
  },
  da: {
    code: 'da',
    name: '<PERSON><PERSON>',
  },
  de: {
    code: 'de',
    name: '<PERSON><PERSON><PERSON>',
  },
  et: {
    code: 'et',
    name: '<PERSON><PERSON><PERSON>',
  },
  es: {
    code: 'es',
    name: '<PERSON>sp<PERSON><PERSON><PERSON>',
  },
  fr: {
    code: 'fr',
    name: 'Français',
  },
  hr: {
    code: 'hr',
    name: '<PERSON><PERSON><PERSON><PERSON>',
  },
  it: {
    code: 'it',
    name: '<PERSON><PERSON>',
  },
  lv: {
    code: 'lv',
    name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  },
  lt: {
    code: 'lt',
    name: '<PERSON><PERSON><PERSON><PERSON>',
  },
  hu: {
    code: 'hu',
    name: '<PERSON><PERSON><PERSON>',
  },
  nl: {
    code: 'nl',
    name: 'Nederlands',
  },
  nn: {
    code: 'nn',
    name: 'Norsk',
  },
  pl: {
    code: 'pl',
    name: 'Polski',
  },
  pt: {
    code: 'pt',
    name: 'Português',
  },
  br: {
    code: 'br',
    name: 'Português do Brasil',
  },
  ro: {
    code: 'ro',
    name: 'Română',
  },
  ch: {
    code: 'ch',
    name: 'Schweizerdeutsch',
  },
  sqi: {
    code: 'sqi',
    name: 'Shqip',
  },
  sk: {
    code: 'sk',
    name: 'Slovenčina',
  },
  sl: {
    code: 'sl',
    name: 'Slovenščina',
  },
  srl: {
    code: 'srl',
    name: 'Srpski',
  },
  fi: {
    code: 'fi',
    name: 'Suomeksi',
  },
  sv: {
    code: 'sv',
    name: 'Svenska',
  },
  vi: {
    code: 'vi',
    name: 'Tiếng Việt',
  },
  tr: {
    code: 'tr',
    name: 'Türkçe',
  },
  el: {
    code: 'el',
    name: 'Ελληνικά',
  },
  bg: {
    code: 'bg',
    name: 'Български',
  },
  mk: {
    code: 'mk',
    name: 'русский',
  },
  ru: {
    code: 'ru',
    name: 'русский',
  },
  ukr: {
    code: 'ukr',
    name: 'Українська',
  },
  ka: {
    code: 'ka',
    name: 'ქართული',
  },
  aa: {
    code: 'aa',
    name: 'العربية',
  },
  th: {
    code: 'th',
    name: 'ไทย',
  },
  mm: {
    code: 'mm',
    name: 'ဗမာ',
  },
  km: {
    code: 'km',
    name: 'ភាសាខ្មែរ',
  },
  ja: {
    code: 'ja',
    name: '日本語',
  },
  zht: {
    code: 'zht',
    name: '中文繁體',
  },
  zh: {
    code: 'zh',
    name: '中文简体',
  },
  ko: {
    code: 'ko',
    name: '한국어',
  },
  bn: {
    code: 'ko',
    name: 'চূড়ান্ত ফলাফল',
  },
  hi: {
    code: 'hi',
    name: 'फाइनल रिजल्ट',
  },
  fa: {
    code: 'fa',
    name: 'فارسی',
  },
};

const livePathName = {
  icehockey: 'ice-hockey',
};

const profileConfig = {
  football: '1ymvkh9ludugqv4',
  basketball: '8nrzkt4dt0ulr4e',
  tennis: 'olrjwf5da8uwmgp',
  baseball: '23mk5s50a5uzm87',
  icehockey: '74reeb6jfeunr0n',
  cricket: '9gm96cxpfeuzm02',
  volleyball: '74m1nh7gbpuoqwe',
};

const typeConfig = {
  football: '/pro',
  basketball: '/3d',
  tennis: '/3d',
  baseball: '',
  icehockey: '/3d',
  cricket: '',
  volleyball: '/3d',
};

const versionConfig = {
  v1: 'widgets',
  v2: 'widgets-v2',
};

export const getLiveUrl = (matchId = '') => {
  const language = languageMaps[GlobalConfig.lng] ? languageMaps[GlobalConfig.lng].code : 'en';
  const pathName = livePathName[GlobalConfig.pathname] || GlobalConfig.pathname;
  const profile = profileConfig[GlobalConfig.pathname];
  const type = typeConfig[GlobalConfig.pathname];
  const version = ['football'].includes(GlobalConfig.pathname) ? versionConfig['v2'] : versionConfig['v1'];
  if (matchId && profile) {
    return `https://${version}.thesports01.com/${language}${type}/${pathName}?profile=${profile}&uuid=${matchId}`;
  }
  return null;
};

export const getVideoUrl = (id = '3823439') => {
  // test url: `//tracker-g.aiscore.com/football/animate?profile=YOvEiPHNNhaHB&id=${id}&lang=zht&timezone=-02%3A00`
  return `https://livestream.thesports01.com/${GlobalConfig.lng || 'en'}/${GlobalConfig.pathname}?token=${
    GlobalConfig.profile
  }&uuid=${id}`;
};
