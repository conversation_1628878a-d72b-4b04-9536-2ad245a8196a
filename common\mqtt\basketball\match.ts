// @ts-nocheck
import MqttClientInstance from '../index';
import matchDataController, { matchDataControllerType } from './matchDataController';
import { GlobalConfig } from '../../const/globalConfig';
import { MqttTopicEnum, MqttListenKeyEnum } from '../constants';
import store from '../../mobx';
// import moment from "moment";

export const subscribeMatchData = (matchId) => {
  // subscribe home match data
  MqttClientInstance.subscribe(MqttTopicEnum.singleMatch + matchId, MqttListenKeyEnum.allMatch, (topic, message) => {
    const { WebHome } = store;
    const { data, time } = message;
    // console.log('MqttClientInstance match', topic, data);
    if (data) {
      GlobalConfig.serverTime = time;
      WebHome.changeServerTime(time);
      for (let dataKey in matchDataControllerType) {
        if (data[dataKey] && typeof matchDataController[dataKey] === 'function') {
          matchDataController[dataKey](data);
        }
      }
    }
  });

  // subscribe match odds
  MqttClientInstance.subscribe(MqttTopicEnum.odds, MqttListenKeyEnum.odds, (topic, message = {}) => {
    // console.log('MqttClientInstance odds', topic, message);
    if (message && typeof message === 'object' && Object.keys(message).length) {
      matchDataController.syncMatchOdds(message);
    }
  });
};

export const unsubscribeMatchData = (matchId) => {
  MqttClientInstance.unsubscribe(MqttTopicEnum.singleMatch + matchId);
  MqttClientInstance.unsubscribe(MqttListenKeyEnum.odds);
};
