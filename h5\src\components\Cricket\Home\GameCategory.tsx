import { inject, observer } from 'mobx-react';
import React, { useMemo } from 'react';

import { getCricketScore } from 'iscommon/utils/dataUtils';

import SmallballCommonGameCategory from '@/components/SmallGameCommon/Home/GameCategory';
import { CricketInLiveStatusEnum, CricketMatchStatusCodeToText, CricketStatusCodeEnum } from 'iscommon/const/cricket/constant';
import _ from 'lodash';
import './GameCategory.less';

interface PureMatchStatusTextProps {
  serverTime: number;
  matchStatus: number;
  remainSeconds: number;
}

const MatchStatusText = React.memo<PureMatchStatusTextProps>((props) => {
  const { matchStatus } = props;
  const { statusText, isIng } = useMemo(() => {
    return {
      statusText: (CricketMatchStatusCodeToText as any)[matchStatus],
      isIng: CricketInLiveStatusEnum.includes(matchStatus),
    };
  }, [matchStatus]);

  return <span className={`time fs-10-center ${isIng ? 'ing' : ''}`}>{statusText}</span>;
});

interface ScoreItem {
  h: number;
  w: number;
  isRed: boolean;
  compareStatus: number; // 1 home > away 2 home < away 0 =
}

const ScoreItem = React.memo(({ item }: { item: ScoreItem; isTotal: boolean }) => {
  return (
    <div className={`listRightSection ${item?.isRed && 'color-c1272d'}`}>
      <div className={`${item?.isRed && 'ing'} ${item?.compareStatus === 1 && !item?.isRed && 'color-333'}`}>{item?.h || 0}</div>
      <div className={`${item?.isRed && 'ing'} ${item?.compareStatus === 2 && !item?.isRed && 'color-333'}`}>{item?.w || 0}</div>
    </div>
  );
});

const ScoreText = (props: any) => {
  const { item } = props;
  const { matchStatus, extraScores } = item;
  if (matchStatus === CricketStatusCodeEnum.NotStarted || _.isEmpty(extraScores)) {
    return <div className="listRight">-</div>;
  }
  const list: any = getCricketScore(matchStatus, extraScores);
  // show ft
  return <div className="listRight rowFlex">{list && <ScoreItem item={list[0]} isTotal />}</div>;
};

const ScoreOrOdds = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Smallball: { SmallballCommon },
      },
      item,
    } = props;
    const { matchId, matchStatus } = item;
    const { homeCompetitionOdds } = SmallballCommon;
    const matchOdds = homeCompetitionOdds[matchId] || [];
    if (matchStatus === CricketStatusCodeEnum.NotStarted) {
      const currentOdd = matchOdds?.filter((item: any) => item.oddsType === 'eu')[0]?.oddsData;
      // future games show odds
      /*
      if (currentOdd) {
        return (
          <div className="listRight">
            {[0, 2].map((key) => (
              <span key={key} className="odd">
                {Number(currentOdd[key]).toFixed(2)}
              </span>
            ))}
          </div>
        );
      }
      */
      return (
        <div className="listRight">
          <span className="odd">-</span>
        </div>
      );
    }
    return <ScoreText item={item} />;
  }),
);

const GameCategory = ({ competition }: any) => {
  return (
    <SmallballCommonGameCategory
      competition={competition}
      renderMatchStatusText={(item) => <MatchStatusText {...item} />}
      renderScoreOrOdds={(item) => {
        return (
          <>
            <ScoreOrOdds item={item} />
          </>
        );
      }}
      renderTeamNameExra={() => null}
    />
  );
};

export default GameCategory;
