{"All": "All", "Live": "Live", "Finished": "Finished", "Scheduled": "Scheduled", "Favourite": "Favourite", "TimeSort": "Sort by time", "OddsMarkets": "Odds markets", "1x2": "1 X 2", "AsianHandicap": "Asian Handicap", "TotalGoals": "Total Goals", "TotalCorners": "Total Corners", "OverBall": "Over", "UnderBall": "Under", "Over": "Over", "Under": "Under", "AllGames": "All Games", "Leagues": "Leagues", "LiveH5": "Live", "SortByTime": "Sort by time", "Today": "Today", "Cancel": "Cancel", "Popular": "Popular", "OtherLeagues": "Other Leagues [A-Z]", "Search": "Filter...", "Settings": "Settings", "Language": "Language", "Timezone": "Timezone", "OddsFormat": "Odds Format", "SoccerGoalReminderSettings": "Football alerts settings", "RemindersOnlyForRacesToWatch": "Fav match only", "GoalSound": "Sound for goal", "GoalPopup": "Popup window for goal", "LiveScore": "Live Score", "Overview": "Overview", "Schedule": "Schedule", "Standings": "Standings", "Squad": "Squad", "Stats": "Stats", "Transfer": "Transfer", "Champions": "Champions", "Football": "Football", "Basketball": "Basketball", "Rugby": "Rugby", "Baseball": "Baseball", "Icehockey": "Ice hockey", "Tennis": "Tennis", "Volleyball": "Volleyball", "Esports": "Esports", "Handball": "Handball", "Cricket": "Cricket", "WaterPolo": "Water Polo", "TableTennis": "Table Tennis", "Snooker": "Snooker", "Badminton": "Bad<PERSON>ton", "FooterContentFootball": "IGScore Football LiveScore provides you with unparalleled football live scores and football results from over 2600+ football leagues, cups and tournaments. Get live scores, halftime and full time soccer results, goal scorers and assistants, cards, substitutions, match statistics and live stream from Premier League, La Liga, Serie A, Bundesliga, Ligue 1, Eredivisie, Russian Premier League, Brasileirão, MLS, Super Lig and Championship on igscore.net. IGScore offers to all the soccer fans live scores, soccer LiveScore, soccer scores, league tables and fixtures for leagues, cups and tournaments, and not only from the most popular football leagues as England Premier League, Spain La Liga, Italy Serie A, Germany Bundesliga, France Ligue 1, but also from a large range of football countries all over the world, including from North and South America, Asia and Africa. Our football LiveScore scorecards are updated live in real-time to keep you up to date with all the football match LiveScore updates happening today along with football LiveScore results for all finished football matches for every football & soccer league. On the match page, our football scorecards are allowing you to view past game results for all previously played fixtures for every football competitions. Get all of your football live results on igscore.net!", "FooterContentBasketball": "IGScore Basketball LiveScore provides you with NBA league live scores, results, tables, statistics, fixtures, standings and previous results by quarters, halftime or final result. IGScore offers scores service from more than 200 basketball competitions from around the world(like NCAA, ABA League, Baltic league, Euroleague, national basketball leagues). You'll find here not only live scores, quarter results, final results and lineups, but also number of 2- and 3-point attempts, free throws, shooting percentage, rebounds, turnovers, steals, personal fouls, match history and player statistics.On IGScore Basketball LiveScore you can watch basketball online by just clicking it, and provides you the online coverage of the top-leagues matches. The match page there will also be the table with all of the basketball statistics on the latest games of the teams. our basketball scorecards are updated in live real-time to keep you up to date with all the basketball results happening today and allowing you to view past game results for all previously played fixtures for every basketball competitions. Get all of your NBA live results on igscore.net! Follow NBA livescores, NBA fixtures, NBA standings and team pages!", "FooterContentAmFootball": "IGScore american football live score provides you all the results and live scores from the biggest and most popular American football league in the world - NFL and when regular NFL season is finished, follow live scores of NFL playoffs and Superbowl. In addition to NFL we will also provide you with the livescores, results, standings and schedules for the NCAA College American football and Canadian CFL. ", "FooterContentBaseball": "IGScore baseball live score provides you with the live scores, results, standings from the world most popular baseball league - USSSA Baseball, NCAA Baseball, MLB Baseball, MLB Allstar game. We also provide live score for Japan Professional league, Mexican league, German 1.Bundesliga, NCAA as well as international baseball tournament World Baseball Classic. You can also see at any time league standings, past games with result by innings and schedule for the forthcoming baseball matches on IGScore Baseball live score.", "FooterContentIcehockey": "IGScore ice hockey Live score provides you real time ice hockey results scores for ice hockey leagues, cups and tournaments. IGScore ice hockey live score provide you hockey livescore, tables, statistics, fixtures, results and scores from NHL, SHL, KHL and we also provide national Finland hockey leagues, Sweden hockey leagues, Slovakia hockey leagues, Czech hockey leagues, league tables, goal scorers, thirds and final ice hockey results live. After the ice hockey regular season ends we offer you hockey live scores, standings and results of the top ice hockey events- IIHF World Championship Stanley Cup and also hockey scores from Winter Olympic tournament. On IGScore ice hockey live score you can also find free hockey live stream for NHL, SHL and others. ", "FooterContentTennis": "IGScore tennis live score provides you with livescores, results, ATP rankings and WTA rankings, fixtures and statistics from all biggest tennis tournaments like Davis and Fed Cup, French open tennis, or for all Grand Slam tournaments - Australian open tennis, US open tennis, <PERSON> and Wimbledon both woman and men for singles and doubles. Also for any tennis player you can see in details his played matches individually and results of them by set and in which tournament was that match played. IGScore tennis live score provides you with head to head results, statistics, live scores and live stream between two players who play match. ", "FooterContentVolleyball": "IGScore volleyball live score offers you coverage from all important men's and women's national volleyball leagues including Italy Serie A1 and Italy Seria A1 Women, Russian Superliga, Polish PlusLiga, Turkey 1. Lig and many others. Beside national volleyball leagues we also provide you with livescore information from major volleyball international tournaments, like FIVB World Championship and European Championship, as well as volleyball livescore results on Olympic games. You can also check the old results from your favourite volleyball team, see future volleyball schedules and check the league volleyball standings on IGScore volleyball live score.", "FooterContentEsports": "Esports Live scores service at IGScore livescore offers Esports live scores, schedules, results and tables. Follow your favourite teams right here live! Esports live score on AiScore.com live score is automatically updated and you don't need to refresh it manually. With adding games you want to follow in \"My games\" following your matches livescores, results and statistics will be even more simple.", "FooterContentHandball": "IGScore handball livescore provides you with handball live scores and live results from most popular handball leagues like German Bundesliga, Spain Liga Asobal, Denmark mens Handboldligaen and France D1. We also provide livescore, results, statistics, standings, tables and fixtures from important cups like European handball Champions League, SEHA league and EHF handball Cup. On IGScore handball livescore, you can find livescores and free live stream for international teams handball tournaments like European Championship and World Championship, both for woman and men. At any time you can check handball results and statistics of last 10 games your team played and also head to head score between teams that are scheduled to play with statistics. ", "FooterContentCricket": "IGScore cricket live score provides you to follow real time cricket results, cricket standings and cricket fixtures. All of that is available for the most popular leagues and cups: Indian Premier League, Champions League Twenty20, Big Bash League, Caribbean Premier League, Friends Life T20 and for ICC Cricket World Cu. All cricket scores on IGScore are automatically updated and there is no need to refresh it manually. With all of that, there is an option to watch free cricket live stream and check the latest odds for the final outcome of the most interesting cricket matches around the world.", "FooterContentWaterPolo": "IGScore water polo live score provides you with the water polo live scores and results from Italy Serie A1, Hungary OB1, Champions and Adriatic league on club level, while on the international level, IGScore water polo live score follows major tournaments like water polo World Championship and European waterpolo Championship. We provide you with goal by goal livescore and free live stream. ", "FooterContentTableTennis": "IGScore table tennis live score provides you with livescores, table, results, table tennis ranking, fixtures and statistics from all biggest table tennis tournaments like russian table tennis, table tennis olympics. Also for any table tennis player you can see in details his played matches individually and results of them by set and in which tournament was that match played. IGScore table tennis live score provides you with head to head results, statistics, live scores and live stream between two players who play match. ", "FooterContentSnooker": "IGScore snooker live score gives you ability to follow live score, results and standings from all snooker tournaments. We follow livescores from UK and World Championship as well as snooker livescore, snooker fixtures and final snooker results from international tournaments like World Snooker tour. At any time you can see the schedule of snooker tournaments that are set to go, results from past snooker tournaments and last 10 games for every player. Additionally, you can check past head to head matches between players. On IGScore snooker live score, you can find the list of matches that are covered with free snooker live stream.", "FooterContentBadminton": "IGScore badminton live score provides you with the badminton live results, standings, fixtures and statistics from international tournaments like World Championships, BWF Super Series and badminton results from Olympic Games. You can also check the results of badminton played games, see the badminton schedule of play and check the badminton results from players individually on IGScore badminton live score.", "ContactUs": "Contact Us", "UpdateLineups": "Update Lineups", "BusinessCooperation": "Business Cooperation", "TermsOfService": "Terms of Service", "PrivacyPolicy": "Privacy Policy", "FreeLiveScoresWidget": "Free Livescores Widget", "Players": "Players", "ForeignPlayers": "Foreign Players", "NumberOfTeams": "Number of Teams", "TotalMarketValue": "Total Market Value", "PlayerSalary": "Player Salary", "DivisionLevel": "Division Level", "Rounds": "Rounds", "Foreigners": "Foreigners", "YellowCards": "Yellow cards", "RedCards": "Red cards", "LeagueInfo": "League Info", "TeamInfo": "Team Info", "Venues": "Venues", "Capacity": "Capacity", "City": "City", "MostValuablePlayer": "Most valuable players", "UpperDivision": "Upper division", "LowerDivision": "Lower division", "NewcomersFromUpperDivision": "Newcomers from upper division", "NewcomersFromLowerDivision": "Newcomers from lower division", "Info": "Info", "TitleHolder": "Title holder", "MostTitle": "Most title", "Matches": "Matches", "TeamStats": "Team Stats", "Team": "Team", "Teams": "Teams", "Goals": "Goals", "Pts": "PTS", "Assists": "Assists", "FullStandings": "Full Standings", "FullStats": "Full Stats", "GoalsPk": "Goals(PK)", "Relegation": "Relegation", "Home": "Home", "Away": "Away", "Result": "Result", "Score": "Score", "PlayerStats": "Player stats", "topScorers": "Top scorers", "teamWeek": "Team of The Week", "round": "Round", "fixtures": "Fixtures", "season": "Season", "topPlayers": "Top players", "topTeams": "Top teams", "Shots": "Shots", "ShotsOnTarget": "Shots on Target", "Clearances": "Clearances", "Tackles": "Tackles", "keyPasses": "Key passes", "Crosses": "Crosses", "CrossesAccuracy": "Crosses accuracy", "Fouls": "Fouls", "WasFouled": "Was fouled", "Penalty": "Penalty", "MinutesPlayed": "Minutes Played", "Dribble": "Dribble", "DribbleSucc": "Dribble Succ", "Interceptions": "Interceptions", "Steals": "Steals", "Passes": "Passes", "LongBalls": "Long balls", "LongBallsAccuracy": "Long balls accuracy", "Duels": "Duels", "DuelsWon": "<PERSON><PERSON> won", "Dispossessed": "Dispossessed", "Saves": "Saves", "Punches": "Punches", "RunsOut": "Runs out", "RunsOutSucc": "Runs out succ", "GoodHighClaim": "Good high claim", "BlockedShots": "Blocked shots", "Loan": "Loan", "EndOfLoan": "End of loan", "Signed": "Signed", "Released": "Released", "Retirement": "Retirement", "Unknown": "Unknown", "Draft": "Draft", "TransferIn": "Transfer in", "TransferOut": "Transfer out", "TopScorers": "Top Scorers", "homeTopScorers": "Top Scorers", "SelectTeam": "Select team", "SelectBasketBallTeam": "Select team", "SelectAll": "Select All", "league": "League", "weekTop": "Team of The Week", "SquadSize": "Squad size", "AverageAge": "Average age", "ViewAll": "View All", "offensiveData": "Offensive Data", "defenseData": "Defense data", "otherData": "Other Data", "ballPossession": "Ball possession", "shotsPerGame": "Shots per game", "penaltiesWon": "Penalties won", "cornersPerGame": "Corners per game", "keyPassesPerGame": "Key passes per game", "accurateLongBallsPerGame": "Accurate long balls per game", "accurateCrossesPerGame": "Accurate crosses per game", "goalsConceded": "Goals conceded", "tacklesPerGame": "Tackles per game", "interceptionsPerGame": "Interceptions per game", "clearancesPerGame": "Clearances per game", "blockedShotsPerGame": "Blocked shots per game", "turnoversPerGame": "Turnovers per game", "foulsPerGame": "Fouls per game", "scoringFrequencyFiveGoals": "Scoring Frequency (≥ 5 goals)", "accuracyCrosses": "Accuracy Crosses", "totalShorts": "Total Shots", "accuracyLongBalls": "Long Balls Accuracy", "blockShots": "Block Shots", "MatchesToday": "Matches Today", "Strikers": "Strikers", "Coach": "Coach", "Midfielders": "Midfielders", "Defender": "Defender", "Goalkeeper": "Goalkeeper", "MarketValue": "Market value", "Year": "Year", "Loans": "Loans", "TopValuablePlayers": "Top Valuable Players", "total": "Total", "Salary": "Salary", "TeamChampions": "Champions", "Results": "Results", "Next": "Next", "Prev": "Prev", "Apps": "Apps", "ShotsPg": "Shots pg", "Discipline": "Discipline", "Possession": "Possession", "Pass": "Pass", "TotalAndAve": "Total / Average", "Suspended": "Injured or suspended", "Position": "Position", "Since": "Since", "Overall": "Overall", "Age": "Age", "LastMatchFormations": "LastMatchFormations", "Formation": "Formation", "GoalDistribution": "Goal Distribution", "Favorite": "Favorite", "FoundedIn": "Founded in", "LocalPlayers": "Local players", "ShowNext": "Show next matches", "HideNext": "<PERSON>de next matches", "CTR": "Current Transfer Record", "Stadium": "Stadium", "Login": "Sign In", "h5_Leagues": "Leagues", "FIFAWorldRanking": "FIFA World Ranking", "Register": "Greate new account or log in", "FB_Login": "Continue with Facebook", "Google_Login": "Continue with Google", "Substitutes": "Substitutes", "injuredOrSuspended": "Injured or suspended", "MatchLive": "Live", "OP": "1X2", "Handicap": "Handicap", "OU": "O/U", "Corner": "Corner", "FoulBall": "Foul ball", "FreeKick": "Free kick", "GoalKick": "Goal kick", "PenaltyKick": "Penalty kick", "Midfield": "Midfield", "HalftimeScore": "Halftime score", "CardUpgradeConfirmed": "Card upgrade confirmed", "InjuryTime": "Injury time", "ShotsOffTarget": "Shots off target", "OvertimeIsOver": "Overtime is over", "PenaltyKickEnded": "Penalty kick ended", "VAR": "VAR", "LiveOverview": "Overview", "H2H": "H2H", "LatestMatches": "Latest Matches", "ScheduledMatches": "Scheduled Matches", "Date": "Date", "Last": "Last", "Only": "Only", "LeagueFilter": "League Filter", "Win": "Win", "Draw": "Draw", "Lose": "Lose", "WinProb": "Win prob", "ScoreHalf": "Score half", "Lineup": "Lineup", "Animation": "Animation", "FigureLegends": "Figure legends", "OwnGoal": "Own Goal", "PenaltyMissed": "Penalty Missed", "YellowCard": "Yellow card", "RedCard": "Red card", "SecondYellow": "Second yellow", "Substitution": "Substitution", "Chatroom": "Chatroom", "ShareYourViews": "Share your views", "Send": "Send", "Logout": "Log out", "TeamOfTheWeek": "Team of the week", "Nodata": "No data", "CurrentLeague": "Current League", "ShirtNumber": "Shirt number", "Foot": "Foot", "ContractUntil": "Contract until", "PlayerInfo": "Player Info", "Height": "Height", "Weight": "Weight", "PlayerValue": "Player Value", "Odds": "Odds", "1X2": "1X2", "O/U": "O/U", "View": "View", "teamChampions": "Champions", "h5Handicap": "Handicap", "Time": "Time", "h5Over": "Over", "h5Under": "Under", "attacks": "Attacks", "dangerousAttack": "Dangerous Attacks", "onTarget": "On Target", "offTarget": "Off Target", "venue": "venue", "playerStatistics": "Player statistics", "Started": "Started", "Chat": "Cha<PERSON>", "Played": "Played", "Rating": "Rating", "Strengths": "Strengths", "Weaknesses": "Weaknesses", "Attacking": "Attacking", "Creativity": "Creativity", "Defending": "Defending", "Tactical": "Tactical", "Technical": "Technical", "Other": "Other", "Cards": "Cards", "TotalPlayed": "Total played", "MinutesPerGame": "Minutes per game", "GoalsFrequency": "Goals frequency", "GoalsPerGame": "Goals per game", "ShotsPerGame": "Shots per game", "KeyPasses": "Key passes", "AccuratePerGame": "Accurate per game", "AccLongBalls": "Acc. long balls", "AccCrosses": "Acc. crosses", "InterceptionsPerGame": "Interceptions per game", "TacklesPerGame": "Tackles per game", "ClearancesPerGame": "Clearances per game", "SuccDribbles": "Succ. dribbles", "TotalDuelsWon": "Total duels won", "Offsides": "Offsides", "YellowRedCards": "Yellow-red cards", "Arrivals": "Arrivals", "Departures": "Departures", "Group": "Group", "Birthday": "Birthday", "Club": "Club", "MainPosition": "Main position", "IGScoreRatings": "IGScore ratings", "Pergame": "score {{perGoalText}} and conceded {{perLostText}} per game", "PassesAccuracy": "Passes Accuracy", "Player": "Player", "LeftFoot": "Left", "RightFoot": "Right", "Upcoming": "Today's Upcoming Matches", "FreeKicks": "Free Kicks", "Corners": "Corners", "DaysUntil": "Days until", "In": "In", "Out": "Out", "NoStrengths": "No outstanding strengths", "NoWeaknesses": "No outstanding weaknesses", "LatestTransfers": "Latest Transfers", "UpcomingMatches": "Upcoming matches", "Preseason": "Preseason", "RegularSeason": "Regular Season", "Playoffs": "Playoffs", "Season": "Season", "Qualifier": "Qualifier", "GroupStage": "Group Stage", "Pos": "Pos", "Ht(cm)": "Ht(cm)", "wt(kg)": "wt(kg)", "Exp": "Exp", "College": "College", "Salary/Year": "Salary/Year", "Manager": "Manager", "TotalPlayers": "Total players", "Form": "Form", "Points": "Points", "Rebounds": "Rebounds", "PointsPerGame": "Points per Game", "Glossary": "GLOSSARY", "GamesPlayed": "Games Played", "WinPercentage": "Win Percentage", "BasketballMinutesPlayed": "Minutes Played", "FieldGoalsMade": "Field Goals Made", "FieldGoalsAttempted": "Field Goals Attempted", "FieldGoalPercentage": "Field Goal Percentage", "threePointFieldGoalsMade": "3 Point Field Goals Made", "threePointFieldGoalsAttempted": "3 Point Field Goals Attempted", "threePointFieldGoalsPercentage": "3 Point Field Goals Percentage", "FieldGoaldsMade": "Field Goalds Made", "FreeThrowsAttempted": "Free Throws Attempted", "FreeThrowPercentage": "Free Throw Percentage", "OffensiveRebounds": "Offensive Rebounds", "DefensiveRebounds": "Defensive Rebounds", "Turnovers": "Turnovers", "Blocks": "Blocks", "BlockedFieldGoalAttempts": "Blocked Field Goal Attempts", "PersonalFouls": "Personal Fouls", "PersonalFoulsDrawn": "Personal Fouls Drawn", "Efficiency": "Efficiency", "PlusMinus": "Plus Minus", "DraftInfo": "Draft info", "Career": "Career", "Atlantic": "Atlantic", "Central": "Central", "Southeast": "Southeast", "Northwest": "Northwest", "Pacific": "Pacific", "Southwest": "Southwest", "EasternConference": "Eastern Conference", "WesternConference": "Western Conference", "ToWin": "To win", "Spread": "Spread", "TotalPoints": "Total Points", "Wins": "Wins", "Losses": "Losses", "WinningPercentage": "Winning percentage", "GamesBack": "Games Back", "HomeRecord": "home Record", "AwayRecord": "Away Record", "DivisionRecord": "Division Record", "ConferenceRecord": "Conference Record", "OpponentPointsPerGame": "Opponent Points Per Game", "AveragePointDifferential": "Average Point Differential", "CurrentStreak": "Current Streak", "RecordLast5Games": "Record Last 5 games", "BoxScore": "Box Score", "Total": "Total", "Match": "Match", "OnTheCourt": "On the court", "h5Glossary": "Glossary", "Starters": "Starters", "Bench": "Bench", "OK": "OK", "FieldGoals": "Field goals", "Timeout": "Timeout", "Foul": "F<PERSON>l", "FreeThrows": "Free throws", "OpeningOdds": "Opening odds", "PreMatchOdds": "Pre-match odds", "PointPerGame": "Point per Game", "ReboundsPerGame": "Rebounds per Game", "AssistsPerGame": "Assists per Game", "PointsAllowed": "Points Allowed", "StartingLineups": "Starting lineups", "HandicapGames": "Handicap - Games", "TotalGames": "Total Games", "fieldGoalsScored": "Field Goal", "fieldGoalsTotal": "Field Goal %", "threePointersScored": "3-Point", "threePointersTotal": "3-Point %", "freeThrowsScored": "Free Throws", "freeThrowsTotal": "Free Throws %", "rebounds": "Rebounds", "offensiveRebounds": "Offensive Rebounds", "defensiveRebounds": "Defensive Rebounds", "assists": "Assists", "blocks": "Blocks", "steals": "Steals", "turnovers": "Turnovers", "totalFouls": "Fouls", "Defensive": "Defensive", "Offensive": "Offensive", "Points(PerGame)": "Points(per game)", "Rebounds(PerGame)": "Rebounds(per game)", "Other(PerGame)": "Other(per game)", "Attists": "Attists", "FirstServe": "First serve %", "FirstServePoints": "First serve points %", "BreakPoints": "Break points %", "Aces": "Aces", "DoubleFaults": "Double faults", "Winner": "Winner", "TennisHandicap": "Handicap", "HandicapSets": "Handicap - Sets", "TotalPoint": "Total Points", "MoneyLine": "Money Line", "TableTennisHandicapGames": "Handicap Games", "HandicapRuns": "Handicap - Runs", "TotalRuns": "Total Runs", "DIFF": "DIFF", "BasketPergame": "PTS {{perGoalText}} and OPP PTS {{perLostText}}", "TennisHand": "Handicap - Games", "TennisTotalGames": "Total Games", "BaseballHand": "Handicap - Runs", "BaseballTotalGames": "Total Runs", "TableTennisHand": "Handicap - Sets", "TableTennisTotalGames": "Total Points", "AmFootballHand": "Spread", "AmFootballTotalGames": "Total Goals", "HandicapPoints": "Handicap Points", "HandicapFrames": "Handicap-Frames", "TotalFrames": "Total Frames", "SeeAll": "See all", "LiveStream": "Live Stream", "LoginTips": "Sign in to watch the live stream", "MoreInfo": "More Info", "NextMatch": "Next Match", "SelectLeague": "Select League", "MatchInfo": "Match Info"}