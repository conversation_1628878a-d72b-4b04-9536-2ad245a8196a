// @ts-nocheck
import { useMemo } from "react";

import { PLAYER_CHARACTERISTICS_LIST as LIST } from "iscommon/const/player";
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

const getValues = (arr = []) => {
    if (!arr || !arr.length) {
        return [];
    }
    return arr.map(([k]) => LIST[k] ? LIST[k].char : "").filter(Boolean)
};

export const usePlayerAbilities = (labelMaps = {}, parsedCharacteristics = [[[]]]) => {
    const mylabelMaps = useTranslateKeysToMaps(['NoStrengths', 'NoWeaknesses']);
    const abilities = useMemo(() => {
        const strengths = !!(parsedCharacteristics && parsedCharacteristics.length) && parsedCharacteristics[0];
        const weaknesses = !!(parsedCharacteristics && parsedCharacteristics.length && parsedCharacteristics.length >= 2) && parsedCharacteristics[1];

        return [
            {
                label: labelMaps.Strengths,
                values: getValues(strengths).length === 0 ? [mylabelMaps.NoStrengths] : getValues(strengths),
            },
            {
                label: labelMaps.Weaknesses,
                values: getValues(weaknesses).length === 0 ? [mylabelMaps.NoWeaknesses] : getValues(weaknesses),
            },
        ];
    }, [labelMaps, parsedCharacteristics]);

  return {
    abilities,
  };
};
