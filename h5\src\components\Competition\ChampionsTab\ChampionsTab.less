.competition-champion-history-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .container-body {
    background: #1e1e1e;
    padding: 10px;

    .custom-collapse {
      .adm-list-default, 
      .adm-list-body,
      .adm-list-item,
      .adm-list-item-content {
        background: transparent;
        border: none;
      }

      .adm-list {
        --border-inner: none;
      }

      .panel-header {
        display: flex;
        align-items: center;
  
        .team-logo {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 48px;
          height: 48px;
          border-radius: 50%;
          margin-right: 10px;
          overflow: hidden;
          background-size: cover;
        }

        .team-name {
          font-size: 22px;
          font-weight: bold;
          color: #fff;
          text-overflow: ellipsis;
        }
      }

      .seasons-grid {
        display: flex;
        flex-wrap: wrap;

        .season-details {
          flex: 1 1 25%;
          padding: 10px;
          display: flex;
          justify-content: center;

          .season-value {
            color: #fff;
            font-size: 20px;
          }
        }
      }
    }
  }
}