import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { useCountDown } from 'iscommon/hooks/countdown';
import { translate } from 'iscommon/i18n/utils';
import { MatchTabH5 } from 'iscommon/mobx/modules/match';
import { momentTimeZone } from 'iscommon/utils';
import moment from 'moment';
import { useCallback, useEffect, useMemo } from 'react';
import { history } from 'umi';

import SectionTitle from '../Competition/Overview/SectionTitle';
import './NextSchedule.less';

interface Props {
  nextMatch: any;
  clickMore: any;
}

const NextSchedule = (props: Props) => {
  const { nextMatch, clickMore } = props;
  const [count, setCount] = useCountDown(0);
  const fixtures = translate('fixtures');

  const timeText = useMemo(() => momentTimeZone(nextMatch.matchTime, 'YYYY/MM/DD hh:mma'), [nextMatch]);

  const goMatch = useCallback(() => {
    if (nextMatch) {
      history.push(GlobalUtils.getPathname(PageTabs.match, nextMatch.id, MatchTabH5.h2h));
    }
  }, [nextMatch]);

  useEffect(() => {
    setCount((nextMatch.matchTime - (nextMatch.updatedAt ? nextMatch.updatedAt : moment().unix())) * 1000);
  }, [nextMatch, setCount]);

  return (
    <div>
      <SectionTitle leftTitle={fixtures} rightTitle="More" onClick={clickMore} />
      <div className="nextGame" onClick={goMatch}>
        <div className="team flex-2">
          <span className="time">{timeText}</span>
          <img src={nextMatch.homeTeam.logo} alt="" />
          <span className="teamName text-overflow">{nextMatch.homeTeam.name}</span>
        </div>
        <div className="team flex-1">
          <span className="time">{count.d > 0 ? `In ${count.d} days` : `${count.h}:${count.m}:${count.s}`}</span>
          <div className="vs">VS</div>
          <span className="teamName"></span>
        </div>
        <div className="team flex-2">
          <span className="time"></span>
          <img src={nextMatch.awayTeam.logo} alt="" />
          <span className="teamName text-overflow">{nextMatch.awayTeam.name}</span>
        </div>
      </div>
    </div>
  );
};

export default NextSchedule;
