import './AllCompetitions.less';

import { CategoryIcon, FallbackImage } from 'iscommon/const/icon';
import { getCompetitionByCountryOrCategory, getCountriesAndCategories } from 'iscommon/api/competition-hot';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { useCallback, useEffect, useRef, useState } from 'react';

import Loading from '@/components/Loading';
import { translate } from 'iscommon/i18n/utils';

interface Item {
  id: string;
  name: string;
  logo: string;
  path: string;
  active: boolean;
  mouseActive: boolean;
  isCategory?: boolean;
  competitions: {
    id: string;
    name: string;
    logo: string;
    active: boolean;
    path: string;
  }[];
}

const AllCompetitions = () => {
  const [list, setList] = useState<Item[]>([]);
  const selectedId = useRef('');
  const [loading, setLoading] = useState<boolean>(false);

  const fetchCountriesAndCategories = async () => {
    setLoading(true);
    const data: any = (await getCountriesAndCategories()) as unknown as {
      countries: Item[];
      categories: Item[];
    };
    const countries = data.countries || [];
    const categories = data.categories
      ? data.categories.map((item: any) => {
          return {
            ...item,
            isCategory: true,
          };
        })
      : [];
    const mergeList = [...categories, ...countries];

    setList(mergeList);
    setLoading(false);
  };

  const fetchCompetitions = useCallback(async (params: any) => {
    return await getCompetitionByCountryOrCategory(params);
  }, []);

  const showInnerList = async (id: string, isCategory: boolean = false) => {
    let newFilterList: any[];
    // 点击相同的li
    if (selectedId.current === id) {
      newFilterList = list.map((item) => {
        return {
          ...item,
          active: false,
        };
      });
      selectedId.current = '';
      return setList(newFilterList);
    }
    selectedId.current = id;

    const selectedItem = list.filter((item) => item.id === id);
    if (selectedItem[0].competitions?.length) {
      newFilterList = list.map((item) => {
        return {
          ...item,
          active: item.id === id,
        };
      });
    } else {
      const competitions = await fetchCompetitions(isCategory ? { categoryId: id } : { countryId: id });
      newFilterList = list.map((item) => {
        return {
          ...item,
          active: item.id === id,
          competitions: item.id === id ? competitions : item.competitions,
        };
      });
    }
    setList(newFilterList);
  };

  useEffect(() => {
    fetchCountriesAndCategories();
  }, []);

  const goCompetition = useCallback((item) => {
    if (item.curStageId) {
      GlobalUtils.goToPage(PageTabs.competition, item.id);
    }
  }, []);

  return (
    <div className='common-all-competition-container'>
      <div className='container-title'>{translate('h5_Leagues')}</div>
      <div className='container-body'>
        <Loading loading={loading} isEmpty={list.length === 0}>
          {list.map((item, i) => {
            return (
              <div key={item.id + i}>
                <div className="all-competition-item" onClick={() => showInnerList(item.id, item.isCategory)}>
                  <img className="all-competition-logo" src={(CategoryIcon as any)[item.logo] || item.logo} alt={item.name}  loading="lazy"/>
                  <span className="all-competition-name">{item.name}</span>
                </div>
                {item.active && (
                  <ul className="competition-item-list">
                    {item.competitions &&
                      item.competitions.map((competition, index) => {
                        return (
                          <div className='competition-item-inner-list' key={competition.id + index} onClick={() => {goCompetition(competition)}}>
                            <img className="inner-list-logo" src={competition.logo || FallbackImage} alt={competition.name}  loading="lazy"/>
                            <span className="inner-list-name">{competition.name}</span>
                          </div>
                        );
                      })}
                  </ul>
                )}
              </div>
            );
          })}
        </Loading>
      </div>
    </div>
  );
};

export default AllCompetitions;
