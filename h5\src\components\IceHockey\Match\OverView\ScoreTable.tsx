import { getIceHockeyScoreList } from 'iscommon/utils/dataUtils';
import { inject, observer } from 'mobx-react';
import styles from './ScoreTable.less';

const ScoreTable = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Smallball: {
          SmallballMatch: { matchHeaderInfo },
        },
      },
    } = props;
    const { scores, matchStatus, servingSide, homeTeam, awayTeam } = matchHeaderInfo;
    const list: any = getIceHockeyScoreList(matchStatus, scores, true);

    const renderScore = (type = 1) => {
      const isHome = type === 1;
      const teamInfo = isHome ? homeTeam : awayTeam;
      const isServingSide = servingSide == type;
      return (
        <div className={styles.tableContent}>
          <div className={styles.teamNameBox}>
            <span className={styles.teamTip} style={{ background: isHome ? '#2196f3' : '#ffae0f' }}></span>
            <span className={styles.teamName}>{teamInfo?.name}</span>
            {isServingSide && (
              <svg aria-hidden="true" className={`icon fs-12 wangqiu svgPostop ${styles.wangqiu}`}>
                <use xlinkHref="#iconiconwangqiu"></use>
              </svg>
            )}
          </div>
          {list.sectionList.map((item: any, index) => {
            return (
              <div
                key={index}
                className={`${styles.flex1} ${item.isRed && 'color-ff4747'} ${
                  item.compareStatus !== type && !item.isRed && styles.insideLoserColor
                }`}
              >
                {isHome ? item.h ?? '-' : item.w ?? '-'}
              </div>
            );
          })}
          {list.ot && (
            <div className={`${styles.flex1} ${list.ot.compareStatus === type && styles.insideLoserColor}`}>
              {isHome ? list.ot.h : list.ot.w}
            </div>
          )}
          {list.ap && (
            <div className={`${styles.flex1} ${list.ap.compareStatus === type && styles.insideLoserColor}`}>
              {isHome ? list.ap.h : list.ap.w}
            </div>
          )}
          {list.ft && (
            <div
              className={`${styles.flex1} ${list.ft.isRed && 'color-ff4747'} ${
                list.ft.compareStatus === type && !list.ft.isRed && styles.insideLoserColor
              }`}
            >
              {isHome ? list.ft.h : list.ft.w}
            </div>
          )}
        </div>
      );
    };
    return (
      <div className={styles.scoreTableContainer}>
        <div className={styles.tableTitle}>
          <div className={styles.teamNameBox} />
          {list.sectionList.map((i, index) => {
            return (
              <div key={index} className={styles.flex1}>
                P{index + 1}
              </div>
            );
          })}
          {list.ot && <div className={styles.flex1}>OT</div>}
          {list.ap && <div className={styles.flex1}>AP</div>}
          {list.ft && <div className={styles.flex1}>T</div>}
        </div>
        {renderScore(1)}
        {renderScore(2)}
      </div>
    );
  }),
);

export default ScoreTable;
