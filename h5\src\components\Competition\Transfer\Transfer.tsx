import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd-mobile';
import { inject, observer } from 'mobx-react';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { getTransfer } from 'iscommon/api/competition';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { compare, getLastSixYear } from 'iscommon/utils';

import Loading from '@/components/Loading';

import TransferList from './TransferList';

import styles from './Transfer.less';

import './Transfer.less'
import { DownOutline } from 'antd-mobile-icons'

const Transfer = inject('store')(
  observer((props: any) => {
    const {
      store: { Competitions },
    } = props;
    const { 
      competitionId,
      currentSeason: { year: seasonText },
     } = Competitions;

    const labelMaps = useTranslateKeysToMaps(['SelectTeam', 'SelectAll', 'Departures', 'Arrivals', 'Transfer']);

    const [sourceList, setSourceList] = useState([]);
    const [visible, setVisible] = useState(false);
    // const [year, setYear] = useState(['2022']);
    const [teamVisible, setTeamVisible] = useState(false);
    const [teamValue, setTeamValue] = useState([{ name: labelMaps['SelectTeam'], logo: '', id: '' }]);
    const [selectTeamId, setSelectTeamId] = useState('');
    const [teamList, setTeamList] = useState([
      [
        {
          label: labelMaps['SelectAll'],
          value: {
            name: labelMaps['SelectTeam'],
            logo: '',
            id: '',
          },
          marketValueCurrency: null,
        },
      ],
    ]);
    const [data, setData] = useState<any[]>([]);
    const [currentType, setCurrentType] = useState<string>('Arrivals');
    const [yearList, setYearList] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);

    useEffect(() => {
      // @ts-ignore
      setYearList([getLastSixYear(6)]);
    }, []);

    useEffect(() => {
      if (competitionId) {
        setLoading(true);
        getTransfer({ competitionId, teamId: selectTeamId, year: String(seasonText) }).then(({ teamTransfers }: any) => {
          if (teamTransfers) {
            setSourceList(teamTransfers);
            let newTeamList = [...teamList[0]];
            (teamTransfers || []).map((item: any) => {
              const index = teamTransfers.findIndex((val: any) => val.id === item.team?.id);
              if (index < 0) {
                let teamObj = {
                  label: item.team?.name,
                  key: `${item.team?.logo}${item.team?.id}`,
                  value: {
                    ...item.team,
                  },
                };
                // @ts-ignore
                newTeamList?.push(teamObj);
              }
            });
            setTeamList([newTeamList]);
            setLoading(false);
          }
        });
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [competitionId, seasonText, selectTeamId]);

    useEffect(() => {
      if (sourceList.length) {
        let newList: any[] = [];
        sourceList.forEach((item: any) => {
          if (currentType === 'Arrivals') {
            newList = [...newList, ...item.transferIn];
          } else {
            newList = [...newList, ...item.transferOut];
          }
        });
        // if (selectTeamId) {
        //   if (currentType === 'Arrivals') {
        //     newList.filter((item) => item.toTeam.id === selectTeamId);
        //   } else {
        //     newList.filter((item) => item.fromTeam.id === selectTeamId);
        //   }
        // }
        // @ts-ignore
        setData(newList.sort(compare('transferTime')));
      }
    }, [sourceList, currentType, selectTeamId]);

    console.log('data123', data)
    console.log('sourcelist', sourceList)
    console.log('currenttype', currentType)

    return (
      <div className='competition-transfer-container'>
        <div className='container-title'>
          {labelMaps.Transfer}
          <>
            <Button className='transfer-header-btn' size="small" onClick={() => setTeamVisible(true)}>
              <span className='transfer-content'>
                <span className='transfer-text'>{teamValue.map((item) => (item.name))}</span>
                <DownOutline className='transfer-icon'/>
              </span>
            </Button>
            <Picker
              columns={teamList as any[]}
              visible={teamVisible}
              onClose={() => setTeamVisible(false)}
              onConfirm={(v: any) => {
                setSelectTeamId(v[0].id);
                setTeamValue(v);
              }}
            />
          </>
        </div>
        <div className='secondary-title'>
          <Button className={`secondary-title-btn ${currentType === 'Departures' ? 'active' : ''}`} onClick={() => setCurrentType('Departures')}>
            {labelMaps.Departures}
          </Button>
          <Button className={`secondary-title-btn ${currentType === 'Arrivals' ? 'active' : ''}`} onClick={() => setCurrentType('Arrivals')}>
            {labelMaps.Arrivals}
          </Button>
        </div>
        <div className='container-body'>
          <Loading loading={loading} isEmpty={!data.length}>
            <TransferList data={data} type={currentType} comeType="Competition" />
          </Loading>
        </div>
      </div>
      // <div className={styles.container_box}>
      //   <div className={styles.select_data_container}>
      //     <div className={styles.select_team_style} onClick={() => setTeamVisible(true)}>
      //       <div className={styles.team_box}>
      //         {teamValue[0]?.logo ? (
      //           <Avatar src={teamValue[0]?.logo} style={{ width: 20, height: 20 }} />
      //         ) : (
      //           <span
      //             style={{
      //               width: 20,
      //               height: 20,
      //               backgroundColor: '#D8D8D8',
      //               borderRadius: '50%',
      //             }}
      //           ></span>
      //         )}
      //         <span style={{ marginLeft: 24 }}>{teamValue[0]?.name}</span>
      //       </div>
      //       <i className="iconfont iconxiala" style={{ fontSize: 24 }}></i>
      //     </div>
      //     <div className={styles.select_year_style} onClick={() => setVisible(true)}>
      //       <span>{year}</span>
      //       <i className="iconfont iconxiala" style={{ fontSize: 24 }}></i>
      //     </div>
      //   </div>
      //   <div className={styles.transfer_type_style}>
          // <Button
          //   size="mini"
          //   color={currentType === 'Arrivals' ? 'primary' : 'default'}
          //   onClick={() => setCurrentType('Arrivals')}
          // >
          //   {labelMaps['Arrivals']}
          // </Button>
          // <Button
          //   size="mini"
          //   color={currentType === 'Departures' ? 'primary' : 'default'}
          //   style={{ marginLeft: 10 }}
          //   onClick={() => setCurrentType('Departures')}
          // >
          //   {labelMaps['Departures']}
          // </Button>
      //   </div>
        // <Loading loading={loading} isEmpty={!data.length}>
        //   <TransferList data={data} type={currentType} comeType="Competition" />
        // </Loading>
      //   <Picker
      //     columns={yearList}
      //     visible={visible}
      //     value={year}
      //     onClose={() => setVisible(false)}
      //     onConfirm={(v: any) => setYear(v)}
      //   />
      //   <Picker
      //     columns={teamList as any[]}
      //     visible={teamVisible}
      //     value={teamValue as any}
      //     onClose={() => setTeamVisible(false)}
      //     onConfirm={(v: any) => {
      //       setSelectTeamId(v[0].id);
      //       setTeamValue(v);
      //     }}
      //   />
      // </div>
    );
  }),
);

export default Transfer;
