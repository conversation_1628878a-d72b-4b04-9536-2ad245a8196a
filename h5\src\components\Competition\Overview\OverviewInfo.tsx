import { Ava<PERSON>, Card, List, Typography } from 'antd';
import { inject, observer } from 'mobx-react';
import React, { useEffect, useState } from 'react';
import { Link, useParams } from 'umi';

import { getMostMarketValuePlayer } from 'iscommon/api/competition';
import { translate, useTranslateKeysToMaps } from 'iscommon/i18n/utils';

// import DescList from '@/components/Common/DescList';
// import ListItemSimple from '@/components/Common/ListItemSimple';

// import { PlayerLink, TeamLink } from '@/components/Common/Link';
// import WidgetBox from '@/components/Common/Widget';
// import styles from './ChampionInfoCard.less';

import './OverviewInfo.less'
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';

interface TeamItemProps {
  data: {
    id: string;
    name: string;
    logo: string;
  };
  champions: number;
  transKey: string;
}

const OtherInfoList = [
  { transKey: 'Players', key: 'totalPlayers' },
  { transKey: 'Foreigners', key: 'foreignPlayers' },
  { transKey: 'YellowCards', key: 'yellowCards' },
  { transKey: 'RedCards', key: 'redCards' },
];

const TeamItem = React.memo<TeamItemProps>(({ data, champions, transKey }) => {
  return (
    <div className='team-item-container'>
      <img className='team-icon' src={data?.logo} alt={data?.name} loading="lazy"/>
      <span>{data?.name}</span>
      <span className='team-transkey'>{translate(transKey)}({champions})</span>
    </div>
  );
});

interface DivisionListProps {
  title: string;
  divisionData: any[];
  descTitleMaps: any;
}

const DivisionList = ({ title, divisionData, descTitleMaps }: DivisionListProps) => {
  if (!divisionData?.length) {
    return null;
  }

  return (
    <div className='desc-item-container'>
      <div className='desc-title'>{descTitleMaps[title]}</div>
      {divisionData.map((item: any) => (
        <Link key={item.id} className='desc-container' to={GlobalUtils.getPathname(PageTabs.team, item.id)}>
          <div className='desc-info'>
            <img className='desc-icon' src={item.logo} alt={item.name} loading="lazy"/>
            <span className='desc-value'>{item.name}</span>
          </div>
        </Link>
      ))}
    </div>
  );
};

const OverviewInfo: React.FC = inject('store')(
  observer((props: any) => {
    const { store } = props;
    const { commonInfo, currentSeason } = store.Competitions;
    const {
      competition: {
        titleHolderTeam = null,
        titleHolderChampions,
        mostTitlesTeams = [],
        mostTitleChampions,
        newComersFromHigherDivision = [],
        newComersFromLowerDivision = [],
        upperDivision = [],
        lowerDivision = [],
      },
      competitionPlayerStatsDto = {},
    } = commonInfo || {};

    const { categoryId: competitionId } = useParams<{ categoryId: string; tabId: string }>();

    // @ts-ignore
    const descTitleMaps: any = useTranslateKeysToMaps([
      'LeagueInfo',
      'MostValuablePlayer',
      'UpperDivision',
      'LowerDivision',
      'NewcomersFromLowerDivision',
      'NewcomersFromUpperDivision',
      'Info',
    ]);
    const infoLabelMaps: any = useTranslateKeysToMaps(OtherInfoList, 'transKey');
    const [mostMarketValuePlayer, setMostMarketValuePlayer] = useState<any>(null);

    useEffect(() => {
      if (competitionId && currentSeason?.id) {
        const request = async () => {
          const res: any = await getMostMarketValuePlayer({ competitionId, seasonId: currentSeason?.id });
          setMostMarketValuePlayer(res?.mostMarketValuePlayer);
        };
        request();
      }
    }, [competitionId, currentSeason?.id]);

    return (
      <div className='competition-overview-info-container'>
        <div className='container-title'>{descTitleMaps.LeagueInfo}</div>
        <div className='container-body'>
          {!!titleHolderTeam && !!mostTitlesTeams?.length && (
            <div className='champion-container'>
              {!!titleHolderTeam && <TeamItem transKey="TitleHolder" data={titleHolderTeam} champions={titleHolderChampions} />}
              {!!mostTitlesTeams?.length && (
                <TeamItem transKey="MostTitle" data={mostTitlesTeams[0]} champions={mostTitleChampions} />
              )}
            </div>
          )}
          {!!mostMarketValuePlayer && (
            <div className='desc-item-container'>
              <div className='desc-title'>{descTitleMaps.MostValuablePlayer}</div>
              <Link className='desc-container' to={GlobalUtils.getPathname(PageTabs.player, mostMarketValuePlayer.id)}>
                <div className='desc-info'>
                  <img className='desc-icon' src={mostMarketValuePlayer.logo} alt={mostMarketValuePlayer.name} loading="lazy"/>
                  <div className='desc-player'>
                    <span className='player-name'>{mostMarketValuePlayer.name}</span>
                    <span className='player-team'>{mostMarketValuePlayer.teamDto?.name}</span>
                  </div> 
                </div>
                <span className='player-value'>{mostMarketValuePlayer.marketValueCurrency} {(mostMarketValuePlayer.marketValue || 0) / 1000000}M</span>
              </Link>
            </div>
          )}
          {!!upperDivision?.length && (
            <DivisionList title='UpperDivision' divisionData={upperDivision} descTitleMaps={descTitleMaps}/>
          )}
          {!!lowerDivision?.length && (
            <DivisionList title='LowerDivision' divisionData={lowerDivision} descTitleMaps={descTitleMaps}/>
          )}
          {!!newComersFromHigherDivision?.length && (
            <DivisionList title='NewcomersFromUpperDivision' divisionData={newComersFromHigherDivision} descTitleMaps={descTitleMaps}/>
          )}
          {!!newComersFromLowerDivision?.length && (
            <DivisionList title='NewcomersFromLowerDivision' divisionData={newComersFromLowerDivision} descTitleMaps={descTitleMaps}/>
          )}
          {!!competitionPlayerStatsDto && (
            <div className='desc-item-container'>
              <div className='desc-title'>{descTitleMaps.Info}</div>
              {OtherInfoList.map(({ transKey, key }) => {
                const text = !competitionPlayerStatsDto[key] && competitionPlayerStatsDto[key] !== 0 ? '--' : competitionPlayerStatsDto[key];
                return (
                  <div key={transKey} className='info-container'>
                    <div className='info-value'>{infoLabelMaps[transKey]}</div>
                    <div className='info-value'>{text}</div>
                  </div>
                );
              })}
              {/* <div className='info-container'>
                <div></div>
              </div> */}
            </div>
          )}
        </div>
      </div>
    );
  }),
);

export default OverviewInfo;