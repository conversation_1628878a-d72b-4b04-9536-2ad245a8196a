import SmallballMatchHeader from '@/components/SmallGameCommon/Match/MatchHeader';
import { TennisInLiveStatusEnum, TennisMatchStatusCodeToText, TennisStatusCodeEnum } from 'iscommon/const/tennis/constant';
import { getTennisScoreList } from 'iscommon/utils/dataUtils';

const MatchHeader = () => {
  const getHomeBasketBallMatchTimeText = (matchHeaderInfo) => {
    const { matchStatus, scores, servingSide } = matchHeaderInfo;
    const list: any[] = getTennisScoreList(matchStatus, scores, servingSide, false);
    const isIng = TennisInLiveStatusEnum.includes(matchStatus);
    const latestScore = list[list.length - 1];
    const text = (TennisMatchStatusCodeToText as any)[matchStatus];
    const res = {
      statusText: isIng ? `${text}, ${latestScore.h}-${latestScore.w}` : text,
      isIng,
    };
    return res;
  };

  return (
    <SmallballMatchHeader
      checkIsIng={(matchStatus) => TennisInLiveStatusEnum.includes(matchStatus)}
      getHomeScore={({ scores }) => scores?.ft?.[0] || 0}
      getAwayScore={({ scores }) => scores?.ft?.[1] || 0}
      notStartedCode={TennisStatusCodeEnum.NotStarted}
      getHomeBasketBallMatchTimeText={getHomeBasketBallMatchTimeText}
    />
  );
};

export default MatchHeader;
