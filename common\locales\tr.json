{"All": "<PERSON><PERSON><PERSON>", "Live": "CANLI", "LiveH5": "CANLI", "MatchLive": "CANLI", "TimeSort": "Zamana g<PERSON> s<PERSON>a", "SortByTime": "Zamana g<PERSON> s<PERSON>a", "AllGames": "TÜM MAÇLAR", "Leagues": "LİGLER", "h5_Leagues": "LİGLER", "Today": "<PERSON><PERSON><PERSON><PERSON>", "Cancel": "İptal et", "Popular": "<PERSON><PERSON><PERSON>", "Settings": "<PERSON><PERSON><PERSON>", "Language": "Dil", "Overview": "<PERSON><PERSON>", "LiveOverview": "<PERSON><PERSON>", "Standings": "Sıralama", "Stats": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Transfer": "Transfer", "Champions": "Şampiyonlar", "TeamChampions": "Şampiyonlar", "teamChampions": "Şampiyonlar", "Football": "FUTBOL", "Basketball": "BASKETBOL", "Baseball": "Beyzbol", "Icehockey": "<PERSON><PERSON>", "Tennis": "TENIS", "Volleyball": "VOLEYBOL", "Esports": "E-SPORLAR", "Handball": "HENTBOL", "Cricket": "KRİKET", "WaterPolo": "SUTOPU", "TableTennis": "<PERSON><PERSON>", "Snooker": "SNOOKER", "Badminton": "BADMİNTON", "BusinessCooperation": "İş Birliği", "TermsOfService": "Hizmet kullanım şartları", "PrivacyPolicy": "Gizlilik politikası", "Players": "OYUNCULAR", "ForeignPlayers": "Yabancı oyuncular", "NumberOfTeams": "Takım sayı<PERSON>ı", "YellowCards": "Sarı kart", "RedCards": "Kırmızı kart", "Capacity": "Kapasite", "City": "Şehir", "Info": "<PERSON><PERSON><PERSON>", "Matches": "MAÇLAR", "Team": "Takım", "Teams": "Takımlar", "Goals": "Gol", "Assists": "<PERSON><PERSON>", "assists": "<PERSON><PERSON>", "Home": "<PERSON>v sahibi", "Away": "<PERSON><PERSON><PERSON><PERSON>", "topScorers": "En Çok Gol Atanlar", "TopScorers": "En Çok Gol Atanlar", "homeTopScorers": "En Çok Gol Atanlar", "season": "Sezon", "Season": "Sezon", "ShotsOnTarget": "<PERSON><PERSON><PERSON>", "Clearances": "<PERSON><PERSON> uzak<PERSON>ştı<PERSON>", "Tackles": "Top çalma", "keyPasses": "<PERSON><PERSON> pas", "KeyPasses": "<PERSON><PERSON> pas", "Fouls": "<PERSON><PERSON>", "totalFouls": "<PERSON><PERSON>", "WasFouled": "e yapılan faul", "Penalty": "Penaltı", "MinutesPlayed": "<PERSON><PERSON><PERSON>", "BasketballMinutesPlayed": "<PERSON><PERSON><PERSON>", "Interceptions": "Top kapma", "Steals": "Top çalma", "steals": "Top çalma", "Passes": "Geçen", "Saves": "Kurtarış", "BlockedShots": "<PERSON><PERSON><PERSON><PERSON>", "Signed": "İmzalanan", "league": "", "offensiveData": "<PERSON><PERSON><PERSON> veri", "defenseData": "<PERSON><PERSON><PERSON><PERSON> veri", "otherData": "<PERSON><PERSON><PERSON>", "ballPossession": "<PERSON>a sa<PERSON> olma", "shotsPerGame": "<PERSON><PERSON> ba<PERSON>ına şut", "ShotsPerGame": "<PERSON><PERSON> ba<PERSON>ına şut", "keyPassesPerGame": "<PERSON><PERSON> başına kilit pas", "accurateLongBallsPerGame": "<PERSON><PERSON> başına isabetli uzun top", "accurateCrossesPerGame": "<PERSON><PERSON> ba<PERSON>ına isabe<PERSON> orta", "tacklesPerGame": "Oyun başına top çalma", "TacklesPerGame": "Oyun başına top çalma", "interceptionsPerGame": "<PERSON>yun başına top kapma", "InterceptionsPerGame": "<PERSON>yun başına top kapma", "clearancesPerGame": "Oyun başına top uzaklaştırma", "ClearancesPerGame": "Oyun başına top uzaklaştırma", "blockedShotsPerGame": "<PERSON><PERSON> ba<PERSON>ına bloklanan şut", "turnoversPerGame": "Oyun başına top kaybı", "foulsPerGame": "<PERSON><PERSON> ba<PERSON><PERSON>na faul", "scoringFrequencyFiveGoals": "", "Coach": "Teknik Direktör", "Goalkeeper": "<PERSON><PERSON><PERSON>", "Stadium": "Stadyum", "Login": "Oturum aç", "Corner": "<PERSON><PERSON>", "ShotsOffTarget": "<PERSON><PERSON><PERSON>", "H2H": "H2H", "Date": "<PERSON><PERSON><PERSON>", "OwnGoal": "<PERSON><PERSON> ka<PERSON> atılan gol", "PenaltyMissed": "Kaçan Penaltı", "SecondYellow": "İkinci sarı karttan kırmızı kart", "Odds": "<PERSON><PERSON>an<PERSON>", "attacks": "<PERSON><PERSON><PERSON>", "Started": "As kadro", "Chat": "<PERSON><PERSON><PERSON>", "Strengths": "Güçlü yanları", "Weaknesses": "<PERSON><PERSON><PERSON><PERSON>", "Group": "Grup", "Birthday": "Doğum günü", "Club": "<PERSON><PERSON><PERSON><PERSON>", "MainPosition": "<PERSON> mevki", "PassesAccuracy": "", "Preseason": "", "RegularSeason": "", "PointsPerGame": "Oyun Başına Sayı", "Glossary": "Sözlük", "h5Glossary": "Sözlük", "Career": "<PERSON><PERSON><PERSON>", "Bench": "<PERSON><PERSON><PERSON>", "ReboundsPerGame": "Oyun Başına R<PERSON>und", "AssistsPerGame": "<PERSON><PERSON> Başına <PERSON>", "OddsFormat": "Oran Formatı", "Squad": "Diziliş", "TotalMarketValue": "<PERSON>lam piyasa <PERSON>", "Rounds": "<PERSON><PERSON>", "LowerDivision": "Alt lig", "TeamStats": "Takım istatistikleri", "GoalsPk": "Gol(PK)", "Crosses": "<PERSON><PERSON><PERSON>", "CrossesAccuracy": "Başarıyla Topu", "Dribble": "Olağanüstü", "DribbleSucc": "Olağanüstü bir başarı", "LongBalls": "Uzun top", "LongBallsAccuracy": "Uzun geçen başarı oranı", "Duels": "<PERSON><PERSON>", "DuelsWon": "Sabıkasız kimse", "Dispossessed": "Kurtarış", "Punches": "<PERSON><PERSON>ci saldırı başarı", "RunsOut": "<PERSON><PERSON><PERSON>", "RunsOutSucc": "<PERSON><PERSON><PERSON><PERSON>", "GoodHighClaim": "Geçme doğruluğu", "Loan": "Kiralık", "EndOfLoan": "<PERSON><PERSON><PERSON><PERSON>ı<PERSON>", "Unknown": "bilinmiyor", "AverageAge": "<PERSON><PERSON><PERSON>", "cornersPerGame": "<PERSON><PERSON> ba<PERSON><PERSON>na korner", "goalsConceded": "<PERSON><PERSON><PERSON> goller", "Defender": "<PERSON><PERSON><PERSON>", "Discipline": "Disi<PERSON>lin", "Pass": "", "FB_Login": "Facebook ile giriş yap", "Google_Login": "Google ile giriş yap", "Substitutes": "<PERSON><PERSON><PERSON>", "PenaltyKick": "", "ShareYourViews": "<PERSON><PERSON><PERSON>", "Nodata": "Veri Yok", "Foot": "Ayak", "dangerousAttack": "", "venue": "Stat", "playerStatistics": "<PERSON><PERSON><PERSON> istatist<PERSON>", "TotalPlayed": "Toplam oynanan maç", "MinutesPerGame": "<PERSON><PERSON> ba<PERSON><PERSON><PERSON> oynanan dakika", "GoalsFrequency": "", "GoalsPerGame": "<PERSON><PERSON>", "Arrivals": "<PERSON><PERSON><PERSON><PERSON>", "Departures": "Ayrılanlar", "LeftFoot": "Sol", "RightFoot": "Sağ", "LatestTransfers": "<PERSON> transferler", "DraftInfo": "<PERSON><PERSON>me Bilgileri", "OK": "<PERSON><PERSON>", "AsianHandicap": "", "TotalGoals": "", "AmFootballTotalGames": "", "TotalCorners": "", "OverBall": "Üstünde", "Over": "Üstünde", "h5Over": "Üstünde", "UnderBall": "Altında", "Under": "Altında", "h5Under": "Altında", "OtherLeagues": "<PERSON><PERSON><PERSON> [A-Z]", "GoalPopup": "", "FullStandings": "<PERSON><PERSON><PERSON>", "teamWeek": "Haftanın karması", "weekTop": "Haftanın karması", "TeamOfTheWeek": "Haftanın karması", "round": "<PERSON><PERSON>", "Released": "Serbest bırakılan", "Retirement": "", "Draft": "<PERSON><PERSON><PERSON>", "TransferIn": "", "TransferOut": "", "MarketValue": "<PERSON><PERSON><PERSON>", "Salary": "Maaş", "Next": "<PERSON><PERSON><PERSON>", "Position": "Pozisyon", "CTR": "Mevcut transfer gelirleri", "FoulBall": "", "FreeKick": "", "GoalKick": "", "Midfield": "", "HalftimeScore": "", "InjuryTime": "", "OvertimeIsOver": "", "PenaltyKickEnded": "", "Last": "Son", "Win": "Galibiyet", "Draw": "<PERSON><PERSON><PERSON>", "Lose": "Mağlubiyet", "Lineup": "", "Substitution": "Oyuncu <PERSON>ği", "Offsides": "<PERSON>say<PERSON>", "Playoffs": "", "Qualifier": "", "GroupStage": "", "Rebounds": "<PERSON><PERSON><PERSON>", "rebounds": "<PERSON><PERSON><PERSON>", "OffensiveRebounds": "<PERSON><PERSON><PERSON> ribau<PERSON>u", "offensiveRebounds": "<PERSON><PERSON><PERSON> ribau<PERSON>u", "DefensiveRebounds": "<PERSON><PERSON><PERSON><PERSON>", "defensiveRebounds": "<PERSON><PERSON><PERSON><PERSON>", "Turnovers": "Top kaybı", "turnovers": "Top kaybı", "Blocks": "Blok", "blocks": "Blok", "BoxScore": "<PERSON><PERSON><PERSON> istatist<PERSON>", "Foul": "<PERSON><PERSON>", "FreeThrows": "Serbest atış", "freeThrowsScored": "Serbest atış", "fieldGoalsScored": "", "fieldGoalsTotal": "", "threePointersScored": "", "threePointersTotal": "", "freeThrowsTotal": "", "Finished": "<PERSON><PERSON>", "Scheduled": "Fikstür", "Favourite": "", "OddsMarkets": "<PERSON><PERSON> p<PERSON>", "Search": "", "Timezone": "", "SoccerGoalReminderSettings": "", "RemindersOnlyForRacesToWatch": "", "GoalSound": "", "LiveScore": "", "Schedule": "Program", "Rugby": "", "FooterContentFootball": "IGScore Futbol Canlı Skor, 2600'den fazla futbol liginden, kupa ve turnuvadan benzersiz futbol canlı skorları ve futbol sonuçları sunar. Premier League, La Liga, Serie A, Bundesliga, Ligue 1, Eredivisie, Russian Premier League, Brasileirão, MLS'den canlı skorlar, devre ve tam zamanlı futbol sonuçları, gol atanlar ve asistanlar, kartlar, oyuncu değişiklikleri, maç istatistikleri ve canlı yayın alın igscore.net'da Süper Lig ve Şampiyona. IGScore, İngiltere Premier Ligi, İspanya La Liga, İtalya Serie A, Almanya gibi en popüler futbol liglerinden canlı skorlar, futbol canlı skorları, futbol skorları, lig tabloları ve ligler, kupalar ve turnuvalar için fikstürler sunar. Bundesliga, Fransa Ligue 1, ancak aynı zamanda Kuzey ve Güney Amerika, Asya ve Afrika dahil olmak üzere dünyanın her yerinden çok çeşitli futbol ülkelerinden. Futbol canlı skor kartlarımız, bugün gerçekleşen tüm futbol maçı canlı skor güncellemelerinin yanı sıra her futbol ve futbol ligindeki tüm bitmiş futbol maçlarının futbol canlı skor sonuçları ile sizi güncel tutmak için gerçek zamanlı olarak güncellenir. Maç sayfasında, futbol skor kartlarımız, her futbol müsabakası için önceden oynanan tüm fikstürlerin geçmiş maç sonuçlarını görüntülemenize olanak tanır. Tüm canlı futbol sonuçlarınızı igscore.net'da alın!", "FooterContentBasketball": "IGScore Basketball LiveScore NBA liginin canlı skorlarını, sonu<PERSON>lar<PERSON>n<PERSON>, tab<PERSON>lar<PERSON>n<PERSON>, istat<PERSON><PERSON><PERSON><PERSON>, fi<PERSON><PERSON><PERSON><PERSON><PERSON>, puan durumlarını ve önceki sonuçları, ç<PERSON>rek, devre veya final sonucuna göre sağlar. IGScore, dünyanın dört bir yanından gelen 200'den fazla basketbol yarışmasından (NCAA, ABA Ligi, Baltık Ligi, Euroleague, ulusal basketbol ligleri gibi) puan hizmeti sunuyor. Burada sadece canlı skorlar, çeyrek sonuçları, final sonuçları ve kadroları değil, aynı zamanda 2 ve 3 sayı denemelerini, serbest atışları, atış yüzdesini, ribaundları, ciroları, çalmaları, kişisel faulleri, maç geçmişini ve oyuncu istatistiklerini de bulacaksınız .IGScore Basketball canlı basketbol maçını canlı olarak izleyebilir ve sadece liglerin maçlarını online olarak izleyebilirsiniz. Maç sayfasında ayrıca takımların son maçlarındaki tüm basketbol istatistiklerini içeren bir masa da olacak. basketbol puan kartlarımız, bugün gerçekleşen tüm basketbol sonuçlarıyla güncel kalmanızı sağlamak ve her basketbol yarışması için daha önce oynanan tüm fikstürlerin geçmiş oyun sonuçlarını görüntülemenizi sağlamak için gerçek zamanlı olarak güncellenir. Tüm NBA canlı sonuçlarını igscore.net'dan alın! NBA canlı skorlarını, NBA fikstürünü, NBA puan durumunu ve takım sayfalarını takip edin!", "FooterContentAmFootball": "IGScore american football live score Dünyanın en büyük ve en popüler Amerikan futbol liginden tüm sonuçları ve canlı skorları sunar - NFL ve normal NFL sezonu bittiğ<PERSON>e, NFL Playoffs ve Superbowl'ın canlı skorlarını izleyin. NFL'ye e<PERSON>, Size NCAA Koleji Amerikan Futbolu ve Kanadalı CFL için canlı skorlar, sonuçlar, sıralamalar ve programları da sunacağız.", "FooterContentBaseball": "IGScore baseball live score size dünya<PERSON><PERSON><PERSON> en popüler beyzbol ligi - USSSA Beyzbol, NCAA Beyzbol, MLB Beyzbol, MLB Allstar oyunundan canlı skorlar, sonuçlar ve puan durumu sağlar. Ayrıca Japonya Profesyonel ligi, <PERSON><PERSON><PERSON> ligi, Alman 1.Bundesliga, NCAA ve uluslararası beyzbol turnuvası World Baseball Classic için canlı skor sağlıyoruz. Ayrıca istediğiniz zaman beyzbol ligi puan durumu, geçmiş maçların vuruş sırası ve IGScore baseball live score tarihindeki gelecek beyzbol maçlarının takvimini görebilirsiniz.", "FooterContentIcehockey": "IGScore ice hockey live score size buz hokeyi ligleri, kupalar ve turnuvalar için gerçek zamanlı buz hokeyi sonuç puanları sağlar. IGScore ice hockey live score size NHL, SHL, KHL'den hokey canlı skorları, tab<PERSON><PERSON>, istat<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, sonuçlar ve skorlar sağlar ve ayrıca ulusal Finlandiya hokey ligleri, İsveç hokey ligleri, Slovakya hokey ligleri, Çek hokey ligleri, lig tab<PERSON><PERSON><PERSON>, gol atan<PERSON>, üçte bir ve son buz hokeyi sonuçları canlı. Buz hokeyi normal sezonu sona erdikten sonra, size en iyi buz hokeyi etkinliklerinin - IIHF Dünya Şampiyonası Stanley Kupası'nın hokey canlı skorlarını, sıralamalarını ve sonuçlarını ve ayrıca Kış Olimpiyatları turnuvasından hokey skorlarını sunuyoruz. IGScore ice hockey live score'da ayrıca NHL, SHL ve diğerleri için ücretsiz hokey canlı yayınını da bulabilirsiniz.", "FooterContentTennis": "IGScore tennis live score size <PERSON> ve Fed Cup, Fransa açık tenisi veya tüm Grand Slam turnuvaları - Avustralya açık tenis, ABD açık tenis, <PERSON> gibi en büyük tenis turnuvalarında<PERSON> canl<PERSON> skorlar, <PERSON><PERSON><PERSON><PERSON>, ATP sıralamaları ve WTA sıralamaları, fikstürler ve istatistikler sunar. Gar<PERSON> ve Wimbledon, tekler ve çiftler için hem kadın hem de erkekler. Ayrıca herhangi bir tenisçi için bireysel olarak oynadığı maçları ve setlere göre sonuçlarını ve o maçın hangi turnuvada oynandığını ayrıntılı olarak görebilirsiniz. IGScore tennis live score, maç oynayan iki oyuncu arasında kafa kafaya sonuçlar, istat<PERSON><PERSON><PERSON>, canlı skorlar ve canlı yayın sağlar.", "FooterContentVolleyball": "IGScore volleyball live score size İtalya Serie A1 ve İtalya Seria A1 Bayanlar, Rusya Superliga, Polonya PlusLiga, Türkiye 1. Lig ve diğerleri dahil olmak üzere tüm önemli erkek ve kadın ulusal voleybol liglerinden kapsama alanı sunar. Ulusal voleybol liglerinin yanı sıra, size FIVB Dünya Şampiyonası ve Avrupa Şampiyonası gibi büyük uluslararası voleybol turnuvalarından canlı skor bilgilerinin yanı sıra Olimpiyat oyunlarındaki voleybol canlı skor sonuçlarını da sunuyoruz. Ayrıca favori voleybol takımınızın eski sonuçlarını kontrol edebilir, gelecekteki voleybol programlarını görebilir ve lig voleybolu sıralamalarını IGScore volleyball live score üzerinden kontrol edebilirsiniz.", "FooterContentEsports": "ESPORTS Live Skorları IGScore Canlı Skor, ESPORTS canlı skorları, programlar<PERSON>, sonuçları ve masaları sunuyor. En sevdiğiniz takımları burada takip edin. ESPORTS canlı skor igscore.net canlı skor otomatik olarak güncellenir ve manuel olarak yenilemeniz gerekmez. Maçlarınızı takip etmek için \"Oyunlarım\" nda takip etmek istediğiniz oyun ekleyerek canlı skorlar, sonuçlar ve istatistikler daha da basit olacaktır.", "FooterContentHandball": "IGScore handball live score size Alman Bundesliga, İspanya Liga Asobal, Danimarka erkek Handboldligaen ve Fransa D1 gibi en popüler hentbol liglerinden hentbol canlı skorları ve canlı sonuçlar sağlar. Ayrıca Avrupa hentbol Şampiyonlar Ligi, SEHA ligi ve EHF hentbol Kupası gibi önemli kupalardan canlı skorlar, sonuçlar, istatistik<PERSON>, puan durumu, puan durumu ve fikstürleri sunuyoruz. IGScore handball live score'da hem kadın hem de erkekler için Avrupa Şampiyonası ve Dünya Şampiyonası gibi uluslararası takımların hentbol turnuvaları için canlı skorlar ve ücretsiz canlı yayın bulabilirsiniz. İstediğiniz zaman takımınızın oynadığı son 10 maçın hentbol sonuçlarını ve istatistiklerini ve ayrıca istatistiklerle oynaması planlanan takımlar arasındaki kafa kafaya skorları kontrol edebilirsiniz.", "FooterContentCricket": "IGScore cricket live score ger<PERSON><PERSON> zamanlı kriket sonuçlarını, kriket puan durumunu ve kriket fikstürlerini takip etmenizi sağlar. Tüm bunlar en popüler ligler ve kupalar için mevcuttur: Indian Premier League, Champions League Twenty20, Big Bash League, Karayipler Premier Ligi, Friends Life T20 ve ICC Cricket World Cup için. IGScore'daki tüm kriket puanları otomatik olarak güncellenir ve manuel olarak yenilemeye gerek yoktur. Tüm bunlarla birlikte, ücretsiz kriket canlı akışını izleme ve dünyadaki en ilginç kriket maçlarının nihai sonucu için en son oranları kontrol etme seçeneği var.", "FooterContentWaterPolo": "IGScore water polo live score size kulüp düzeyinde İtalya Serie A1, Macaristan OB1, Şampiyonlar ve Adriyatik liginden sutopu canlı skorları ve sonuçları sağlarken, uluslararası düzeyde IGScore water polo live score sutopu Dünya Şampiyonası ve Avrupa sutopu Şampiyonası gibi büyük turnuvalar sağlar . Gol gollü canlı skor ve ücretsiz canlı yayın sunuyoruz.", "FooterContentTableTennis": "IGScore table tennis live score size rusya masa tenisi, masa tenisi olimpiyatları gibi tüm büyük masa tenisi turnuvalarından canlı skorlar, masa tenisi sonuçları, masa tenisi sıralaması, fikst<PERSON>rler ve istatistikler sunar. Ayrıca herhangi bir masa tenisi oyuncusu için bireysel olarak oynadığı maçları ve setlere göre sonuçlarını ve o maçın hangi turnuvada oynandığını ayrıntılı olarak görebilirsiniz. IGScore table tennis live score, maç oynayan iki oyuncu arasında kafa kafaya sonuçlar, istatist<PERSON><PERSON>, canlı skorlar ve canlı yayın sağlar.", "FooterContentSnooker": "IGScore snooker live score size tüm bilardo turnuvalarından canlı skorları, sonuçları ve sıralamaları takip etme olanağı sağlar. Ayrıca İngiltere ve Dünya Şampiyonası'ndan canlı skorların yanı sıra bilardo canlı skorları, bilardo fikstürleri ve Dünya Snooker turu gibi uluslararası turnuvalardan son bilardo sonuçlarını da sağlıyoruz. Her zaman başlayacak olan bilardo turnuvalarının programını, geçmiş bilardo turnuvalarının sonuçlarını ve her oyuncu için son 10 maçı görebilirsiniz. <PERSON><PERSON> o<PERSON>ak, oyuncular arasındaki başa baş maçları kontrol edebilirsiniz. IGScore snooker live score tarihinde, ücretsiz bilardo canlı akışıyla kapsanan maçların listesini bulabilirsiniz.", "FooterContentBadminton": "IGScore badminton live score size <PERSON><PERSON><PERSON>ampiyonaları, BWF Süper Serisi ve Olimpiyat Oyunlarından badminton sonuçları gibi uluslararası turnuvalardan canlı badminton sonuçları, s<PERSON><PERSON><PERSON>lar, fikst<PERSON><PERSON>r ve istatistikler sağlar. Ayrıca IGScore badminton live score üzerinden oynanan badminton oyunlarının sonuçlarını kontrol edebilir, badminton oyun programını görebilir ve oyuncuların badminton sonuçlarını bireysel olarak kontrol edebilirsiniz.", "ContactUs": "Bize Ulaşın", "UpdateLineups": "Update Lineups", "FreeLiveScoresWidget": "Free Livescores Widget", "PlayerSalary": "", "DivisionLevel": "<PERSON><PERSON>", "Foreigners": "Yabancı oyuncu sayısı", "LeagueInfo": "<PERSON><PERSON><PERSON><PERSON>", "TeamInfo": "<PERSON><PERSON><PERSON><PERSON>", "Venues": "", "MostValuablePlayer": "", "UpperDivision": "Üst lig", "NewcomersFromUpperDivision": "", "NewcomersFromLowerDivision": "", "TitleHolder": "<PERSON><PERSON>", "MostTitle": "<PERSON><PERSON> ka<PERSON>an <PERSON>", "Pts": "PTS", "FullStats": "", "Relegation": "", "Result": "<PERSON><PERSON><PERSON>", "Score": "Skor", "PlayerStats": "", "fixtures": "", "topPlayers": "<PERSON><PERSON><PERSON>", "Shots": "<PERSON><PERSON><PERSON>", "SelectTeam": "", "SelectBasketBallTeam": "", "SelectAll": "<PERSON><PERSON><PERSON>", "SquadSize": "Oyuncu sayı<PERSON>ı", "ViewAll": "<PERSON><PERSON><PERSON><PERSON>", "penaltiesWon": "Kazanılan penaltılar", "accuracyCrosses": "", "totalShorts": "", "accuracyLongBalls": "", "blockShots": "", "MatchesToday": "<PERSON><PERSON><PERSON><PERSON>", "Strikers": "<PERSON><PERSON>", "Midfielders": "<PERSON><PERSON>", "Year": "", "Loans": "", "TopValuablePlayers": "", "total": "", "Total": "", "Results": "", "Prev": "", "Apps": "Oynadığı", "ShotsPg": "Ortalama alma atış", "Possession": "", "TotalAndAve": "", "Suspended": "Sakatlık Bilgisi", "injuredOrSuspended": "Sakatlık Bilgisi", "Since": "Yaralanma zamanı", "Overall": "Overall", "Age": "Yaş", "LastMatchFormations": "<PERSON> oyun sı<PERSON>ı", "Formation": "", "GoalDistribution": "<PERSON><PERSON><PERSON>", "Favorite": "", "FoundedIn": "<PERSON><PERSON><PERSON><PERSON>", "LocalPlayers": "<PERSON><PERSON><PERSON>", "ShowNext": "Gelecek maçları göster", "HideNext": "Gelecek maçları gizle", "FIFAWorldRanking": "FIFA Dünya Sıralaması", "Register": "", "OP": "1X2", "Handicap": "", "h5Handicap": "", "TennisHandicap": "", "OU": "Ü/A", "CardUpgradeConfirmed": "Kart yükseltme onaylandı", "VAR": "", "LatestMatches": "<PERSON>", "ScheduledMatches": "", "Only": "", "LeagueFilter": "", "WinProb": "", "ScoreHalf": "", "Animation": "", "FigureLegends": "", "YellowCard": "Sarı kart", "RedCard": "Kırmızı kart", "Chatroom": "<PERSON><PERSON><PERSON> o<PERSON>ı", "Send": "<PERSON><PERSON><PERSON>", "Logout": "", "CurrentLeague": "", "ShirtNumber": "", "ContractUntil": "<PERSON><PERSON><PERSON>", "PlayerInfo": "PLAYER INFO", "Height": "Boy", "Weight": "Ağırlık", "PlayerValue": "Sosyal durum", "View": "", "Time": "zaman", "onTarget": "", "offTarget": "", "Played": "", "Rating": "", "Attacking": "<PERSON><PERSON><PERSON>", "Creativity": "Yaratıcılık", "Defending": "<PERSON><PERSON><PERSON><PERSON>", "Tactical": "<PERSON><PERSON>", "Technical": "Teknik", "Other": "", "Cards": "Penaltı kartları", "AccuratePerGame": "Doğru geçen", "AccLongBalls": "Ön uzun pas", "AccCrosses": "<PERSON><PERSON><PERSON>", "SuccDribbles": "Olağanüstü bir başarı", "TotalDuelsWon": "<PERSON><PERSON>", "YellowRedCards": "", "AiScoreRatings": "", "Pergame": "", "Player": "", "Upcoming": "", "FreeKicks": "", "Corners": "<PERSON><PERSON>", "DaysUntil": "<PERSON><PERSON>", "In": "<PERSON><PERSON><PERSON> girdi", "Out": "Oyundan çıktı", "NoStrengths": "Olağanüstü güçlü yanı yok", "NoWeaknesses": "Olağanüstü zayıf yanı yok", "UpcomingMatches": "", "Pos": "", "Ht(cm)": "", "wt(kg)": "", "Exp": "EXP", "College": "", "Salary/Year": "", "Manager": "", "TotalPlayers": "Oyuncu sayı<PERSON>ı", "Form": "", "Points": "<PERSON><PERSON>", "GamesPlayed": "", "WinPercentage": "", "FieldGoalsMade": "", "FieldGoalsAttempted": "", "FieldGoalPercentage": "", "threePointFieldGoalsMade": "", "threePointFieldGoalsAttempted": "", "threePointFieldGoalsPercentage": "", "FieldGoaldsMade": "", "FreeThrowsAttempted": "", "FreeThrowPercentage": "", "BlockedFieldGoalAttempts": "", "PersonalFouls": "", "PersonalFoulsDrawn": "", "Efficiency": "", "PlusMinus": "", "Atlantic": "", "Central": "", "Southeast": "", "Northwest": "", "Pacific": "", "Southwest": "", "EasternConference": "", "WesternConference": "", "ToWin": "", "Spread": "<PERSON><PERSON><PERSON>", "AmFootballHand": "<PERSON><PERSON><PERSON>", "TotalPoints": "", "TotalPoint": "", "TableTennisTotalGames": "", "Wins": "Galibiyet", "Losses": "Mağlubiyet", "WinningPercentage": "", "GamesBack": "", "HomeRecord": "", "AwayRecord": "", "DivisionRecord": "", "ConferenceRecord": "", "OpponentPointsPerGame": "", "AveragePointDifferential": "", "CurrentStreak": "", "RecordLast5Games": "", "Match": "maç", "OnTheCourt": "<PERSON><PERSON><PERSON>", "Starters": "<PERSON><PERSON> beş", "FieldGoals": "İsabetli şut oranı", "Timeout": "", "OpeningOdds": "", "PreMatchOdds": "", "PointPerGame": "", "PointsAllowed": "", "StartingLineups": "Başlangıç dizilişi", "HandicapGames": "", "TennisHand": "", "TotalGames": "", "TennisTotalGames": "", "Defensive": "", "Offensive": "", "Points(PerGame)": "", "Rebounds(PerGame)": "", "Other(PerGame)": "<PERSON><PERSON><PERSON> (ortalama)", "Attists": "", "FirstServe": "", "FirstServePoints": "", "BreakPoints": "", "Aces": "Ace", "DoubleFaults": "Ç<PERSON> hata", "Winner": "Winner", "HandicapSets": "", "TableTennisHand": "", "MoneyLine": "", "TableTennisHandicapGames": "", "HandicapRuns": "", "BaseballHand": "", "TotalRuns": "", "BaseballTotalGames": "", "DIFF": "", "BasketPergame": "", "HandicapPoints": "", "HandicapFrames": "", "TotalFrames": "", "SeeAll": ""}