import { LeftOutline } from 'antd-mobile-icons';
import { useEffect, useMemo } from 'react';

import { getLiveUrl } from 'iscommon/utils/live';
import CommonEmpty from '../CommonEmpty';

import styles from './index.less';

interface Props {
  matchId?: string;
  background?: string;
  url?: any;
  onPress(): void;
}

const CommonLiveHeader = (props: Props) => {
  const { matchId, onPress, background = '#763222' } = props;
  const liveHeight = (404 / 800) * window.document.body.offsetWidth + 130;
  const liveUrl = useMemo(() => getLiveUrl(matchId), [matchId]);

  useEffect(() => {
    if (liveUrl) {
      window.gtag('event', 'animation_module_view', { platform: 'mobile' });
    }
  }, [liveUrl]);

  return (
    <div className={styles.live} style={{ height: `${liveHeight}px`, marginTop: '-45px', backgroundColor: background }}>
      <div className={styles.inner} style={{ height: `${liveHeight}px` }}>
        {liveUrl ? (
          <iframe className={styles.liveIframe} src={liveUrl} />
        ) : (
          <div>
            <CommonEmpty />
          </div>
        )}
      </div>
      <div className={styles.back} onClick={onPress}>
        <LeftOutline color="rgba(255, 255, 255, 0.6)" fontSize={24} />
      </div>
    </div>
  );
};

export const CommonLiveOrVideoHeader = (props: Props) => {
  const { url, onPress, background = '#763222' } = props;
  const liveHeight = (404 / 800) * window.document.body.offsetWidth + 130;

  useEffect(() => {
    if (url) {
      window.gtag('event', 'animation_module_view', { platform: 'mobile' });
    }
  }, [url]);

  return (
    <div className={styles.live} style={{ height: `${liveHeight}px`, marginTop: '-50px', background: background }}>
      <div className={styles.inner} style={{ height: `${liveHeight}px` }}>
        {url ? (
          <iframe className={styles.liveIframe} src={url} height={liveHeight} />
        ) : (
          <div>
            <CommonEmpty />
          </div>
        )}
      </div>
      <div className={styles.back} onClick={onPress}>
        <LeftOutline color="rgba(255, 255, 255, 0.6)" fontSize={24} />
      </div>
    </div>
  );
};

export default CommonLiveHeader;
