import { <PERSON><PERSON>, <PERSON><PERSON> } from "antd-mobile";
import { getFootBallTeamStats } from "iscommon/api/football-team";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { compare } from "iscommon/utils";
import { inject, observer } from "mobx-react";
import { useEffect, useState } from "react";
import { DownOutline } from 'antd-mobile-icons'
import './TeamStatsCard.less'

interface Props {
  [key: string]: any;
}

const TeamStatsCard = inject('store')(
  observer((props: Props) => {
    const {
      store: {
        Team: {
          teamId,
          teamHeaderInfo: {
            name: teamName,
            currentSeason: { year: seasonText },
          },
        },
      },
    } = props;

    const labelMaps = useTranslateKeysToMaps([
      'Stats',
      'Overall',
      'Goals',
      'ShotsPg',
      'Discipline',
      'Possession',
      'Passes',
      'Matches',
      'Assists',
      'Possession',
      'Passes'
    ]);
    const [teamStatList, setTeamStatList] = useState<any[]>([]);
    const [selectedCompetition, setSelectedCompetition] = useState(null);
    const [defaultCompetition, setDefaultCompetition] = useState(null);
    const [competitionVisible, setCompetitionVisible] = useState(false);

    const teamInfoList = [
      { transKey: 'Matches', key: 'matches' },
      { transKey: 'Goals', key: 'goals' },
      { transKey: 'Assists', key: 'assists' },
      { transKey: 'ShotsPg', key: 'shotsPg', format: true },
      { transKey: 'Discipline', key: 'discipline' },
      { transKey: 'Possession', key: 'ballPossession', append: ' %', format: true },
      { transKey: 'Passes', key: 'passesPercent', append: ' %', format: true },
    ];

    const formatValue = (value: any, append?: string, format?: boolean) => {
      const number = parseFloat(value);
      const formattedValue = format ? number.toFixed(1) : number;
      return !isNaN(number) ? `${formattedValue}${append || ''}` : 'N/A';
    };

    useEffect(() => {
      if (teamId) {
        getFootBallTeamStats({ teamId }).then(({ teamStatistics }:  any) => {
          const sortedStats = teamStatistics.sort(compare('matches')).map((i: any) => {
            const passesPercent = (100 * (i.passesAccuracy || 0)) / (i.passes || 1);
            const shotsPg = i.shots && i.matches ? (i.shots / i.matches).toFixed(1) : '0';
            return {
              ...i,
              passesPercent: passesPercent ? passesPercent.toFixed(1) : '0',
              shotsPg,
              discipline: {
                yellowCards: i.yellowCards || 0,
                redCards: i.redCards || 0
              }
            };
          });
          setTeamStatList(sortedStats);
          if (sortedStats.length > 0) {
            setDefaultCompetition(sortedStats[0].competition.id);
            setSelectedCompetition(sortedStats[0].competition.id);
          }
        });
      }
    }, [teamId]);

    const handleCompetitionChange = (value: any) => {
      setSelectedCompetition(value);
    };
  
    const filteredStats = teamStatList.find(
      (stat) => stat.competition.id === selectedCompetition
    ) || {};

    const pickerOption = teamStatList.map(({ competition }) => ({
      value: competition.id,
      label: competition.name
    }));

    const selectedCompetitionName = teamStatList.find(
      (stat) => stat.competition.id === selectedCompetition
    )?.competition.name;

    return (
      <div className="team-stats-container">
        <div className="container-title">
          {labelMaps.Stats}
          <>
            <Button className='team-stat-header-btn' size="small" onClick={() => setCompetitionVisible(true)}>
              <span className='team-stat-content'>
                <span className='team-stat-text'>{selectedCompetitionName}</span>
                <DownOutline className='team-stat-icon'/>
              </span>
            </Button>
            <Picker
              columns={[pickerOption]}
              visible={competitionVisible}
              onClose={() => setCompetitionVisible(false)}
              onConfirm={(val: any) => {
                handleCompetitionChange(val[0]);
              }}
            />
          </>
        </div>
        <div className='secondary-title'>
          <span className="secondary-title-text">#</span>
          <span className="secondary-title-text">{labelMaps.Overall}</span>
        </div>
        <div className="container-body">
          {teamInfoList.map(({ transKey, key, append, format }) => (
            <div key={key} className="team-details">
              <span className="team-value">{labelMaps[transKey]}</span>
              {key === 'discipline' ? (
                <div className="discipline-details">
                  <div className="y">{filteredStats.discipline?.yellowCards || '0'}</div>
                  <div className="r">{filteredStats.discipline?.redCards || '0'}</div>
                </div>
              ) : (
                <span className="team-value">{formatValue(filteredStats[key], append, format)}</span>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  }),
);

export default TeamStatsCard;