import { Avatar } from 'antd-mobile';
import { useEffect, useState } from 'react';

import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

import { TeamLink } from '@/components/Common/Football/Link';
import styles from './StandingsIntegral.less';

const StandingsIntegral: React.FC<any> = (props: any) => {
  const { type, data = [], promotions = [], isMatch = false } = props;
  const [isGroup, setIsGroup] = useState(0); //isGroup：1：杯赛   0：联赛

  const labelMaps = useTranslateKeysToMaps(['Team', 'Pts']);

  useEffect(() => {
    if (data.length) {
      setIsGroup(data[0].isGroup);
    }
  }, [data]);

  return (
    <div className={styles.integral_table_container}>
      <div className={styles.heater_style}>
        <div className={styles.index_column}>#</div>
        <div className={styles.team_column}>{labelMaps.Team}</div>
        <div className={styles.data_column}>P</div>
        <div className={styles.data_column}>W</div>
        <div className={styles.data_column}>D</div>
        <div className={styles.data_column}>L</div>
        <div className={styles.goals_column}>G</div>
        <div className={styles.pts_column}>{labelMaps.Pts}</div>
      </div>
      {data.map((item: any) => {
        const currentPosition = type === 0 ? 'position' : type === 1 ? 'homePosition' : 'awayPosition';
        return (
          <div className={styles.rows_style} key={item.teamId}>
            <div className={styles.index_column}>
              {isGroup || type !== 0 || isMatch ? (
                ''
              ) : (
                <span
                  className={`${styles.index_box} ${
                    item[currentPosition] <= 5
                      ? `${styles[`index${item[currentPosition]}`]}`
                      : item[currentPosition] > 16
                      ? `${styles.color999}`
                      : `${styles.color333}`
                  }`}
                />
              )}
            </div>
            <TeamLink className={styles.team_column} teamId={item.teamId}>
              <Avatar src={item.team?.logo} style={{ width: 20, height: 20 }} />
              <span className={styles.team_name}>{item.team?.name}</span>
            </TeamLink>
            <div className={`${styles.data_column} ${styles.bg_eff6fc}`}>
              {type === 0 ? item.total : type === 1 ? item.homeTotal : item.awayTotal}
            </div>
            <div className={`${styles.data_column} ${styles.bg_eff6fc}`}>
              {type === 0 ? item.won : type === 1 ? item.homeWon : item.awayWon}
            </div>
            <div className={`${styles.data_column} ${styles.bg_eff6fc}`}>
              {type === 0 ? item.draw : type === 1 ? item.homeDraw : item.awayDraw}
            </div>
            <div className={`${styles.data_column} ${styles.bg_eff6fc}`}>
              {type === 0 ? item.loss : type === 1 ? item.homeLoss : item.awayLoss}
            </div>
            <div className={`${styles.goals_column} ${styles.bg_F6F8F9}`}>
              {type === 0
                ? `${item.goals}:${item.goalsAgainst}`
                : type === 1
                ? `${item.homeGoals}:${item.homeGoalsAgainst}`
                : `${item.awayGoals}:${item.awayGoalsAgainst}`}
            </div>
            <div className={`${styles.pts_column} ${styles.bg_FEF4E1}`}>
              {type === 0 ? item.points : type === 1 ? item.homePoints : item.awayPoints}
            </div>
          </div>
        );
      })}
      {type === 0 && !isGroup ? (
        <div className={styles.legend_container}>
          {promotions.map((item: any) => (
            <div className={styles.legend_box} key={item.id}>
              <div className={`${styles.legend}`} style={{ background: item.color }} />
              <span className={styles.ml_16}>{item.name}</span>
            </div>
          ))}
        </div>
      ) : null}
    </div>
  );
};

export default StandingsIntegral;
