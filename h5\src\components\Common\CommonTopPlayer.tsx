import { Avatar } from 'antd-mobile';
import React, { useEffect, useState } from 'react';
import styles from './CommonTopPlayer.less';
import { PlayerLink } from './Football/Link';

interface Props {
  hasMoreButton?: boolean;
  type?: string;
  list: any[];
  showTeamInfo?: boolean;
}

const CommonTopPlayer = (props: Props) => {
  const { hasMoreButton = true, type = 'goals', list = [], showTeamInfo = true } = props;
  const [hiddenIconStatus, setHiddenIconStatus] = useState(true);
  const [listData, setListData] = useState<any[]>([]);

  useEffect(() => {
    if (hiddenIconStatus && list.length > 1) {
      setListData(list.slice(0, 3));
    } else {
      setListData(list);
    }
  }, [hiddenIconStatus, list]);

  const renderMoreButton = () => {
    return hiddenIconStatus ? (
      <div
        className={`${styles.action_style} ${!hiddenIconStatus ? styles.action_on : ''}`}
        onClick={() => setHiddenIconStatus((prevState) => !prevState)}
      >
        <i className="icon iconfont iconjiantou2" style={{ color: '#999', fontSize: 12 }} />
      </div>
    ) : (
      <></>
    );
  };

  return (
    <div className={styles.box}>
      <div className={styles.container}>
        {listData.map((item: any, index: any) => (
          <PlayerLink className={styles.row_item} key={item.player.id} playId={item.player.id}>
            <div className={styles.icon_column}>
              {index <= 2 ? (
                <span className={`icon iconfont icongoals1 f${index + 1}`} style={{ color: '#ffca28', fontSize: 20 }} />
              ) : (
                <span style={{ height: 20, width: 20 }}></span>
              )}
            </div>
            <Avatar src={item.player ? item.player.logo : ''} className={styles.avatar_style} />
            <div className={styles.player_info_column}>
              <span className={styles.player_name_style}>{item.player?.name}</span>
              {showTeamInfo ? (
                <div className={styles.team_style}>
                  <Avatar src={item.team?.logo} className={styles.team_avatar_style} />
                  <span className={styles.team_name_style}>{item.team?.name}</span>
                </div>
              ) : null}
            </div>
            <div className={styles.value_column}>
              {type === 'goals' && item.penalty ? `${item.goals}(${item.penalty})` : item[type]}
            </div>
          </PlayerLink>
        ))}
      </div>
      {hasMoreButton && renderMoreButton()}
    </div>
  );
};

export default React.memo(CommonTopPlayer);
