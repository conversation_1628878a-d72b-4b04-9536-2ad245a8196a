import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { CompetitionsTabH5 } from "iscommon/mobx/modules/competition";
import { inject, observer } from "mobx-react";
import { useCallback, useEffect, useState } from "react";
import { RightOutline } from 'antd-mobile-icons'
import { getCompetitionStatsTeam } from "iscommon/api/basketball/basketball-competition";
import Loading from "@/components/Loading";
import { Link } from "umi";
import './TeamCard.less'

interface Props {
  store?: any;
}

const TeamCard: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: {
        Basketball: {
          Competitions: {
            competitionId,
            currentSeason: { id: seasonId },
          },
        },
      },
    } = props;

    const labelMaps = useTranslateKeysToMaps([
      'Team',
      'Points',
      'Top',
      'topTeams',
    ])

    const [tablesList, setTablesList] = useState<any>([]);
    const [loading, setLoading] = useState<boolean>(false);
    
    useEffect(() => {
      if (competitionId && seasonId) {
        setLoading(true); 
        getCompetitionStatsTeam({
          competitionId,
          seasonId,
          scope: 5,
        }).then(({ list }: any) => {
          console.log('list', list)
          if (list.length > 0) {
            const topThree = list
              .sort((a: any, b: any) => b.points - a.points)
              .slice(0, 3);
            setTablesList(topThree);
          }
          setLoading(false);
        });
      }
    }, [competitionId, seasonId]);

    const goStanding = useCallback(() => {
      GlobalUtils.goToPage(PageTabs.competition, competitionId, {
        target: 'history',
        customTab: CompetitionsTabH5.teamStats
      });
    }, [competitionId]);

    console.log('team table list', tablesList)
    return (
      <div className="basketball-overview-team-card-container">
        <div className="container-title">
          {labelMaps.Team}
          <div onClick={goStanding}>
            <RightOutline />
          </div>
        </div>
        <div className="container-body">
          <div className='custom-player-standing-table'>
            <div className='table-header'>
              <div className="header-cell">#</div>
              <div className="header-cell team-cell">{labelMaps.topTeams}</div>
              <div className="header-cell">{labelMaps.Points}</div>
            </div>
            <div className="table-body">
              <Loading loading={loading} isEmpty={tablesList.length === 0}>
                {tablesList.map((item: any, index) => {
                  return (
                    <div className="table-row" key={index}>
                      <div className="table-cell">{index+1}</div>
                      <Link className='table-cell team-cell' key={index} to={GlobalUtils.getPathname(PageTabs.team, item.team.id)}>
                        <img src={item.team.logo} alt={item.team.name} className="team-icon" />
                        <span className="team-name">{item.team.name}</span>
                      </Link>
                      <div className="table-cell">{item.points}</div>
                    </div>
                  )
                })}
              </Loading>
            </div>
          </div>
        </div>
      </div>
    )
  }),
);

export default TeamCard;