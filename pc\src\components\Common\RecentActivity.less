.recent-activity-container {
  background: #fff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .recent-activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid #f0f0f0;

    .ant-typography {
      margin: 0;
      color: #1890ff;

      .anticon {
        margin-right: 8px;
      }
    }
  }

  .recent-activity-list {
    .recent-activity-item {
      cursor: pointer;
      padding: 12px;
      border-radius: 6px;
      transition: all 0.2s ease;
      border: 1px solid transparent;

      &:hover {
        background-color: #f5f5f5;
        border-color: #d9d9d9;
      }

      .ant-list-item-meta {
        align-items: center;

        .ant-list-item-meta-avatar {
          margin-right: 12px;
        }

        .ant-list-item-meta-content {
          .ant-list-item-meta-title {
            margin-bottom: 4px;

            .item-title {
              font-size: 14px;
              color: #262626;
            }
          }

          .ant-list-item-meta-description {
            .item-description {
              font-size: 12px;
              color: #8c8c8c;
            }
          }
        }
      }

      .ant-list-item-action {
        margin-left: 8px;

        .ant-btn {
          opacity: 0;
          transition: opacity 0.2s ease;
        }
      }

      &:hover .ant-list-item-action .ant-btn {
        opacity: 1;
      }
    }
  }
}

// Dark theme support
.dark-theme .recent-activity-container {
  background: #1f1f1f;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

  .recent-activity-header {
    border-bottom-color: #303030;

    .ant-typography {
      color: #1890ff;
    }
  }

  .recent-activity-list {
    .recent-activity-item {
      &:hover {
        background-color: #262626;
        border-color: #434343;
      }

      .item-title {
        color: #fff !important;
      }

      .item-description {
        color: #bfbfbf !important;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .recent-activity-container {
    padding: 12px;

    .recent-activity-header {
      margin-bottom: 12px;

      .ant-typography {
        font-size: 16px;
      }
    }

    .recent-activity-list {
      .recent-activity-item {
        padding: 8px;

        .ant-list-item-meta-content {
          .item-title {
            font-size: 13px;
          }

          .item-description {
            font-size: 11px;
          }
        }
      }
    }
  }
}

// Compact mode
.recent-activity-container.compact {
  padding: 8px;

  .recent-activity-header {
    margin-bottom: 8px;

    .ant-typography {
      font-size: 14px;
    }
  }

  .recent-activity-list {
    .recent-activity-item {
      padding: 6px 8px;

      .ant-list-item-meta {
        .ant-list-item-meta-avatar {
          margin-right: 8px;

          .ant-avatar {
            width: 24px;
            height: 24px;
            line-height: 24px;
            font-size: 12px;
          }
        }

        .ant-list-item-meta-content {
          .item-title {
            font-size: 12px;
          }

          .item-description {
            font-size: 10px;
          }
        }
      }
    }
  }
}
