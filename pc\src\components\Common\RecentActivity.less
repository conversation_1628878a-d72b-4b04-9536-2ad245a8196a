
.recent-activity-header {
  // background: #2c2c2c;
  // color: #fff;
  // display: flex;
  // align-items: center;
  // justify-content: space-between;
  // margin-bottom: 16px;
  // padding: 8px 12px 8px 12px;
  // border-radius: 16px 16px 0 0;
  // border-bottom: 1px solid #232323;
  background: #2c2c2c;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 5px 10px;
  border-radius: 16px 16px 0px 0px;
  font-size: 16px;
  font-weight: 700;
  justify-content: space-between;
  flex-direction: row;

  .clear-btn {
    height: auto;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    color: #fff;

    &:hover {
      opacity: 0.8;
    }
  }
}

.recent-activity-container {
  background: #121212;
  border-radius: 16px;
  padding: 16px;
  margin-bottom: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.18);

  .recent-activity-list {
    background: #121212;
    color: #fff;
    padding: 10px;
    border-radius: 0 0 16px 16px;

    .recent-activity-item {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 10px 8px;
      border-radius: 10px;
      transition: all 0.2s ease;
      border: 1px solid transparent;
      color: inherit;
      background: none;
      margin-bottom: 4px;

      &:hover {
        background-color: #232323;
        border-color: #333;
        transform: translateX(5px);
      }

      .ant-list-item-meta {
        align-items: center;

        .ant-list-item-meta-avatar {
          margin-right: 12px;
        }

        .ant-list-item-meta-content {
          .ant-list-item-meta-title {
            margin-bottom: 4px;
            .item-title {
              font-size: 14px;
              color: #fff;
            }
          }
          .ant-list-item-meta-description {
            .item-description {
              font-size: 12px;
              color: #bfbfbf;
            }
          }
        }
      }

      .ant-list-item-action {
        margin-left: 8px;
        .ant-btn {
          opacity: 0;
          transition: opacity 0.2s ease;
        }
      }
      &:hover .ant-list-item-action .ant-btn {
        opacity: 1;
      }
    }
  }
}

// Dark theme support
.dark-theme .recent-activity-container {
  background: #1f1f1f;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

  .recent-activity-header {
    border-bottom-color: #303030;
    .ant-typography {
      color: #1890ff;
    }
  }

  .recent-activity-list {
    background: #1f1f1f;
    .recent-activity-item {
      &:hover {
        background-color: #262626;
        border-color: #434343;
      }
      .item-title {
        color: #fff !important;
      }
      .item-description {
        color: #bfbfbf !important;
      }
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .recent-activity-container {
    padding: 12px;

    .recent-activity-header {
      margin-bottom: 12px;
      .ant-typography {
        font-size: 16px;
      }
    }

    .recent-activity-list {
      padding: 6px;
      .recent-activity-item {
        padding: 6px 4px;
        .ant-list-item-meta-content {
          .item-title {
            font-size: 13px;
          }
          .item-description {
            font-size: 11px;
          }
        }
      }
    }
  }
}

// Compact mode
.recent-activity-container.compact {
  padding: 8px;

  .recent-activity-header {
    margin-bottom: 8px;

    .ant-typography {
      font-size: 14px;
    }
  }

  .recent-activity-list {
    .recent-activity-item {
      padding: 6px 8px;

      .ant-list-item-meta {
        .ant-list-item-meta-avatar {
          margin-right: 8px;

          .ant-avatar {
            width: 24px;
            height: 24px;
            line-height: 24px;
            font-size: 12px;
          }
        }

        .ant-list-item-meta-content {
          .item-title {
            font-size: 12px;
          }

          .item-description {
            font-size: 10px;
          }
        }
      }
    }
  }
}
