import { useMount } from "ahooks";
import { getTeamInfoData } from "iscommon/api/football-team";
import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";
import { FallbackCategoryImage } from "iscommon/const/icon";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { inject, observer } from "mobx-react";
import { useState } from "react";
import { Link, useParams } from "umi";
import './TeamInfo.less'

interface TeamInfoResponse {
	teamHeaderInfo: {
    country: {
      name?: string;
      logo?: string;
    };
    avgAge?: number;
    foreignPlayers: number;
    foundationTime: number;
    nationalPlayers: number;
    totalPlayers: number;
  };
  venue: {
    name: string;
    city: string;
    capacity: number;
  };
  competitionList: Array<{
    id: string;
    categoryId: string;
    logo: string;
    name: string;
    shortName: string;
  }>;
  coach: {
    id: string;
    logo: string;
    name: string;
  }
}

const TeamInfo: React.FC = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Team: {
          teamHeaderInfo,
          teamHeaderInfo: { competition },
        },
      },
    } = props;
    
    const { categoryId } = useParams<{ categoryId: string }>();
    const [teamInfo, setTeamInfo] = useState<TeamInfoResponse| null>(null);

    useMount(() => {
      getTeamInfoData({ teamId: categoryId }).then((res) => {
        setTeamInfo(res);
      });
    });
    
    const labelMaps = useTranslateKeysToMaps([
      'Venue',
      'Stadium',
      'Capacity',
      'City',
      'TeamInfo',
      'Players',
      'ForeignPlayers',
      'LocalPlayers',
      'FoundedIn',
      'Coach',
      'Country',
      'AverageAge',
      'LeagueInfo',
    ]);

    const venue = teamInfo?.venue
    const coach = teamInfo?.coach
    const competitionList = teamInfo?.competitionList

    const venueInfoList = [
      { transKey: 'Stadium', key: venue?.name },
      { transKey: 'Capacity', key: venue?.capacity },
      { transKey: 'City', key: venue?.city },
    ];

    const teamInfoList = [
      { transKey: 'Coach', key: coach?.name },
      { transKey: 'Country', key: teamHeaderInfo?.country?.name , logo: teamHeaderInfo?.country?.logo },
      { transKey: 'FoundedIn', key: teamHeaderInfo?.foundationTime },
      { transKey: 'Players', key: teamHeaderInfo?.totalPlayers },
      { transKey: 'AverageAge', key: teamHeaderInfo?.avgAge?.toFixed(1) },
      { transKey: 'LocalPlayers', key: teamHeaderInfo?.nationalPlayers },
      { transKey: 'ForeignPlayers', key: teamHeaderInfo?.foreignPlayers },
    ];

    console.log('venue', venue)
    console.log('teaminfo', teamInfo)
    console.log('teamHeaderInfo', JSON.stringify(teamHeaderInfo.country))

    return (
      <div className="team-overview-info-container">

        <div className="team-competition-container">
          <div className="container-title">{labelMaps.LeagueInfo}</div>
          <div className="container-body">
            {competitionList?.map((item: any) => 
              <Link to={GlobalUtils.getPathname(PageTabs.competition, item.id)} key={item.id} className='competition-card'>
                <img className='competition-icon' src={item.logo || FallbackCategoryImage} alt={item.name}/>
                <span className="competition-name">{item.shortName}</span>
              </Link>
            )}
          </div>
        </div>

        <div className="team-info-container">
          <div className="container-title">{labelMaps.TeamInfo}</div>
          <div className="container-body">
            {teamInfoList.map(({ transKey, key, logo }) => (
              <div key={key} className="team-details">
                <span className="team-value">{labelMaps[transKey]}</span>
                <span className="team-value">
                  {transKey === 'Country' ? (
                    <div className="country-info">
                      {logo && <img src={logo} alt={key} className="country-logo" />}
                      <span>{key || '-'}</span>
                    </div>
                  ) : (
                    key || '-'
                  )}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="team-venue-container">
          <div className="container-title">{labelMaps.Venue}</div>
          <div className="container-body">
            {venueInfoList.map(({ transKey, key }) => (
              <div key={key} className='venue-details'>
                <span className="venue-value">{labelMaps[transKey]}</span>
                <span className="venue-value">{[key as keyof typeof venue] || '-'}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  })
)

export default TeamInfo;