.popular-competitions-section {
  display: flex;
  flex-direction: column;
  background: transparent;
  border: 1px solid #63717a;
  border-radius: 12px;
  backdrop-filter: blur(12px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.4);
  width: 280px;
  max-height: 420px;

  .popular-competitions-title {
    font-weight: 600;
    font-size: 18px;
    color: #fff;
    padding: 12px;
    border-radius: 12px 12px 0px 0px;
    border-bottom: 1px solid #63717a;
  }

  .popular-competitions-body {
    display: flex;
    flex-direction: column;
    padding: 6px;
    overflow-y: auto;

    .popular-competition-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;
      padding: 4px;
      transition: all 0.3s ease;

      &:hover {
        transform: translateX(5px);
      }
      
      &.selected {
        border-radius: 12px;
        background-color: lighten(#383838, 20%) !important;
      }
      
      .left-content {
        display: flex;
        align-items: center;
        gap: 12px;
        overflow: hidden;

        .popular-competition-logo {
          width: 26px;
          height: 26px;
          border-radius: 50%;
          object-fit: cover;
        }
        
        .popular-competition-name {
          font-size: 14px;
          color: #ffffff;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .live-indicator {
        background: #ff4d4f;
        color: #fff;
        font-size: 10px;
        font-weight: 700;
        padding: 2px 6px;
        border-radius: 4px;
        flex-shrink: 0;
        animation: pulse 2s infinite;
      }
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.9;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.9;
  }
}