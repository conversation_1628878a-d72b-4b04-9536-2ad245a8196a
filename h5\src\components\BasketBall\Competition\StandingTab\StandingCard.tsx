import Loading from "@/components/Loading";
import { Collapse } from "antd-mobile";
import { getCompetitionStandings } from "iscommon/api/basketball/basketball-competition";
import GlobalUtils, { PageTabs } from "iscommon/const/globalConfig";
import { FallbackImage } from "iscommon/const/icon";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import _ from "lodash";
import { inject, observer } from "mobx-react";
import { useState, useEffect } from "react";
import { Link } from "umi";
import './StandingCard.less'

interface Props {
  store?: any;
}

const StandingsCard: React.FC<Props> = inject('store')(
  observer((props) => {
    const {
      store: {
        Basketball: {
          Competitions: {
            competitionId,
            currentSeason: { id: seasonId },
          },
        },
      },
    } = props;

    const labelMaps = useTranslateKeysToMaps(['All', 'Home', 'Away', 'Standings', 'Team']);

    const [loading, setLoading] = useState<boolean>(false);
    const [data, setData] = useState<any[]>([]);
    const [, setFilterList] = useState<any>([]);
    const [, setCurFilterId] = useState('');
    const [, setTablesList] = useState<any[]>([]);
  
    useEffect(() => {
      if (seasonId && competitionId) {
        setLoading(true);
        getCompetitionStandings({
          seasonId,
          competitionId,
          scope: 5,
        }).then(({ tables }: any) => {
          if (tables?.length > 0) {
            setData(tables);
            if (competitionId === '49vjxm8xt4q6odg') {
              const filter = tables.map((item: any) => ({ id: item.name, name: item.name }));
              setFilterList(filter);
              setCurFilterId(filter[0].id);
            } else {
              setTablesList(tables[0]?.standings || []);
            }
          } else {
            setTablesList([]);
          }
          setLoading(false);
        });
      }
    }, [competitionId, seasonId]);

    const titleMap = [
      { label: 'W', value: 'won', },
      { label: 'L', value: 'lost', },
      { label: 'Win%', value: ({ won_rate }) => (won_rate * 100).toFixed(1), },
      { label: 'STR', value: 'streaks', },
      { label: 'GB', value: ({game_back}) => game_back === '-' ? '0.0' : game_back, },
    ];

    const renderTable = (tableData: any) => {
      return (
        <div className='custom-common-standing-table'>
          <div className='header-row'>
            <div className="header-cell">#</div>
            <div className="header-cell team-cell">{labelMaps.Team}</div>
            {titleMap.map((item, index) => {
              return (
                <div className='header-cell' key={index}>{item.label}</div>
              )
            })}
          </div>
          {tableData.standings.map((item: any, index: any) => {
            return (
              <Link className="table-row" key={item?.team?.id} to={GlobalUtils.getPathname(PageTabs.team, item?.team?.id)}>
                <div className="table-cell">{item?.position}</div>
                <div className="table-cell team-cell">
                  <img className="team-logo" src={item?.team?.logo || FallbackImage} alt={item?.team?.name} loading="lazy"/>
                  <span className='team-name'>{item?.team?.name}</span>
                </div>
                {titleMap.map((title, index) => {
                  return (
                    <div className='table-cell' key={index}>
                      {_.isString(title.value) ? item[title.value] : title.value(item)}
                    </div>
                  );
                })}
              </Link>
            )})
          }
        </div>
      )
    };

    return (
      <div className="basketball-standing-card-container">
        <div className="container-title">{labelMaps.Standings}</div>
        <div className="container-body">
          <Loading loading={loading} isEmpty={!data.length}>
            {competitionId === '49vjxm8xt4q6odg' ? (
              <Collapse className='custom-collapse'>
                {data.map((item: any, index) => (
                  <Collapse.Panel 
                    key={index} 
                    title={
                      <div className="panel-header">
                        <span className="panel-title">{item.name}</span>
                      </div>
                    }>
                    {renderTable(item)}
                  </Collapse.Panel>
                ))}
              </Collapse>
            ) : (
              renderTable(data)
            )}
          </Loading>
        </div>
      </div>
    );
  }),
);

export default StandingsCard;