// @ts-nocheck
import { makeAutoObservable } from 'mobx';

export const PlayerTab = {
  overview: 'Overview',
  champions: 'Champions',
  stats: 'Stats',
};

export const PlayerTabH5 = {
  overview: 'Overview',
  champions: 'Champions',
  playerStatistics: 'Statistics',
  matches: 'Matches',
};

export default class Player {
  constructor() {
    this.playerId = '';
    this.playerHeaderInfo = {
      player: {
        id: '',
        teamId: '',
        name: '',
        shortName: '',
        logo: '',
        national: '',
        nationalLogo: '',
        age: 0,
        birthday: 0,
        weight: 0,
        height: 0,
        nationality: '',
        countryId: '',
        countryDto: null,
        marketValue: 0,
        marketValueCurrency: '',
        contractUntil: 0,
        preferredFoot: 0,
        ability: '',
        characteristics: '',
        position: '',
        positions: '',
        parsedPositions: {
          mainLocation: '',
          secondaryLocations: [],
        },
        parsedAbility: [],
        parsedCharacteristics: [],
        shirtNumber: null,
      },
      team: {
        id: '',
        name: '',
        logo: '',
        shortName: '',
        marketValueCurrency: '',
        competitionId: '',
        countryId: '',
        coachId: '',
        venueId: '',
        national: '',
        countryLogo: '',
        foundationTime: 0,
        website: '',
        marketValue: 0,
        totalPlayers: 0,
        foreignPlayers: 0,
        nationalPlayers: 0,
      },
    };

    makeAutoObservable(this);
  }

  changePlayerId(id = '') {
    this.playerId = id;
  }

  changePlayerHeaderInfo(data = {}) {
    this.playerHeaderInfo = data;
  }
}
