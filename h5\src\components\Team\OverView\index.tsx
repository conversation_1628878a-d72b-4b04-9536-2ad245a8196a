import { inject, observer } from 'mobx-react';
import moment from 'moment';
import { useCallback, useEffect, useState } from 'react';

import CommonTopPlayer from '@/components/Common/CommonTopPlayer';
import Mvp from '@/components/Common/Mvp';
import NextSchedule from '@/components/Common/NextSchedule';
import TeamBasicInfo from '@/components/Common/TeamBasicInfo';
import SectionTitle from '@/components/Competition/Overview/SectionTitle';
import { getStatsPlayer } from 'iscommon/api/competition';
import { getFootBallTeamMatches, getTeamInfoData } from 'iscommon/api/football-team';
import GlobalUtils, { PageTabs } from 'iscommon/const/globalConfig';
import { translate } from 'iscommon/i18n/utils';
import { TeamTabH5 } from 'iscommon/mobx/modules/team';

import TeamLastTransfer from './TeamLastTransfer';
// import TeamResult from './TeamResult';

import './index.less';
import TeamInfo from './TeamInfo';
import TeamStandings from '../Standings';
import TeamStatsCard from './TeamStatsCard';
import TeamTopPlayer from './TeamTopPlayer';

const TeamOverView = inject('store')(
  observer((props: any) => {
    const {
      store: {
        Team: {
          teamHeaderInfo,
          teamHeaderInfo: { competition, currentSeason, mvp },
          teamId,
        },
      },
    } = props;
    const [lastSixMatch, setLastSixMatch] = useState<any[]>([]);
    const [nextMatch, setNextMatch] = useState<any>(null);
    const [statsPlayer, setStatsPlayer] = useState<any[]>([]);
    // console.log('lastSixMatch', lastSixMatch);
    useEffect(() => {
      getFootBallTeamMatches({
        teamId,
        month: moment().format('YYYY-MM'),
        externalType: 0,
      }).then(({ matchList }: any) => {
        if (matchList.length > 0) {
          setNextMatch(matchList[0]);
        }
      });
      getFootBallTeamMatches({
        teamId,
        month: moment().format('YYYY-MM'),
        externalType: 1,
      }).then(({ matchList }: any) => {
        setLastSixMatch(matchList);
      });
    }, [teamId]);

    useEffect(() => {
      if (teamId && currentSeason.id && competition.id) {
        getStatsPlayer({
          teamId,
          competitionId: competition.id,
          seasonId: currentSeason.id,
          property: 'goals',
        }).then(({ playerStatistics }: any) => {
          // console.log('statsPlayer', playerStatistics);
          setStatsPlayer(playerStatistics);
        });
      }
    }, [competition.id, currentSeason.id, teamId]);

    const goSchedule = useCallback(() => {
      GlobalUtils.goToPage(PageTabs.team, teamId, {
        target: 'history',
        customTab: TeamTabH5.schedule,
      });
    }, [teamId]);

    console.log('statsplayer', statsPlayer)

    // const goPlayerStats = useCallback(() => {
    //   GlobalUtils.goToPage(PageTabs.team, teamId, {
    //     target: 'history',
    //     customTab: TeamTabH5.playerStats,
    //   });
    // }, [teamId]);

    return (
      <div className="teamOverView">
        {/* <SectionTitle leftTitle={translate('Results')} rightTitle="More" /> */}
        {/* <TeamResult lastSixMatch={lastSixMatch} teamId={teamId} /> */}
        {/* {nextMatch ? <NextSchedule nextMatch={nextMatch} clickMore={goSchedule} /> : null} */}

        {/* <SectionTitle leftTitle={translate('homeTopScorers')} rightTitle="More" onClick={goPlayerStats} />*/}
        {/* <CommonTopPlayer hasMoreButton={false} type={'goals'} list={statsPlayer} showTeamInfo={false} />  */}

        {/* <SectionTitle leftTitle="Most Value Player" />
        <Mvp mvp={mvp} />

        <TeamLastTransfer teamId={teamId} teamHeaderInfo={teamHeaderInfo} />

        <SectionTitle leftTitle={translate('Info')} />
        <TeamBasicInfo teamId={teamId} teamHeaderInfo={teamHeaderInfo} /> */}
        <TeamStandings />
        <TeamTopPlayer />
        <TeamStatsCard />
        <TeamInfo />
      </div>
    );
  }),
);

export default TeamOverView;
