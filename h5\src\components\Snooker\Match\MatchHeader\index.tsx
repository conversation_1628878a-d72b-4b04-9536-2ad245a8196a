import SmallballMatchHeader from '@/components/SmallGameCommon/Match/MatchHeader';
import { SnookerInLiveStatusEnum, SnookerMatchStatusCodeToText, SnookerStatusCodeEnum } from 'iscommon/const/snooker/constant';
import { getSnookerScoreList } from 'iscommon/utils/dataUtils';

const MatchHeader = () => {
  const getHomeBasketBallMatchTimeText = (matchHeaderInfo) => {
    const { matchStatus, scores, servingSide } = matchHeaderInfo;
    const list: any = getSnookerScoreList(matchStatus, scores);
    const isIng = SnookerInLiveStatusEnum.includes(matchStatus);
    // const latestScore = list[list.length - 1];
    const text = (SnookerMatchStatusCodeToText as any)[matchStatus];
    const res = {
      statusText: text,
      isIng,
    };
    return res;
  };

  return (
    <SmallballMatchHeader
      checkIsIng={(matchStatus) => SnookerInLiveStatusEnum.includes(matchStatus)}
      getHomeScore={({ scores }) => scores?.ft?.[0] || 0}
      getAwayScore={({ scores }) => scores?.ft?.[1] || 0}
      notStartedCode={SnookerStatusCodeEnum.NotStarted}
      getHomeBasketBallMatchTimeText={getHomeBasketBallMatchTimeText}
      // ballIconCode="#icontabletennis_ball"
    />
  );
};

export default MatchHeader;
