// .baseInfo {
//   position: relative;
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   flex-wrap: wrap;
//   padding: 24px;
//   background-color: #fff;

//   .baseItem {
//     display: flex;
//     flex-direction: column;
//     justify-content: center;
//     align-items: center;
//     width: 50%;
//     height: 130px;
//     box-sizing: border-box;

//     &.topLeft {
//       padding-right: 96px;
//       border-right: 1px solid #E3E3E3;
//       border-bottom: 1px solid #E3E3E3;
//     }

//     &.topRight {
//       padding-left: 96px;
//       border-left: 1px solid #E3E3E3;
//       border-bottom: 1px solid #E3E3E3;
//     }

//     &.bottomLeft {
//       padding-right: 96px;
//       border-top: 1px solid #E3E3E3;
//       border-right: 1px solid #E3E3E3;
//     }

//     &.bottomRight {
//       padding-left: 96px;
//       border-top: 1px solid #E3E3E3;
//       border-left: 1px solid #E3E3E3;
//     }
//   }

//   .baseItemCenter {
//     position: absolute;
//     left: 50%;
//     top: 50%;
//     padding: 16px;
//     background: #FFFFFF;
//     border-radius: 50%;
//     transform: translate(-50%, -50%);

//     .baseCenterWrap {
//       position: relative;
//       display: flex;
//       justify-content: center;
//       align-items: center;
//       width: 160px;
//       height: 160px;
//       border: 2px solid #E3E3E3;
//       border-radius: 50%;
//       overflow: hidden;

//       .baseCenterImg {
//         position: absolute;
//         left: 50%;
//         top: 50%;
//         display: block;
//         width: 64px;
//         height: 72px;
//         transform: translate(-50%, -50%);
//         z-index: 0;
//       }

//       .baseCenterInfo {
//         position: relative;
//         z-index: 1;
//         font-size: 14px;
//         color: #666;
        
//         font-weight: 500;
//       }
//     }
//   }
// }

// .baseDesc {
//   background-color: #fff;
//   padding-bottom: 24px;

//   .baseRow {
//     display: flex;
//     align-items: center;
//     justify-content: space-between;
//     height: 44px;
//     padding: 0 24px;

//     .label {
//       font-size: 24px;
      
//       font-weight: 500;
//       color: #999999;
//       line-height: 24px;
//     }

//     .value {
//       display: flex;
//       align-items: center;
//       font-size: 24px;
      
//       font-weight: 500;
//       color: #333333;
//       line-height: 24px;

//       .logo {
//         width: 30px;
//         height: 30px;
//         margin-right: 8px;
//       }
//     }
//   }
// }

.player-base-info-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .container-body {
    background: #1e1e1e;
    padding: 10px;

    .team-container {
      background: #121212;
      border-radius: 24px;
      padding: 20px;
      margin: 5px;
      display: flex;
      align-items: center;

      .team-icon {
        width: 58px;
        height: 58px;
        border-radius: 50%;
        margin: 5px 10px;
        overflow: hidden;
        object-fit: cover;
      }

      .team-detail {
        display: flex;
        flex-direction: column;

        .team-name {
          font-size: 22px;
          color: #fff;
        }

        .contract-date {
          font-size: 20px;
          color: #C0C5C9;
        }
      }
    }

    .player-info-container {
      background: #121212;
      display: flex;
      margin: 5px;
      border-radius: 24px;
      flex-wrap: wrap;

      .detail-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 15px;
        color: #fff;
        box-sizing: border-box;
        flex: 1 1 calc(50% - 16px);

        .detail-info {
          display: flex;
          flex-direction: row;
          align-items: center;

          .detail-logo {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 5px 10px;
            overflow: hidden;
            background-size: cover;
          }
        }

        .detail-title {
          color: #fff;
          font-size: 22px;
        }

        .detail-value {
          color: #C0C5C9;
          font-size: 20px;
        }
      }
    }
  }
}