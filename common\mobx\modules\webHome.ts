// @ts-nocheck
import { makeAutoObservable } from "mobx";
import { getFavorites, getFavoritesCompetitions } from "../../api/favorite";
import { HomeGameTab, InLiveStatusEnum } from "../../const/constant";
import { GlobalConfig } from "../../const/globalConfig";
import { getStore, saveStore } from "../../store";
import { FavoriteStoreH5Key } from "../../store/favoriteStore";
import { isLiveEditModel, momentTimeZone, simpleCloneObj } from "../../utils";
class WebHome {
  constructor() {
    // 0-Live，1-ALL, 2-Finished, 3-Scheduled
    this.serverTime = GlobalConfig.serverTime;
    this.currentGameTab = isLiveEditModel() ? HomeGameTab.All : HomeGameTab.Live;
    this.isSortByTime = false;
    this.date = momentTimeZone();
    this.odds = 0; // 0: 1x2, 1: Asian Handicap, 2: Total Goals 3: Total Corners
    this.favoriteMatches = [];
    this.favoriteCompetitions = [];
    this.h5StickIdList = getStore(FavoriteStoreH5Key.match, []);

    this.homeCompetitionList = [];
    this.homeCompetitionOdds = {};
    this.upcomingCompetitions = [];
    // todo
    this.homeOddsMaps = {};
    this.upcomingOddsMaps = {};
    // latest matchStatus only from mqtt
    this.latestMatchStatus = {}

    makeAutoObservable(this);

    getFavorites().then((ids) => (this.favoriteMatches = ids));
    getFavoritesCompetitions().then(
      (list) => (this.favoriteCompetitions = list)
    );
  }

  // filter current tab matches
  get homeCompetitions() {
    try {
      if(this.currentGameTab === HomeGameTab.Live) {
        let comp = []
        for(let i = 0; i < this.homeCompetitionList.length ; i ++) {
          let matches = []
          const cItem = this.homeCompetitionList[i]
          for(let v of cItem.matches) {
            const {matchId} = v
            const lId = this.latestMatchStatus[matchId]
            const matchStatus = lId !== null && lId !== undefined ?
              this.latestMatchStatus[matchId] : v.matchStatus 
            if(InLiveStatusEnum.includes(matchStatus)) {
              matches.push(v)
            }
          }
          if(matches.length > 0) {
            comp.push({
              ...cItem,
              matches
            })
          }
        }
        return isLiveEditModel() ? comp.slice(0, 7) : comp
      } else {
        return isLiveEditModel() ? this.homeCompetitionList.slice(0, 7) : this.homeCompetitionList
      }
    } catch(e) {
      return isLiveEditModel() ? this.homeCompetitionList.slice(0, 7) : this.homeCompetitionList
    }
  }

  setLatestMatchStatus(data = {}) {
    const {key, value, allStatusMap} = data
    if(key) {
      this.latestMatchStatus[key] = value
    } else {
      this.latestMatchStatus = allStatusMap
    }
  }

  // mobx set list
  setHomeCompetitions(list) {
    this.homeCompetitionList = list;
  }

  setUpComingCompetitions(list) {
    this.upcomingCompetitions = list;
  }

  setHomeOdds(listMap) {
    this.homeCompetitionOdds = listMap;
  }

  switchHomeGameTab(nextTab) {
    this.currentGameTab = nextTab;
  }

  changeServerTime(time) {
    this.serverTime = time;
  }

  changeSortByTime() {
    this.isSortByTime = !this.isSortByTime;
  }

  changeDate(newDate) {
    this.date = newDate;
  }

  changeOdds(newOdds) {
    this.odds = newOdds;
  }

  changeFavoriteMatches(ids) {
    this.favoriteMatches = JSON.parse(JSON.stringify(ids));
  }

  changeFavoriteCompetitions(list) {
    this.favoriteCompetitions = JSON.parse(JSON.stringify(list));
  }

  addH5Stick(id) {
    console.log(id);
    this.h5StickIdList.push(id);
    this.h5StickIdList = [].concat(this.h5StickIdList);
    saveStore(FavoriteStoreH5Key.match, this.h5StickIdList);
  }

  removeH5Stick(id) {
    const index = this.h5StickIdList.indexOf(id);
    this.h5StickIdList.splice(index, 1);
    this.h5StickIdList = [].concat(this.h5StickIdList);
    saveStore(FavoriteStoreH5Key.match, this.h5StickIdList);
  }
}

export default WebHome;
