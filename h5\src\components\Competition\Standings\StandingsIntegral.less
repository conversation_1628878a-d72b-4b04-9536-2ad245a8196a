.integral_table_container {
  width: 100%;
  display: flex;
  flex-direction: column;
}

.heater_style {
  height: 60px;
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: white;
  color: rgba(153, 153, 153, 0.7);
  border-bottom: 1px solid hsla(0, 0%, 89%, 0.6);
}

.rows_style {
  height: 60px;
  display: flex;
  flex-direction: row;
  align-items: center;
  background-color: white;
  color: black;
  border-bottom: 1px solid hsla(0, 0%, 89%, 0.6);
}

.index_column {
  width: 50px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
}

.team_column {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding-left: 20px;
}

.team_logo {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  overflow: hidden;
}

.team_name {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-left: 12px;
  font-size: 24px;
}

.data_column {
  width: 60px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  color: rgba(51, 51, 51, .7);
}

.bg_eff6fc {
  background: #eff6fc;
}

.bg_F6F8F9 {
  background: #f6f8f9;
}

.bg_FEF4E1 {
  background: #fef4e1;
}

.goals_column {
  height: 100%;
  flex: 0.5;
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgba(51, 51, 51, .7);
}

.pts_column {
  width: 65px;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgba(51, 51, 51, .7);
}

.index_box {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.color333 {
  color: #333 !important;
}

.color999 {
  background: #999999 !important;
}

.index1 {
  background: #ff9966 !important;
}

.index2 {
  background: #ff9966 !important;
}

.index3 {
  background: #00d200 !important;
}

.index4 {
  background: #52b2f5 !important;
}

.index5 {
  background: #57a87b !important;
}

.legend_container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 10px 0 10px 24px;

  .legend_box {
    display: flex;
    flex-direction: row;
    align-items: center;
    font-size: 24px;

    .legend {
      // margin-left: 15px;
      width: 16px;
      height: 16px;
      border-radius: 50%;
      // margin-right: 8px;
    }

    .legend_name {
      margin-left: 16px;
      color: #999;
    }

    .ml_16 {
      margin-left: 16px;
    }

    .mr_20 {
      margin-left: 20px;
    }
  }
}
