// @ts-nocheck
import igsRequest from 'iscommon/request/instance';

export const sendMessage = async ({ roomId, message, userId, name, avatar } = {}) => {
  try {
    return await igsRequest.post('chatroom/message/send', {
      noBaseUrl: true,
      roomId,
      message,
      userId,
      name,
      avatar,
    });
  } catch (e) {
    console.log('sendMessage error:', e);
    return null;
  }
};

export const getMessageList = async ({ roomId, limit = 100, serial = 0 } = {}) => {
  try {
    return await igsRequest.post('chatroom/message/list', {
      noBaseUrl: true,
      roomId,
      limit,
      serial,
    });
  } catch (e) {
    console.log('getMessageList error:', e);
    return null;
  }
};

export const roomLogin = async ({ uid, name, avatar } = {}) => {
  try {
    return await igsRequest.post('chatroom/login', {
      noBaseUrl: true,
      type: 1,
      uid,
      name,
      avatar,
    });
  } catch (e) {
    console.log('roomLogin error:', e);
    return null;
  }
};
