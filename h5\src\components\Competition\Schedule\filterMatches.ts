export interface CurrentStage {
  roundCount: number;
  id: string;
  name: string;
  groupCount: number;
}

export type Matches = {
  id: string;
  [key: string]: any;
}[];

export const getCupNoRoundFilterData = (matches: Matches, currentStage: CurrentStage, curStageId: string) => {
  const res = [];
  const { id, name } = currentStage;
  const arr = matches.filter((item) => item.stageId == id);
  res.push({
    title: name,
    isCurrent: curStageId === id,
    list: arr.sort((a: any, b: any) => a.matchTime - b.matchTime),
  });

  return res;
};

export const getCupFilterData = (
  matches: Matches,
  currentStage: CurrentStage,
  initCount = 0,
  curRound: number,
  curStageId: string,
  roundText: string,
) => {
  const res = [];
  const { roundCount, id, name } = currentStage;
  for (let j = 0; j <= roundCount; j++) {
    const cr = j + 1 + initCount;
    const arr = matches.filter((item: any) => item.roundNum == cr && item.stageId == id);
    if (arr.length > 0) {
      res.push({
        title: `${name}, ${roundText} ${cr}`,
        isCurrent: curRound === j + 1 && curStageId === id,
        list: arr.sort((a: any, b: any) => a.matchTime - b.matchTime),
      });
    }
  }

  return res;
};

export const getLeagueFilterData = (
  matches: Matches,
  currentStage: CurrentStage,
  initCount = 0,
  curRound: number,
  curStageId: string,
  roundText: string,
  leagueTitle: string,
) => {
  const res = [];
  const { roundCount, id } = currentStage;
  for (let j = 0; j <= roundCount; j++) {
    const cr = j + 1 + initCount;
    const arr = matches.filter((item: any) => item.roundNum == cr && item.stageId == id);
    if (arr.length > 0) {
      res.push({
        title: `${leagueTitle}, ${roundText} ${cr}`,
        isCurrent: curRound === j + 1 && curStageId === id,
        list: arr.sort((a: any, b: any) => a.matchTime - b.matchTime),
      });
    }
  }
  return res;
};
