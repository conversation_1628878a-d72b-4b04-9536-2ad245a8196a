// @ts-nocheck
import { px2vw } from 'iscommon/utils/vw';

// Technical Features Field Description
export const PLAYER_CHARACTERISTICS_LIST = [
    null,
    { char: 'Unloading' },
    { char: 'Penalty Kick' },
    { char: 'Direct Free Kick' },
    { char: 'Long Shot' },
    { char: 'Single Shot' },
    { char: 'pass' },
    { char: 'organize the attack' },
    { char: 'dribble' },
    { char: 'interrupt the ball' },
    { char: 'tackle' },
    { char: 'stability' },
    { char: 'excellent' },
    { char: 'long pass' },
    { char: 'ball control' },
    { char: 'air confrontation' },
    { char: 'ground confrontation' },
    { char: 'error tendency' },
    { char: 'Discipline' },
    { char: 'Punch penalty' },
    { char: 'Reaction' },
    { char: 'Abandon goal to participate in attack' },
    { char: 'High ball interception' },
    { char: 'Handle the ball' },
    { char: 'Long Shots' },
    { char: 'Stan<PERSON>' },
    { char: 'High Pressing' },
    { char: 'Long Shots Save' },
    { char: 'Crossing' },
    { char: 'Offside awareness' },
    { char: 'Close shot saves' },
    { char: 'Concentration' },
    { char: 'Defensive participation' },
    { char: 'Key passing Ball' },
    { char: 'Header' },
    { char: 'Set ball' },
    { char: 'Straight pass' },
    { char: 'Counter attack' },
    { char: 'One kick' },
    { char: 'up High ball' },
    { char: 'fouling' },
    { char: 'inward cut' },
    { char: 'boxing ball' },
    { char: 'clearance' },
];

/**
 * Detailed position field description:
 * LW-left forward
 * RW-right forward
 * ST-forward
 * AM- Attacking type
 * ML-left midfield
 * MC-center midfield
 * MR-right midfield
 * DM-Defensive center
 * DL-left back
 * DC-center back
 * DR-right back
 * GK-goalkeeper
 */
export const PLAYER_POSITION_MAP = {
    ST: {
        top: 88,
        left: 297,
    },
    LW: {
        top: 24,
        left: 297,
    },
    RW: {
        top: 152,
        left: 297,
    },
    AM: {
        top: 88,
        left: 246,
    },
    ML: {
        top: 24,
        left: 176,
    },
    MC: {
        top: 88,
        left: 176,
    },
    MR: {
        top: 152,
        left: 176,
    },
    DM: {
        top: 88,
        left: 125,
    },
    DL: {
        top: 24,
        left: 70,
    },
    DC: {
        top: 88,
        left: 70,
    },
    DR: {
        top: 152,
        left: 70,
    },
    GK: {
        top: 88,
        left: 14,
    },
}

const PLAYER_POSITION_DATA_H5 = {
    ST: {
        top: 60,
        left: 84,
    },
    LW: {
        top: 60,
        left: 12,
    },
    RW: {
        top: 60,
        left: 156,
    },
    AM: {
        top: 104,
        left: 84,
    },
    ML: {
        top: 144,
        left: 12,
    },
    MC: {
        top: 144,
        left: 84,
    },
    MR: {
        top: 144,
        left: 156,
    },
    DM: {
        top: 180,
        left: 84,
    },
    DL: {
        top: 216,
        left: 12,
    },
    DC: {
        top: 216,
        left: 84,
    },
    DR: {
        top: 216,
        left: 156,
    },
    GK: {
        top: 270,
        left: 84,
    },
}

export const PLAYER_POSITION_MAP_H5 = (function () {
    const map = {};
    for (const key in PLAYER_POSITION_DATA_H5) {
        map[key] = {
            top: px2vw(PLAYER_POSITION_DATA_H5[key].top),
            left: px2vw(PLAYER_POSITION_DATA_H5[key].left),
        };
    }
    return map;
})();
