.overview-live-animation-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;

  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;

    .container-item {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;

      &.active {
        background: rgba(70, 69, 69, 1);
        color: rgba(15, 128, 218, 1);
        border-radius: 24px;
      }

      &:first-child.container-item {
        width: 100%;
      }
    }
  }

  .live-animation-container {
    width: 100%;
    background: #121212;
    position: relative;
    text-align: center;

    .frame-container {
      width: 100%;
      padding: 10px;
      border: none;
    }
  }
}