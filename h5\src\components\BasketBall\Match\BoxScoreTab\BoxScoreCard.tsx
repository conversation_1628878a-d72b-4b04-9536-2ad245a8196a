import { Button, Dialog, Space } from 'antd-mobile';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { useMemo, useState } from 'react';
import styles from './BoxScoreCard.less';
import BoxScoreTable from './BoxScoreTable';
import './BoxScoreCard.less'
interface Props {
  matchHeaderInfo: any;
  result: any;
}

const BoxScoreCard = (props: Props) => {
  const { matchHeaderInfo, result } = props;
  const labelMaps = useTranslateKeysToMaps([
    'BoxScore',
    'OnTheCourt',
    'h5Glossary',
    'OK',
    'MinutesPlayed',
    'Points',
    'Rebounds',
    'Assists',
    'Blocks',
    'Steals',
    'FieldGoals',
    'ThreePoints',
    'FreeThrows',
    'OffensiveRebounds',
    'DefensiveRebounds',
    'Turnovers',
    'PersonalFouls',
    'PlusMinus',
  ]);
  const [sourceData, setSourceData] = useState([]);
  const [tablesList, setTablesList] = useState<any>({});
  const [loading, setLoading] = useState<boolean>(false);
  const [currentActive, setCurrentActive] = useState(0);
  const [dialogShow, setDialogShow] = useState(false);
  const buttonGroupMaps = [
    {
      label: labelMaps.All,
      value: 0,
    },
    {
      label: labelMaps.Home,
      value: 1,
    },
    {
      label: labelMaps.Away,
      value: 2,
    },
  ];

  const clickGlossary = () => {
    Dialog.alert({
      content: (
        <div className={styles.block}>
          <div>{`MIN - ${labelMaps.MinutesPlayed}`}</div>
          <div>{`PTS - ${labelMaps.Points}`}</div>
          <div>{`REB - ${labelMaps.Rebounds}`}</div>
          <div>{`AST - ${labelMaps.Assists}`}</div>
          <div>{`BLK - ${labelMaps.Blocks}`}</div>
          <div>{`STL - ${labelMaps.Blocks}`}</div>
          <div style={{ marginTop: '0.5rem' }}>{`FG - ${labelMaps.FieldGoals}`}</div>
          <div>{`3PT - ${labelMaps.ThreePoints}`}</div>
          <div>{`FT - ${labelMaps.FreeThrows}`}</div>
          <div style={{ marginTop: '0.5rem' }}>{`OREB - ${labelMaps.OffensiveRebounds}`}</div>
          <div>{`DREB - ${labelMaps.DefensiveRebounds}`}</div>
          <div>{`TOV - ${labelMaps.Turnovers}`}</div>
          <div>{`A/T - ${labelMaps.Turnovers}/${labelMaps.Turnovers}`}</div>
          <div>{`PF - ${labelMaps.PersonalFouls}`}</div>
          <div>{`+/— - ${labelMaps.PlusMinus}`}</div>
        </div>
      ),
      confirmText: labelMaps.OK,
    });
  };

  const list = useMemo(() => {
    return currentActive === 0 ? result.home : result.away;
  }, [currentActive]);

  const { homeTeam, awayTeam } = matchHeaderInfo;

  return (
    <div className='basketball-box-score-card-container'>
      <div className='container-title'>{labelMaps.BoxScore}</div>
      <div className='container-body'>
        <div className='btn-container'>
          <Button className={`team-btn ${currentActive === 0 ? 'active' : ''}`} size='small' onClick={() => setCurrentActive(0)}>
            <Space className='space-wrap'>
              <img className='team-icon' src={homeTeam.logo} alt={homeTeam.name} loading="lazy"/>
              <span className='team-name'>{homeTeam.name}</span>
            </Space>
          </Button>
          <Button className={`team-btn ${currentActive === 1 ? 'active' : ''}`} size='small' onClick={() => setCurrentActive(1)}>
            <Space className='space-wrap'>
              <img className='team-icon' src={awayTeam.logo} alt={awayTeam.name} loading="lazy"/>
              <span className='team-name'>{awayTeam.name}</span>
            </Space>
          </Button>
        </div>
        <BoxScoreTable data={list} teamInfo={currentActive === 0 ? homeTeam : awayTeam} />
      </div>
    </div>
    // <div className={styles.container}>
    //   <div className={styles.teamBtnBox}>
    //     <div className={`${styles.typeBtn} ${currentActive === 0 && styles.active}`} onClick={() => setCurrentActive(0)}>
    //       <img className={styles.teamImg} src={homeTeam.logo} />
    //       <span className={styles.teamName}>{homeTeam.name}</span>
    //     </div>
    //     <div className={`${styles.typeBtn} ${currentActive === 1 && styles.active}`} onClick={() => setCurrentActive(1)}>
    //       <img className={styles.teamImg} src={awayTeam.logo} />
    //       <span className={styles.teamName}>{awayTeam.name}</span>
    //     </div>
    //   </div>
    //   <div className={styles.tipsBox}>
    //     <div className={styles.tipsItem}>
    //       <i className="icon iconfont iconiconlanqiu fs-12" style={{ color: '#e6732e' }} />
    //       <span className={styles.tipsText}>{labelMaps.OnTheCourt}</span>
    //     </div>
    //     <div className={styles.tipsItem} onClick={clickGlossary}>
    //       <i className="icon iconfont iconwenhao fs-12" style={{ color: '#8e8e8e' }}></i>
    //       <span className={styles.tipsText}>{labelMaps.h5Glossary}</span>
    //     </div>
    //   </div>
    //   <BoxScoreTable data={list} teamInfo={currentActive === 0 ? homeTeam : awayTeam} />
    // </div>
  );
};

export default BoxScoreCard;
