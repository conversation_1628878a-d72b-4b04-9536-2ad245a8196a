// .baseDesc {
//   background-color: #fff;
//   padding-bottom: 20px;

//   .baseRow {
//     display: flex;
//     align-items: center;
//     width: 100%;
//     height: 102px;
//     padding: 0 24px;
//     background: #FFFFFF;
//     border-bottom: 1px solid #E3E3E3;
//     margin-bottom: 2px;
//     box-sizing: border-box;

//     .left {
//       flex: 1;
//       display: flex;
//       flex-direction: column;
//       color: #999999;

//       .time {
//         height: 24px;
//         font-size: 24px;
//         font-weight: 500;
//         color: #999999;
//         line-height: 24px;
//         margin-bottom: 14px;
//       }

//       .team {
//         display: flex;
//         align-items: center;

//         .teamName {
//           width: 180px;
//           overflow: hidden;
//           white-space: nowrap;
//           text-overflow: ellipsis;
//         }

//         .fromTeam {
//           display: flex;
//           align-items: center;
//           font-size: 24px;
//           font-weight: 500;
//           color: #343434;
//           line-height: 30px;

//           .fromTeamLogo {
//             width: 40px;
//             height: 40px;
//             margin-right: 20px;
//           }
//         }

//         .arrow {
//           width: 30px;
//           height: 14px;
//           margin-right: 14px;
//           background-position: center center;
//           background-repeat: no-repeat;
//           background-size: 100% 100%;
//           background-image: url('../../../../assets/images/arrow-right.png');
//         }

//         .toTeam {
//           display: flex;
//           justify-content: center;
//           align-items: center;
//           font-size: 24px;
//           font-weight: 500;
//           color: #343434;
//           line-height: 30px;
//           overflow: hidden;
//           white-space: nowrap;
//           text-overflow: ellipsis;

//           .toTeamLogo {
//             width: 40px;
//             height: 40px;
//             margin-right: 20px;
//           }
//         }
//       }
//     }

//     .right {
//       display: flex;
//       flex-direction: column;
//       justify-content: center;
//       align-items: flex-end;
//       height: 28px;
//       line-height: 28px;
//       font-size: 24px;
//       font-weight: 500;
//       color: #999999;

//       .cost {
//         color: #0F80D9;
//         margin-bottom: 8px;
//       }
//     }
//   }
// }

.player-transfer-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .container-body {
    background: #1e1e1e;
    padding: 10px;

    .transfer-container {
      display: flex;
      flex-direction: column;

      .transfer-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 16px;
        border-radius: 24px;
        background: #2C2C2C;
        font-size: 20px;
        color: #C0C5C9;
      }

      .transfer-body {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: #fff;

        .home-team-container {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          width: 40%;
        }

        .away-team-container {
          display: flex;
          align-items: center;
          width: 40%;
        }

        .home-team-container,
        .away-team-container {
          .team-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 5px 10px;
            overflow: hidden;
            background-size: cover;
          }
        }
      }
    }
  }
}
