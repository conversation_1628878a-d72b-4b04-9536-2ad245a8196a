import MatchBattleList from './MatchBattleList';
import MatchFutureList from './MatchFutureList';
import React from 'react';
import styles from './index.less';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';

interface Params {
  teamId: string;
  size: number;
}

interface Props {
  competition: any;
  homeTeam: any;
  awayTeam: any;
  getMatchFutureList(params: { teamId: string }): Promise<any>;
  getMatchHistoryList(params: { homeTeamId: string; awayTeamId: string; size: number }): Promise<any>;
  getMatchRecentList(params: Params): Promise<any>;
}

const CommonH2HTab: React.FC<Props> = (props: Props) => {
  const { competition, homeTeam, awayTeam, getMatchFutureList, getMatchHistoryList, getMatchRecentList } = props;
  const labelMaps = useTranslateKeysToMaps(['ScoreHalf', 'LatestMatches', 'H2H']);

  return (
    <div className={styles.container}>
      <MatchBattleList
        type="home"
        titleLabel={labelMaps.LatestMatches}
        competition={competition}
        team={homeTeam}
        teamId={homeTeam.id}
        teamName={homeTeam.name}
        request={({ teamId, size }) => getMatchRecentList({ teamId, size })}
      />
      <MatchBattleList
        type="away"
        titleLabel={labelMaps.LatestMatches}
        competition={competition}
        team={awayTeam}
        teamId={awayTeam.id}
        teamName={awayTeam.name}
        request={({ teamId, size }) => getMatchRecentList({ teamId, size })}
      />
      <MatchBattleList
        type="home"
        titleLabel={labelMaps.H2H}
        competition={competition}
        team={homeTeam}
        teamId={homeTeam.id}
        request={({ size }) =>
          getMatchHistoryList({
            homeTeamId: homeTeam.id,
            awayTeamId: awayTeam.id,
            size,
          })
        }
      />
      {/* <MatchFutureList homeTeam={homeTeam} awayTeam={awayTeam} request={(teamId) => getMatchFutureList({ teamId, size: 3 })} /> */}
    </div>
  );
};

export default CommonH2HTab;
