.container {
  width: 100%;
  padding: 16px 0;
  margin-top: 12px;
  background-color: #fff;
}

.teamBtnBox {
  width: 100%;
  padding: 0 34px;
  box-sizing: border-box;
  background: #fff;
  display: flex;
  justify-content: space-between;
}

.typeBtn {
  width: 335px;
  height: 80px;
  background: #f0f0f0;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.active{
  background-color: rgba(255,186,90,.2);
}

.teamImg {
  width: 52px;
  height: 52px;
  border-radius: 0.64rem;
  -o-object-fit: contain;
  object-fit: contain;
  margin-right: 16px;
}

.teamName {
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 24px;
  
  font-weight: 400;
  color: #666;
}

.tipsBox {
  padding: 16px 34px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tipsText {
  font-size: 20px;
  font-family: Roboto-Regular, <PERSON><PERSON>;
  font-weight: 400;
  color: #999;
  margin-left: 6.4px;
}

.tipsItem {
  display: flex;
  align-items: center;
}

.block {
  > div {
    line-height: 40px;
    height: 40px;
    color: #666;
    padding: 0 16px;
    font-weight: 500;
    box-sizing: border-box;
  }
}

.basketball-box-score-card-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .container-body {
    background: #1e1e1e;
    padding: 10px;

    .btn-container {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-evenly;
      padding: 0px 15px;

      .team-btn {
        background-color: #121212;
        color: #fff;
        border: none;
        margin-right: 8px;
        padding: 4px 12px;
        cursor: pointer;
        display: flex;
        align-items: center;
        border-radius: 16px;

        &.active {
          background-color: #fff;
          color: #000;
        }

        .space-wrap {
          display: flex;
          align-items: center;

          .team-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin: 5px 10px 5px 0px;
            overflow: hidden;
            background-size: cover;
          }
  
          .team-name {
            font-size: 24px;
          }
        }
      }
    }
  }
}