import TransferList from '@/components/Competition/Transfer/TransferList';
import Loading from '@/components/Loading';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'antd-mobile';
import { getTransfer } from 'iscommon/api/competition';
import { compare, getLastSixYear } from 'iscommon/utils';
import { inject, observer } from 'mobx-react';
import { useEffect, useState } from 'react';
import styles from './index.less';
import './index.less'
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { DownOutline } from 'antd-mobile-icons'

const yearList: any[] = getLastSixYear(6);

const TransferTab = inject('store')(
  observer((props: any) => {
    const {
      store: { Team },
    } = props;
    const { teamId, teamHeaderInfo } = Team;
    const {
      competition: { id: competitionId },
      currentSeason: { id: seasonId },
    } = teamHeaderInfo;

    const labelMaps = useTranslateKeysToMaps(['SelectTeam', 'SelectAll', 'Departures', 'Arrivals', 'Transfer']);

    const [sourceList, setSourceList] = useState<any[]>([]);
    const [visible, setVisible] = useState<boolean>(false);
    const [year, setYear] = useState<string[]>([yearList[0].value]);
    const [currentType, setCurrentType] = useState<string>('Arrivals');
    const [data, setData] = useState<any[]>([]);
    const [loading, setLoading] = useState<boolean>(false);

    useEffect(() => {
      if (competitionId && seasonId && teamId && year.length) {
        setLoading(true);
        getTransfer({ competitionId, seasonId, teamId, year: year[0] }).then(({ teamTransfers }: any) => {
          if (teamTransfers && teamTransfers.length && teamTransfers[0].transferIn) {
            teamTransfers[0].transferIn.forEach((item: any) => {
              item.getType = 'fromTeam';
            });
          }
          if (teamTransfers && teamTransfers.length && teamTransfers[0].transferOut) {
            teamTransfers[0].transferOut.forEach((item: any) => {
              item.getType = 'toTeam';
            });
          }
          setSourceList(teamTransfers);
          setLoading(false);
        });
      }
    }, [competitionId, seasonId, teamId, year]);

    useEffect(() => {
      if (sourceList.length) {
        let newList: any[] = [];
        sourceList.forEach((item: any) => {
          if (currentType === 'Arrivals') {
            newList = [...newList, ...item.transferIn];
          } else {
          newList = [...newList, ...item.transferOut];
          }
        });
        // @ts-ignore
        setData(newList.sort(compare('transferTime')));
      }
    }, [sourceList, currentType]);

    return (
      <div className='team-transfer-container'>
        <div className='container-title'>
          {labelMaps.Transfer}
          <>
            <Button className='transfer-header-btn' size="small" onClick={() => setVisible(true)}>
              <span className='transfer-content'>
                <span className='transfer-text'>{year}</span>
                <DownOutline className='transfer-icon'/>
              </span>
            </Button>
            <Picker
              columns={[yearList]}
              visible={visible}
              onClose={() => setVisible(false)}
              onConfirm={(v: any) => { setYear(v)}}
            />
          </>
        </div>
        <div className='secondary-title'>
          <Button className={`secondary-title-btn ${currentType === 'Departures' ? 'active' : ''}`} onClick={() => setCurrentType('Departures')}>
            {labelMaps.Departures}
          </Button>
          <Button className={`secondary-title-btn ${currentType === 'Arrivals' ? 'active' : ''}`} onClick={() => setCurrentType('Arrivals')}>
            {labelMaps.Arrivals}
          </Button>
        </div>
        <div className='container-body'>
          <Loading loading={loading} isEmpty={!data.length}>
            <TransferList data={data} type="Arrivals" comeType="Team" />
          </Loading>
        </div>
      </div>
      // <div className={styles.container_box}>
      //   <div className={styles.select_data_container}>
      //     <div className={styles.select_year_style} onClick={() => setVisible(true)}>
      //       <span>{year[0]}</span>
      //       <i className="iconfont iconxiala" style={{ fontSize: 24 }}></i>
      //     </div>
      //   </div>
      //   <div style={{ marginTop: 12 }}>
          // <Loading loading={loading} isEmpty={!data.length}>
          //   <TransferList data={data} type="Arrivals" comeType="Team" />
          // </Loading>
      //   </div>

      //   {/* <InfiniteScroll loadMore={loadMore} hasMore={hasMore} /> */}
      //   <Picker
      //     columns={[yearList]}
      //     visible={visible}
      //     value={year}
      //     onClose={() => setVisible(false)}
      //     onConfirm={(v) => setYear(v as any[])}
      //   />
      // </div>
    );
  }),
);

export default TransferTab;
