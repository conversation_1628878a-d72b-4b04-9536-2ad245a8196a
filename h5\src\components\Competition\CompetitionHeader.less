// .container {
//   display: flex;
//   flex-direction: column;
//   align-items: center;
//   margin-bottom: 20px;
// }

// .logo {
//   width: 108px;
//   height: 108px;
//   margin-bottom: 12px;
// }

// .name {
//   height: 28px;
//   font-size: 28px;
//   color: #ffffff;
//   line-height: 28px;
//   margin-bottom: 26px;
// }

// .info {
//   display: flex;
//   align-items: center;
//   justify-content: center;

//   .countryLogo {
//     display: inline-block;
//     width: 24px;
//     height: 24px;
//     margin-right: 8px;
//     border-radius: 50%;
//     overflow: hidden;
//   }

//   .country {
//     height: 24px;
//     font-size: 24px;
//     color: #ffffff;
//     line-height: 24px;
//   }

//   .worth {
//     height: 24px;
//     font-size: 24px;
//     line-height: 24px;
//     color: #0d80d9;
//     margin-left: 5px;
//   }
// }

.common-competition-header-container {
  background: #1e1e1e;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 80px 32px;
  width: 100%;

  .competition-container {
    display: flex;
    flex-direction: row;
    align-items: center;

    .competition-icon {
      height: 96px;
      width: 96px;
      border-radius: 50%;
      object-fit: contain;
      vertical-align: middle;
      margin-right: 10px;
    }

    .competition-info {
      display: flex;
      flex-direction: column;

      .competition-name-container {
        display: flex;
        align-items: center;

        .competition-name {
          font-size: 28px;
          font-weight: bold;
          color: #fff;
        }
      }

      .competition-detail {
        display: flex;
        align-items: center;

        .country-icon {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin-right: 10px;
          overflow: hidden;
          background-size: cover;
        }

        .country-name {
          font-size: 24px;
          color: #fff;
        }
      }
    }
  }

  .competition-header-btn {
    background: transparent;
    color: #fff;
    border-radius: 32px;
    width: 150px;
  }
}

.adm-picker {
  .adm-picker-header {
    background: #2c2c2c;

    .adm-picker-header-button {
      color: #fff;
    }
  }

  .adm-picker-body {
    .adm-picker-view {
      background: #1e1e1e;
      color: #fff;

      .adm-picker-view-mask-top,
      .adm-picker-view-mask-bottom {
        background: #1e1e1e;
      }
    }
  }
}