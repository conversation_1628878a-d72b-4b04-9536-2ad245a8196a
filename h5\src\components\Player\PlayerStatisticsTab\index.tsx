import { But<PERSON>, <PERSON>lapse, Picker } from 'antd-mobile';
import { DownOutline } from 'antd-mobile-icons'
import { getPlayerStatistics, getPlayerStatisticsSelector } from 'iscommon/api/player';
import { useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { endWith, startWith } from 'iscommon/utils/index';
import { inject, observer } from 'mobx-react';
import moment from 'moment';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'umi';
import './index.less'
import styles from './index.less';

interface Props {
  store?: any;
}

const PlayerStatisticsTab: React.FC<Props> = inject('store')(
  observer(() => {
    const { categoryId: playerId } = useParams<{ categoryId: string }>();
    const labelMaps = useTranslateKeysToMaps([
      'PlayerInfo',
      'Matches',
      'Attacking',
      'Passes',
      'Defending',
      'Other',
      'Cards',
      'TotalPlayed',
      'Started',
      'MinutesPerGame',
      'Goals',
      'GoalsFrequency',
      'GoalsPerGame',
      'ShotsPerGame',
      'Assists',
      'Passes',
      'KeyPasses',
      'AccuratePerGame',
      'AccLongBalls',
      'AccCrosses',
      'InterceptionsPerGame',
      'TacklesPerGame',
      'ClearancesPerGame',
      'SuccDribbles',
      'TotalDuelsWon',
      'Fouls',
      'WasFouled',
      'Offsides',
      'YellowCards',
      'YellowRedCards',
      'RedCards',
    ]);
    const [dataInfo, setDataInfo] = useState<any>({});
    const [competitionList, setCompetitions] = useState<any[]>([]);
    const [competition, setCurrentCompetition] = useState<string>('');
    const [seasonList, setSeasonList] = useState<any[]>([]);
    const [seasonId, setSeasonId] = useState<string>('');
    const [comptitionVisible, setCompetitionPickerVisible] = useState(false);
    const [seasonVisible, setSeasonPickerVisible] = useState(false);
    const [loading, setLoading] = useState<boolean>(false);
    const localSeasonMaps = useRef<any>({});
    const dataKeyMaps: any = useMemo(() => {
      return {
        matches: { label: labelMaps.Matches },
        attacking: { label: labelMaps.Attacking },
        defending: { label: labelMaps.Defending },
        other: { label: labelMaps.Other },
        cards: { label: labelMaps.Cards },

        court: { label: labelMaps.TotalPlayed },
        first: { label: labelMaps.Started },
        minutesPlayed: { label: labelMaps.MinutesPerGame },
        goals: { label: labelMaps.Goals },
        goalFrequency: { label: labelMaps.GoalsFrequency },
        goalPerGame: { label: labelMaps.GoalsPerGame },
        shotPerGame: { label: labelMaps.ShotsPerGame },
        assist: { label: labelMaps.Assists },
        passes: { label: labelMaps.Passes },
        keyPasses: { label: labelMaps.KeyPasses },
        accuracyPasses: { label: labelMaps.AccuratePerGame },
        longBallsAccuracy: { label: labelMaps.AccLongBalls },
        crossesAccuracy: { label: labelMaps.AccCrosses },
        interceptions: { label: labelMaps.InterceptionsPerGame },
        tackles: { label: labelMaps.TacklesPerGame },
        clearances: { label: labelMaps.ClearancesPerGame },
        dribbleSucc: { label: labelMaps.SuccDribbles },
        duelsWon: { label: labelMaps.TotalDuelsWon },
        fouls: { label: labelMaps.Fouls },
        wasFouled: { label: labelMaps.WasFouled },
        offsides: { label: labelMaps.Offsides },
        yellowCards: { label: labelMaps.YellowCards },
        yellow2RedCards: { label: labelMaps.YellowRedCards },
        redCards: { label: labelMaps.RedCards },
      };
    }, [labelMaps]);

    const currentYear = useMemo(() => moment().format('YYYY'), []);
    const prevYear = useMemo(() => moment().subtract(1, 'year').format('YYYY'), []);
    const nextYear = useMemo(() => moment().add(1, 'year').format('YYYY'), []);
    const getCurrentSeason = useCallback((year: string) => startWith(year, currentYear), [currentYear]);
    const getPrevSeason = useCallback((year: string) => endWith(year, prevYear), [prevYear]);
    const getNextSeason = useCallback((year: string) => endWith(year, nextYear), [nextYear]);

    const getStats = useCallback(
      async (competitionId: string, seasonId: string) => {
        setLoading(true);
        const res = await getPlayerStatistics({
          playerId,
          seasonId,
          competitionId,
        });
        // console.log('setDataInfo', res);
        setDataInfo(res || {});
        setLoading(false);
      },
      [playerId],
    );

    const handleCompetition = useCallback(
      (value) => {
        // console.log('choose competition', value);
        if (value) {
          setCurrentCompetition(value);
          const seasons = localSeasonMaps.current[value];
          const seasonList = seasons.map((item: any) => {
            return { label: item.year, value: item.id, competitionId: item.competitionId };
          });
          setSeasonList(seasonList);
          const currentSeason = seasons.find((item: any) => item.isCurrent === 1) || seasons[0];
          if (currentSeason) {
            setSeasonId(currentSeason.id);
            getStats(currentSeason.competitionId, currentSeason.id);
          }
        }
      },
      [getStats],
    );

    const handleSeason = useCallback(
      (id) => {
        // console.log('choose seasonId', id);
        if (id) {
          const season = seasonList.find((i) => i.value === id);
          if (season) {
            setSeasonId(id);
            getStats(season.competitionId, id);
          }
        }
      },
      [getStats, seasonList],
    );

    useEffect(() => {
      if (playerId) {
        setLoading(true);
        getPlayerStatisticsSelector({ playerId }).then((res: any) => {
          if (res?.seasons) {
            localSeasonMaps.current = res.seasons;
            const competitionNames = Object.keys(res.seasons);
            const nextYearName = Object.keys(res.seasons).find((key) =>
              res.seasons[key].find((item: any) => getNextSeason(item.year)),
            );
            const currentYearName = Object.keys(res.seasons).find((key) =>
              res.seasons[key].find((item: any) => getCurrentSeason(item.year)),
            );
            const currentName = nextYearName || currentYearName || Object.keys(res.seasons)[0];
            if (currentName) {
              const seasons = res.seasons[currentName];
              const seasonList = seasons.map((item: any) => {
                return { label: item.year, value: item.id, competitionId: item.competitionId };
              });

              const currentSeason =
                seasons.find(
                  (item: any) => getNextSeason(item.year) || getCurrentSeason(item.year) || getPrevSeason(item.year),
                ) || seasons[0];

              const competitions = competitionNames.map((item: string) => {
                return { label: item, value: item };
              });

              // console.log('competitions', competitions);
              // console.log('currentName', currentName);
              // console.log('seasonList', seasonList);
              // console.log('currentSeason', currentSeason);

              setCompetitions(competitions);
              setCurrentCompetition(currentName);
              setSeasonList(seasonList);
              setSeasonId(currentSeason?.id);

              getStats(currentSeason?.competitionId, currentSeason?.id);
            }
          }
        });
      }
    }, [getCurrentSeason, getNextSeason, getPrevSeason, getStats, playerId]);

    const year = () => {
      const item = seasonList.find((item) => item.value === seasonId);
      return item?.label || '';
    };

    return (
      <div className='player-statistics-container'>
        <div className='container-title'>
          <>
            <Button className='statistics-competition-btn' size="small" onClick={() => setCompetitionPickerVisible(true)}>
              <span className='btn-content'>
                <span className='btn-value'>{competition}</span>
                <DownOutline className='down-icon'/>
              </span>
            </Button>
            <Picker
              columns={[competitionList]}
              visible={comptitionVisible}
              onClose={() => setCompetitionPickerVisible(false)}
              onConfirm={(v) => handleCompetition(v[0])}
            />
          </>
          <>
            <Button className='statistics-season-btn' size="small" onClick={() => setSeasonPickerVisible(true)}>
              <span className='btn-content'>
                <span className='btn-value'>{year()}</span>
                <DownOutline className='down-icon'/>
              </span>
            </Button>
            <Picker
              columns={[seasonList]}
              visible={seasonVisible}
              onClose={() => setSeasonPickerVisible(false)}
              onConfirm={(v) => handleSeason(v[0])}
            />
          </>
        </div>
        <div className='container-body'>
          <Collapse className='custom-collapse'>
            {Object.entries(dataInfo).map(([key, value]) => {
              if (!value) return null; 
              return (
                <Collapse.Panel key={key} title={<span className="panel-header">{dataKeyMaps[key]?.label || key}</span>}>
                  {Object.entries(value).map(([itemKey, itemValue]) => (
                    <div className='desc-item-container' key={itemKey}>
                      <span className='desc-value'>{dataKeyMaps[itemKey]?.label || itemKey}</span>
                      <span className='desc-value'>{itemValue}</span>
                    </div>
                  ))}
                </Collapse.Panel>
              );
            })}
          </Collapse> 
        </div>
      </div>
      // <div className={styles.container}>
      //   <div className={styles.select_data_container}>
      //     <div className={styles.select_team_style} onClick={() => setCompetitionPickerVisible(true)}>
      //       <div className={styles.team_box}>
      //         {/* {competitionInfo[0]?.logo ? (
      //          <Avatar src={competitionInfo[0]?.logo} style={{ width: 20, height: 20 }} />
      //          ) : (
      //          <span
      //          style={{
      //          width: 20,
      //          height: 20,
      //          backgroundColor: '#D8D8D8',
      //          borderRadius: '50%',
      //          }}
      //          ></span>
      //          )} */}
      //         <span className={styles.competition_name}>{competition}</span>
      //       </div>
      //       <i className="iconfont iconxiala" style={{ fontSize: 24 }}></i>
      //     </div>
      //     <div className={styles.select_year_style} onClick={() => setSeasonPickerVisible(true)}>
      //       <span>{year()}</span>
      //       <i className="iconfont iconxiala" style={{ fontSize: 24 }}></i>
      //     </div>
      //   </div>
      //   {Object.keys(dataInfo || {}).map((key) => {
          // const value = dataInfo[key];
          // if (!value) return null;
          // return (
          //   <div className={styles.stats_container} key={key}>
          //     <div className={styles.list_item}>{dataKeyMaps[key]?.label || key}</div>
      //         {Object.keys(value).map((subKey) => {
      //           const subValue = value[subKey];
      //           return (
      //             <div className={styles.list_item} key={subKey}>
      //               <span className={styles.color_999}>{dataKeyMaps[subKey]?.label || subKey}</span>
      //               <span>{subValue}</span>
      //             </div>
      //           );
      //         })}
      //       </div>
      //     );
      //   })}
      //   <Picker
      //     columns={[seasonList]}
      //     visible={seasonVisible}
      //     value={[seasonId]}
      //     onClose={() => setSeasonPickerVisible(false)}
      //     onConfirm={(v) => handleSeason(v[0])}
      //   />
      //   <Picker
      //     columns={[competitionList]}
      //     visible={comptitionVisible}
      //     value={[competition]}
      //     onClose={() => setCompetitionPickerVisible(false)}
      //     onConfirm={(v) => handleCompetition(v[0])}
      //   />
      // </div>
    );
  }),
);

export default PlayerStatisticsTab;
