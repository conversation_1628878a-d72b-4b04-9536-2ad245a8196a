// .container {
//   display: flex;
//   flex-direction: column;
//   width: 100%;
// }

// .select_data_container {
//   width: 100%;
//   height: 72px;
//   display: flex;
//   align-items: center;
//   background-color: #fff;
//   margin-top: 16px;
//   padding: 0 13px;
// }

// .select_team_style {
//   display: flex;
//   flex-direction: row;
//   justify-content: space-between;
//   flex: 1;
//   align-items: center;
// }

// .team_box {
//   display: flex;
//   flex-direction: row;
// }

// .select_year_style {
//   display: flex;
//   flex-direction: row;
//   justify-content: space-between;
//   align-items: center;
//   padding-left: 0.32rem;
//   // margin-left: 0.32rem;
//   position: relative;
//   width: 312px;
// }

// .select_year_style:nth-child(2):before {
//   border-left: 1px solid #e3e3e3;
//   margin-top: -0.26rem;
//   position: absolute;
//   height: 0.53rem;
//   content: '';
//   left: 1px;
//   top: 50%;
// }

// .transfer_type_style {
//   padding: 15px 20px;
//   display: flex;
//   flex-direction: row;
//   align-items: center;
// }

// .competition_name {
//   max-width: 320px;
//   overflow: hidden; //超出的文本隐藏
//   text-overflow: ellipsis; //溢出用省略号显示
//   white-space: nowrap; //溢出不换行
//   margin-left: 24px;
// }

// .stats_container {
//   display: flex;
//   flex-direction: column;
//   width: 100%;
//   color: #999;
// }

// .list_item {
//   height: 100%;
//   padding: 0 20px;
//   height: 60px;
//   display: flex;
//   align-items: center;
//   color: #333;
//   justify-content: space-between;
// }

// .list_item:not(:first-child) {
//   background: white;
// }

// .color_333 {
//   color: #333;
// }

// .color_999 {
//   color: #999;
// }

.player-statistics-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;

    .statistics-competition-btn,
    .statistics-season-btn {
      display: block;
      background: transparent;
      color: #fff;
      border-radius: 32px;

      .btn-content {
        display: flex; 
        justify-content: space-between;
        align-items: center;
        width: 100%;

        .btn-value {
          white-space: nowrap;   
          overflow: hidden;      
          text-overflow: ellipsis;
          flex-grow: 1;
          margin-right: 8px;
        }

        .down-icon {
          flex-shrink: 0;  
          margin-left: auto;
        }
      }
    }

    .statistics-season-btn {
      width: 250px;
    }

    .statistics-competition-btn {
      width: 400px;
    }
  }
  
  .container-body {
    background: #1e1e1e;
    padding: 10px;

    .custom-collapse {
      .adm-list-default, 
      .adm-list-body,
      .adm-list-item,
      .adm-list-item-content {
        background: transparent;
        border: none;
      }

      .adm-list {
        --border-inner: none;
      }

      .panel-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 22px;
        font-weight: bold;
        color: #fff;
        text-overflow: ellipsis;
      }

      .desc-item-container {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 10px;
        margin: 5px;
        border-bottom: 1px solid #fff;

        .desc-value {
          font-size: 22px;
          color: #fff;
        }
      }
    }
  }
}

// .adm-picker {
//   .adm-picker-header {
//     background: #2c2c2c;

//     .adm-picker-header-button {
//       color: #fff;
//     }
//   }

//   .adm-picker-body {
//     .adm-picker-view {
//       background: #1e1e1e;
//       color: #fff;

//       .adm-picker-view-mask-top,
//       .adm-picker-view-mask-bottom {
//         background: #1e1e1e;
//       }
//     }
//   }
// }