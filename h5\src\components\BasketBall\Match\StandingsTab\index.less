// .container_box {
//   display: flex;
//   flex-direction: column;
// }

// .radio_container {
//   background: rgb(241, 241, 241);
//   display: flex;
//   flex-direction: row;
//   align-items: center;
//   padding: 12px 0;
// }

// .color_999 {
//   color: #999;
// }

// .radio_btn {
//   padding: 8px 20px;
//   font-size: 24px;
//   color: #999;
//   display: inline-block;
//   border-radius: 4px;
//   overflow: hidden;
//   white-space: nowrap;
//   text-overflow: ellipsis;
//   margin-left: 24px;
// }

// // .radio_btn:not(:first-child) {
// //   margin-left: 24px;
// // }

// .active {
//   background: #2196f3;
//   color: #fff;
// }

// .table_title_team {
//   height: 60px;
//   display: flex;
//   flex-direction: row;
//   align-items: center;
// }

// .table_title_team_name {
//   color: #999;
//   padding-left: 20px;
// }

// .table_title_team_img {
//   width: 38px;
//   height: 38px;
//   object-fit: fill;
//   overflow: hidden;
//   border-radius: 50%;
//   margin-left: 24px;
// }

// .comp{
//   padding: 10px;
//   color: #999;
//   img {
//     width: 40px;
//     margin-right: 10px;
//   }
// }

.basketball-overview-standing-container {
  display: flex;
  flex-direction: column;
  border-radius: 24px;
  margin: 10px;
  flex: 1;
  overflow: hidden;
  
  .container-title {
    font-size: 26px;
    font-weight: 700;
    background: #2c2c2c;
    color: #fff;
    border-radius: 24px 24px 0px 0px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding: 10px 15px 10px 15px;
    align-items: center;
  }

  .container-body {
    background: #1e1e1e;
    padding: 10px;

    .custom-collapse {
      .adm-list-default, 
      .adm-list-body,
      .adm-list-item,
      .adm-list-item-content {
        background: transparent;
        border: none;
      }

      .adm-list {
        --border-inner: none;
      }

      .adm-list-item-content {
        padding-right: 0px;
      }

      .panel-header {
        display: flex;
        align-items: center;

        .panel-title {
          font-size: 22px;
          font-weight: bold;
          color: #fff;
          text-overflow: ellipsis;
        }
      }
    }
  }
}