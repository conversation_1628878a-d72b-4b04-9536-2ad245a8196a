import { getArrayFromString } from ".";

const filterValidMatch = (matches, inLiveStatusEnum) => {
  const validMatch = [];
  const eachMatchIds = [];
  let minTime = 0;
  const currentTime = Math.floor(Date.now() / 1000);
  for (let j = 0; j < matches.length; j++) {
    const matchItem = matches[j];
    const { statusId, matchTime, id, scores } = matchItem;
    const matchStatus = statusId;
    const matchId = id;
    let isValid = true;
    // 筛选出正在进行中的比赛
    if (inLiveStatusEnum.includes(matchStatus)) {
      matchItem.inProgress = true;
      matchItem.period = true;
      matchItem.matchActiveTime = matchTime;
      // If match is older than last 24 hours
      if (matchTime < (currentTime - 24 * 60 * 60)) {
        isValid = false;
      }
    } else if(!matchTime || statusId === 0) {
      // 非live的，如果没有matchTime，过滤掉
      isValid = false;
    }
    if (isValid) {
      minTime = minTime === 0 ? matchTime : Math.min(matchTime, minTime);
      validMatch.push({
        ...matchItem,
        matchStatus,
        matchId,
        scores: getArrayFromString(scores)
      });
      eachMatchIds.push(matchId);
    }
  }

  return { validMatch, minTime, eachMatchIds };
};

export const filterEachCompetitions = (result, inLiveStatusEnum) => {
  try {
    let total = 0;
    let matchIds = [];
    const { matchGroups } = result;
    let competitions = []
    for (let i = 0; i < matchGroups.length; i++) {
      const {uniqueTournament, tournament} = matchGroups[i]
      const {
        name,
        id,
      } = (uniqueTournament || tournament)
      const category = uniqueTournament ? uniqueTournament.category : {
        id,
        logo: '',
        name
      }
      const {
        matches
      } = matchGroups[i]
      let comp = {
        additionalCompetitionName:'',
        competitionId: id,
        competitionName: name,
        country: {
          ...category,
          logo: uniqueTournament ? uniqueTournament.logo : ''
        },
        onlineCount: null
      }
      if (!matches || !matches.length) {
        comp['matches'] = [];
        continue;
      }
      const { validMatch, minTime, eachMatchIds } = filterValidMatch(matches, inLiveStatusEnum);
      comp['matches'] = validMatch;
      total += validMatch.length;
      comp['minMatchTime'] = minTime;
      matchIds = matchIds.concat(eachMatchIds);

      competitions.push(comp);
    }
    console.log(competitions, '==competitions')
    console.log(matchIds, '==matches')
    return { competitions, total, matchIds };
  } catch (e) {
    console.log(e)
    return { competitions: [], total: 0, matchIds: [] };
  }
}