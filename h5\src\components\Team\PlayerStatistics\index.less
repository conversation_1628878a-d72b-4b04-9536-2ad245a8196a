.container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.select_data_container {
  width: 100%;
  height: 72px;
  display: flex;
  align-items: center;
  background-color: #fff;
  margin-top: 16px;
  padding: 0 13px;
}

.select_team_style {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  flex: 1;
  align-items: center;
}

.team_box {
  display: flex;
  flex-direction: row;
}

.select_year_style {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding-left: 0.32rem;
  // margin-left: 0.32rem;
  position: relative;
  width: 312px;
}

.select_year_style:nth-child(2):before {
  border-left: 1px solid #e3e3e3;
  margin-top: -0.26rem;
  position: absolute;
  height: 0.53rem;
  content: '';
  left: 1px;
  top: 50%;
}

.transfer_type_style {
  padding: 15px 20px;
  display: flex;
  flex-direction: row;
  align-items: center;
}

.competition_name {
  max-width: 680px;
  overflow: hidden; //超出的文本隐藏
  text-overflow: ellipsis; //溢出用省略号显示
  white-space: nowrap; //溢出不换行
  margin-left: 24px;
}

.stats_container {
  display: flex;
  flex-direction: column;
  width: 100%;
  color: #999;
}

.list_item {
  height: 100%;
  padding: 0 20px;
  height: 60px;
  display: flex;
  align-items: center;
  color: #333;
  justify-content: space-between;
}

.list_item:not(:first-child) {
  background: white;
}

.color_333 {
  color: #333;
}

.color_999 {
  color: #999;
}
