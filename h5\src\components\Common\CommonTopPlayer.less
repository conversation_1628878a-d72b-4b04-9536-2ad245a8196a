.box {
  position: relative;
}

.container {
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
  min-height: 210px;
  // height: 200px;
  // overflow: hidden;
}

.row_item {
  height: 70px;
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0 20px;
}

.icon_column {
  // width: 45px;
  height: 100%;
  color: #ffca28;
  font-size: 8.48px;
  display: flex;
  align-items: center;
  // width: 30px;
}

.avatar_style {
  --size: 50px;
  --border-radius: 50%;
  border: 2px solid #eeeeee;
  margin: 0 20px;
}

.player_info_column {
  display: flex;
  flex-direction: column;
  justify-content: center;
  flex: 1;
  height: 100%;
}

.player_name_style {
  font-size: 24px;
  // line-height: 20px;
}

.team_name_style {
  font-size: 24px;
  line-height: 24px;
  color: #999;
  margin-left: 10px;
}

.team_style {
  display: inline-flex;
  align-items: center;
}

.team_avatar_style {
  --size: 28px;
  --border-radius: 50%;
  border: 2px solid #eeeeee;
}

.value_column {
  color: #0f80da;
  justify-content: flex-end;
  align-items: center;
  font-weight: 400;
  display: flex;
  height: 100%;
  width: 32px;
}

.action_style {
  text-align: center;
  font-size: 15px;
  background-color: white;
  transform: rotate(0deg);
}

.action_on {
  transform: rotate(180deg);
}
