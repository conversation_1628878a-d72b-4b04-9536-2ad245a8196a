// @ts-nocheck
// const HomeDataList = {
//   '2022-10-15': {
//     All: {}
//   }
// }
import {simpleCloneObj} from 'iscommon/utils'

export const HomeDataList = {}

export const setHomeListWithDate = (data, date, tab) => {
  const dateData = HomeDataList[date]
  if(dateData) {
    HomeDataList[date][tab] = simpleCloneObj(data)
  } else {
    const listData = {}
    listData[tab] = simpleCloneObj(data)
    HomeDataList[date] = listData
  }
}

export const getHomeListWithDate = (date, tab) => {
  try {
    return simpleCloneObj(HomeDataList[date][tab])
  } catch(e) {
    return {}
  }
}