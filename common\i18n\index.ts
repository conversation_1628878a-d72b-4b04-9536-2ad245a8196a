import i18n from 'i18next';
import moment from 'moment';
import { initReactI18next } from 'react-i18next';
import { GlobalConfig } from '../const/globalConfig';
// import resources from "../locales/resource";
import resourcesToBackend from 'i18next-resources-to-backend';
// @ts-ignore
import resources from '../locales-module/en';
// don't want to use this?
// have a look at the Quick start guide
// for passing in lng and translations on init

// GlobalUtils.setGlobalLng('zht');

i18n
  .use(resourcesToBackend(() => import(`../locales/${GlobalConfig.lng}.json`)))
  // pass the i18n instance to react-i18next.
  .use(initReactI18next)
  // init i18next
  // for all options read: https://www.i18next.com/overview/configuration-options
  .init({
    fallbackLng: 'en',
    lng: GlobalConfig.lng,
    debug: false,
    partialBundledLanguages: true,
    resources: resources,
    // backend: {
    //   // for all available options read the backend's repository readme file
    //   loadPath: '/locales/{{lng}}.json'
    // },
    interpolation: {
      escapeValue: false, // not needed for react as it escapes by default
    },
  });

// i18n.loadResources(() => import(`../locales/${GlobalConfig.lng}.json`))

moment.locale('en');

export default i18n;
