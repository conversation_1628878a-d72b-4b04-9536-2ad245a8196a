import { Avatar, Badge } from 'antd-mobile';
import { changePlayerName, formatRateColor, sortRating } from 'iscommon/utils/dataUtils';
import { iconProps } from 'iscommon/const/constant';
import React, { useMemo } from 'react';
import styles from './LineupList.less';

import './LineupList.less'
import { translate, useTranslateKeysToMaps } from 'iscommon/i18n/utils';
import { FallbackImage, FallbackPlayerImage } from 'iscommon/const/icon';

const getBadgeBackgroundColor = (count: number) => {
  if (count >= 8.0 && count <= 10.0) {
    return '#00ADC4';
  } else if (count >= 7.0 && count <= 7.9) {
    return '#1EC853';
  } else if (count >= 6.6 && count <= 6.9) {
    return '#FFAE0F';
  } else if (count >= 6.0 && count <= 6.5) {
    return '#F08022';
  } else if (count >= 0.1 && count <= 5.9) {
    return '#ED5454';
  }
};

const LineupList: React.FC = (props: any) => {
  const { homeSubstitute = [], awaySubstitute, matchLineupInfo = {} } = props;
  const { homeTeam = {}, awayTeam = {} } = matchLineupInfo;

  const iconH5Maps = {
    1: 'icongoal', //进球
    3: 'icon-yellow-card', //黄牌,
    4: 'icon-red-card', //红牌
    8: 'icon-Penalty', // 点球
    9: 'iconin', //换人
    15: 'icon-twoyellow-red', //两黄变红
    16: 'iconPenaltySaved', //点球未进
    17: 'icon-own-goal', //乌龙球
  };

  const labelMaps = useTranslateKeysToMaps([
    'Coach',
    'Forwards',
    'Midfielders',
    'Defender',
    'Goalkeeper',
  ]);
  
  const positionList = useMemo(
    () => [
      { key: 'F', label: labelMaps.Forwards },
      { key: 'M', label: labelMaps.Midfielders },
      { key: 'D', label: labelMaps.Defender },
      { key: 'G', label: labelMaps.Goalkeeper },
    ],
    [labelMaps],
  );

  const renderSubstitute = (renderList = [], teamInfo = {}) => {
    
    return (
      <div className='container-column'>
        <div className='coach-container'>
          <span>{teamInfo.coachName}</span>
          <span className='coach-label'>{labelMaps.Coach}</span>
        </div>
        {renderList.map((item: any) => (
          <div className='player-container'>
            <div className='player-left-aligned-container'>
              <div className='player-shirt-container'>
                <span className='player-shirt-number'>{item?.shirtNumber}</span>
              </div>
              <img className='player-icon' src={item?.logo || FallbackPlayerImage} alt={item?.name}/>
              <div className='player-info-container'>
                <span className='player-name'>{changePlayerName(item?.name)}</span>
                <span className='player-position'>{positionList.find(pos => pos.key === item.position)?.label || item.position}</span>
              </div>
              <div className='player-incident-container'>
                {item.incidents &&
                  Object.keys(item.incidents).map((val: any) =>
                    <div className='player-incident'>
                      <img src={iconProps[val]} className='incident-icon'/>
                      {val === '9' ? <span className='incident-time'>{`${item.incidents[val][0]?.minute}'`}</span> : null}
                    </div>
                  )
                }
              </div>
            </div>
            {item.rating > 0 && (
              <Badge className='player-rating-text-size' content={parseFloat(item.rating || 0).toFixed(1)} style={{ background: getBadgeBackgroundColor(parseFloat(item.rating || 0)) }}/>
            )}
          </div>
        ))}
      </div>
      // <div className={styles.lineup_list_column}>
      //   <div className={styles.lineup_list_item}>
      //     <Avatar src={teamInfo?.logo} style={{ '--size': '20px', margin: '0 12.48px' }} />
      //     <span>{teamInfo?.name}</span>
      //   </div>
      //   {renderList.map((item: any) => (
      //     <div className={styles.lineup_list_item} key={item.name}>
      //       <div className={styles.line_item_left}>
      //         <span className={styles.shirtNumber}>{item.shirtNumber}</span>
      //         <div className={styles.player_avatar_box}>
      //           <Avatar src={item.logo} style={{ '--size': '30px', borderRadius: '50%', border: '1px solid #eee' }} />
      //           {item.rating != '0.0' && (
      //             <span className={styles.player_rating} style={{ background: formatRateColor(item.rating) }}>
      //               {item.rating}
      //             </span>
      //           )}
      //         </div>
      //         <div className={styles.player_name_box}>
      //           <span className={styles.player_name}>{changePlayerName(item.name)}</span>
      //         </div>
      //       </div>
            // <div className={styles.event_box}>
            //   {item.incidents &&
            //     Object.keys(item.incidents).map((val: any) =>
            //       ['1', '3', '4', '9', '15'].includes(val) ? (
            //         <div className={styles.event} key={val + Math.random()}>
            //           {/* <div className={styles.align_center}> */}
            //           <svg className="svg-icon" style={{ width: 16, height: 16 }}>
            //             <use xlinkHref={`#${iconH5Maps[val]}`} className="h16"></use>
            //           </svg>
            //           {val === '9' ? <span className={styles.event_time}>{`${item.incidents[val][0]?.minute}'`}</span> : null}
            //         </div>
            //       ) : // </div>
            //       null,
            //     )}
            //   {/* <svg aria-hidden="true" className={`icon ${styles.fs_12}`} style={{ width: 15, height: 15 }}>
            //       <use xlinkHref="#icon-red-card"></use>
            //     </svg>
            //     <span>23'</span> */}
            // </div>
      //     </div>
      //   ))}
      // </div>
    );
  };

  console.log('home123', homeTeam)

  return (
    <div className='line-up-list-container'>
      <div className='container-title'>{translate('Coaches and Substitutes')}</div>
      <div className='container-body'>
        <div className='team-container-body'>
          <div className='home-body-left-aligned'>
            <img className='team-logo' src={homeTeam?.logo || FallbackImage} alt={homeTeam?.name}/>
            <span className='team-name'>{homeTeam?.name}</span>
          </div>
          <div className='away-body-right-aligned'>
            <span className='team-name'>{awayTeam?.name}</span>
            <img className='team-logo' src={awayTeam?.logo || FallbackImage} alt={awayTeam?.name}/>
          </div>
        </div>
        <div className='player-line-up-container'>
          {renderSubstitute(sortRating(homeSubstitute), homeTeam)}
          {renderSubstitute(sortRating(awaySubstitute), awayTeam)}
        </div>
      </div>
    </div>
    // <div className={styles.container}>
    //   {renderSubstitute(sortRating(homeSubstitute), homeTeam)}
    //   {renderSubstitute(sortRating(awaySubstitute), awayTeam)}
    // </div>
  );
};

export default LineupList;
