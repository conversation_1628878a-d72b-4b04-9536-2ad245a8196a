import { getGameVideoInfo } from "iscommon/api/match";
import { useTranslateKeysToMaps } from "iscommon/i18n/utils";
import { getLiveUrl } from 'iscommon/utils/live';
import { inject, observer } from "mobx-react";
import { useEffect, useState } from "react";

import './LiveAndVideo.less'
import { GlobalConfig } from 'iscommon/const/globalConfig';

const showLiveStream = () => {
  window.gtag('event', 'click_stream_button', { platform: 'mobile' });
  window.location.href = 'https://onelink.to/igscore-newapp';
};

const Animation = observer(({ matchId }: { matchId: any }) => {
  const src = getLiveUrl(matchId);
  const liveHeight = (404 / 800) * document.body.offsetWidth + 130;

  useEffect(() => {
    if (src) {
      window.gtag('event', 'animation_module_view', { platform: 'mobile' });
    }
  }, [src]);

  if (!src) return null;

  return (
    <div className="live-animation-container" style={{ height: liveHeight }}>
      <iframe className="frame-container" src={src} height={liveHeight} />
    </div>
  );
});

const LiveAndVideo = inject('store')(
  observer((props: any) => {
    const {
      store: { Match },
    } = props;
    const { matchId, matchHeaderInfo } = Match;

    const [tab, setTab] = useState(0);
    const [videoLink, setVideoLink] = useState('');
    const labelMaps = useTranslateKeysToMaps(['Animation', 'LiveStream']);

    useEffect(() => {
      if (matchId && matchId !== -1 && matchHeaderInfo) {
        const getVideoLink = async () => {
          try {
            const { link }: any = await getGameVideoInfo(matchId);
            
            if (link) {
              setVideoLink(link);
            }
          } catch (e) {
            console.error("Failed to fetch video link", e);
          }
        };

        getVideoLink();
      }
    }, [matchId, matchHeaderInfo]);

    const id = `${GlobalConfig.pathname}LivePlugin`;

    return (
      <div className='overview-live-animation-container' id={id} >
        <div className='container-title'>
          <div key={1} className={`container-item ${tab === 0 ? 'active' : ''}`} onClick={() => setTab(0)}>
            {labelMaps.Animation}
          </div>
          {videoLink && (
            <div key={2} className={`container-item ${tab === 1 ? 'active' : ''}`} onClick={showLiveStream}>
              {labelMaps.LiveStream}
            </div>
          )} 
        </div>
        {tab === 0 && <Animation matchId={matchId}/>}
        {tab === 1 && videoLink && <LiveStream videoLink={videoLink} />}
      </div>
    );
  }),
);

export default LiveAndVideo;